# Test Script: Storefront Product Variants

## Test Scenario: Verify that the storefront product details page properly displays and handles variants

### **Test Setup:**
1. Ensure you have a product with variants in the database
2. Navigate to the storefront product page: `/products/[product-slug]`

### **Test Cases:**

## **Test 1: Basic Variant Display**
**Objective:** Verify that variants are properly displayed and selectable

**Steps:**
1. Navigate to a product page with variants
2. Check that the product info section shows:
   - Product title and description
   - Base price (from default variant)
   - Available colors and sizes
   - Variant-specific information (SKU, title)

**Expected Results:**
- ✅ Product displays with variant options
- ✅ Color swatches are clickable and show available colors only
- ✅ Size options show available sizes only
- ✅ SKU and variant title are displayed when variant is selected
- ✅ Inventory status shows correct stock levels

## **Test 2: Variant Selection and Price Updates**
**Objective:** Verify that selecting variants updates price and availability

**Steps:**
1. Select different color options
2. Select different size options
3. Observe price changes
4. Check inventory status updates

**Expected Results:**
- ✅ Price updates when selecting different variants
- ✅ Compare-at-price shows correctly for sale variants
- ✅ Inventory status reflects selected variant stock
- ✅ Out-of-stock variants show appropriate messaging
- ✅ Add to cart button is disabled for unavailable variants

## **Test 3: Gallery Integration**
**Objective:** Verify that variant selection affects product gallery

**Steps:**
1. Select different variants
2. Check if gallery images update
3. Verify variant-specific images are prioritized

**Expected Results:**
- ✅ Gallery shows variant-specific images when available
- ✅ Variant badge appears on main image
- ✅ Out-of-stock badge shows for unavailable variants
- ✅ Image counter updates correctly
- ✅ Thumbnail navigation works properly

## **Test 4: Add to Cart with Variants**
**Objective:** Verify that cart integration works with variants

**Steps:**
1. Select a specific variant (color + size)
2. Set quantity
3. Click "Add to Cart"
4. Check cart contents

**Expected Results:**
- ✅ Cart item includes variant ID
- ✅ Cart shows correct variant details (color, size, SKU)
- ✅ Price in cart matches selected variant price
- ✅ Variant title/options are displayed in cart
- ✅ Out-of-stock variants cannot be added to cart

## **Test 5: Availability Filtering**
**Objective:** Verify that only available options are shown

**Steps:**
1. Navigate to product with some out-of-stock variants
2. Check color options
3. Select a color and check size options
4. Verify filtering behavior

**Expected Results:**
- ✅ Only colors with available variants are shown
- ✅ Size options filter based on selected color
- ✅ Unavailable combinations are not selectable
- ✅ Clear messaging when no variants are available
- ✅ Graceful handling of all out-of-stock scenarios

## **Test 6: Variant Information Display**
**Objective:** Verify that variant-specific information is properly shown

**Steps:**
1. Select different variants
2. Check information updates
3. Verify all variant details are accessible

**Expected Results:**
- ✅ SKU updates for each variant
- ✅ Variant title shows when available
- ✅ Individual variant pricing displays
- ✅ Inventory quantity shows correctly
- ✅ Variant options (color/size names) display properly

## **Test 7: Error Handling**
**Objective:** Verify graceful handling of edge cases

**Steps:**
1. Test product with no variants
2. Test product with only one variant
3. Test product with all variants out of stock
4. Test invalid variant combinations

**Expected Results:**
- ✅ Products without variants work normally
- ✅ Single variant products show appropriate UI
- ✅ All out-of-stock products show clear messaging
- ✅ Invalid selections are prevented
- ✅ Error states are user-friendly

## **Test 8: Performance and UX**
**Objective:** Verify smooth user experience

**Steps:**
1. Test variant selection speed
2. Check for loading states
3. Verify smooth transitions
4. Test on mobile devices

**Expected Results:**
- ✅ Variant selection is responsive
- ✅ Price updates are immediate
- ✅ Gallery transitions are smooth
- ✅ Mobile experience is optimized
- ✅ No flickering or layout shifts

## **Test 9: SEO and Accessibility**
**Objective:** Verify proper SEO and accessibility implementation

**Steps:**
1. Check page titles and meta descriptions
2. Test keyboard navigation
3. Verify screen reader compatibility
4. Check image alt texts

**Expected Results:**
- ✅ Page title includes variant information
- ✅ All interactive elements are keyboard accessible
- ✅ Screen readers can navigate variant options
- ✅ Images have descriptive alt texts
- ✅ Proper ARIA labels are used

## **Test 10: Integration with Other Features**
**Objective:** Verify variants work with other storefront features

**Steps:**
1. Test wishlist functionality with variants
2. Check social sharing with variant URLs
3. Verify related products work correctly
4. Test size guide integration

**Expected Results:**
- ✅ Wishlist saves product (not specific variant)
- ✅ Social sharing includes variant context
- ✅ Related products algorithm works
- ✅ Size guide shows relevant information
- ✅ All integrations maintain variant context

## **API Integration Tests:**

### **Test A: Variant Data Loading**
```bash
# Test variant API endpoint
curl -X GET "http://localhost:3000/api/e-commerce/products/[product-id]/variants"
```

**Expected Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "variant-id",
      "title": "Red Large",
      "sku": "PROD-RED-L",
      "price": { "amount": 299, "currency": "ZAR" },
      "available": true,
      "inventoryQuantity": 10,
      "options": [
        { "name": "Color", "value": "Red" },
        { "name": "Size", "value": "Large" }
      ]
    }
  ]
}
```

### **Test B: Product with Variants Loading**
```bash
# Test product API with variants
curl -X GET "http://localhost:3000/api/e-commerce/products/slug/[product-slug]"
```

**Expected Response:**
- Product data includes variants array
- Variants have proper structure
- Default variant is identified
- All variant options are included

## **Browser Console Tests:**

### **Test C: Variant Selection Logging**
1. Open browser console
2. Select different variants
3. Check console logs for:
   - Variant selection events
   - Price update calculations
   - Cart integration calls
   - Error handling

### **Test D: Network Requests**
1. Open Network tab in DevTools
2. Navigate to product page
3. Select variants
4. Verify:
   - Minimal API calls
   - Efficient data loading
   - Proper caching
   - No unnecessary requests

## **Success Criteria:**

### **Functional Requirements:**
- ✅ All variant selection works correctly
- ✅ Prices update accurately
- ✅ Inventory status is real-time
- ✅ Cart integration includes variant data
- ✅ Gallery shows variant-specific content

### **Performance Requirements:**
- ✅ Page loads in under 2 seconds
- ✅ Variant selection is instant
- ✅ No layout shifts during updates
- ✅ Mobile performance is optimized

### **UX Requirements:**
- ✅ Interface is intuitive and clear
- ✅ Error states are helpful
- ✅ Accessibility standards are met
- ✅ Visual feedback is immediate

### **Technical Requirements:**
- ✅ API integration is robust
- ✅ Error handling is comprehensive
- ✅ Code is maintainable
- ✅ SEO is properly implemented
