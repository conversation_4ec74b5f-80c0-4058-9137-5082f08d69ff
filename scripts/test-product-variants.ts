#!/usr/bin/env tsx

/**
 * Test script for Product Variants API
 * Tests the enhanced variant service functionality
 */

import { EnhancedVariantService } from '../lib/ecommerce/modules/products/enhanced-variant-service'

async function testProductVariants() {
  console.log('🧪 Testing Product Variants API...')
  
  const variantService = new EnhancedVariantService()
  
  // Test 1: Get variants for a non-existent product
  console.log('\n📋 Test 1: Get variants for non-existent product')
  try {
    const result = await variantService.getVariantsByProduct('non-existent-id')
    console.log('✅ Result:', result.success ? 'Success' : 'Failed as expected')
    console.log('📊 Data:', result.data?.length || 0, 'variants found')
  } catch (error) {
    console.log('❌ Error:', error)
  }

  // Test 2: Create variant with invalid data
  console.log('\n📋 Test 2: Create variant with invalid data')
  try {
    const result = await variantService.createVariant({
      productId: 'test-product',
      title: '', // Invalid: empty title
      price: -10, // Invalid: negative price
    })
    console.log('✅ Result:', result.success ? 'Unexpected success' : 'Failed as expected')
    console.log('📝 Error:', result.error?.message)
  } catch (error) {
    console.log('❌ Error:', error)
  }

  // Test 3: Create variants in bulk with mixed data
  console.log('\n📋 Test 3: Create variants in bulk')
  try {
    const variants = [
      {
        title: 'Test Variant 1',
        price: { amount: 100, currency: 'ZAR' },
        sku: 'TEST-001',
        inventoryQuantity: 10,
        available: true,
        taxable: true,
        requiresShipping: true,
        trackQuantity: true,
      },
      {
        title: 'Test Variant 2',
        price: { amount: 150, currency: 'ZAR' },
        sku: 'TEST-002',
        inventoryQuantity: 5,
        available: true,
        taxable: true,
        requiresShipping: true,
        trackQuantity: true,
      }
    ]

    const result = await variantService.createVariants('test-product-id', variants as any)
    console.log('✅ Result:', result.success ? 'Success' : 'Failed as expected')
    console.log('📝 Message:', result.error?.message || `Created ${result.data?.length} variants`)
  } catch (error) {
    console.log('❌ Error:', error)
  }

  // Test 4: Delete variants
  console.log('\n📋 Test 4: Delete variants')
  try {
    const result = await variantService.deleteVariants(['variant-1', 'variant-2'])
    console.log('✅ Result:', result.success ? 'Success' : 'Failed as expected')
    console.log('📝 Message:', result.error?.message || 'Variants deleted')
  } catch (error) {
    console.log('❌ Error:', error)
  }

  // Test 5: Bulk update variants
  console.log('\n📋 Test 5: Bulk update variants')
  try {
    const updates = [
      {
        id: 'variant-1',
        price: { amount: 200, currency: 'ZAR' },
        inventoryQuantity: 20,
      },
      {
        id: 'variant-2',
        price: 250,
        inventoryQuantity: 15,
      }
    ]
    
    const result = await variantService.bulkUpdateVariants(updates)
    console.log('✅ Result:', result.success ? 'Success' : 'Failed as expected')
    console.log('📝 Message:', result.error?.message || `Updated ${result.data?.length} variants`)
  } catch (error) {
    console.log('❌ Error:', error)
  }

  console.log('\n🎉 Product Variants API testing completed!')
}

// Run the test
testProductVariants().catch(console.error)
