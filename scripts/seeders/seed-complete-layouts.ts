/**
 * Complete Layouts Seeder
 * Seeds comprehensive layout collection including NextJS layouts, landing pages, e-commerce layouts, and more
 */

import { seedNextJSLayouts } from './seed-nextjs-layouts'
import { seedLandingLayouts } from './seed-landing-layouts'

interface CompleteSeedingOptions {
  skipExisting?: boolean
  verbose?: boolean
  includeNextJS?: boolean
  includeLanding?: boolean
  includeEcommerce?: boolean
  includeBlog?: boolean
  includeDashboard?: boolean
}

const defaultOptions: CompleteSeedingOptions = {
  skipExisting: true,
  verbose: true,
  includeNextJS: true,
  includeLanding: true,
  includeEcommerce: true,
  includeBlog: true,
  includeDashboard: true
}

async function seedCompleteLayouts(options: CompleteSeedingOptions = {}) {
  const config = { ...defaultOptions, ...options }
  
  if (config.verbose) {
    console.log('🎨 Starting Complete Layouts Seeding...')
    console.log('📋 Configuration:', {
      skipExisting: config.skipExisting,
      includeNextJS: config.includeNextJS,
      includeLanding: config.includeLanding,
      includeEcommerce: config.includeEcommerce,
      includeBlog: config.includeBlog,
      includeDashboard: config.includeDashboard
    })
  }

  const results = {
    nextjs: { created: 0, skipped: 0 },
    landing: { created: 0, skipped: 0 },
    ecommerce: { created: 0, skipped: 0 },
    blog: { created: 0, skipped: 0 },
    dashboard: { created: 0, skipped: 0 },
    total: { created: 0, skipped: 0 }
  }

  try {
    // Seed NextJS Layouts
    if (config.includeNextJS) {
      if (config.verbose) {
        console.log('\n🚀 Seeding NextJS Layouts...')
      }
      const nextjsResult = await seedNextJSLayouts({
        skipExisting: config.skipExisting,
        verbose: config.verbose
      })
      results.nextjs = nextjsResult
      results.total.created += nextjsResult.created
      results.total.skipped += nextjsResult.skipped
    }

    // Seed Landing Page Layouts
    if (config.includeLanding) {
      if (config.verbose) {
        console.log('\n🎯 Seeding Landing Page Layouts...')
      }
      const landingResult = await seedLandingLayouts({
        skipExisting: config.skipExisting,
        verbose: config.verbose
      })
      results.landing = landingResult
      results.total.created += landingResult.created
      results.total.skipped += landingResult.skipped
    }

    // TODO: Add more layout types
    // - E-commerce Layouts
    // - Blog Layouts
    // - Dashboard Layouts

    if (config.verbose) {
      console.log(`
🎨 Complete Layouts Seeding Summary
=====================================

📊 Results by Category:
- NextJS Layouts: ${results.nextjs.created} created, ${results.nextjs.skipped} skipped
- Landing Pages: ${results.landing.created} created, ${results.landing.skipped} skipped  
- E-commerce: ${results.ecommerce.created} created, ${results.ecommerce.skipped} skipped
- Blog Layouts: ${results.blog.created} created, ${results.blog.skipped} skipped
- Dashboard: ${results.dashboard.created} created, ${results.dashboard.skipped} skipped

🎯 Total: ${results.total.created} layouts created, ${results.total.skipped} skipped

✅ Seeding completed successfully!
      `)
    }

    return results

  } catch (error) {
    console.error('❌ Error during complete layouts seeding:', error)
    throw error
  }
}

// Export for use in other scripts
export { seedCompleteLayouts }

// Run directly if called as main module
if (require.main === module) {
  seedCompleteLayouts({ verbose: true })
    .then(() => {
      console.log('✅ Complete layouts seeding finished successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Complete layouts seeding failed:', error)
      process.exit(1)
    })
}
