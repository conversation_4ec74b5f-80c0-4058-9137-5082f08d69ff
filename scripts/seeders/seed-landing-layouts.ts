/**
 * Landing Page Layouts Seeder
 * Creates comprehensive landing page layouts for marketing and conversion
 */

import { PrismaClient } from '@prisma/client'
import { NextJSLayout, NextJSLayoutType, AppRouterFeature } from '@/lib/layout-builder/types/nextjs-types'

const prisma = new PrismaClient()

interface SeedingOptions {
  skipExisting?: boolean
  verbose?: boolean
}

const defaultOptions: SeedingOptions = {
  skipExisting: true,
  verbose: true
}

// Helper functions (reused from nextjs seeder)
function createDefaultResponsive() {
  const defaultSpacing = { top: 0, right: 0, bottom: 0, left: 0 }
  const defaultTypography = {
    fontFamily: 'system-ui',
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 1.5,
    letterSpacing: 0,
    textAlign: 'left' as const,
    textTransform: 'none' as const
  }

  return {
    mobile: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: { ...defaultTypography, fontSize: 14 }
    },
    tablet: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: defaultTypography
    },
    desktop: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: defaultTypography
    },
    large: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: defaultTypography
    }
  }
}

function createDefaultStyling() {
  const defaultSpacing = { top: 0, right: 0, bottom: 0, left: 0 }
  
  return {
    theme: 'default',
    colorScheme: 'light' as const,
    typography: {
      fontFamily: 'system-ui',
      fontSize: 16,
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: 0,
      textAlign: 'left' as const,
      textTransform: 'none' as const
    },
    spacing: defaultSpacing,
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#8b5cf6',
      text: '#1f2937',
      background: '#ffffff',
      border: '#e5e7eb'
    }
  }
}

function createDefaultBlockConfiguration(overrides: any = {}) {
  return {
    size: 'medium' as const,
    alignment: 'left' as const,
    spacing: { top: 0, right: 0, bottom: 0, left: 0 },
    animation: {
      type: 'none' as const,
      duration: 0,
      delay: 0,
      easing: 'ease' as const
    },
    ...overrides
  }
}

function createDefaultBlockStyling(overrides: any = {}) {
  return {
    background: { type: 'color', color: 'transparent' },
    border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
    spacing: { top: 0, right: 0, bottom: 0, left: 0 },
    typography: {
      fontFamily: 'system-ui',
      fontSize: 16,
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: 0,
      textAlign: 'left' as const,
      textTransform: 'none' as const
    },
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#8b5cf6',
      text: '#1f2937',
      background: '#ffffff',
      border: '#e5e7eb'
    },
    shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' },
    ...overrides
  }
}

// Landing page layout templates
const landingLayoutTemplates: Omit<NextJSLayout, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'SaaS Landing Page',
    description: 'Modern SaaS landing page with hero section, features, pricing, and CTA',
    type: 'landing',
    category: 'marketing',
    structure: {
      header: {
        id: 'header-saas',
        type: 'header',
        name: 'SaaS Header',
        position: 1,
        blocks: [
          {
            id: 'saas-logo',
            type: 'logo',
            name: 'SaaS Logo',
            position: 1,
            configuration: createDefaultBlockConfiguration({ size: 'large', alignment: 'left' }),
            content: { text: 'SaaS App', image: '/saas-logo.svg' },
            styling: createDefaultBlockStyling({
              spacing: { top: 20, right: 24, bottom: 20, left: 24 }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          },
          {
            id: 'saas-nav',
            type: 'navigation',
            name: 'SaaS Navigation',
            position: 2,
            configuration: createDefaultBlockConfiguration({
              alignment: 'center',
              layout: 'horizontal'
            }),
            content: { menu: 'saas-menu' },
            styling: createDefaultBlockStyling(),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          },
          {
            id: 'saas-cta',
            type: 'button',
            name: 'Header CTA',
            position: 3,
            configuration: createDefaultBlockConfiguration({
              size: 'medium',
              alignment: 'right'
            }),
            content: { 
              text: 'Get Started Free',
              url: '/signup',
              style: 'primary'
            },
            styling: createDefaultBlockStyling({
              colors: {
                primary: '#3b82f6',
                secondary: '#ffffff',
                accent: '#1d4ed8',
                text: '#ffffff',
                background: '#3b82f6',
                border: '#3b82f6'
              }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'flex',
          alignment: 'center',
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          background: { type: 'color', color: '#ffffff' },
          container: { 
            maxWidth: 1200, 
            padding: { top: 0, right: 24, bottom: 0, left: 24 },
            margin: { top: 0, right: 0, bottom: 0, left: 0 },
            centered: true
          }
        },
        styling: {
          background: { type: 'color', color: '#ffffff' },
          border: { width: 1, style: 'solid', color: '#e5e7eb', radius: 0 },
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          shadow: { type: 'box', x: 0, y: 1, blur: 3, spread: 0, color: 'rgba(0,0,0,0.1)' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      },
      main: {
        id: 'main-saas',
        type: 'main',
        name: 'SaaS Main Content',
        position: 2,
        blocks: [
          {
            id: 'hero-section',
            type: 'hero',
            name: 'Hero Section',
            position: 1,
            configuration: createDefaultBlockConfiguration({
              size: 'large',
              alignment: 'center'
            }),
            content: {
              headline: 'Transform Your Business with Our SaaS Solution',
              subheadline: 'Streamline operations, boost productivity, and scale your business with our powerful platform.',
              ctaText: 'Start Free Trial',
              ctaUrl: '/signup',
              image: '/hero-saas.jpg'
            },
            styling: createDefaultBlockStyling({
              spacing: { top: 80, right: 24, bottom: 80, left: 24 },
              background: { type: 'gradient', color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
              typography: {
                fontFamily: 'system-ui',
                fontSize: 48,
                fontWeight: 700,
                lineHeight: 1.2,
                letterSpacing: -1,
                textAlign: 'center' as const,
                textTransform: 'none' as const
              },
              colors: {
                primary: '#ffffff',
                secondary: '#f3f4f6',
                accent: '#3b82f6',
                text: '#ffffff',
                background: 'transparent',
                border: 'transparent'
              }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'block',
          alignment: 'left',
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          background: { type: 'color', color: 'transparent' },
          container: { 
            maxWidth: '100%', 
            padding: { top: 0, right: 0, bottom: 0, left: 0 },
            margin: { top: 0, right: 0, bottom: 0, left: 0 },
            centered: false
          }
        },
        styling: {
          background: { type: 'color', color: 'transparent' },
          border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      }
    },
    styling: createDefaultStyling(),
    responsive: createDefaultResponsive(),
    conditions: {},
    isTemplate: true,
    isSystem: false,
    isActive: true,
    usageCount: 0,
    tags: ['landing', 'saas', 'marketing', 'conversion'],
    nextjs: {
      type: 'nested' as NextJSLayoutType,
      route: 'app/(marketing)/layout.tsx',
      metadata: {
        title: { default: 'SaaS Landing Page', template: '%s | SaaS App' },
        description: 'Transform your business with our powerful SaaS solution. Start your free trial today.',
        keywords: ['saas', 'business', 'productivity', 'software'],
        openGraph: {
          type: 'website',
          locale: 'en_US',
          siteName: 'SaaS App',
          title: 'Transform Your Business with Our SaaS Solution',
          description: 'Streamline operations, boost productivity, and scale your business with our powerful platform.'
        },
        twitter: {
          card: 'summary_large_image',
          site: '@saasapp'
        }
      },
      imports: [
        'import React from "react"',
        'import { LandingHeader } from "@/components/landing/header"',
        'import { LandingFooter } from "@/components/landing/footer"'
      ],
      exports: [
        {
          name: 'default',
          type: 'default',
          isAsync: false,
          parameters: ['children: React.ReactNode'],
          returnType: 'JSX.Element'
        }
      ],
      appRouterFeatures: ['metadata', 'loading'] as AppRouterFeature[]
    }
  }
]

async function seedLandingLayouts(options: SeedingOptions = {}) {
  const config = { ...defaultOptions, ...options }
  
  if (config.verbose) {
    console.log('🎯 Seeding Landing Page Layouts...')
  }

  try {
    let createdCount = 0
    let skippedCount = 0

    for (const template of landingLayoutTemplates) {
      const layoutId = `landing-${template.nextjs.type}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
      
      if (config.skipExisting) {
        const existing = await prisma.layout.findFirst({
          where: {
            name: template.name,
            type: template.type
          }
        })
        
        if (existing) {
          if (config.verbose) {
            console.log(`⏭️  Skipping existing layout: ${template.name}`)
          }
          skippedCount++
          continue
        }
      }

      const layout = await prisma.layout.create({
        data: {
          id: layoutId,
          name: template.name,
          description: template.description,
          type: template.type,
          category: template.category,
          structure: template.structure as any,
          styling: template.styling as any,
          responsive: template.responsive as any,
          conditions: template.conditions as any,
          isTemplate: template.isTemplate,
          isSystem: template.isSystem,
          isActive: template.isActive,
          usageCount: template.usageCount,
          tags: template.tags
        }
      })

      // Create layout assignment
      await prisma.layoutAssignment.create({
        data: {
          id: `assignment-${layoutId}`,
          layoutId: layout.id,
          targetType: 'conditional',
          priority: 75,
          conditions: { path: '/landing/*' },
          isActive: true
        }
      })

      if (config.verbose) {
        console.log(`✅ Created landing layout: ${template.name}`)
      }
      createdCount++
    }

    if (config.verbose) {
      console.log(`
🎯 Landing Page Layouts Seeding Complete!
📊 Summary:
- Created: ${createdCount} layouts
- Skipped: ${skippedCount} layouts
- Total Templates: ${landingLayoutTemplates.length}
      `)
    }

    return { created: createdCount, skipped: skippedCount }

  } catch (error) {
    console.error('❌ Error seeding landing page layouts:', error)
    throw error
  }
}

// Export for use in other seeders
export { seedLandingLayouts }

// Run directly if called as main module
if (require.main === module) {
  seedLandingLayouts({ verbose: true })
    .then(() => {
      console.log('✅ Landing page layouts seeding completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Landing page layouts seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}
