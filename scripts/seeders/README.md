# 🌱 Modular Database Seeders

This directory contains a comprehensive set of modular seeders that create realistic, interconnected e-commerce data for the Coco Milk Kids store. Each seeder is designed to work independently while using existing database data to avoid orphaned records.

## 📋 Available Seeders

### 🎨 **Layout Seeders** (NEW)

#### **Complete Layouts Seeder** (`seed-complete-layouts.ts`)
Main orchestrator for seeding all layout types with comprehensive NextJS routing and rendering configurations.

**Features:**
- NextJS App Router layouts (root, nested, template, route groups)
- Landing page layouts for marketing and conversion
- E-commerce layouts for product and checkout pages
- Blog layouts for content management
- Dashboard layouts for admin interfaces
- Universal Renderer integration
- Complete responsive configurations
- SEO-optimized metadata

**Usage:**
```bash
# Seed all layout types
npm run seed:layouts

# Seed with verbose output
npm run seed:layouts:verbose

# Seed specific layout types
npm run seed:nextjs-layouts
npm run seed:landing-layouts
```

#### **NextJS Layouts Seeder** (`seed-nextjs-layouts.ts`)
Creates NextJS App Router layouts with complete routing configuration.

**Features:**
- Root application layout with global navigation
- Admin dashboard layout with sidebar
- App Router features (metadata, loading, error, not-found)
- Responsive design configurations
- Universal Renderer registration

#### **Landing Page Layouts Seeder** (`seed-landing-layouts.ts`)
Creates marketing and conversion-focused landing page layouts.

**Features:**
- SaaS landing page with hero, features, pricing
- Conversion-optimized design
- Marketing-specific metadata
- Lead generation components

### 1. **User & Customer Seeder** (`seed-users.ts`)
Creates realistic users with South African data and customer profiles.

**Features:**
- South African names, cities, provinces, and phone numbers
- Customer profiles with order history and loyalty data
- User addresses (both billing and shipping)
- Realistic user preferences and settings
- Customer segmentation (VIP, frequent buyers, etc.)

**Usage:**
```bash
pnpm seed:users [count]
# Example: pnpm seed:users 150
```

### 2. **Product Catalog Seeder** (`seed-products.ts`)
Creates comprehensive product catalog with variants, images, and categories.

**Features:**
- Kids clothing specific products and categories
- Product variants (colors, sizes)
- Realistic pricing in ZAR
- Product images and SEO data
- Category relationships
- Inventory tracking setup

**Usage:**
```bash
pnpm seed:catalog [count]
# Example: pnpm seed:catalog 75
```

### 3. **Inventory Management Seeder** (`seed-inventory.ts`)
Sets up inventory locations, items, and movements using existing products.

**Features:**
- Multiple warehouse locations across South Africa
- Inventory items for all product variants
- Stock movements and history
- Low stock alerts
- Realistic inventory levels and costs

**Usage:**
```bash
pnpm seed:inventory
```

### 4. **Orders Seeder** (`seed-orders.ts`)
Creates realistic orders using existing users and products.

**Features:**
- Realistic order status distribution
- Proper order lifecycle (pending → confirmed → shipped → delivered)
- South African shipping addresses
- Multiple payment methods
- Order notes and tags
- Realistic order values and item combinations

**Usage:**
```bash
pnpm seed:orders-new [count]
# Example: pnpm seed:orders-new 100
```

### 5. **Reviews & Ratings Seeder** (`seed-reviews.ts`)
Creates product reviews using existing users and products.

**Features:**
- Realistic rating distribution (mostly positive)
- Context-aware review content
- Verified purchase indicators
- Review helpfulness metrics
- Product rating statistics updates

**Usage:**
```bash
pnpm seed:reviews [reviews-per-product]
# Example: pnpm seed:reviews 8
```

### 6. **Master Seeder** (`seed-all.ts`)
Orchestrates all seeders in the correct order for complete database population.

**Features:**
- Intelligent dependency management
- Skip existing data option
- Comprehensive progress reporting
- Business metrics calculation
- CLI options for customization

**Usage:**
```bash
# Basic usage (recommended)
pnpm seed:all

# Custom configuration
pnpm seed:all --users 200 --products 100 --orders 150 --reviews 10

# Force recreation of existing data
pnpm seed:all --force

# Quiet mode
pnpm seed:all --quiet

# Help
pnpm seed:all --help
```

## 🔄 Seeding Order & Dependencies

The seeders are designed to work in a specific order to maintain data integrity:

1. **Users** → Creates the foundation user base
2. **Products** → Creates the product catalog
3. **Inventory** → Sets up inventory management for products
4. **Orders** → Creates orders using existing users and products
5. **Reviews** → Adds reviews using existing users and products

## 📊 Data Characteristics

### **South African Context**
- **Currency**: ZAR (South African Rand)
- **Locations**: Cape Town, Johannesburg, Durban, Pretoria, etc.
- **Names**: Mix of Afrikaans, English, Zulu, Xhosa, and Indian names
- **Phone Numbers**: Realistic SA mobile formats (+27 8X XXX XXXX)
- **Addresses**: Realistic SA addresses with proper postal codes

### **E-commerce Realism**
- **Order Values**: Range from R89 to R2,500+ 
- **Status Distribution**: Weighted towards delivered orders
- **Payment Methods**: Card, EFT, PayFast, Ozow, COD
- **Shipping**: Free shipping over R500, realistic shipping costs
- **Reviews**: 4.2+ average rating with realistic distribution

### **Business Logic**
- **Inventory**: Realistic stock levels with low stock alerts
- **Orders**: Proper lifecycle progression with timestamps
- **Customers**: Loyalty tiers based on spending
- **Products**: Kids clothing focus with appropriate categories

## 🛠️ Advanced Usage

### **Individual Seeders**
Run specific seeders when you need targeted data:

```bash
# Add more users to existing database
pnpm seed:users 50

# Add more products without affecting existing data
pnpm seed:catalog 25

# Create additional orders using existing users/products
pnpm seed:orders-new 75
```

### **Custom Configuration**
The master seeder accepts various options:

```bash
# Large dataset for performance testing
pnpm seed:all --users 500 --products 200 --orders 1000

# Minimal dataset for development
pnpm seed:all --users 20 --products 10 --orders 15 --reviews 2

# Force complete recreation
pnpm seed:all --force --users 100 --products 50
```

### **Incremental Seeding**
Add data to existing database without duplicates:

```bash
# This will only add missing data
pnpm seed:all

# Check current state first
pnpm db:studio
```

## 📈 Expected Results

After running the complete seeder (`pnpm seed:all`), you'll have:

- **100 Users** with complete profiles and addresses
- **50 Products** with variants, images, and categories  
- **4 Inventory Locations** with stock management
- **75 Orders** with realistic progression and values
- **250+ Reviews** with realistic ratings and content

**Business Metrics:**
- Total Revenue: ~R150,000 - R300,000
- Average Order Value: ~R400 - R800
- Average Product Rating: ~4.2 stars
- Inventory Value: ~R50,000 - R100,000

## 🔍 Verification

After seeding, verify your data:

```bash
# Open Prisma Studio to browse data
pnpm db:studio

# Check the admin dashboard
# Navigate to /admin in your application

# Test the orders management system
# Navigate to /admin/orders
```

## 🚨 Important Notes

1. **Run in Development Only**: These seeders are for development/testing
2. **Database Backup**: Always backup production data before seeding
3. **Dependencies**: Ensure Prisma schema is up to date
4. **Performance**: Large datasets may take several minutes to create
5. **Uniqueness**: Seeders check for existing data to avoid duplicates

## 🐛 Troubleshooting

**Common Issues:**

1. **"No users found"**: Run `pnpm seed:users` first
2. **"No products found"**: Run `pnpm seed:catalog` first  
3. **Unique constraint errors**: Use `--force` to recreate data
4. **Slow performance**: Reduce counts or run individual seeders
5. **Schema errors**: Run `pnpm db:push` to sync schema

**Getting Help:**
```bash
# Show seeder help
pnpm seed:all --help

# Check database schema
pnpm db:studio

# View logs for detailed error information
```

## 🎯 Best Practices

1. **Start Small**: Begin with default counts, scale up as needed
2. **Incremental**: Use individual seeders for targeted data addition
3. **Verify**: Always check results in Prisma Studio or admin dashboard
4. **Clean**: Use `--force` when you need fresh data
5. **Monitor**: Watch for performance issues with large datasets

---

**Happy Seeding! 🌱**
