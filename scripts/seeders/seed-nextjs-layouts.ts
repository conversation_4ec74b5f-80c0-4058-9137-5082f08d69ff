/**
 * NextJS Layouts Seeder
 * Creates comprehensive NextJS layouts with complete routing and rendering configurations
 */

import { PrismaClient } from '@prisma/client'
import { NextJSLayout, NextJSLayoutType, AppRouterFeature } from '@/lib/layout-builder/types/nextjs-types'

const prisma = new PrismaClient()

interface SeedingOptions {
  skipExisting?: boolean
  verbose?: boolean
}

const defaultOptions: SeedingOptions = {
  skipExisting: true,
  verbose: true
}

// Helper function to create default responsive settings
function createDefaultResponsive() {
  const defaultSpacing = { top: 0, right: 0, bottom: 0, left: 0 }
  const defaultTypography = {
    fontFamily: 'system-ui',
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 1.5,
    letterSpacing: 0,
    textAlign: 'left' as const,
    textTransform: 'none' as const
  }

  return {
    mobile: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: { ...defaultTypography, fontSize: 14 }
    },
    tablet: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: defaultTypography
    },
    desktop: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: defaultTypography
    },
    large: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: defaultTypography
    }
  }
}

// Helper function to create default styling
function createDefaultStyling() {
  const defaultSpacing = { top: 0, right: 0, bottom: 0, left: 0 }

  return {
    theme: 'default',
    colorScheme: 'light' as const,
    typography: {
      fontFamily: 'system-ui',
      fontSize: 16,
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: 0,
      textAlign: 'left' as const,
      textTransform: 'none' as const
    },
    spacing: defaultSpacing,
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#8b5cf6',
      text: '#1f2937',
      background: '#ffffff',
      border: '#e5e7eb'
    }
  }
}

// Helper function to create default block configuration
function createDefaultBlockConfiguration(overrides: any = {}) {
  return {
    size: 'medium' as const,
    alignment: 'left' as const,
    spacing: { top: 0, right: 0, bottom: 0, left: 0 },
    animation: {
      type: 'none' as const,
      duration: 0,
      delay: 0,
      easing: 'ease' as const
    },
    ...overrides
  }
}

// Helper function to create default block styling
function createDefaultBlockStyling(overrides: any = {}) {
  return {
    background: { type: 'color', color: 'transparent' },
    border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
    spacing: { top: 0, right: 0, bottom: 0, left: 0 },
    typography: {
      fontFamily: 'system-ui',
      fontSize: 16,
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: 0,
      textAlign: 'left' as const,
      textTransform: 'none' as const
    },
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#8b5cf6',
      text: '#1f2937',
      background: '#ffffff',
      border: '#e5e7eb'
    },
    shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' },
    ...overrides
  }
}

// Layout templates
const layoutTemplates: Omit<NextJSLayout, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: 'Root Application Layout',
    description: 'Main application layout with global navigation, header, and footer',
    type: 'site',
    category: 'corporate',
    structure: {
      header: {
        id: 'header-root',
        type: 'header',
        name: 'Global Header',
        position: 1,
        blocks: [
          {
            id: 'logo-block',
            type: 'logo',
            name: 'Application Logo',
            position: 1,
            configuration: createDefaultBlockConfiguration({ size: 'large', alignment: 'left' }),
            content: { text: 'My App', image: '/logo.svg' },
            styling: createDefaultBlockStyling({
              spacing: { top: 16, right: 16, bottom: 16, left: 16 }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          },
          {
            id: 'nav-block',
            type: 'navigation',
            name: 'Main Navigation',
            position: 2,
            configuration: createDefaultBlockConfiguration({
              alignment: 'center',
              layout: 'horizontal',
              background: { type: 'color', color: 'transparent' },
              container: {
                maxWidth: 1200,
                padding: { top: 0, right: 0, bottom: 0, left: 0 },
                margin: { top: 0, right: 0, bottom: 0, left: 0 },
                centered: true
              }
            }),
            content: { menu: 'main-menu' },
            styling: createDefaultBlockStyling(),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'flex',
          alignment: 'center',
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          background: { type: 'color', color: '#ffffff' },
          container: { 
            maxWidth: 1200, 
            padding: { top: 16, right: 16, bottom: 16, left: 16 },
            margin: { top: 0, right: 0, bottom: 0, left: 0 },
            centered: true
          }
        },
        styling: {
          background: { type: 'color', color: '#ffffff' },
          border: { width: 1, style: 'solid', color: '#e5e7eb', radius: 0 },
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          shadow: { type: 'box', x: 0, y: 1, blur: 3, spread: 0, color: 'rgba(0,0,0,0.1)' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      },
      main: {
        id: 'main-root',
        type: 'main',
        name: 'Main Content Area',
        position: 2,
        blocks: [],
        configuration: {
          layout: 'block',
          alignment: 'left',
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          background: { type: 'color', color: 'transparent' },
          container: { 
            maxWidth: 1200, 
            padding: { top: 32, right: 16, bottom: 32, left: 16 },
            margin: { top: 0, right: 0, bottom: 0, left: 0 },
            centered: true
          }
        },
        styling: {
          background: { type: 'color', color: 'transparent' },
          border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      },
      footer: {
        id: 'footer-root',
        type: 'footer',
        name: 'Global Footer',
        position: 3,
        blocks: [
          {
            id: 'footer-content',
            type: 'content',
            name: 'Footer Content',
            position: 1,
            configuration: createDefaultBlockConfiguration({ size: 'medium', alignment: 'center' }),
            content: {
              text: '© 2024 My App. All rights reserved.',
              html: '<p class="text-center text-gray-600">© 2024 My App. All rights reserved.</p>'
            },
            styling: createDefaultBlockStyling({
              spacing: { top: 16, right: 16, bottom: 16, left: 16 },
              typography: {
                fontFamily: 'system-ui',
                fontSize: 14,
                fontWeight: 400,
                lineHeight: 1.5,
                letterSpacing: 0,
                textAlign: 'center' as const,
                textTransform: 'none' as const
              },
              colors: {
                primary: '#6b7280',
                secondary: '#9ca3af',
                accent: '#3b82f6',
                text: '#6b7280',
                background: 'transparent',
                border: 'transparent'
              }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'flex',
          alignment: 'center',
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          background: { type: 'color', color: '#f9fafb' },
          container: { 
            maxWidth: 1200, 
            padding: { top: 32, right: 16, bottom: 32, left: 16 },
            margin: { top: 0, right: 0, bottom: 0, left: 0 },
            centered: true
          }
        },
        styling: {
          background: { type: 'color', color: '#f9fafb' },
          border: { width: 1, style: 'solid', color: '#e5e7eb', radius: 0 },
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      }
    },
    styling: createDefaultStyling(),
    responsive: createDefaultResponsive(),
    conditions: {},
    isTemplate: true,
    isSystem: false,
    isActive: true,
    usageCount: 0,
    tags: ['nextjs', 'root', 'application', 'global'],
    nextjs: {
      type: 'root' as NextJSLayoutType,
      route: 'app/layout.tsx',
      metadata: {
        title: { default: 'My App', template: '%s | My App' },
        description: 'A modern web application built with NextJS',
        keywords: ['nextjs', 'react', 'typescript', 'web app'],
        authors: [{ name: 'Development Team' }],
        openGraph: {
          type: 'website',
          locale: 'en_US',
          siteName: 'My App',
          title: 'My App',
          description: 'A modern web application built with NextJS'
        },
        twitter: {
          card: 'summary_large_image',
          site: '@myapp'
        },
        robots: {
          index: true,
          follow: true
        }
      },
      imports: [
        'import React from "react"',
        'import { Inter } from "next/font/google"',
        'import "./globals.css"'
      ],
      exports: [
        {
          name: 'default',
          type: 'default',
          isAsync: false,
          parameters: ['children: React.ReactNode'],
          returnType: 'JSX.Element'
        }
      ],
      appRouterFeatures: ['metadata', 'loading', 'error', 'not-found'] as AppRouterFeature[]
    }
  },

  // Dashboard Layout
  {
    name: 'Admin Dashboard Layout',
    description: 'Administrative dashboard layout with sidebar navigation and header',
    type: 'page',
    category: 'custom',
    structure: {
      header: {
        id: 'header-dashboard',
        type: 'header',
        name: 'Dashboard Header',
        position: 1,
        blocks: [
          {
            id: 'dashboard-logo',
            type: 'logo',
            name: 'Dashboard Logo',
            position: 1,
            configuration: createDefaultBlockConfiguration({ size: 'medium', alignment: 'left' }),
            content: { text: 'Admin Dashboard', image: '/admin-logo.svg' },
            styling: createDefaultBlockStyling({
              spacing: { top: 12, right: 16, bottom: 12, left: 16 }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          },
          {
            id: 'user-menu',
            type: 'navigation',
            name: 'User Menu',
            position: 2,
            configuration: createDefaultBlockConfiguration({
              alignment: 'right',
              layout: 'horizontal',
              background: { type: 'color', color: 'transparent' },
              container: {
                maxWidth: 'auto',
                padding: { top: 0, right: 0, bottom: 0, left: 0 },
                margin: { top: 0, right: 0, bottom: 0, left: 0 },
                centered: false
              }
            }),
            content: { menu: 'user-menu' },
            styling: createDefaultBlockStyling(),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'flex',
          alignment: 'center',
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          background: { type: 'color', color: '#1f2937' },
          container: {
            maxWidth: '100%',
            padding: { top: 0, right: 24, bottom: 0, left: 24 },
            margin: { top: 0, right: 0, bottom: 0, left: 0 },
            centered: false
          }
        },
        styling: {
          background: { type: 'color', color: '#1f2937' },
          border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          shadow: { type: 'box', x: 0, y: 1, blur: 3, spread: 0, color: 'rgba(0,0,0,0.1)' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      },
      sidebar: {
        id: 'sidebar-dashboard',
        type: 'sidebar',
        name: 'Dashboard Sidebar',
        position: 2,
        blocks: [
          {
            id: 'sidebar-nav',
            type: 'navigation',
            name: 'Sidebar Navigation',
            position: 1,
            configuration: createDefaultBlockConfiguration({
              alignment: 'left',
              layout: 'vertical',
              background: { type: 'color', color: 'transparent' },
              container: {
                maxWidth: '100%',
                padding: { top: 16, right: 16, bottom: 16, left: 16 },
                margin: { top: 0, right: 0, bottom: 0, left: 0 },
                centered: false
              }
            }),
            content: { menu: 'dashboard-menu' },
            styling: createDefaultBlockStyling(),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'block',
          alignment: 'left',
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          background: { type: 'color', color: '#f9fafb' },
          container: {
            maxWidth: 280,
            padding: { top: 0, right: 0, bottom: 0, left: 0 },
            margin: { top: 0, right: 0, bottom: 0, left: 0 },
            centered: false
          }
        },
        styling: {
          background: { type: 'color', color: '#f9fafb' },
          border: { width: 1, style: 'solid', color: '#e5e7eb', radius: 0 },
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      },
      main: {
        id: 'main-dashboard',
        type: 'main',
        name: 'Dashboard Content',
        position: 3,
        blocks: [],
        configuration: {
          layout: 'block',
          alignment: 'left',
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          background: { type: 'color', color: '#ffffff' },
          container: {
            maxWidth: '100%',
            padding: { top: 24, right: 24, bottom: 24, left: 24 },
            margin: { top: 0, right: 0, bottom: 0, left: 0 },
            centered: false
          }
        },
        styling: {
          background: { type: 'color', color: '#ffffff' },
          border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
          spacing: { top: 0, right: 0, bottom: 0, left: 0 },
          shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      }
    },
    styling: createDefaultStyling(),
    responsive: createDefaultResponsive(),
    conditions: {},
    isTemplate: true,
    isSystem: false,
    isActive: true,
    usageCount: 0,
    tags: ['nextjs', 'dashboard', 'admin', 'sidebar'],
    nextjs: {
      type: 'nested' as NextJSLayoutType,
      route: 'app/(dashboard)/layout.tsx',
      metadata: {
        title: { default: 'Dashboard', template: '%s | Admin Dashboard' },
        description: 'Administrative dashboard for managing the application',
        robots: {
          index: false,
          follow: false
        }
      },
      imports: [
        'import React from "react"',
        'import { DashboardSidebar } from "@/components/dashboard/sidebar"',
        'import { DashboardHeader } from "@/components/dashboard/header"'
      ],
      exports: [
        {
          name: 'default',
          type: 'default',
          isAsync: false,
          parameters: ['children: React.ReactNode'],
          returnType: 'JSX.Element'
        }
      ],
      appRouterFeatures: ['metadata', 'loading', 'error'] as AppRouterFeature[]
    }
  }
]

async function seedNextJSLayouts(options: SeedingOptions = {}) {
  const config = { ...defaultOptions, ...options }
  
  if (config.verbose) {
    console.log('🎨 Seeding NextJS Layouts...')
  }

  try {
    let createdCount = 0
    let skippedCount = 0

    for (const template of layoutTemplates) {
      const layoutId = `nextjs-${template.nextjs.type}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
      
      if (config.skipExisting) {
        const existing = await prisma.layout.findFirst({
          where: {
            name: template.name,
            type: template.type
          }
        })
        
        if (existing) {
          if (config.verbose) {
            console.log(`⏭️  Skipping existing layout: ${template.name}`)
          }
          skippedCount++
          continue
        }
      }

      const layout = await prisma.layout.create({
        data: {
          id: layoutId,
          name: template.name,
          description: template.description,
          type: template.type,
          category: template.category,
          structure: template.structure as any,
          styling: template.styling as any,
          responsive: template.responsive as any,
          conditions: template.conditions as any,
          isTemplate: template.isTemplate,
          isSystem: template.isSystem,
          isActive: template.isActive,
          usageCount: template.usageCount,
          tags: template.tags
        }
      })

      // Create layout assignment
      await prisma.layoutAssignment.create({
        data: {
          id: `assignment-${layoutId}`,
          layoutId: layout.id,
          targetType: template.nextjs.type === 'root' ? 'global' : 'conditional',
          priority: template.nextjs.type === 'root' ? 100 : 50,
          conditions: {},
          isActive: true
        }
      })

      if (config.verbose) {
        console.log(`✅ Created layout: ${template.name}`)
      }
      createdCount++
    }

    if (config.verbose) {
      console.log(`
🎨 NextJS Layouts Seeding Complete!
📊 Summary:
- Created: ${createdCount} layouts
- Skipped: ${skippedCount} layouts
- Total Templates: ${layoutTemplates.length}

🚀 Available Layouts:
${layoutTemplates.map(t => `- ${t.name} (${t.nextjs.type})`).join('\n')}
      `)
    }

    return { created: createdCount, skipped: skippedCount }

  } catch (error) {
    console.error('❌ Error seeding NextJS layouts:', error)
    throw error
  }
}

// Export for use in other seeders
export { seedNextJSLayouts }

// Run directly if called as main module
if (require.main === module) {
  seedNextJSLayouts({ verbose: true })
    .then(() => {
      console.log('✅ NextJS Layouts seeding completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ NextJS Layouts seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}
