import { NextRequest, NextResponse } from 'next/server'
import { CodebaseAnalyzer } from '@/lib/ai-visual-editor/services/codebase-analyzer'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    
    const analyzer = new CodebaseAnalyzer()
    
    switch (action) {
      case 'discover-components': {
        const includeNodeModules = searchParams.get('includeNodeModules') === 'true'
        const patterns = searchParams.get('patterns')?.split(',') || undefined
        const excludePatterns = searchParams.get('excludePatterns')?.split(',') || undefined
        
        const components = await analyzer.discoverComponents({
          includeNodeModules,
          patterns,
          excludePatterns
        })
        
        return NextResponse.json({ components })
      }
      
      case 'usage-patterns': {
        const patterns = await analyzer.getComponentUsagePatterns()
        return NextResponse.json({ patterns })
      }
      
      case 'architectural-patterns': {
        const patterns = await analyzer.getArchitecturalPatterns()
        return NextResponse.json({ patterns })
      }
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: discover-components, usage-patterns, architectural-patterns' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Codebase analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to analyze codebase' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, filePath } = body
    
    const analyzer = new CodebaseAnalyzer()
    
    switch (action) {
      case 'analyze-component': {
        if (!filePath) {
          return NextResponse.json(
            { error: 'filePath is required for component analysis' },
            { status: 400 }
          )
        }
        
        const component = await analyzer.analyzeComponentFile(filePath)
        return NextResponse.json({ component })
      }
      
      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: analyze-component' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Codebase analysis error:', error)
    return NextResponse.json(
      { error: 'Failed to analyze codebase' },
      { status: 500 }
    )
  }
}
