import { NextRequest, NextResponse } from 'next/server'
import { enhancedGenerationService } from '@/lib/ai-visual-editor/services/enhanced-generation-service'
import type { EnhancedGenerationRequest } from '@/lib/ai-visual-editor/services/enhanced-generation-service'

export async function POST(request: NextRequest) {
  try {
    const body: EnhancedGenerationRequest = await request.json()
    
    // Validate required fields
    if (!body.description || !body.blockType) {
      return NextResponse.json(
        { error: 'Description and blockType are required' },
        { status: 400 }
      )
    }

    // Set defaults for missing fields
    const enhancedRequest: EnhancedGenerationRequest = {
      quality: 'standard',
      requirements: {
        accessibility: false,
        performance: false,
        security: false,
        errorHandling: false,
        uxOptimization: false,
        testing: false
      },
      ...body
    }

    const result = await enhancedGenerationService.generateEnhancedComponent(enhancedRequest)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Enhanced generation error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to generate enhanced component',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'health':
        return NextResponse.json({
          status: 'healthy',
          service: 'enhanced-generation',
          timestamp: new Date().toISOString()
        })

      case 'capabilities':
        return NextResponse.json({
          qualities: ['standard', 'high', 'premium'],
          requirements: [
            'accessibility',
            'performance', 
            'security',
            'errorHandling',
            'uxOptimization',
            'testing'
          ],
          blockTypes: [
            'hero', 'feature', 'testimonial', 'pricing', 
            'contact', 'product', 'content', 'navigation', 'footer'
          ]
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: health, capabilities' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Enhanced generation GET error:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}
