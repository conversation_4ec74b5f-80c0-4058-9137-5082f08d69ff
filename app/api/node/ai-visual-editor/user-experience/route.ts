import { NextRequest, NextResponse } from 'next/server'
import { userExperienceService } from '@/lib/ai-visual-editor/services/user-experience-service'
import type { UserInteraction } from '@/lib/ai-visual-editor/services/user-experience-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...params } = body
    
    switch (action) {
      case 'track-interaction': {
        const { sessionId, interaction } = params
        
        if (!sessionId || !interaction) {
          return NextResponse.json(
            { error: 'SessionId and interaction are required' },
            { status: 400 }
          )
        }

        await userExperienceService.trackInteraction(sessionId, interaction)
        return NextResponse.json({ success: true })
      }

      case 'start-journey': {
        const { userId, journeyType, context } = params
        
        if (!userId || !journeyType) {
          return NextResponse.json(
            { error: 'UserId and journeyType are required' },
            { status: 400 }
          )
        }

        const journey = await userExperienceService.startUser<PERSON><PERSON>ney(userId, journeyType, context)
        return NextResponse.json({ journey })
      }

      case 'complete-journey': {
        const { journeyId, outcome, feedback } = params
        
        if (!journeyId) {
          return NextResponse.json(
            { error: 'JourneyId is required' },
            { status: 400 }
          )
        }

        const result = await userExperienceService.completeUserJourney(journeyId, outcome, feedback)
        return NextResponse.json({ result })
      }

      case 'personalize-experience': {
        const { userId, context } = params
        
        if (!userId) {
          return NextResponse.json(
            { error: 'UserId is required' },
            { status: 400 }
          )
        }

        const personalization = await userExperienceService.personalizeExperience(userId, context)
        return NextResponse.json({ personalization })
      }

      case 'submit-feedback': {
        const { userId, feedback, context } = params
        
        if (!userId || !feedback) {
          return NextResponse.json(
            { error: 'UserId and feedback are required' },
            { status: 400 }
          )
        }

        await userExperienceService.collectFeedback(userId, feedback, context)
        return NextResponse.json({ success: true })
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: track-interaction, start-journey, complete-journey, personalize-experience, submit-feedback' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('User experience service error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to process user experience request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'health':
        return NextResponse.json({
          status: 'healthy',
          service: 'user-experience',
          timestamp: new Date().toISOString()
        })

      case 'user-insights': {
        const userId = searchParams.get('userId')
        
        if (!userId) {
          return NextResponse.json(
            { error: 'UserId is required' },
            { status: 400 }
          )
        }

        const insights = await userExperienceService.getUserInsights(userId)
        return NextResponse.json({ insights })
      }

      case 'journey-analytics': {
        const timeframe = searchParams.get('timeframe') || '7d'
        const journeyType = searchParams.get('journeyType')
        
        const analytics = await userExperienceService.getJourneyAnalytics(timeframe, journeyType)
        return NextResponse.json({ analytics })
      }

      case 'interaction-patterns': {
        const userId = searchParams.get('userId')
        const timeframe = searchParams.get('timeframe') || '30d'
        
        const patterns = await userExperienceService.getInteractionPatterns(userId, timeframe)
        return NextResponse.json({ patterns })
      }

      case 'experience-metrics':
        return NextResponse.json({
          journeyTypes: [
            'component-generation', 'layout-creation', 'page-building',
            'theme-customization', 'code-export', 'collaboration'
          ],
          interactionTypes: [
            'click', 'drag', 'drop', 'edit', 'generate', 'export',
            'share', 'save', 'preview', 'publish'
          ],
          outcomeTypes: ['success', 'partial', 'failure', 'abandoned']
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: health, user-insights, journey-analytics, interaction-patterns, experience-metrics' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('User experience GET error:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}
