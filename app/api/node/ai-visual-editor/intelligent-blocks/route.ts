import { NextRequest, NextResponse } from 'next/server'
import { intelligentBlockService } from '@/lib/ai-visual-editor/services/intelligent-block-service'
import type { IntelligentBlockRequest } from '@/lib/ai-visual-editor/services/intelligent-block-service'

export async function POST(request: NextRequest) {
  try {
    const body: IntelligentBlockRequest = await request.json()
    
    // Validate required fields
    if (!body.description || !body.blockType) {
      return NextResponse.json(
        { error: 'Description and blockType are required' },
        { status: 400 }
      )
    }

    // Set defaults for missing fields
    const intelligentRequest: IntelligentBlockRequest = {
      learnFromCodebase: true,
      matchExistingStyle: true,
      complexity: 'moderate',
      requirements: {
        responsive: true,
        accessibility: true,
        themeSupport: true,
        animations: false
      },
      ...body
    }

    const result = await intelligentBlockService.generateIntelligentBlock(intelligentRequest)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Intelligent block generation error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to generate intelligent block',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'health':
        return NextResponse.json({
          status: 'healthy',
          service: 'intelligent-blocks',
          timestamp: new Date().toISOString()
        })

      case 'block-types':
        return NextResponse.json({
          blockTypes: [
            'hero', 'feature', 'testimonial', 'pricing', 
            'contact', 'product', 'content', 'navigation', 'footer'
          ],
          complexities: ['simple', 'moderate', 'complex'],
          defaultRequirements: {
            responsive: true,
            accessibility: true,
            themeSupport: true,
            animations: false
          }
        })

      case 'codebase-stats':
        try {
          // Get basic codebase statistics
          const stats = await intelligentBlockService.getCodebaseStats()
          return NextResponse.json(stats)
        } catch (error) {
          return NextResponse.json(
            { error: 'Failed to get codebase stats' },
            { status: 500 }
          )
        }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: health, block-types, codebase-stats' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Intelligent blocks GET error:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}
