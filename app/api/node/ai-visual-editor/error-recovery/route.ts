import { NextRequest, NextResponse } from 'next/server'
import { errorRecoveryService } from '@/lib/ai-visual-editor/services/error-recovery-service'
import type { ErrorContext } from '@/lib/ai-visual-editor/services/error-recovery-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...params } = body
    
    switch (action) {
      case 'handle-error': {
        const errorContext = params as ErrorContext
        
        if (!errorContext.errorType || !errorContext.message) {
          return NextResponse.json(
            { error: 'ErrorType and message are required' },
            { status: 400 }
          )
        }

        const recovery = await errorRecoveryService.handleError(errorContext)
        return NextResponse.json({ recovery })
      }

      case 'analyze-error': {
        const { error, context } = params
        
        if (!error) {
          return NextResponse.json(
            { error: 'Error is required for analysis' },
            { status: 400 }
          )
        }

        const analysis = await errorRecoveryService.analyzeError(error, context)
        return NextResponse.json({ analysis })
      }

      case 'suggest-fixes': {
        const { errorType, errorMessage, componentCode } = params
        
        if (!errorType || !errorMessage) {
          return NextResponse.json(
            { error: 'ErrorType and errorMessage are required' },
            { status: 400 }
          )
        }

        const suggestions = await errorRecoveryService.suggestFixes(
          errorType,
          errorMessage,
          componentCode
        )
        return NextResponse.json({ suggestions })
      }

      case 'apply-fix': {
        const { fixStrategy, componentCode, errorContext } = params
        
        if (!fixStrategy || !componentCode) {
          return NextResponse.json(
            { error: 'FixStrategy and componentCode are required' },
            { status: 400 }
          )
        }

        const result = await errorRecoveryService.applyFix(
          fixStrategy,
          componentCode,
          errorContext
        )
        return NextResponse.json({ result })
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: handle-error, analyze-error, suggest-fixes, apply-fix' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error recovery service error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to process error recovery request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'health':
        return NextResponse.json({
          status: 'healthy',
          service: 'error-recovery',
          timestamp: new Date().toISOString()
        })

      case 'error-types':
        return NextResponse.json({
          errorTypes: [
            'syntax', 'type', 'runtime', 'build', 'import',
            'dependency', 'configuration', 'generation'
          ],
          severityLevels: ['low', 'medium', 'high', 'critical'],
          recoveryStrategies: [
            'auto-fix', 'suggest-alternatives', 'rollback',
            'manual-intervention', 'component-regeneration'
          ]
        })

      case 'recovery-stats': {
        const timeframe = searchParams.get('timeframe') || '24h'
        
        try {
          const stats = await errorRecoveryService.getRecoveryStats(timeframe)
          return NextResponse.json({ stats })
        } catch (error) {
          return NextResponse.json(
            { error: 'Failed to get recovery stats' },
            { status: 500 }
          )
        }
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: health, error-types, recovery-stats' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error recovery GET error:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}
