import { NextRequest, NextResponse } from 'next/server'
import { componentPersistence } from '@/lib/ai-visual-editor/services/component-persistence'
import type { GeneratedComponent } from '@/lib/ai-visual-editor/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...params } = body
    
    switch (action) {
      case 'save-component': {
        const { component, filePath } = params
        
        if (!component || !filePath) {
          return NextResponse.json(
            { error: 'Component and filePath are required' },
            { status: 400 }
          )
        }

        const result = await componentPersistence.saveComponent(component, filePath)
        return NextResponse.json({ success: true, result })
      }

      case 'load-component': {
        const { filePath } = params
        
        if (!filePath) {
          return NextResponse.json(
            { error: 'FilePath is required' },
            { status: 400 }
          )
        }

        const component = await componentPersistence.loadComponent(filePath)
        return NextResponse.json({ component })
      }

      case 'save-project': {
        const { components, projectPath } = params
        
        if (!components || !projectPath) {
          return NextResponse.json(
            { error: 'Components and projectPath are required' },
            { status: 400 }
          )
        }

        const result = await componentPersistence.saveProject(components, projectPath)
        return NextResponse.json({ success: true, result })
      }

      case 'export-component': {
        const { component, format } = params
        
        if (!component) {
          return NextResponse.json(
            { error: 'Component is required' },
            { status: 400 }
          )
        }

        const exported = await componentPersistence.exportComponent(component, format || 'tsx')
        return NextResponse.json({ exported })
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: save-component, load-component, save-project, export-component' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Component persistence error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to process component persistence request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'health':
        return NextResponse.json({
          status: 'healthy',
          service: 'component-persistence',
          timestamp: new Date().toISOString()
        })

      case 'list-components': {
        const projectPath = searchParams.get('projectPath')
        
        if (!projectPath) {
          return NextResponse.json(
            { error: 'ProjectPath is required' },
            { status: 400 }
          )
        }

        const components = await componentPersistence.listComponents(projectPath)
        return NextResponse.json({ components })
      }

      case 'supported-formats':
        return NextResponse.json({
          formats: ['tsx', 'jsx', 'ts', 'js'],
          defaultFormat: 'tsx',
          features: [
            'component-saving',
            'project-export',
            'file-generation',
            'dependency-resolution'
          ]
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: health, list-components, supported-formats' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Component persistence GET error:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filePath = searchParams.get('filePath')
    
    if (!filePath) {
      return NextResponse.json(
        { error: 'FilePath is required' },
        { status: 400 }
      )
    }

    const result = await componentPersistence.deleteComponent(filePath)
    return NextResponse.json({ success: true, result })
  } catch (error) {
    console.error('Component deletion error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to delete component',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
