import { NextRequest, NextResponse } from 'next/server'
import { NextJSGenerator } from '@/lib/ai-visual-editor/services/nextjs-generator'
import type { LayoutGenerationParams, PageGenerationParams } from '@/lib/ai-visual-editor/types/nextjs-types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...params } = body
    
    switch (action) {
      case 'generate-layout': {
        const layoutParams = params as LayoutGenerationParams
        
        if (!layoutParams.name || !layoutParams.type) {
          return NextResponse.json(
            { error: 'Layout name and type are required' },
            { status: 400 }
          )
        }

        const layout = await NextJSGenerator.generateLayout(layoutParams)
        return NextResponse.json({ layout })
      }

      case 'generate-page': {
        const pageParams = params as PageGenerationParams
        
        if (!pageParams.name || !pageParams.route) {
          return NextResponse.json(
            { error: 'Page name and route are required' },
            { status: 400 }
          )
        }

        const page = await NextJSGenerator.generatePage(pageParams)
        return NextResponse.json({ page })
      }

      case 'generate-project': {
        const { layouts, pages } = params
        
        if (!Array.isArray(layouts) || !Array.isArray(pages)) {
          return NextResponse.json(
            { error: 'Layouts and pages must be arrays' },
            { status: 400 }
          )
        }

        const project = await NextJSGenerator.generateProject(layouts, pages)
        return NextResponse.json({ project })
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: generate-layout, generate-page, generate-project' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('NextJS generator error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to generate NextJS structure',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'health':
        return NextResponse.json({
          status: 'healthy',
          service: 'nextjs-generator',
          timestamp: new Date().toISOString()
        })

      case 'templates':
        return NextResponse.json({
          layoutTypes: [
            'default', 'dashboard', 'marketing', 'blog', 
            'ecommerce', 'documentation', 'portfolio'
          ],
          pageTypes: [
            'static', 'dynamic', 'api', 'middleware',
            'loading', 'error', 'not-found'
          ],
          features: [
            'seo', 'responsive', 'accessibility', 'performance',
            'analytics', 'internationalization'
          ]
        })

      case 'file-structure':
        return NextResponse.json({
          structure: {
            'app/': 'Next.js 13+ App Router directory',
            'components/': 'Reusable React components',
            'lib/': 'Utility functions and configurations',
            'public/': 'Static assets',
            'styles/': 'Global styles and themes'
          }
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: health, templates, file-structure' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('NextJS generator GET error:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}
