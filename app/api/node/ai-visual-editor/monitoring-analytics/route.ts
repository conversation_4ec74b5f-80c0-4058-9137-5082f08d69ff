import { NextRequest, NextResponse } from 'next/server'
import { monitoringAnalyticsService } from '@/lib/ai-visual-editor/services/monitoring-analytics-service'
import type { SystemMetrics } from '@/lib/ai-visual-editor/services/monitoring-analytics-service'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...params } = body
    
    switch (action) {
      case 'collect-metrics': {
        const metrics = params as SystemMetrics
        
        if (!metrics.timestamp) {
          metrics.timestamp = new Date()
        }

        await monitoringAnalyticsService.collectMetrics(metrics)
        return NextResponse.json({ success: true })
      }

      case 'create-alert': {
        const { alertConfig } = params
        
        if (!alertConfig || !alertConfig.name || !alertConfig.condition) {
          return NextResponse.json(
            { error: 'Alert configuration with name and condition is required' },
            { status: 400 }
          )
        }

        const alert = await monitoringAnalyticsService.createAlert(alertConfig)
        return NextResponse.json({ alert })
      }

      case 'trigger-alert': {
        const { alertId, severity, message, context } = params
        
        if (!alertId || !severity || !message) {
          return NextResponse.json(
            { error: 'AlertId, severity, and message are required' },
            { status: 400 }
          )
        }

        await monitoringAnalyticsService.triggerAlert(alertId, severity, message, context)
        return NextResponse.json({ success: true })
      }

      case 'log-performance': {
        const { operation, duration, success, metadata } = params
        
        if (!operation || duration === undefined) {
          return NextResponse.json(
            { error: 'Operation and duration are required' },
            { status: 400 }
          )
        }

        await monitoringAnalyticsService.logPerformance(operation, duration, success, metadata)
        return NextResponse.json({ success: true })
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: collect-metrics, create-alert, trigger-alert, log-performance' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Monitoring analytics service error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to process monitoring analytics request',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'health':
        return NextResponse.json({
          status: 'healthy',
          service: 'monitoring-analytics',
          timestamp: new Date().toISOString()
        })

      case 'system-metrics': {
        const timeframe = searchParams.get('timeframe') || '1h'
        
        const metrics = await monitoringAnalyticsService.getSystemMetrics(timeframe)
        return NextResponse.json({ metrics })
      }

      case 'performance-insights': {
        const timeframe = searchParams.get('timeframe') || '24h'
        
        const insights = await monitoringAnalyticsService.getPerformanceInsights(timeframe)
        return NextResponse.json({ insights })
      }

      case 'quality-metrics': {
        const timeframe = searchParams.get('timeframe') || '7d'
        
        const quality = await monitoringAnalyticsService.getQualityMetrics(timeframe)
        return NextResponse.json({ quality })
      }

      case 'alerts': {
        const status = searchParams.get('status') // 'active', 'resolved', 'all'
        const severity = searchParams.get('severity') // 'low', 'medium', 'high', 'critical'
        
        const alerts = await monitoringAnalyticsService.getAlerts(status, severity)
        return NextResponse.json({ alerts })
      }

      case 'insights': {
        const timeframe = searchParams.get('timeframe') || '7d'
        
        const insights = await monitoringAnalyticsService.generateInsights(timeframe)
        return NextResponse.json({ insights })
      }

      case 'dashboard-data': {
        const timeframe = searchParams.get('timeframe') || '24h'
        
        const [metrics, insights, alerts] = await Promise.all([
          monitoringAnalyticsService.getSystemMetrics(timeframe),
          monitoringAnalyticsService.generateInsights(timeframe),
          monitoringAnalyticsService.getAlerts('active')
        ])
        
        return NextResponse.json({
          dashboard: {
            metrics,
            insights,
            alerts,
            timestamp: new Date().toISOString()
          }
        })
      }

      case 'metric-types':
        return NextResponse.json({
          metricTypes: {
            performance: ['responseTime', 'throughput', 'errorRate', 'cpuUsage', 'memoryUsage'],
            quality: ['averageScore', 'accessibilityCompliance', 'performanceScore', 'userSatisfaction'],
            usage: ['activeUsers', 'totalRequests', 'successfulGenerations', 'failedGenerations'],
            agents: ['activeAgents', 'averageExecutionTime', 'successRate', 'resourceUtilization']
          },
          alertSeverities: ['low', 'medium', 'high', 'critical'],
          timeframes: ['1h', '6h', '24h', '7d', '30d']
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: health, system-metrics, performance-insights, quality-metrics, alerts, insights, dashboard-data, metric-types' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Monitoring analytics GET error:', error)
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    )
  }
}
