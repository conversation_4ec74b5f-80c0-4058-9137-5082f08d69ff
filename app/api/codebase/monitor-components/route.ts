import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'

export const maxDuration = 60

interface ComponentChange {
  type: 'added' | 'modified' | 'deleted'
  filePath: string
  timestamp: Date
  componentName?: string
  changes?: {
    imports?: string[]
    exports?: string[]
    shadcnComponents?: string[]
    complexity?: string
  }
}

interface MonitoringSession {
  id: string
  startTime: Date
  changes: ComponentChange[]
  patterns: {
    addedComponents: number
    modifiedComponents: number
    deletedComponents: number
    shadcnUsageChanges: Record<string, number>
  }
}

// In-memory storage for monitoring sessions (in production, use Redis or database)
const monitoringSessions = new Map<string, MonitoringSession>()
const activeWatchers = new Map<string, any>()

export async function POST(req: NextRequest) {
  try {
    const { action, sessionId, watchPaths } = await req.json()

    switch (action) {
      case 'start':
        return startMonitoring(watchPaths)
      
      case 'stop':
        return stopMonitoring(sessionId)
      
      case 'getChanges':
        return getChanges(sessionId)
      
      case 'analyze':
        return analyzeChanges(sessionId)
      
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Component monitoring error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process monitoring request' },
      { status: 500 }
    )
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const sessionId = searchParams.get('sessionId')
    const action = searchParams.get('action') || 'status'

    if (!sessionId) {
      // Return all active sessions
      const sessions = Array.from(monitoringSessions.values()).map(session => ({
        id: session.id,
        startTime: session.startTime,
        changesCount: session.changes.length,
        patterns: session.patterns
      }))

      return NextResponse.json({
        success: true,
        activeSessions: sessions.length,
        sessions
      })
    }

    const session = monitoringSessions.get(sessionId)
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Session not found' },
        { status: 404 }
      )
    }

    switch (action) {
      case 'status':
        return NextResponse.json({
          success: true,
          session: {
            id: session.id,
            startTime: session.startTime,
            changesCount: session.changes.length,
            patterns: session.patterns,
            isActive: activeWatchers.has(sessionId)
          }
        })

      case 'changes':
        return NextResponse.json({
          success: true,
          changes: session.changes,
          patterns: session.patterns
        })

      case 'stream':
        // Server-sent events for real-time updates
        return createSSEResponse(sessionId)

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Component monitoring GET error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get monitoring data' },
      { status: 500 }
    )
  }
}

async function startMonitoring(watchPaths: string[] = ['components', 'app', 'lib']) {
  const sessionId = `monitor-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`

  const session: MonitoringSession = {
    id: sessionId,
    startTime: new Date(),
    changes: [],
    patterns: {
      addedComponents: 0,
      modifiedComponents: 0,
      deletedComponents: 0,
      shadcnUsageChanges: {}
    }
  }

  monitoringSessions.set(sessionId, session)

  // Note: File watching functionality removed for simplicity
  // In a production environment, you would implement proper file watching here

  return NextResponse.json({
    success: true,
    sessionId,
    message: `Started monitoring session for ${watchPaths.length} paths`,
    watchPaths,
    note: 'File watching is disabled in this simplified version'
  })
}

async function stopMonitoring(sessionId: string) {
  // Remove any active watchers (simplified version)
  activeWatchers.delete(sessionId)

  const session = monitoringSessions.get(sessionId)
  if (!session) {
    return NextResponse.json(
      { success: false, error: 'Session not found' },
      { status: 404 }
    )
  }

  return NextResponse.json({
    success: true,
    message: 'Monitoring stopped',
    summary: {
      duration: Date.now() - session.startTime.getTime(),
      totalChanges: session.changes.length,
      patterns: session.patterns
    }
  })
}

async function getChanges(sessionId: string) {
  const session = monitoringSessions.get(sessionId)
  if (!session) {
    return NextResponse.json(
      { success: false, error: 'Session not found' },
      { status: 404 }
    )
  }

  return NextResponse.json({
    success: true,
    changes: session.changes,
    patterns: session.patterns,
    summary: {
      totalChanges: session.changes.length,
      recentChanges: session.changes.slice(-10)
    }
  })
}

async function analyzeChanges(sessionId: string) {
  const session = monitoringSessions.get(sessionId)
  if (!session) {
    return NextResponse.json(
      { success: false, error: 'Session not found' },
      { status: 404 }
    )
  }

  const analysis = {
    changeFrequency: calculateChangeFrequency(session.changes),
    componentTrends: analyzeComponentTrends(session.changes),
    shadcnUsageTrends: analyzeShadcnUsageTrends(session.changes),
    hotspots: identifyHotspots(session.changes),
    recommendations: generateMonitoringRecommendations(session.changes)
  }

  return NextResponse.json({
    success: true,
    analysis,
    session: {
      id: session.id,
      duration: Date.now() - session.startTime.getTime(),
      totalChanges: session.changes.length
    }
  })
}

async function handleFileChange(type: 'added' | 'modified' | 'deleted', filePath: string, sessionId: string) {
  const session = monitoringSessions.get(sessionId)
  if (!session) return

  // Only process React component files
  if (!isReactComponentFile(filePath)) return

  const change: ComponentChange = {
    type,
    filePath: path.relative(process.cwd(), filePath),
    timestamp: new Date()
  }

  if (type !== 'deleted') {
    try {
      const analysis = await analyzeChangedFile(filePath)
      change.componentName = analysis.componentName
      change.changes = analysis.changes
      
      // Update patterns
      updateSessionPatterns(session, type, analysis)
    } catch (error) {
      console.warn(`Failed to analyze changed file ${filePath}:`, error)
    }
  }

  session.changes.push(change)
  
  // Update session patterns
  switch (type) {
    case 'added':
      session.patterns.addedComponents++
      break
    case 'modified':
      session.patterns.modifiedComponents++
      break
    case 'deleted':
      session.patterns.deletedComponents++
      break
  }

  // Limit changes history to last 1000 entries
  if (session.changes.length > 1000) {
    session.changes = session.changes.slice(-1000)
  }
}

async function analyzeChangedFile(filePath: string) {
  const content = await fs.readFile(filePath, 'utf-8')
  
  const componentName = extractComponentName(content, filePath)
  const imports = extractImports(content)
  const exports = extractExports(content)
  const shadcnComponents = extractShadcnComponents(content)
  const complexity = calculateComplexity(content)

  return {
    componentName,
    changes: {
      imports,
      exports,
      shadcnComponents,
      complexity
    }
  }
}

function updateSessionPatterns(session: MonitoringSession, type: string, analysis: any) {
  if (analysis.changes?.shadcnComponents) {
    analysis.changes.shadcnComponents.forEach((comp: string) => {
      const key = `${type}:${comp}`
      session.patterns.shadcnUsageChanges[key] = (session.patterns.shadcnUsageChanges[key] || 0) + 1
    })
  }
}

function createSSEResponse(sessionId: string) {
  const encoder = new TextEncoder()
  
  const stream = new ReadableStream({
    start(controller) {
      const interval = setInterval(() => {
        const session = monitoringSessions.get(sessionId)
        if (!session) {
          controller.close()
          clearInterval(interval)
          return
        }

        const data = {
          timestamp: new Date().toISOString(),
          changesCount: session.changes.length,
          recentChanges: session.changes.slice(-5),
          patterns: session.patterns
        }

        const sseData = `data: ${JSON.stringify(data)}\n\n`
        controller.enqueue(encoder.encode(sseData))
      }, 1000)

      // Clean up on close
      setTimeout(() => {
        clearInterval(interval)
        controller.close()
      }, 300000) // 5 minutes max
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  })
}

// Utility functions
function isReactComponentFile(filePath: string): boolean {
  return /\.(tsx|jsx)$/.test(filePath) && 
         !filePath.includes('.test.') && 
         !filePath.includes('.spec.') &&
         !filePath.includes('.stories.')
}

function extractComponentName(content: string, filePath: string): string {
  const defaultExportMatch = content.match(/export\s+default\s+(?:function\s+)?(\w+)/)
  if (defaultExportMatch) return defaultExportMatch[1]
  
  const functionMatch = content.match(/export\s+(?:default\s+)?function\s+(\w+)/)
  if (functionMatch) return functionMatch[1]
  
  return path.basename(filePath, path.extname(filePath))
}

function extractImports(content: string): string[] {
  const importRegex = /import\s+.*from\s+['"]([^'"]+)['"]/g
  const imports: string[] = []
  let match
  
  while ((match = importRegex.exec(content)) !== null) {
    imports.push(match[1])
  }
  
  return imports
}

function extractExports(content: string): string[] {
  const exportRegex = /export\s+(?:default\s+)?(?:function|const|class)\s+(\w+)/g
  const exports: string[] = []
  let match
  
  while ((match = exportRegex.exec(content)) !== null) {
    exports.push(match[1])
  }
  
  return exports
}

function extractShadcnComponents(content: string): string[] {
  const shadcnRegex = /import\s*\{([^}]+)\}\s*from\s*['"]@\/components\/ui/g
  const components: string[] = []
  let match
  
  while ((match = shadcnRegex.exec(content)) !== null) {
    const imported = match[1].split(',').map(comp => comp.trim()).filter(Boolean)
    components.push(...imported)
  }
  
  return [...new Set(components)]
}

function calculateComplexity(content: string): 'simple' | 'moderate' | 'complex' {
  const lines = content.split('\n').length
  const hooks = (content.match(/use\w+/g) || []).length
  const jsx = (content.match(/<\w+/g) || []).length
  
  const score = lines * 0.1 + hooks * 3 + jsx * 0.5
  
  if (score < 25) return 'simple'
  if (score < 75) return 'moderate'
  return 'complex'
}

function calculateChangeFrequency(changes: ComponentChange[]) {
  const now = Date.now()
  const intervals = [
    { name: 'last_hour', ms: 60 * 60 * 1000 },
    { name: 'last_day', ms: 24 * 60 * 60 * 1000 },
    { name: 'last_week', ms: 7 * 24 * 60 * 60 * 1000 }
  ]
  
  return intervals.reduce((freq, interval) => {
    const count = changes.filter(change => 
      now - change.timestamp.getTime() < interval.ms
    ).length
    freq[interval.name] = count
    return freq
  }, {} as Record<string, number>)
}

function analyzeComponentTrends(changes: ComponentChange[]) {
  const trends = {
    mostActiveFiles: {} as Record<string, number>,
    changeTypes: { added: 0, modified: 0, deleted: 0 }
  }
  
  changes.forEach(change => {
    trends.mostActiveFiles[change.filePath] = (trends.mostActiveFiles[change.filePath] || 0) + 1
    trends.changeTypes[change.type]++
  })
  
  return trends
}

function analyzeShadcnUsageTrends(changes: ComponentChange[]) {
  const trends: Record<string, number> = {}
  
  changes.forEach(change => {
    if (change.changes?.shadcnComponents) {
      change.changes.shadcnComponents.forEach(comp => {
        trends[comp] = (trends[comp] || 0) + 1
      })
    }
  })
  
  return trends
}

function identifyHotspots(changes: ComponentChange[]) {
  const fileChanges: Record<string, number> = {}
  
  changes.forEach(change => {
    fileChanges[change.filePath] = (fileChanges[change.filePath] || 0) + 1
  })
  
  return Object.entries(fileChanges)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([file, count]) => ({ file, changeCount: count }))
}

function generateMonitoringRecommendations(changes: ComponentChange[]): string[] {
  const recommendations: string[] = []
  
  const hotspots = identifyHotspots(changes)
  if (hotspots.length > 0 && hotspots[0].changeCount > 10) {
    recommendations.push(`Consider refactoring ${hotspots[0].file} - it has been modified ${hotspots[0].changeCount} times`)
  }
  
  const deletions = changes.filter(c => c.type === 'deleted').length
  if (deletions > changes.length * 0.2) {
    recommendations.push('High deletion rate detected - consider component reusability')
  }
  
  return recommendations
}
