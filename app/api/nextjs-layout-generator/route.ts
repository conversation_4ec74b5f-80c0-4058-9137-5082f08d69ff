import { NextRequest, NextResponse } from 'next/server'
import { NextJSLayout, NextJSCodeGenerationResult } from '@/lib/layout-builder/types/nextjs-types'

export async function POST(request: NextRequest) {
  try {
    const { layout, exportFormat, includeTypes, includeStyles } = await request.json()

    if (!layout) {
      return NextResponse.json(
        { error: 'Layout is required' },
        { status: 400 }
      )
    }

    const result = await generateNextJSLayoutCode(layout, {
      exportFormat: exportFormat || 'typescript',
      includeTypes: includeTypes !== false,
      includeStyles: includeStyles !== false
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('NextJS layout generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate NextJS layout code' },
      { status: 500 }
    )
  }
}

async function generateNextJSLayoutCode(
  layout: NextJSLayout,
  options: {
    exportFormat: 'typescript' | 'javascript'
    includeTypes: boolean
    includeStyles: boolean
  }
): Promise<NextJSCodeGenerationResult> {
  const { exportFormat, includeTypes, includeStyles } = options
  const isTypeScript = exportFormat === 'typescript'
  const fileExtension = isTypeScript ? 'tsx' : 'jsx'

  // Generate main layout file
  const layoutCode = generateLayoutFile(layout, isTypeScript)
  
  // Generate component files
  const components = generateComponentFiles(layout, isTypeScript)
  
  // Generate types file if TypeScript
  const types = isTypeScript && includeTypes ? generateTypesFile(layout) : undefined
  
  // Generate styles file if requested
  const styles = includeStyles ? generateStylesFile(layout) : undefined

  return {
    layout: {
      path: layout.nextjs.route,
      code: layoutCode
    },
    components,
    types,
    styles
  }
}

function generateLayoutFile(layout: NextJSLayout, isTypeScript: boolean): string {
  const { nextjs, structure } = layout
  const { metadata, appRouterFeatures } = nextjs

  // Generate imports
  const imports = [
    "import React from 'react'",
    "import type { Metadata } from 'next'",
    ...generateComponentImports(structure),
    ...generateFeatureImports(appRouterFeatures)
  ]

  // Generate metadata export
  const metadataExport = generateMetadataExport(metadata)

  // Generate layout component
  const layoutComponent = generateLayoutComponent(layout, isTypeScript)

  return `${imports.join('\n')}

${metadataExport}

${layoutComponent}`
}

function generateComponentImports(structure: any): string[] {
  const imports: string[] = []
  
  // Add imports for header, footer, sidebar components if they exist
  if (structure.header?.isVisible) {
    imports.push("import { Header } from '@/components/layout/header'")
  }
  if (structure.footer?.isVisible) {
    imports.push("import { Footer } from '@/components/layout/footer'")
  }
  if (structure.sidebar?.isVisible) {
    imports.push("import { Sidebar } from '@/components/layout/sidebar'")
  }

  return imports
}

function generateFeatureImports(features: string[]): string[] {
  const imports: string[] = []
  
  if (features.includes('loading')) {
    imports.push("import { LoadingSpinner } from '@/components/ui/loading-spinner'")
  }
  
  return imports
}

function generateMetadataExport(metadata: any): string {
  if (!metadata) {
    return `export const metadata: Metadata = {
  title: 'My App',
  description: 'Generated with NextJS Layout Builder',
}`
  }

  const metadataObj = {
    title: metadata.title || 'My App',
    description: metadata.description || 'Generated with NextJS Layout Builder',
    ...(metadata.keywords && { keywords: metadata.keywords }),
    ...(metadata.openGraph && { openGraph: metadata.openGraph }),
    ...(metadata.twitter && { twitter: metadata.twitter }),
    ...(metadata.robots && { robots: metadata.robots })
  }

  return `export const metadata: Metadata = ${JSON.stringify(metadataObj, null, 2)}`
}

function generateLayoutComponent(layout: NextJSLayout, isTypeScript: boolean): string {
  const { structure } = layout
  const hasHeader = structure.header?.isVisible
  const hasFooter = structure.footer?.isVisible
  const hasSidebar = structure.sidebar?.isVisible

  const propsType = isTypeScript ? ': { children: React.ReactNode }' : ''

  return `export default function RootLayout({ children }${propsType}) {
  return (
    <html lang="en">
      <body>
        <div className="min-h-screen flex flex-col">
          ${hasHeader ? '<Header />' : ''}
          
          <div className="flex flex-1">
            ${hasSidebar ? '<Sidebar />' : ''}
            
            <main className="flex-1">
              {children}
            </main>
          </div>
          
          ${hasFooter ? '<Footer />' : ''}
        </div>
      </body>
    </html>
  )
}`
}

function generateComponentFiles(layout: NextJSLayout, isTypeScript: boolean): Array<{
  name: string
  path: string
  code: string
}> {
  const components: Array<{ name: string; path: string; code: string }> = []
  const { structure } = layout
  const fileExtension = isTypeScript ? 'tsx' : 'jsx'

  // Generate Header component
  if (structure.header?.isVisible) {
    components.push({
      name: 'Header',
      path: `components/layout/header.${fileExtension}`,
      code: generateHeaderComponent(structure.header, isTypeScript)
    })
  }

  // Generate Footer component
  if (structure.footer?.isVisible) {
    components.push({
      name: 'Footer',
      path: `components/layout/footer.${fileExtension}`,
      code: generateFooterComponent(structure.footer, isTypeScript)
    })
  }

  // Generate Sidebar component
  if (structure.sidebar?.isVisible) {
    components.push({
      name: 'Sidebar',
      path: `components/layout/sidebar.${fileExtension}`,
      code: generateSidebarComponent(structure.sidebar, isTypeScript)
    })
  }

  return components
}

function generateHeaderComponent(headerSection: any, isTypeScript: boolean): string {
  return `'use client'

import React from 'react'
import Link from 'next/link'

export function Header() {
  return (
    <header className="bg-white border-b border-gray-200">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="text-xl font-bold text-gray-900">
              Logo
            </Link>
          </div>
          
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-600 hover:text-gray-900">
              Home
            </Link>
            <Link href="/about" className="text-gray-600 hover:text-gray-900">
              About
            </Link>
            <Link href="/contact" className="text-gray-600 hover:text-gray-900">
              Contact
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}`
}

function generateFooterComponent(footerSection: any, isTypeScript: boolean): string {
  return `'use client'

import React from 'react'
import Link from 'next/link'

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">Company</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-gray-300 hover:text-white">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-white">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Products</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/products" className="text-gray-300 hover:text-white">
                  All Products
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Support</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/help" className="text-gray-300 hover:text-white">
                  Help Center
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Legal</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/privacy" className="text-gray-300 hover:text-white">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-300 hover:text-white">
                  Terms of Service
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 Your Company. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}`
}

function generateSidebarComponent(sidebarSection: any, isTypeScript: boolean): string {
  return `'use client'

import React from 'react'
import Link from 'next/link'

export function Sidebar() {
  return (
    <aside className="w-64 bg-gray-50 border-r border-gray-200">
      <div className="p-4">
        <nav className="space-y-2">
          <Link 
            href="/dashboard" 
            className="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100"
          >
            Dashboard
          </Link>
          <Link 
            href="/settings" 
            className="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100"
          >
            Settings
          </Link>
          <Link 
            href="/profile" 
            className="block px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100"
          >
            Profile
          </Link>
        </nav>
      </div>
    </aside>
  )
}`
}

function generateTypesFile(layout: NextJSLayout): { path: string; code: string } {
  return {
    path: 'types/layout.ts',
    code: `// Generated layout types

export interface LayoutProps {
  children: React.ReactNode
}

export interface HeaderProps {
  // Add header-specific props here
}

export interface FooterProps {
  // Add footer-specific props here
}

export interface SidebarProps {
  // Add sidebar-specific props here
}

export interface LayoutConfig {
  name: string
  type: '${layout.nextjs.type}'
  route: string
  features: string[]
}
`
  }
}

function generateStylesFile(layout: NextJSLayout): { path: string; code: string } {
  return {
    path: 'styles/layout.css',
    code: `/* Generated layout styles */

.layout-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  /* Header styles */
}

.layout-main {
  flex: 1;
  display: flex;
}

.layout-sidebar {
  /* Sidebar styles */
}

.layout-content {
  flex: 1;
}

.layout-footer {
  /* Footer styles */
}

/* Responsive styles */
@media (max-width: 768px) {
  .layout-sidebar {
    display: none;
  }
}
`
  }
}
