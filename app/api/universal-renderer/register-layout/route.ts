import { NextRequest, NextResponse } from 'next/server'
import { UniversalRendererIntegration } from '@/lib/layout-builder/services/universal-renderer-integration'
import { NextJSLayout } from '@/lib/layout-builder/types/nextjs-types'

export async function POST(request: NextRequest) {
  try {
    const { layout } = await request.json()

    if (!layout) {
      return NextResponse.json(
        { error: 'Layout is required' },
        { status: 400 }
      )
    }

    // Validate the NextJS layout
    if (!isValidNextJSLayout(layout)) {
      return NextResponse.json(
        { error: 'Invalid NextJS layout structure' },
        { status: 400 }
      )
    }

    // Register the layout with the Universal Rendering System
    await UniversalRendererIntegration.registerLayout(layout)

    return NextResponse.json({
      success: true,
      message: `Layout "${layout.name}" registered successfully with Universal Renderer`,
      layoutId: layout.id,
      route: layout.nextjs.route
    })
  } catch (error) {
    console.error('Layout registration error:', error)
    return NextResponse.json(
      { error: 'Failed to register layout with Universal Renderer' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const layoutId = searchParams.get('layoutId')

    if (layoutId) {
      // Get specific layout
      const layout = await getRegisteredLayout(layoutId)
      if (!layout) {
        return NextResponse.json(
          { error: 'Layout not found' },
          { status: 404 }
        )
      }
      return NextResponse.json(layout)
    } else {
      // Get all registered layouts
      const layouts = await getAllRegisteredLayouts()
      return NextResponse.json(layouts)
    }
  } catch (error) {
    console.error('Error fetching registered layouts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch registered layouts' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const layoutId = searchParams.get('layoutId')

    if (!layoutId) {
      return NextResponse.json(
        { error: 'Layout ID is required' },
        { status: 400 }
      )
    }

    // Unregister the layout
    await unregisterLayout(layoutId)

    return NextResponse.json({
      success: true,
      message: `Layout "${layoutId}" unregistered successfully`
    })
  } catch (error) {
    console.error('Layout unregistration error:', error)
    return NextResponse.json(
      { error: 'Failed to unregister layout' },
      { status: 500 }
    )
  }
}

function isValidNextJSLayout(layout: any): layout is NextJSLayout {
  return !!(
    layout &&
    layout.id &&
    layout.name &&
    layout.nextjs &&
    layout.nextjs.type &&
    layout.nextjs.route &&
    layout.structure &&
    Array.isArray(layout.nextjs.appRouterFeatures)
  )
}

async function getRegisteredLayout(layoutId: string) {
  // In a real implementation, this would fetch from database
  // For now, return a mock response
  return {
    id: layoutId,
    name: 'Mock Layout',
    status: 'registered',
    registeredAt: new Date().toISOString()
  }
}

async function getAllRegisteredLayouts() {
  // In a real implementation, this would fetch from database
  // For now, return a mock response
  return [
    {
      id: 'layout-1',
      name: 'Root Layout',
      type: 'root',
      route: 'app/layout.tsx',
      status: 'registered',
      registeredAt: new Date().toISOString()
    },
    {
      id: 'layout-2',
      name: 'Dashboard Layout',
      type: 'nested',
      route: 'app/(dashboard)/layout.tsx',
      status: 'registered',
      registeredAt: new Date().toISOString()
    }
  ]
}

async function unregisterLayout(layoutId: string) {
  // In a real implementation, this would remove from database and clean up files
  console.log(`Unregistering layout: ${layoutId}`)
}
