import { openai } from '@ai-sdk/openai'
import { streamText, generateObject } from 'ai'
import { z } from 'zod'
import { NextRequest } from 'next/server'
import { generatePageBuilderBlockTool } from '@/lib/ai-visual-editor/tools/page-builder-block-generator'

export const maxDuration = 30

// Rate limiting
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT_MAX = 5 // requests per window for page builder blocks
const RATE_LIMIT_WINDOW = 60000 // 1 minute

function checkRateLimit(ip: string): boolean {
  const now = Date.now()
  const userLimit = rateLimitMap.get(ip)

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW })
    return true
  }

  if (userLimit.count >= RATE_LIMIT_MAX) {
    return false
  }

  userLimit.count++
  return true
}

// Request schema
const pageBuilderBlockRequestSchema = z.object({
  action: z.enum(['generate', 'analyze', 'optimize']),
  params: z.object({
    description: z.string(),
    blockType: z.enum(['hero', 'feature', 'testimonial', 'pricing', 'contact', 'product', 'content', 'navigation', 'footer', 'custom']),
    category: z.enum(['content', 'ecommerce', 'marketing', 'layout', 'media']),
    complexity: z.enum(['simple', 'moderate', 'complex']).default('moderate'),
    learnFromExisting: z.boolean().default(true),
    includeConfiguration: z.boolean().default(true),
    responsive: z.boolean().default(true),
    accessibility: z.boolean().default(true)
  })
})

export async function POST(req: NextRequest) {
  try {
    // Rate limiting
    const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown'
    if (!checkRateLimit(ip)) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Rate limit exceeded', 
          message: 'Too many page builder block requests. Please wait before trying again.' 
        }),
        { status: 429, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Parse and validate request
    const body = await req.json()
    const validatedRequest = pageBuilderBlockRequestSchema.parse(body)
    const { action, params } = validatedRequest

    const startTime = Date.now()

    switch (action) {
      case 'generate':
        return await handleBlockGeneration(params, startTime)
      
      case 'analyze':
        return await handleBlockAnalysis(params, startTime)
      
      case 'optimize':
        return await handleBlockOptimization(params, startTime)
      
      default:
        return new Response(
          JSON.stringify({ 
            success: false,
            error: 'Invalid action', 
            message: 'Unsupported action type' 
          }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Page builder blocks API error:', error)
    
    if (error instanceof z.ZodError) {
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'Invalid request format',
          message: 'Request validation failed',
          details: error.errors
        }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ 
        success: false,
        error: 'Internal server error',
        message: 'Failed to process page builder block request'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

async function handleBlockGeneration(params: any, startTime: number) {
  try {
    // Use the page builder block generation tool
    const result = await generatePageBuilderBlockTool.execute(params)
    
    const duration = Date.now() - startTime
    console.log(`[Page Builder AI] Block generation completed in ${duration}ms`)

    if (result.success) {
      return new Response(
        JSON.stringify({
          success: true,
          block: result.block,
          analysis: result.analysis,
          message: result.message,
          performance: {
            ...result.performance,
            totalTime: duration
          }
        }),
        { 
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    } else {
      return new Response(
        JSON.stringify({
          success: false,
          error: result.error,
          message: result.message,
          suggestions: result.suggestions
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      )
    }
  } catch (error) {
    console.error('Block generation error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Block generation failed',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

async function handleBlockAnalysis(params: any, startTime: number) {
  try {
    // Analyze existing blocks for patterns
    const { analyzeExistingBlocks } = await import('@/lib/ai-visual-editor/services/block-pattern-analyzer')
    
    const analysis = await analyzeExistingBlocks(params.category, params.blockType)
    
    const duration = Date.now() - startTime
    console.log(`[Page Builder AI] Block analysis completed in ${duration}ms`)

    return new Response(
      JSON.stringify({
        success: true,
        analysis,
        message: `Analyzed ${analysis.totalBlocks} existing blocks`,
        performance: {
          analysisTime: duration,
          blocksAnalyzed: analysis.totalBlocks
        }
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  } catch (error) {
    console.error('Block analysis error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Block analysis failed',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

async function handleBlockOptimization(params: any, startTime: number) {
  try {
    // This would implement block optimization logic
    // For now, return a placeholder response
    
    const duration = Date.now() - startTime
    
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Block optimization feature coming soon',
        performance: {
          optimizationTime: duration
        }
      }),
      { 
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  } catch (error) {
    console.error('Block optimization error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Block optimization failed',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// Streaming endpoint for real-time generation
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const prompt = searchParams.get('prompt')
    const category = searchParams.get('category') || 'content'
    
    if (!prompt) {
      return new Response('Prompt is required', { status: 400 })
    }

    const result = streamText({
      model: openai('gpt-4o'),
      messages: [
        {
          role: 'system',
          content: `You are an expert page builder block generator. Generate blocks that follow existing patterns and integrate seamlessly with the page builder system. Focus on the ${category} category.`
        },
        {
          role: 'user',
          content: `Generate a page builder block: ${prompt}`
        }
      ],
      tools: {
        generatePageBuilderBlock: generatePageBuilderBlockTool
      },
      maxSteps: 3
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('Streaming generation error:', error)
    return new Response('Internal server error', { status: 500 })
  }
}
