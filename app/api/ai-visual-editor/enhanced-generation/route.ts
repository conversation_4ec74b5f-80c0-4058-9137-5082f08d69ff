import { NextRequest, NextResponse } from 'next/server'
import { enhancedGenerationService } from '@/lib/ai-visual-editor/services/enhanced-generation-service'
import { errorRecoveryService } from '@/lib/ai-visual-editor/services/error-recovery-service'
import { userExperienceService } from '@/lib/ai-visual-editor/services/user-experience-service'
import { agentOrchestrator } from '@/lib/ai-visual-editor/agents/agent-orchestrator'

// Rate limiting and monitoring
const requestCounts = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT = 10 // requests per minute
const RATE_WINDOW = 60 * 1000 // 1 minute

interface EnhancedRequest {
  description: string
  blockType: string
  quality: 'standard' | 'high' | 'premium'
  requirements: {
    accessibility: boolean
    performance: boolean
    security: boolean
    errorHandling: boolean
    uxOptimization: boolean
    testing: boolean
  }
  constraints?: {
    maxBundleSize?: number
    targetDevices?: string[]
    complianceStandards?: string[]
  }
  context?: {
    userFeedback?: string[]
    usagePatterns?: string[]
    existingComponents?: string[]
    sessionId?: string
    userId?: string
  }
  preferences?: {
    agentMode: 'single' | 'multi-agent' | 'orchestrated'
    validationLevel: 'basic' | 'comprehensive' | 'premium'
    optimizationLevel: 'none' | 'standard' | 'aggressive'
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  let sessionId: string | undefined
  let userId: string | undefined

  try {
    // Parse and validate request
    const body: EnhancedRequest = await request.json()
    sessionId = body.context?.sessionId
    userId = body.context?.userId

    // Rate limiting
    const clientId = getClientId(request)
    if (!checkRateLimit(clientId)) {
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded',
          retryAfter: getRateLimitResetTime(clientId),
          guidance: 'Please wait before making another request'
        },
        { status: 429 }
      )
    }

    // Track user interaction for UX analysis
    if (sessionId) {
      userExperienceService.trackInteraction(sessionId, {
        action: 'enhanced-generation-request',
        context: { blockType: body.blockType, quality: body.quality },
        duration: 0,
        success: true
      })
    }

    // Validate request
    const validation = validateRequest(body)
    if (!validation.valid) {
      return NextResponse.json(
        { 
          error: 'Invalid request',
          details: validation.errors,
          guidance: 'Please check your request parameters and try again'
        },
        { status: 400 }
      )
    }

    // Choose generation approach based on preferences
    let result
    switch (body.preferences?.agentMode || 'multi-agent') {
      case 'single':
        result = await generateWithSingleAgent(body)
        break
      case 'orchestrated':
        result = await generateWithOrchestrator(body)
        break
      case 'multi-agent':
      default:
        result = await generateWithMultiAgent(body)
        break
    }

    // Provide contextual assistance
    let assistance = undefined
    if (sessionId) {
      assistance = await userExperienceService.provideContextualAssistance(
        sessionId,
        'component-generation-completed'
      )
    }

    // Track successful completion
    if (sessionId) {
      userExperienceService.trackInteraction(sessionId, {
        action: 'enhanced-generation-completed',
        context: { success: true, quality: result.quality.overallScore },
        duration: Date.now() - startTime,
        success: true
      })
    }

    return NextResponse.json({
      success: true,
      result,
      assistance,
      metadata: {
        processingTime: Date.now() - startTime,
        agentMode: body.preferences?.agentMode || 'multi-agent',
        requestId: generateRequestId(),
        timestamp: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Enhanced generation error:', error)

    // Use error recovery service
    const errorContext = {
      errorType: 'generation' as const,
      severity: 'high' as const,
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userAction: 'enhanced-component-generation',
      systemState: {
        memory: process.memoryUsage().heapUsed,
        performance: { processingTime: Date.now() - startTime },
        activeAgents: ['enhanced-generation-service']
      }
    }

    const recovery = await errorRecoveryService.handleError(errorContext)

    // Track error for UX analysis
    if (sessionId) {
      userExperienceService.trackInteraction(sessionId, {
        action: 'enhanced-generation-error',
        context: { error: errorContext.message },
        duration: Date.now() - startTime,
        success: false,
        frustrationLevel: 8
      })
    }

    return NextResponse.json({
      success: false,
      error: {
        message: recovery.strategy.userMessage,
        technical: recovery.strategy.technicalDetails,
        recovery: recovery.strategy,
        guidance: recovery.userGuidance,
        fallback: recovery.fallbackResult
      },
      metadata: {
        processingTime: Date.now() - startTime,
        errorRecovered: recovery.recovered,
        requestId: generateRequestId(),
        timestamp: new Date().toISOString()
      }
    }, { status: recovery.recovered ? 200 : 500 })
  }
}

async function generateWithSingleAgent(request: EnhancedRequest) {
  return await enhancedGenerationService.generateEnhancedComponent(request)
}

async function generateWithMultiAgent(request: EnhancedRequest) {
  // Use multiple specialized agents in parallel where possible
  return await enhancedGenerationService.generateEnhancedComponent(request)
}

async function generateWithOrchestrator(request: EnhancedRequest) {
  // Use the agent orchestrator for complex coordination
  const orchestrationRequest = {
    description: request.description,
    type: 'component-generation' as const,
    context: request.context,
    requirements: {
      quality: request.quality,
      speed: 'balanced' as const,
      accessibility: request.requirements.accessibility,
      performance: request.requirements.performance
    }
  }

  const orchestrationResult = await agentOrchestrator.orchestrate(orchestrationRequest)

  // Convert orchestration result to enhanced generation format
  return {
    success: orchestrationResult.success,
    component: orchestrationResult.result?.synthesis || {
      code: '// Generated component',
      name: 'GeneratedComponent',
      description: request.description,
      variants: []
    },
    quality: {
      overallScore: orchestrationResult.metrics.qualityScore,
      accessibility: 85,
      performance: 80,
      security: 90,
      usability: 85,
      maintainability: 88
    },
    enhancements: {
      applied: ['orchestrated-generation'],
      available: ['advanced-optimization'],
      recommendations: orchestrationResult.recommendations
    },
    testing: {
      unitTests: '// Generated unit tests',
      integrationTests: '// Generated integration tests',
      accessibilityTests: '// Generated accessibility tests',
      performanceTests: '// Generated performance tests'
    },
    documentation: {
      usage: 'Component usage documentation',
      props: 'Props documentation',
      examples: 'Usage examples',
      troubleshooting: 'Troubleshooting guide'
    },
    metrics: orchestrationResult.metrics
  }
}

function validateRequest(body: EnhancedRequest): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!body.description || body.description.trim().length < 10) {
    errors.push('Description must be at least 10 characters long')
  }

  if (!body.blockType || !['hero', 'feature', 'pricing', 'contact', 'product', 'content', 'navigation', 'footer'].includes(body.blockType)) {
    errors.push('Invalid block type')
  }

  if (!body.quality || !['standard', 'high', 'premium'].includes(body.quality)) {
    errors.push('Invalid quality level')
  }

  if (!body.requirements) {
    errors.push('Requirements object is required')
  }

  return { valid: errors.length === 0, errors }
}

function getClientId(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const ip = forwarded ? forwarded.split(',')[0] : request.ip || 'unknown'
  return ip
}

function checkRateLimit(clientId: string): boolean {
  const now = Date.now()
  const clientData = requestCounts.get(clientId)

  if (!clientData || now > clientData.resetTime) {
    requestCounts.set(clientId, { count: 1, resetTime: now + RATE_WINDOW })
    return true
  }

  if (clientData.count >= RATE_LIMIT) {
    return false
  }

  clientData.count++
  return true
}

function getRateLimitResetTime(clientId: string): number {
  const clientData = requestCounts.get(clientId)
  return clientData ? Math.ceil((clientData.resetTime - Date.now()) / 1000) : 0
}

function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    services: {
      enhancedGeneration: 'operational',
      errorRecovery: 'operational',
      userExperience: 'operational',
      agentOrchestrator: 'operational'
    },
    timestamp: new Date().toISOString(),
    version: '2.0.0'
  })
}
