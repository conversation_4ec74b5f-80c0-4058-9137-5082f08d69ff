'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { FulfillmentForm } from '@/components/admin/fulfillments/fulfillment-form'
import { Button } from '@/components/ui/button'

export default function Page() {
  const router = useRouter()

  const handleFulfillmentCreated = (id: string) => {
    router.push(`/admin/fulfillments/${id}`)
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold">Create Fulfillment</h1>
        <Button variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
      </div>
      <FulfillmentForm onFulfillmentCreated={handleFulfillmentCreated} />
    </div>
  )
}