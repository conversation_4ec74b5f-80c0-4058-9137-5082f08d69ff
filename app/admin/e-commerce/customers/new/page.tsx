'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { CustomerForm } from '@/components/admin/customers/customer-form'
import { Button } from '@/components/ui/button'
import { ChevronLeft } from 'lucide-react'

export default function Page() {
  const router = useRouter()

  const handleSuccess = (id: string) => {
    router.push(`/admin/customers/${id}`)
  }

  return (
    <div className="space-y-6">
      {/* Header with title and back button */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">New Customer</h1>
          <p className="text-muted-foreground">Create a new customer</p>
        </div>
        <Button asChild variant="outline">
          <Link href="/admin/customers">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to list
          </Link>
        </Button>
      </div>

      {/* Customer creation form */}
      <CustomerForm onSuccess={handleSuccess} />
    </div>
  )
}