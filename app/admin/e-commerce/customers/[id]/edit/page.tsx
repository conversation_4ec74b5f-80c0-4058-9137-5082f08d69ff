'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import { useCustomer } from '@/lib/ecommerce/hooks/use-customer'
import { CustomerForm } from '@/components/admin/customers/customer-form'

interface PageProps {
  params: {
    id: string
  }
}

export default function Page({ params }: PageProps) {
  const router = useRouter()
  const { customer, loading, error } = useCustomer(params.id)

  const handleSuccess = (id: string) => {
    router.push(`/admin/customers/${id}`)
  }

  if (loading) {
    return <div>Loading...</div>
  }

  if (error) {
    return <div className="text-destructive">Error: {error}</div>
  }

  if (!customer) {
    return <div>Customer not found</div>
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-semibold mb-4">Edit Customer</h1>
      <CustomerForm initialData={customer} onSuccess={handleSuccess} />
    </div>
  )
}