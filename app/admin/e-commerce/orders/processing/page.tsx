'use client'

import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { B<PERSON><PERSON><PERSON>b, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { OrderProcessingDashboard } from '@/components/admin/orders/order-processing-dashboard'
import { useOrders, useOrderMutations } from '@/lib/ecommerce/hooks/use-orders'
import { toast } from 'sonner'
import type { Order } from '@/lib/ecommerce/types/order'

export default function OrderProcessingPage() {
  const router = useRouter()
  
  const { orders, loading, error, refetch } = useOrders({
    initialParams: {
      page: 1,
      limit: 100,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    },
    autoFetch: true
  })

  const { updateOrderStatus } = useOrderMutations()

  const handleProcessOrder = useCallback((order: Order) => {
    router.push(`/admin/orders/${order.id}`)
  }, [router])

  const handleBulkProcess = useCallback(async (selectedOrders: Order[]) => {
    try {
      // Update all selected orders to "processing" status
      const promises = selectedOrders.map(order => 
        updateOrderStatus(order.id, 'processing')
      )
      
      await Promise.all(promises)
      toast.success(`${selectedOrders.length} orders moved to processing`)
      refetch()
    } catch (error) {
      console.error('Error processing orders in bulk:', error)
      toast.error('Failed to process orders')
    }
  }, [updateOrderStatus, refetch])

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/orders">Orders</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Processing Dashboard</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <div>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>
      </div>

      {/* Processing Dashboard */}
      <OrderProcessingDashboard 
        orders={orders}
        onRefresh={refetch}
        onProcessOrder={handleProcessOrder}
        onBulkProcess={handleBulkProcess}
      />
    </div>
  )
}