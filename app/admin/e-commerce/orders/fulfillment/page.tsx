'use client'

import { useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { B<PERSON><PERSON><PERSON>b, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { useOrders, useOrderMutations } from '@/lib/ecommerce/hooks/use-orders'
import { formatCurrency } from '@/lib/utils'
import {
  Package,
  Search,
  Truck,
  CheckCircle,
  AlertCircle,
  MoreHorizontal,
  Calendar,
  Printer,
  Users,
  ArrowRight
} from 'lucide-react'
import { DataTable } from '@/components/admin/data-table'
import { toast } from 'sonner'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import type { Order } from '@/lib/ecommerce/types/order'


export default function FulfillmentPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('unfulfilled')
  const [activeTab, setActiveTab] = useState('pending')
  
  const { orders, loading, error, pagination, searchOrders, refetch } = useOrders({
    initialParams: {
      page: 1,
      limit: 20,
      filters: {
        status: ['confirmed', 'processing'],
        paymentStatus: ['paid', 'authorized']
      },
      sort: {
        field: 'createdAt',
        direction: 'desc'
      }
    },
    autoFetch: true
  })
  
  const { updateOrderStatus, loading: mutationLoading } = useOrderMutations()
  
  const handleSearch = useCallback(() => {
    searchOrders({
      query: searchQuery,
      page: 1,
      limit: 20,
      filters: {
        status: activeTab === 'pending' ? ['confirmed', 'processing'] : 
                activeTab === 'shipped' ? ['shipped'] : ['delivered'],
        paymentStatus: ['paid', 'authorized']
      },
      sort: {
        field: 'createdAt',
        direction: 'desc'
      }
    })
  }, [searchOrders, searchQuery, activeTab])
  
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value)
    setStatusFilter(value === 'pending' ? 'unfulfilled' : value)
    
    searchOrders({
      page: 1,
      limit: 20,
      filters: {
        status: value === 'pending' ? ['confirmed', 'processing'] : 
                value === 'shipped' ? ['shipped'] : ['delivered'],
        paymentStatus: ['paid', 'authorized']
      },
      sort: {
        field: 'createdAt',
        direction: 'desc'
      }
    })
  }, [searchOrders])
  
  const handleFulfill = useCallback(async (order: Order) => {
    try {
      const result = await updateOrderStatus(order.id, 'shipped')
      if (result) {
        toast.success(`Order #${order.orderNumber} marked as shipped`)
        refetch()
      } else {
        toast.error('Failed to update order status')
      }
    } catch (error) {
      toast.error('An error occurred while updating the order')
      console.error(error)
    }
  }, [updateOrderStatus, refetch])
  
  const handleDeliver = useCallback(async (order: Order) => {
    try {
      const result = await updateOrderStatus(order.id, 'delivered')
      if (result) {
        toast.success(`Order #${order.orderNumber} marked as delivered`)
        refetch()
      } else {
        toast.error('Failed to update order status')
      }
    } catch (error) {
      toast.error('An error occurred while updating the order')
      console.error(error)
    }
  }, [updateOrderStatus, refetch])
  
  const columns = [
    {
      key: 'orderNumber',
      title: 'Order',
      render: (order: Order) => (
        <div>
          <div className="font-medium">#{order.orderNumber}</div>
          <div className="text-sm text-muted-foreground">
            {new Date(order.createdAt).toLocaleDateString()}
          </div>
        </div>
      )
    },
    {
      key: 'customer',
      title: 'Customer',
      render: (order: Order) => (
        <div>
          <div className="font-medium">
            {order.customer?.firstName} {order.customer?.lastName}
          </div>
          <div className="text-sm text-muted-foreground">
            {order.customer?.email}
          </div>
        </div>
      )
    },
    {
      key: 'shipping',
      title: 'Shipping Address',
      render: (order: Order) => (
        <div className="text-sm">
          {order.shippingAddress?.address1}<br />
          {order.shippingAddress?.city}, {order.shippingAddress?.country} {order.shippingAddress?.province} {order.shippingAddress?.postalCode}
        </div>
      )
    },
    {
      key: 'items',
      title: 'Items',
      render: (order: Order) => (
        <div>
          <div className="font-medium">{order.items?.length || 0} items</div>
          <div className="text-sm text-muted-foreground">
            {formatCurrency(order.total.amount, order.total.currency)}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      render: (order: Order) => (
        <Badge variant={
          order.status === 'shipped' ? 'default' :
          order.status === 'delivered' ? 'outline' :
          'secondary'
        }>
          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
        </Badge>
      )
    }
  ]
  
  const renderActions = useCallback((order: Order) => (
    <div className="flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem onClick={() => router.push(`/admin/orders/${order.id}`)}>
            View details
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => router.push(`/admin/orders/${order.id}/edit`)}>
            Edit order
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          {order.status === 'confirmed' || order.status === 'processing' ? (
            <DropdownMenuItem onClick={() => handleFulfill(order)}>
              <Truck className="mr-2 h-4 w-4" />
              Mark as shipped
            </DropdownMenuItem>
          ) : order.status === 'shipped' ? (
            <DropdownMenuItem onClick={() => handleDeliver(order)}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Mark as delivered
            </DropdownMenuItem>
          ) : null}
          <DropdownMenuItem>
            <Printer className="mr-2 h-4 w-4" />
            Print shipping label
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Calendar className="mr-2 h-4 w-4" />
            Schedule delivery
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  ), [router, handleFulfill, handleDeliver])

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/orders">Orders</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Fulfillment</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight mb-1">Order Fulfillment</h1>
        <p className="text-muted-foreground">
          Process and track shipments for customer orders
        </p>
      </div>

      {/* Fulfillment Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Pending Fulfillment
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {orders.filter(o => o.status === 'confirmed' || o.status === 'processing').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Orders ready to be shipped
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              In Transit
            </CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {orders.filter(o => o.status === 'shipped').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Orders currently in transit
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Delivered Today
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {orders.filter(o => {
                if (o.status !== 'delivered') return false
                const today = new Date()
                const deliveredDate = new Date(o.updatedAt || Date.now())
                return today.toDateString() === deliveredDate.toDateString()
              }).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Successfully delivered orders
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Fulfillment Tabs and Table */}
      <Tabs defaultValue="pending" value={activeTab} onValueChange={handleTabChange}>
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="shipped">Shipped</TabsTrigger>
            <TabsTrigger value="delivered">Delivered</TabsTrigger>
          </TabsList>
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search orders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              className="pl-8"
            />
          </div>
        </div>

        <TabsContent value="pending">
          {error ? (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Orders to Fulfill</CardTitle>
                <CardDescription>
                  Orders that are confirmed and paid, ready to be shipped
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DataTable
                  data={orders.filter(o => o.status === 'confirmed' || o.status === 'processing')}
                  columns={columns}
                  renderActions={renderActions}
                  loading={loading || mutationLoading}
                  searchPlaceholder="Filter orders..."
                  emptyMessage="No orders to fulfill at this time."
                  emptyIcon={Package}
                />
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="shipped">
          <Card>
            <CardHeader>
              <CardTitle>In-Transit Orders</CardTitle>
              <CardDescription>
                Orders that have been shipped and are in transit
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                data={orders.filter(o => o.status === 'shipped')}
                columns={columns}
                renderActions={renderActions}
                loading={loading || mutationLoading}
                searchPlaceholder="Filter orders..."
                emptyMessage="No orders currently in transit."
                emptyIcon={Truck}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="delivered">
          <Card>
            <CardHeader>
              <CardTitle>Delivered Orders</CardTitle>
              <CardDescription>
                Orders that have been successfully delivered
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                data={orders.filter(o => o.status === 'delivered')}
                columns={columns}
                renderActions={renderActions}
                loading={loading || mutationLoading}
                searchPlaceholder="Filter orders..."
                emptyMessage="No delivered orders to display."
                emptyIcon={CheckCircle}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Help Section */}
      <Card className="bg-muted/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" /> Need Help?
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm">
            <p className="mb-2">
              Learn how to efficiently manage your orders with our resources:
            </p>
            <ul className="list-disc pl-5 space-y-1">
              <li>
                <a href="#" className="text-primary hover:underline flex items-center gap-1">
                  Fulfillment best practices
                  <ArrowRight className="h-3 w-3" />
                </a>
              </li>
              <li>
                <a href="#" className="text-primary hover:underline flex items-center gap-1">
                  Setting up shipping providers
                  <ArrowRight className="h-3 w-3" />
                </a>
              </li>
              <li>
                <a href="#" className="text-primary hover:underline flex items-center gap-1">
                  Automating order fulfillment
                  <ArrowRight className="h-3 w-3" />
                </a>
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}