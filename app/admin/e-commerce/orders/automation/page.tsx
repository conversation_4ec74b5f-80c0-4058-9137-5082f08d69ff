'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { <PERSON><PERSON><PERSON>rumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Zap, Settings, Clock, Mail, Truck, Package, AlertTriangle, CheckCircle, Plus, Save, Trash2, Download } from 'lucide-react'
import { toast } from 'sonner'

export default function OrderAutomationPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('rules')
  
  // Mock automation rules
  const [automationRules, setAutomationRules] = useState([
    {
      id: '1',
      name: 'Auto-confirm paid orders',
      description: 'Automatically confirm orders when payment is received',
      trigger: 'payment_received',
      action: 'update_status',
      actionParams: { status: 'confirmed' },
      enabled: true,
      priority: 10,
    },
    {
      id: '2',
      name: 'Send shipping notification',
      description: 'Send email notification when order is shipped',
      trigger: 'status_changed',
      triggerParams: { status: 'shipped' },
      action: 'send_email',
      actionParams: { template: 'shipping_notification' },
      enabled: true,
      priority: 20,
    },
    {
      id: '3',
      name: 'Auto-cancel abandoned carts',
      description: 'Cancel orders that remain in pending status for more than 24 hours',
      trigger: 'time_elapsed',
      triggerParams: { status: 'pending', hours: 24 },
      action: 'update_status',
      actionParams: { status: 'cancelled', reason: 'abandoned' },
      enabled: false,
      priority: 30,
    }
  ])

  // Mock email templates
  const [emailTemplates, setEmailTemplates] = useState([
    {
      id: 'order_confirmation',
      name: 'Order Confirmation',
      subject: 'Your order has been confirmed',
      body: 'Thank you for your order! Your order #{{order_number}} has been confirmed and is being processed.',
      enabled: true,
    },
    {
      id: 'shipping_notification',
      name: 'Shipping Notification',
      subject: 'Your order has been shipped',
      body: 'Good news! Your order #{{order_number}} has been shipped and is on its way to you. You can track your package using the following tracking number: {{tracking_number}}',
      enabled: true,
    },
    {
      id: 'delivery_confirmation',
      name: 'Delivery Confirmation',
      subject: 'Your order has been delivered',
      body: 'Your order #{{order_number}} has been delivered. We hope you enjoy your purchase!',
      enabled: true,
    }
  ])

  const toggleRuleStatus = (ruleId: string) => {
    setAutomationRules(rules => 
      rules.map(rule => 
        rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
      )
    )
    
    const rule = automationRules.find(r => r.id === ruleId)
    if (rule) {
      toast.success(`Rule "${rule.name}" ${rule.enabled ? 'disabled' : 'enabled'}`)
    }
  }

  const toggleTemplateStatus = (templateId: string) => {
    setEmailTemplates(templates => 
      templates.map(template => 
        template.id === templateId ? { ...template, enabled: !template.enabled } : template
      )
    )
    
    const template = emailTemplates.find(t => t.id === templateId)
    if (template) {
      toast.success(`Template "${template.name}" ${template.enabled ? 'disabled' : 'enabled'}`)
    }
  }

  const saveChanges = () => {
    toast.success('Automation settings saved successfully')
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/orders">Orders</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Automation Engine</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <div>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Order Automation Engine</h1>
          <p className="text-muted-foreground">
            Configure automated workflows for order processing
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
          <Button onClick={saveChanges}>
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Automation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="rules">Automation Rules</TabsTrigger>
          <TabsTrigger value="emails">Email Templates</TabsTrigger>
          <TabsTrigger value="logs">Activity Logs</TabsTrigger>
        </TabsList>

        {/* Automation Rules */}
        <TabsContent value="rules" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Automation Rules</h2>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Rule
            </Button>
          </div>

          <div className="grid gap-4">
            {automationRules.map((rule) => (
              <Card key={rule.id} className={!rule.enabled ? 'opacity-70' : ''}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center">
                        <Zap className="mr-2 h-5 w-5 text-amber-500" />
                        {rule.name}
                      </CardTitle>
                      <CardDescription>{rule.description}</CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Label htmlFor={`rule-${rule.id}`} className="sr-only">
                        Enable
                      </Label>
                      <Switch 
                        id={`rule-${rule.id}`} 
                        checked={rule.enabled}
                        onCheckedChange={() => toggleRuleStatus(rule.id)}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Trigger</h4>
                      <div className="flex items-center space-x-2">
                        {rule.trigger === 'payment_received' && (
                          <Badge variant="outline" className="bg-green-50">
                            <CheckCircle className="mr-1 h-3 w-3 text-green-600" />
                            Payment Received
                          </Badge>
                        )}
                        {rule.trigger === 'status_changed' && (
                          <Badge variant="outline" className="bg-blue-50">
                            <Truck className="mr-1 h-3 w-3 text-blue-600" />
                            Status Changed to {rule.triggerParams?.status}
                          </Badge>
                        )}
                        {rule.trigger === 'time_elapsed' && (
                          <Badge variant="outline" className="bg-amber-50">
                            <Clock className="mr-1 h-3 w-3 text-amber-600" />
                            {rule.triggerParams?.hours} Hours in {rule.triggerParams?.status} Status
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-1">Action</h4>
                      <div className="flex items-center space-x-2">
                        {rule.action === 'update_status' && (
                          <Badge variant="outline" className="bg-purple-50">
                            <Package className="mr-1 h-3 w-3 text-purple-600" />
                            Update Status to {rule.actionParams?.status}
                          </Badge>
                        )}
                        {rule.action === 'send_email' && (
                          <Badge variant="outline" className="bg-indigo-50">
                            <Mail className="mr-1 h-3 w-3 text-indigo-600" />
                            Send Email: {rule.actionParams?.template}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end space-x-2 pt-0">
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Email Templates */}
        <TabsContent value="emails" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Email Templates</h2>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Template
            </Button>
          </div>

          <div className="grid gap-4">
            {emailTemplates.map((template) => (
              <Card key={template.id} className={!template.enabled ? 'opacity-70' : ''}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="flex items-center">
                        <Mail className="mr-2 h-5 w-5 text-blue-500" />
                        {template.name}
                      </CardTitle>
                      <CardDescription>Subject: {template.subject}</CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Label htmlFor={`template-${template.id}`} className="sr-only">
                        Enable
                      </Label>
                      <Switch 
                        id={`template-${template.id}`} 
                        checked={template.enabled}
                        onCheckedChange={() => toggleTemplateStatus(template.id)}
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-md p-3 bg-muted/50">
                    <p className="text-sm whitespace-pre-line">{template.body}</p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end space-x-2 pt-0">
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                  <Button variant="outline" size="sm">
                    Preview
                  </Button>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Activity Logs */}
        <TabsContent value="logs" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Automation Activity Logs</h2>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export Logs
            </Button>
          </div>

          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="border-l-4 border-green-500 pl-4 py-2">
                  <div className="flex justify-between">
                    <div>
                      <p className="font-medium">Auto-confirm paid orders</p>
                      <p className="text-sm text-muted-foreground">Order #ORD-12345 status updated to confirmed</p>
                    </div>
                    <p className="text-sm text-muted-foreground">Today, 14:32</p>
                  </div>
                </div>
                <div className="border-l-4 border-blue-500 pl-4 py-2">
                  <div className="flex justify-between">
                    <div>
                      <p className="font-medium">Send shipping notification</p>
                      <p className="text-sm text-muted-foreground">Email <NAME_EMAIL> for Order #ORD-12340</p>
                    </div>
                    <p className="text-sm text-muted-foreground">Today, 11:15</p>
                  </div>
                </div>
                <div className="border-l-4 border-red-500 pl-4 py-2">
                  <div className="flex justify-between">
                    <div>
                      <p className="font-medium">Auto-cancel abandoned carts</p>
                      <p className="text-sm text-muted-foreground">Order #ORD-12335 status updated to cancelled</p>
                    </div>
                    <p className="text-sm text-muted-foreground">Yesterday, 09:45</p>
                  </div>
                </div>
                <div className="border-l-4 border-amber-500 pl-4 py-2">
                  <div className="flex justify-between">
                    <div>
                      <p className="font-medium">Rule execution failed</p>
                      <p className="text-sm text-muted-foreground">Failed to send email for Order #ORD-12330: Invalid email address</p>
                    </div>
                    <p className="text-sm text-muted-foreground">Yesterday, 08:22</p>
                  </div>
                </div>
                <div className="border-l-4 border-green-500 pl-4 py-2">
                  <div className="flex justify-between">
                    <div>
                      <p className="font-medium">Auto-confirm paid orders</p>
                      <p className="text-sm text-muted-foreground">Order #ORD-12328 status updated to confirmed</p>
                    </div>
                    <p className="text-sm text-muted-foreground">2 days ago, 16:05</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}