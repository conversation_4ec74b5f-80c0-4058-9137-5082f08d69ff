'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Image as ImageIcon, Save, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useProduct, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { ImageUpload } from '@/components/admin/image-upload'
import { toast } from 'sonner'

// Form validation schema for media tab
const mediaFormSchema = z.object({
  images: z.array(z.object({
    url: z.string().url('Invalid image URL'),
    altText: z.string(),
    position: z.number()
  })).max(10, 'Maximum 10 images allowed').default([])
})

type MediaFormData = z.infer<typeof mediaFormSchema>

export default function ProductMediaEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const { updateProduct, loading: isUpdating } = useProductMutations()

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<MediaFormData>({
    resolver: zodResolver(mediaFormSchema),
    mode: 'onChange',
    defaultValues: {
      images: []
    },
  })

  const { fields: imageFields, append: appendImage, remove: removeImage } = useFieldArray({
    control,
    name: 'images',
  })

  // Update form when product loads
  useEffect(() => {
    if (product?.images) {
      setValue('images', product.images)
    }
  }, [product, setValue])

  const onSubmit = async (data: MediaFormData) => {
    if (!product) return

    try {
      const updateData = {
        id: product.id,
        images: data.images || []
      }

      const result = await updateProduct(updateData as any)
      if (result) {
        toast.success('Product images updated successfully')
        router.push(`/admin/e-commerce/products/${productId}`)
      }
    } catch (error) {
      toast.error('Failed to update product images')
      console.error('Error updating product images:', error)
    }
  }

  const handleImagesChange = (images: string[]) => {
    // Clear existing images
    while (imageFields.length > 0) {
      removeImage(0)
    }
    // Add new images
    images.forEach((url, index) => {
      appendImage({
        url,
        altText: `${product?.title || 'Product'} - Image ${index + 1}`,
        position: index
      })
    })
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading...</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-8 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Error</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-red-500">{error?.message || 'Product not found'}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Product Images</h1>
            <p className="text-muted-foreground">{product.title}</p>
          </div>
        </div>
        <Button
          type="submit"
          disabled={isUpdating || isSubmitting || !isDirty}
          className="min-w-[140px]"
        >
          {(isUpdating || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>

      {/* Form Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ImageIcon className="mr-2 h-5 w-5" />
            Product Images
          </CardTitle>
          <CardDescription>
            Upload high-quality images of your product. The first image will be used as the main product image.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ImageUpload
            images={imageFields.map(field => field.url)}
            onChange={handleImagesChange}
            maxImages={10}
            description="First image will be used as the main product image"
          />
          
          {errors.images && (
            <p className="text-sm text-red-500 mt-2">{errors.images.message}</p>
          )}
        </CardContent>
      </Card>

      {/* Image Preview */}
      {imageFields.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Image Preview</CardTitle>
            <CardDescription>
              Preview of uploaded images in order of appearance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {imageFields.map((field, index) => (
                <div key={field.id} className="relative group">
                  <div className="aspect-square rounded-lg overflow-hidden border">
                    <img
                      src={field.url}
                      alt={field.altText}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="absolute top-2 left-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-black/50 text-white">
                      {index === 0 ? 'Main' : index + 1}
                    </span>
                  </div>
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => removeImage(index)}
                    >
                      Remove
                    </Button>
                  </div>
                  <div className="mt-2">
                    <p className="text-xs text-muted-foreground truncate">
                      {field.altText}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Images State */}
      {imageFields.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <ImageIcon className="mx-auto h-8 w-8 mb-2" />
              <p>No images uploaded yet</p>
              <p className="text-xs mt-1">Use the image upload area above to add product photos</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Image Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle>Image Guidelines</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>• Use high-quality images with good lighting</p>
            <p>• Recommended size: 1200x1200 pixels or larger</p>
            <p>• Supported formats: JPG, PNG, WebP</p>
            <p>• Maximum file size: 5MB per image</p>
            <p>• First image will be used as the main product image</p>
            <p>• Show different angles and details of your product</p>
            <p>• Use consistent background and styling across images</p>
          </div>
        </CardContent>
      </Card>
    </form>
  )
}
