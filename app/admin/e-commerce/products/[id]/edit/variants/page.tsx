'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Palette, Package } from 'lucide-react'
import Link from 'next/link'
import { useProduct } from '@/lib/ecommerce/hooks/use-products'
import { ProductVariantManager } from '@/components/admin/products/product-variant-manager'
import { toast } from 'sonner'
import type { Product, ProductVariant } from '@/lib/ecommerce/types'

export default function ProductVariantsEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const [variants, setVariants] = useState<ProductVariant[]>([])
  const [hasOpenDialogs, setHasOpenDialogs] = useState(false)
  const [hasUnsavedVariantChanges, setHasUnsavedVariantChanges] = useState(false)

  // Update variants when product loads
  useEffect(() => {
    if (product?.variants) {
      setVariants(product.variants)
    }
  }, [product])

  const handleVariantsChange = (newVariants: ProductVariant[]) => {
    setVariants(newVariants)
  }

  const handleDialogStateChange = (isOpen: boolean) => {
    setHasOpenDialogs(isOpen)
  }

  const handleUnsavedChangesChange = (hasChanges: boolean) => {
    setHasUnsavedVariantChanges(hasChanges)
  }

  const handleBackNavigation = () => {
    if (hasOpenDialogs || hasUnsavedVariantChanges) {
      const confirmed = window.confirm(
        'You have unsaved variant changes or open dialogs. Are you sure you want to leave this page?'
      )
      if (!confirmed) {
        return
      }
    }
    router.push(`/admin/e-commerce/products/${productId}/edit`)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={handleBackNavigation}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Edit
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading...</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-8 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Error</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-red-500">{error?.message || 'Product not found'}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={handleBackNavigation}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Edit
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Product Variants
              {(hasOpenDialogs || hasUnsavedVariantChanges) && (
                <span className="ml-2 text-sm text-orange-600 font-normal">
                  • Working on variants
                </span>
              )}
            </h1>
            <p className="text-muted-foreground">
              {product.title}
              {(hasOpenDialogs || hasUnsavedVariantChanges) && (
                <span className="block text-orange-600 text-sm mt-1">
                  Complete variant work before leaving this page
                </span>
              )}
            </p>
          </div>
        </div>
        
        {/* Show Close Form button when there are open dialogs after successful save */}
        {(hasOpenDialogs || hasUnsavedVariantChanges) && (
          <Button
            variant="secondary"
            onClick={() => {
              if (hasUnsavedVariantChanges) {
                const confirmed = window.confirm(
                  'You have unsaved variant changes. Are you sure you want to leave this page?'
                )
                if (!confirmed) return
              }
              router.push(`/admin/e-commerce/products/${productId}`)
            }}
          >
            Close and Go to Product
          </Button>
        )}
      </div>

      {/* Variants Manager */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Palette className="mr-2 h-5 w-5" />
            Manage Variants
          </CardTitle>
          <CardDescription>
            Create and manage different variations of this product (size, color, style, etc.)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ProductVariantManager
            product={product}
            variants={variants}
            onVariantsChange={handleVariantsChange}
            onDialogStateChange={handleDialogStateChange}
            onUnsavedChangesChange={handleUnsavedChangesChange}
          />
        </CardContent>
      </Card>

      {/* Variants Summary */}
      {variants.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="mr-2 h-5 w-5" />
              Variants Summary
            </CardTitle>
            <CardDescription>
              Overview of all product variants
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {variants.map((variant, index) => (
                  <div key={variant.id || index} className="p-4 border rounded-lg">
                    <div className="space-y-2">
                      <h4 className="font-medium">{variant.title || `Variant ${index + 1}`}</h4>
                      {variant.price && (
                        <p className="text-sm text-muted-foreground">
                          Price: R {variant.price.amount.toFixed(2)}
                        </p>
                      )}
                      {variant.sku && (
                        <p className="text-sm text-muted-foreground">
                          SKU: {variant.sku}
                        </p>
                      )}
                      {variant.inventoryQuantity !== undefined && (
                        <p className="text-sm text-muted-foreground">
                          Stock: {variant.inventoryQuantity} units
                        </p>
                      )}
                      {variant.options && variant.options.length > 0 && (
                        <div className="flex flex-wrap gap-1">
                          {variant.options.map((option, optionIndex) => (
                            <span
                              key={optionIndex}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-muted"
                            >
                              {option.name}: {option.value}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="pt-4 border-t">
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Total Variants:</span>
                  <span>{variants.length}</span>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Total Stock:</span>
                  <span>
                    {variants.reduce((total, variant) => total + (variant.inventoryQuantity || 0), 0)} units
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Variants State */}
      {variants.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <Palette className="mx-auto h-8 w-8 mb-2" />
              <p>No variants created yet</p>
              <p className="text-xs mt-1">Use the variant manager above to create product variations</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
