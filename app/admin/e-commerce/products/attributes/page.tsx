'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Plus,
  Edit,
  Trash2,
  Filter,
  Tag,
  Settings
} from 'lucide-react'
import { toast } from 'sonner'

interface ProductAttribute {
  id: string
  name: string
  slug: string
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'color' | 'image'
  description?: string
  isRequired: boolean
  isVariant: boolean
  isFilter: boolean
  position: number
  options: string[]
  validation?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export default function ProductAttributesPage() {
  const [attributes, setAttributes] = useState<ProductAttribute[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddingAttribute, setIsAddingAttribute] = useState(false)
  const [editingAttribute, setEditingAttribute] = useState<ProductAttribute | null>(null)

  const [newAttribute, setNewAttribute] = useState({
    name: '',
    slug: '',
    type: 'text' as const,
    description: '',
    isRequired: false,
    isVariant: false,
    isFilter: false,
    position: 0,
    options: [] as string[],
    validation: {}
  })

  const [newOption, setNewOption] = useState('')

  useEffect(() => {
    fetchAttributes()
  }, [])

  const fetchAttributes = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/e-commerce/attributes')
      const data = await response.json()

      if (data.success) {
        setAttributes(data.data)
      } else {
        toast.error('Failed to fetch attributes')
      }
    } catch (error) {
      console.error('Error fetching attributes:', error)
      toast.error('Failed to fetch attributes')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAttribute = async () => {
    try {
      const response = await fetch('/api/e-commerce/attributes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newAttribute),
      })

      const data = await response.json()

      if (data.success) {
        setAttributes([...attributes, data.data])
        setIsAddingAttribute(false)
        resetNewAttribute()
        toast.success('Attribute created successfully')
      } else {
        toast.error(data.error || 'Failed to create attribute')
      }
    } catch (error) {
      console.error('Error creating attribute:', error)
      toast.error('Failed to create attribute')
    }
  }

  const handleUpdateAttribute = async (attribute: ProductAttribute) => {
    try {
      const response = await fetch(`/api/e-commerce/attributes/${attribute.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(attribute),
      })

      const data = await response.json()

      if (data.success) {
        setAttributes(attributes.map(attr => 
          attr.id === attribute.id ? data.data : attr
        ))
        setEditingAttribute(null)
        toast.success('Attribute updated successfully')
      } else {
        toast.error(data.error || 'Failed to update attribute')
      }
    } catch (error) {
      console.error('Error updating attribute:', error)
      toast.error('Failed to update attribute')
    }
  }

  const handleDeleteAttribute = async (id: string) => {
    if (!confirm('Are you sure you want to delete this attribute?')) {
      return
    }

    try {
      const response = await fetch(`/api/e-commerce/attributes/${id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (data.success) {
        setAttributes(attributes.filter(attr => attr.id !== id))
        toast.success('Attribute deleted successfully')
      } else {
        toast.error(data.error || 'Failed to delete attribute')
      }
    } catch (error) {
      console.error('Error deleting attribute:', error)
      toast.error('Failed to delete attribute')
    }
  }

  const resetNewAttribute = () => {
    setNewAttribute({
      name: '',
      slug: '',
      type: 'text',
      description: '',
      isRequired: false,
      isVariant: false,
      isFilter: false,
      position: 0,
      options: [],
      validation: {}
    })
    setNewOption('')
  }

  const addOption = () => {
    if (newOption.trim() && !newAttribute.options.includes(newOption.trim())) {
      setNewAttribute({
        ...newAttribute,
        options: [...newAttribute.options, newOption.trim()]
      })
      setNewOption('')
    }
  }

  const removeOption = (option: string) => {
    setNewAttribute({
      ...newAttribute,
      options: newAttribute.options.filter(opt => opt !== option)
    })
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'select':
      case 'multiselect':
        return <Filter className="h-4 w-4" />
      case 'color':
        return <div className="h-4 w-4 rounded-full bg-gradient-to-r from-red-500 to-blue-500" />
      default:
        return <Tag className="h-4 w-4" />
    }
  }

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'text':
        return 'default'
      case 'number':
        return 'secondary'
      case 'boolean':
        return 'outline'
      case 'select':
      case 'multiselect':
        return 'destructive'
      case 'color':
        return 'default'
      case 'image':
        return 'secondary'
      default:
        return 'default'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading attributes...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Product Attributes</h1>
          <p className="text-muted-foreground">
            Manage product attributes for variants and filtering
          </p>
        </div>
        <Button onClick={() => setIsAddingAttribute(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Attribute
        </Button>
      </div>

      {/* Attributes Table */}
      <Card>
        <CardHeader>
          <CardTitle>Attributes</CardTitle>
          <CardDescription>
            Configure attributes that can be used for product variants and filtering
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Usage</TableHead>
                <TableHead>Options</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {attributes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    <div className="flex flex-col items-center space-y-2">
                      <Settings className="h-8 w-8 text-muted-foreground" />
                      <p className="text-muted-foreground">No attributes created yet</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsAddingAttribute(true)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add First Attribute
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                attributes.map((attribute) => (
                  <TableRow key={attribute.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getTypeIcon(attribute.type)}
                        <div>
                          <div className="font-medium">{attribute.name}</div>
                          {attribute.description && (
                            <div className="text-sm text-muted-foreground">
                              {attribute.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getTypeBadgeColor(attribute.type) as any}>
                        {attribute.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {attribute.isVariant && (
                          <Badge variant="outline" className="text-xs">
                            Variant
                          </Badge>
                        )}
                        {attribute.isFilter && (
                          <Badge variant="outline" className="text-xs">
                            Filter
                          </Badge>
                        )}
                        {attribute.isRequired && (
                          <Badge variant="outline" className="text-xs">
                            Required
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {attribute.options.length > 0 ? (
                        <div className="text-sm text-muted-foreground">
                          {attribute.options.slice(0, 3).join(', ')}
                          {attribute.options.length > 3 && ` +${attribute.options.length - 3} more`}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingAttribute(attribute)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteAttribute(attribute.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add Attribute Dialog */}
      <Dialog open={isAddingAttribute} onOpenChange={setIsAddingAttribute}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Attribute</DialogTitle>
            <DialogDescription>
              Create a new product attribute for variants and filtering
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="attr-name">Name *</Label>
                <Input
                  id="attr-name"
                  value={newAttribute.name}
                  onChange={(e) => setNewAttribute({ ...newAttribute, name: e.target.value })}
                  placeholder="e.g., Color, Size, Material"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="attr-type">Type *</Label>
                <Select 
                  value={newAttribute.type} 
                  onValueChange={(value: any) => setNewAttribute({ ...newAttribute, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="text">Text</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                    <SelectItem value="boolean">Boolean</SelectItem>
                    <SelectItem value="select">Select</SelectItem>
                    <SelectItem value="multiselect">Multi-select</SelectItem>
                    <SelectItem value="color">Color</SelectItem>
                    <SelectItem value="image">Image</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="attr-description">Description</Label>
              <Textarea
                id="attr-description"
                value={newAttribute.description}
                onChange={(e) => setNewAttribute({ ...newAttribute, description: e.target.value })}
                placeholder="Optional description"
                rows={2}
              />
            </div>

            {(newAttribute.type === 'select' || newAttribute.type === 'multiselect') && (
              <div className="space-y-2">
                <Label>Options</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {newAttribute.options.map((option, index) => (
                    <div key={index} className="flex items-center bg-gray-100 rounded-md px-2 py-1">
                      <span className="text-sm">{option}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="ml-1 h-4 w-4 p-0"
                        onClick={() => removeOption(option)}
                      >
                        ×
                      </Button>
                    </div>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={newOption}
                    onChange={(e) => setNewOption(e.target.value)}
                    placeholder="Add option"
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addOption())}
                  />
                  <Button type="button" variant="outline" onClick={addOption}>
                    Add
                  </Button>
                </div>
              </div>
            )}

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="is-variant"
                  checked={newAttribute.isVariant}
                  onCheckedChange={(checked) => setNewAttribute({ ...newAttribute, isVariant: checked })}
                />
                <Label htmlFor="is-variant">Use for product variants</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is-filter"
                  checked={newAttribute.isFilter}
                  onCheckedChange={(checked) => setNewAttribute({ ...newAttribute, isFilter: checked })}
                />
                <Label htmlFor="is-filter">Use for product filtering</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is-required"
                  checked={newAttribute.isRequired}
                  onCheckedChange={(checked) => setNewAttribute({ ...newAttribute, isRequired: checked })}
                />
                <Label htmlFor="is-required">Required attribute</Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddingAttribute(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateAttribute}>
              Create Attribute
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
