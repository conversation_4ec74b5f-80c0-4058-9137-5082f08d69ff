'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Plus, 
  Edit, 
  Trash2, 
  MoreHorizontal, 
  Tags, 
  Eye, 
  EyeOff,
  Save,
  X,
  AlertTriangle,
  Loader2,
  Package
} from 'lucide-react'
import { useCollections, useCollectionMutations } from '@/lib/ecommerce/hooks/use-collections'
import { toast } from 'sonner'
import type { ProductCollection, CreateCollectionInput, UpdateCollectionInput } from '@/lib/ecommerce/hooks/use-collections'

export default function CollectionsPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedCollection, setSelectedCollection] = useState<ProductCollection | null>(null)
  const [searchQuery, setSearchQuery] = useState('')

  const { collections, loading, error, refetch } = useCollections()
  const { 
    createCollection, 
    updateCollection, 
    deleteCollection, 
    loading: mutationLoading, 
    error: mutationError,
    clearError 
  } = useCollectionMutations()

  // Form state
  const [formData, setFormData] = useState<CreateCollectionInput>({
    title: '',
    slug: '',
    description: '',
    image: '',
    sortOrder: 'manual',
    isVisible: true,
    seoTitle: '',
    seoDescription: '',
    productIds: []
  })

  // Filter collections based on search
  const filteredCollections = collections.filter(collection =>
    collection.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    collection.description?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const resetForm = () => {
    setFormData({
      title: '',
      slug: '',
      description: '',
      image: '',
      sortOrder: 'manual',
      isVisible: true,
      seoTitle: '',
      seoDescription: '',
      productIds: []
    })
  }

  const handleCreate = async () => {
    try {
      const result = await createCollection(formData)
      if (result) {
        toast.success('Collection created successfully')
        setIsCreateDialogOpen(false)
        resetForm()
        refetch()
      }
    } catch (error) {
      toast.error('Failed to create collection')
    }
  }

  const handleEdit = (collection: ProductCollection) => {
    setSelectedCollection(collection)
    setFormData({
      title: collection.title,
      slug: collection.slug,
      description: collection.description || '',
      image: collection.image || '',
      sortOrder: collection.sortOrder,
      isVisible: collection.isVisible,
      seoTitle: collection.seoTitle || '',
      seoDescription: collection.seoDescription || '',
      productIds: []
    })
    setIsEditDialogOpen(true)
  }

  const handleUpdate = async () => {
    if (!selectedCollection) return

    try {
      const result = await updateCollection({
        id: selectedCollection.id,
        ...formData
      })
      if (result) {
        toast.success('Collection updated successfully')
        setIsEditDialogOpen(false)
        setSelectedCollection(null)
        resetForm()
        refetch()
      }
    } catch (error) {
      toast.error('Failed to update collection')
    }
  }

  const handleDelete = async (collection: ProductCollection) => {
    if (!confirm(`Are you sure you want to delete "${collection.title}"?`)) return

    try {
      const result = await deleteCollection(collection.id)
      if (result) {
        toast.success('Collection deleted successfully')
        refetch()
      }
    } catch (error) {
      toast.error('Failed to delete collection')
    }
  }

  const handleInputChange = (field: keyof CreateCollectionInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Auto-generate slug from title
    if (field === 'title' && typeof value === 'string') {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()

      setFormData(prev => ({
        ...prev,
        slug
      }))
    }
  }

  const CollectionForm = ({ isEdit = false }: { isEdit?: boolean }) => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="title">Collection Title *</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="Enter collection title"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="slug">URL Slug</Label>
          <Input
            id="slug"
            value={formData.slug}
            onChange={(e) => handleInputChange('slug', e.target.value)}
            placeholder="collection-slug"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Collection description"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="sortOrder">Sort Order</Label>
          <Select 
            value={formData.sortOrder} 
            onValueChange={(value: any) => handleInputChange('sortOrder', value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="manual">Manual</SelectItem>
              <SelectItem value="best-selling">Best Selling</SelectItem>
              <SelectItem value="created">Date Created</SelectItem>
              <SelectItem value="price-asc">Price: Low to High</SelectItem>
              <SelectItem value="price-desc">Price: High to Low</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2 pt-6">
          <Switch
            id="isVisible"
            checked={formData.isVisible}
            onCheckedChange={(checked) => handleInputChange('isVisible', checked)}
          />
          <Label htmlFor="isVisible">Visible</Label>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="seoTitle">SEO Title</Label>
          <Input
            id="seoTitle"
            value={formData.seoTitle}
            onChange={(e) => handleInputChange('seoTitle', e.target.value)}
            placeholder="SEO optimized title"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="seoDescription">SEO Description</Label>
          <Input
            id="seoDescription"
            value={formData.seoDescription}
            onChange={(e) => handleInputChange('seoDescription', e.target.value)}
            placeholder="SEO meta description"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button 
          variant="outline" 
          onClick={() => {
            if (isEdit) {
              setIsEditDialogOpen(false)
              setSelectedCollection(null)
            } else {
              setIsCreateDialogOpen(false)
            }
            resetForm()
          }}
        >
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button 
          onClick={isEdit ? handleUpdate : handleCreate}
          disabled={mutationLoading || !formData.title.trim()}
        >
          {mutationLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          {isEdit ? 'Update' : 'Create'} Collection
        </Button>
      </div>
    </div>
  )

  const getSortOrderLabel = (sortOrder: string) => {
    const labels = {
      'manual': 'Manual',
      'best-selling': 'Best Selling',
      'created': 'Date Created',
      'price-asc': 'Price: Low to High',
      'price-desc': 'Price: High to Low'
    }
    return labels[sortOrder as keyof typeof labels] || sortOrder
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Collections</h1>
          <p className="text-muted-foreground">
            Create and manage product collections for marketing and organization
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Collection
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Collection</DialogTitle>
              <DialogDescription>
                Add a new collection to organize your products
              </DialogDescription>
            </DialogHeader>
            <CollectionForm />
          </DialogContent>
        </Dialog>
      </div>

      {/* Error Display */}
      {(error || mutationError) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error || mutationError}
            <Button 
              variant="outline" 
              size="sm" 
              className="ml-2"
              onClick={() => {
                clearError()
                refetch()
              }}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Collection Management</CardTitle>
          <CardDescription>
            View and manage all your product collections
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Input
              placeholder="Search collections..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {/* Collections Table */}
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading collections...</span>
            </div>
          ) : filteredCollections.length === 0 ? (
            <div className="text-center py-8">
              <Tags className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-2 text-sm font-semibold">No collections found</h3>
              <p className="mt-1 text-sm text-muted-foreground">
                {searchQuery ? 'No collections match your search.' : 'Get started by creating your first collection.'}
              </p>
              {!searchQuery && (
                <Button className="mt-4" onClick={() => setIsCreateDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Collection
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Slug</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead>Sort Order</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[70px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCollections.map((collection) => (
                  <TableRow key={collection.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{collection.title}</div>
                        {collection.description && (
                          <div className="text-sm text-muted-foreground">
                            {collection.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-sm">
                      {collection.slug}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        <Package className="mr-1 h-3 w-3" />
                        {collection.productCount || 0} products
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {getSortOrderLabel(collection.sortOrder)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {collection.isVisible ? (
                        <Badge variant="default">
                          <Eye className="mr-1 h-3 w-3" />
                          Visible
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          <EyeOff className="mr-1 h-3 w-3" />
                          Hidden
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEdit(collection)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(collection)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Collection</DialogTitle>
            <DialogDescription>
              Update collection information and settings
            </DialogDescription>
          </DialogHeader>
          <CollectionForm isEdit />
        </DialogContent>
      </Dialog>
    </div>
  )
}
