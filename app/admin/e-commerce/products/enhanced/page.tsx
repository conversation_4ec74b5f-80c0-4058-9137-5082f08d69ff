'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Checkbox } from '@/components/ui/checkbox'
import { Plus, MoreHorizontal, Edit, Trash2, Eye, Settings, BarChart3, Upload } from 'lucide-react'
import { ProductManagementDashboard } from '@/components/admin/products/product-management-dashboard'
import { AdvancedProductSearch } from '@/components/admin/advanced-product-search'
import { BulkProductOperations } from '@/components/admin/products/bulk-product-operations'
import { ProductImportExport } from '@/components/admin/products/product-import-export'

interface Product {
  id: string
  title: string
  handle: string
  status: 'active' | 'draft' | 'archived'
  price: number
  currency: string
  inventoryQuantity: number
  hasVariants: boolean
  variantCount: number
  createdAt: string
  updatedAt: string
}

interface SearchFilters {
  query: string
  status: string[]
  categories: string[]
  vendors: string[]
  priceRange: [number, number]
  stockStatus: string[]
  hasVariants: boolean | null
  dateRange: {
    from: Date | null
    to: Date | null
  }
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

export default function EnhancedProductsPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState('products')

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      // Mock data for now - replace with actual API call
      const mockProducts: Product[] = [
        {
          id: '1',
          title: 'Kids Cotton T-Shirt',
          handle: 'kids-cotton-t-shirt',
          status: 'active',
          price: 299.99,
          currency: 'ZAR',
          inventoryQuantity: 25,
          hasVariants: true,
          variantCount: 6,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z'
        },
        {
          id: '2',
          title: 'Summer Dress',
          handle: 'summer-dress',
          status: 'active',
          price: 459.99,
          currency: 'ZAR',
          inventoryQuantity: 18,
          hasVariants: true,
          variantCount: 4,
          createdAt: '2024-01-14T10:00:00Z',
          updatedAt: '2024-01-14T10:00:00Z'
        },
        {
          id: '3',
          title: 'Denim Shorts',
          handle: 'denim-shorts',
          status: 'draft',
          price: 349.99,
          currency: 'ZAR',
          inventoryQuantity: 0,
          hasVariants: false,
          variantCount: 1,
          createdAt: '2024-01-13T10:00:00Z',
          updatedAt: '2024-01-13T10:00:00Z'
        },
        {
          id: '4',
          title: 'Sneakers',
          handle: 'kids-sneakers',
          status: 'active',
          price: 599.99,
          currency: 'ZAR',
          inventoryQuantity: 12,
          hasVariants: true,
          variantCount: 8,
          createdAt: '2024-01-12T10:00:00Z',
          updatedAt: '2024-01-12T10:00:00Z'
        },
        {
          id: '5',
          title: 'Winter Hoodie',
          handle: 'winter-hoodie',
          status: 'active',
          price: 699.99,
          currency: 'ZAR',
          inventoryQuantity: 8,
          hasVariants: true,
          variantCount: 5,
          createdAt: '2024-01-11T10:00:00Z',
          updatedAt: '2024-01-11T10:00:00Z'
        }
      ]
      setProducts(mockProducts)
      setFilteredProducts(mockProducts)
    } catch (error) {
      console.error('Error fetching products:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleFiltersChange = (filters: SearchFilters) => {
    let filtered = [...products]

    // Apply search query
    if (filters.query) {
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(filters.query.toLowerCase()) ||
        product.handle.toLowerCase().includes(filters.query.toLowerCase())
      )
    }

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(product => filters.status.includes(product.status))
    }

    // Apply price range filter
    filtered = filtered.filter(product => 
      product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1]
    )

    // Apply variants filter
    if (filters.hasVariants !== null) {
      filtered = filtered.filter(product => product.hasVariants === filters.hasVariants)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[filters.sortBy as keyof Product]
      let bValue: any = b[filters.sortBy as keyof Product]

      if (filters.sortBy === 'createdAt' || filters.sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    setFilteredProducts(filtered)
  }

  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId])
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(filteredProducts.map(p => p.id))
    } else {
      setSelectedProducts([])
    }
  }

  const handleOperationComplete = () => {
    fetchProducts()
  }

  const handleClearSelection = () => {
    setSelectedProducts([])
  }

  const handleExport = () => {
    // Export functionality would be implemented here
    console.log('Exporting products...')
  }

  const formatPrice = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency
    }).format(amount)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">Active</Badge>
      case 'draft':
        return <Badge variant="secondary">Draft</Badge>
      case 'archived':
        return <Badge variant="outline">Archived</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getStockBadge = (quantity: number) => {
    if (quantity === 0) {
      return <Badge variant="destructive">Out of Stock</Badge>
    } else if (quantity <= 5) {
      return <Badge variant="secondary">Low Stock</Badge>
    } else {
      return <Badge variant="default">In Stock</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading products...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Enhanced Product Management</h1>
          <p className="text-muted-foreground">
            Advanced product catalog management with analytics and bulk operations
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/admin/products/attributes">
            <Button variant="outline">
              <Settings className="mr-2 h-4 w-4" />
              Attributes
            </Button>
          </Link>
          <Link href="/admin/products/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </Link>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="import-export">Import/Export</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <ProductManagementDashboard />
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          {/* Advanced Search */}
          <AdvancedProductSearch
            onFiltersChange={handleFiltersChange}
            onExport={handleExport}
            totalResults={filteredProducts.length}
            loading={loading}
          />

          {/* Bulk Operations */}
          <BulkProductOperations
            selectedProducts={selectedProducts}
            products={filteredProducts}
            onOperationComplete={handleOperationComplete}
            onClearSelection={handleClearSelection}
          />

          {/* Products Table */}
          <Card>
            <CardHeader>
              <CardTitle>Products</CardTitle>
              <CardDescription>
                {filteredProducts.length} products found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Inventory</TableHead>
                    <TableHead>Variants</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className="w-12"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProducts.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <div className="text-muted-foreground">
                          No products found matching your criteria.
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredProducts.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedProducts.includes(product.id)}
                            onCheckedChange={(checked) => handleSelectProduct(product.id, checked as boolean)}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{product.title}</div>
                            <div className="text-sm text-muted-foreground">{product.handle}</div>
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(product.status)}</TableCell>
                        <TableCell>{formatPrice(product.price, product.currency)}</TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="text-sm">{product.inventoryQuantity} in stock</div>
                            {getStockBadge(product.inventoryQuantity)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {product.hasVariants ? (
                              <Badge variant="outline">{product.variantCount} variants</Badge>
                            ) : (
                              <span className="text-muted-foreground">No variants</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(product.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <BarChart3 className="mr-2 h-4 w-4" />
                                Analytics
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="import-export" className="space-y-4">
          <ProductImportExport />
        </TabsContent>
      </Tabs>
    </div>
  )
}
