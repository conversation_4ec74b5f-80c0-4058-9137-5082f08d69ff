'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-react'

export default function ProductAPITestPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  
  // Test data
  const [productId, setProductId] = useState('')
  const [testData, setTestData] = useState({
    title: 'Test Product Update',
    description: 'This is a test product description',
    price: 99.99,
    status: 'active'
  })

  const testCreateProduct = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/e-commerce/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: testData.title,
          description: testData.description,
          price: {
            amount: testData.price,
            currency: 'ZAR'
          },
          status: testData.status,
          vendor: 'Test Vendor',
          productType: 'Test Type'
        }),
      })

      const data = await response.json()
      
      if (response.ok) {
        setResult(data)
        if (data.data?.id) {
          setProductId(data.data.id)
        }
      } else {
        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`)
      }
    } catch (err) {
      setError(`Network error: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testUpdateProduct = async () => {
    if (!productId) {
      setError('Please create a product first or enter a product ID')
      return
    }

    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: productId,
          title: testData.title + ' (Updated)',
          description: testData.description + ' (Updated)',
          price: {
            amount: testData.price + 10,
            currency: 'ZAR'
          },
          status: testData.status
        }),
      })

      const data = await response.json()
      
      if (response.ok) {
        setResult(data)
      } else {
        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`)
      }
    } catch (err) {
      setError(`Network error: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testGetProduct = async () => {
    if (!productId) {
      setError('Please enter a product ID')
      return
    }

    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}`)
      const data = await response.json()
      
      if (response.ok) {
        setResult(data)
      } else {
        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`)
      }
    } catch (err) {
      setError(`Network error: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testListProducts = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/e-commerce/products?limit=5')
      const data = await response.json()
      
      if (response.ok) {
        setResult(data)
      } else {
        setError(`HTTP ${response.status}: ${data.error || 'Unknown error'}`)
      }
    } catch (err) {
      setError(`Network error: ${err instanceof Error ? err.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Product API Test</h1>
        <p className="text-muted-foreground">Test the product API endpoints directly</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Controls */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Test Data</CardTitle>
              <CardDescription>Configure test data for API calls</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="productId">Product ID (for update/get)</Label>
                <Input
                  id="productId"
                  value={productId}
                  onChange={(e) => setProductId(e.target.value)}
                  placeholder="Enter product ID"
                />
              </div>
              
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={testData.title}
                  onChange={(e) => setTestData(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>
              
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={testData.description}
                  onChange={(e) => setTestData(prev => ({ ...prev, description: e.target.value }))}
                />
              </div>
              
              <div>
                <Label htmlFor="price">Price</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  value={testData.price}
                  onChange={(e) => setTestData(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>API Tests</CardTitle>
              <CardDescription>Test different API endpoints</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                onClick={testListProducts} 
                disabled={loading}
                className="w-full"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Test List Products (GET /api/e-commerce/products)
              </Button>
              
              <Button 
                onClick={testCreateProduct} 
                disabled={loading}
                className="w-full"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Test Create Product (POST /api/e-commerce/products)
              </Button>
              
              <Button 
                onClick={testGetProduct} 
                disabled={loading || !productId}
                className="w-full"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Test Get Product (GET /api/e-commerce/products/{productId})
              </Button>
              
              <Button 
                onClick={testUpdateProduct} 
                disabled={loading || !productId}
                className="w-full"
                variant="default"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Test Update Product (PUT /api/e-commerce/products/{productId})
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Results */}
        <div className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {result && (
            <Card>
              <CardHeader>
                <CardTitle>API Response</CardTitle>
                <CardDescription>
                  Status: {result.success ? 'Success' : 'Error'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-sm">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
