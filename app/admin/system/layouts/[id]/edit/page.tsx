'use client'

import React, { useState, useEffect } from 'react'
import { NextJSLayoutEditor } from '@/lib/layout-builder/components/nextjs-layout-editor'
import { NextJSLayout } from '@/lib/layout-builder/types/nextjs-types'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import {
  ArrowLeft,
  Globe,
  FileCode,
  Layout,
  Sparkles,
  Save,
  Eye,
  Download,
  Settings,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { toast } from 'sonner'
import { useRouter, useParams } from 'next/navigation'

interface PageProps {
  params: {
    id: string
  }
}

export default function NextJSLayoutEditPage({ params }: PageProps) {
  const router = useRouter()
  const { id } = params
  const [currentLayout, setCurrentLayout] = useState<NextJSLayout | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [registeredLayouts, setRegisteredLayouts] = useState<any[]>([])

  // Load layout data based on ID
  useEffect(() => {
    const loadLayout = async () => {
      try {
        setIsLoading(true)
        setError(null)

        if (id === 'new') {
          // Create new layout
          const defaultLayout = createDefaultNextJSLayout()
          setCurrentLayout(defaultLayout)
        } else {
          // Load existing layout
          const layout = await fetchLayoutById(id)
          if (layout) {
            setCurrentLayout(layout)
          } else {
            setError('Layout not found')
          }
        }
      } catch (error) {
        console.error('Failed to load layout:', error)
        setError('Failed to load layout')
      } finally {
        setIsLoading(false)
      }
    }

    loadLayout()
    loadRegisteredLayouts()
  }, [id])

  // Create default NextJS layout
  const createDefaultNextJSLayout = (): NextJSLayout => {
    const defaultSpacing = { top: 0, right: 0, bottom: 0, left: 0 }
    const defaultResponsive = {
      mobile: {
        display: 'block' as const,
        width: '100%',
        height: 'auto',
        spacing: defaultSpacing,
        typography: {
          fontFamily: 'system-ui',
          fontSize: 14,
          fontWeight: 400,
          lineHeight: 1.5,
          letterSpacing: 0,
          textAlign: 'left' as const,
          textTransform: 'none' as const
        }
      },
      tablet: {
        display: 'block' as const,
        width: '100%',
        height: 'auto',
        spacing: defaultSpacing,
        typography: {
          fontFamily: 'system-ui',
          fontSize: 16,
          fontWeight: 400,
          lineHeight: 1.5,
          letterSpacing: 0,
          textAlign: 'left' as const,
          textTransform: 'none' as const
        }
      },
      desktop: {
        display: 'block' as const,
        width: '100%',
        height: 'auto',
        spacing: defaultSpacing,
        typography: {
          fontFamily: 'system-ui',
          fontSize: 16,
          fontWeight: 400,
          lineHeight: 1.5,
          letterSpacing: 0,
          textAlign: 'left' as const,
          textTransform: 'none' as const
        }
      },
      large: {
        display: 'block' as const,
        width: '100%',
        height: 'auto',
        spacing: defaultSpacing,
        typography: {
          fontFamily: 'system-ui',
          fontSize: 16,
          fontWeight: 400,
          lineHeight: 1.5,
          letterSpacing: 0,
          textAlign: 'left' as const,
          textTransform: 'none' as const
        }
      }
    }

    return {
      id: id === 'new' ? 'nextjs-layout-' + Date.now() : id,
      name: 'New NextJS Layout',
      description: 'A new NextJS layout created with the visual editor',
      type: 'page',
      category: 'custom',
      structure: {
        main: {
          id: 'main-section',
          type: 'main',
          name: 'Main Content',
          position: 1,
          blocks: [],
          configuration: {
            layout: 'block',
            alignment: 'left',
            spacing: defaultSpacing,
            background: { type: 'color', color: 'transparent' },
            container: {
              maxWidth: 1200,
              padding: defaultSpacing,
              margin: defaultSpacing,
              centered: true
            }
          },
          styling: {
            background: { type: 'color', color: 'transparent' },
            border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
            spacing: defaultSpacing,
            shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
          },
          responsive: defaultResponsive,
          isVisible: true
        }
      },
      styling: {
        theme: 'default',
        colorScheme: 'light' as const,
        typography: {
          fontFamily: 'system-ui',
          fontSize: 16,
          fontWeight: 400,
          lineHeight: 1.5,
          letterSpacing: 0,
          textAlign: 'left' as const,
          textTransform: 'none' as const
        },
        spacing: defaultSpacing,
        colors: {
          primary: '#3b82f6',
          secondary: '#64748b',
          accent: '#8b5cf6',
          text: '#1f2937',
          background: '#ffffff',
          border: '#e5e7eb'
        }
      },
      responsive: defaultResponsive,
      conditions: {},
      isTemplate: false,
      isSystem: false,
      isActive: true,
      usageCount: 0,
      tags: ['nextjs', 'layout', 'universal-renderer'],
      createdAt: new Date(),
      updatedAt: new Date(),
      nextjs: {
        type: 'root',
        route: 'app/layout.tsx',
        metadata: {
          title: { default: 'My App', template: '%s | My App' },
          description: 'A modern web application built with NextJS and Universal Renderer'
        },
        imports: ['import React from "react"'],
        exports: [
          {
            name: 'default',
            type: 'default',
            isAsync: false,
            returnType: 'JSX.Element'
          }
        ],
        appRouterFeatures: ['metadata', 'loading']
      }
    }
  }

  // Fetch layout by ID (mock implementation)
  const fetchLayoutById = async (layoutId: string): Promise<NextJSLayout | null> => {
    try {
      // In a real implementation, this would fetch from your database/API
      // For now, return a mock layout or null
      console.log('Fetching layout:', layoutId)
      return null
    } catch (error) {
      console.error('Error fetching layout:', error)
      return null
    }
  }

  // Load registered layouts
  const loadRegisteredLayouts = async () => {
    try {
      const response = await fetch('/api/universal-renderer/register-layout')
      if (response.ok) {
        const layouts = await response.json()
        setRegisteredLayouts(layouts)
      }
    } catch (error) {
      console.error('Failed to load registered layouts:', error)
    }
  }

  // Handle save layout
  const handleSaveLayout = async (layout: NextJSLayout) => {
    setIsSaving(true)
    try {
      // Save layout to database/storage
      const saveResponse = await fetch(`/api/layouts/${layout.id}`, {
        method: id === 'new' ? 'POST' : 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ layout })
      })

      if (!saveResponse.ok) {
        throw new Error('Failed to save layout')
      }

      // Register with Universal Renderer
      const registerResponse = await fetch('/api/universal-renderer/register-layout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ layout })
      })

      if (!registerResponse.ok) {
        console.warn('Failed to register with Universal Renderer, but layout was saved')
      }

      toast.success(`Layout saved successfully${registerResponse.ok ? ' and registered with Universal Renderer' : ''}`)

      // Reload registered layouts
      await loadRegisteredLayouts()

      // Update current layout
      setCurrentLayout(layout)

      // If this was a new layout, redirect to edit mode
      if (id === 'new') {
        router.replace(`/admin/system/layouts/${layout.id}/edit`)
      }
    } catch (error) {
      toast.error('Failed to save layout')
      console.error('Save error:', error)
    } finally {
      setIsSaving(false)
    }
  }

  // Handle preview
  const handlePreview = () => {
    if (currentLayout) {
      // Open preview in new tab
      const previewUrl = `/preview/layout/${currentLayout.id}`
      window.open(previewUrl, '_blank')
    }
  }

  // Handle back navigation
  const handleBack = () => {
    router.push('/admin/system/layouts')
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Layouts
            </Button>
            
            <Separator orientation="vertical" className="h-6" />
            
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Globe className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {id === 'new' ? 'Create New Layout' : `Edit Layout: ${currentLayout?.name || 'Loading...'}`}
                </h1>
                <p className="text-sm text-muted-foreground">
                  {id === 'new'
                    ? 'Create a new NextJS layout with Universal Renderer integration'
                    : 'Edit NextJS layout with Universal Renderer integration'
                  }
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Badge variant="secondary" className="gap-1">
              <Sparkles className="w-3 h-3" />
              AI-Powered
            </Badge>
            <Badge variant="outline" className="gap-1">
              <Globe className="w-3 h-3" />
              Universal Renderer
            </Badge>
          </div>
        </div>
      </div>

      {/* Info Panel */}
      <div className="bg-blue-50 border-b border-blue-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-blue-800">
            <Globe className="w-4 h-4" />
            <span>
              This layout will be automatically integrated with the Universal Rendering System for dynamic content rendering.
            </span>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-blue-700">
            <div className="flex items-center gap-1">
              <Layout className="w-4 h-4" />
              <span>{registeredLayouts.length} Registered Layouts</span>
            </div>
            <div className="flex items-center gap-1">
              <FileCode className="w-4 h-4" />
              <span>NextJS App Router</span>
            </div>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin text-purple-600" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Loading Layout Editor
              </h3>
              <p className="text-sm text-muted-foreground">
                {id === 'new' ? 'Initializing new layout...' : 'Loading layout data...'}
              </p>
            </div>
          </div>
        ) : error ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Error Loading Layout
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                {error}
              </p>
              <div className="flex gap-2 justify-center">
                <Button variant="outline" onClick={() => window.location.reload()}>
                  Retry
                </Button>
                <Button onClick={handleBack}>
                  Back to Layouts
                </Button>
              </div>
            </div>
          </div>
        ) : currentLayout ? (
          <NextJSLayoutEditor
            initialLayout={currentLayout}
            onSave={handleSaveLayout}
            onPreview={handlePreview}
            onBack={handleBack}
            isSaving={isSaving}
          />
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <Layout className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Layout Data
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Unable to load layout data
              </p>
              <Button onClick={handleBack}>
                Back to Layouts
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="bg-white border-t border-gray-200 px-6 py-2">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>NextJS Layout Editor v1.0</span>
            <span>•</span>
            <span>Universal Renderer Compatible</span>
            <span>•</span>
            <span>App Router Ready</span>
          </div>
          
          <div className="flex items-center gap-2">
            {isSaving ? (
              <>
                <Loader2 className="w-3 h-3 animate-spin text-blue-500" />
                <span>Saving...</span>
              </>
            ) : isLoading ? (
              <>
                <Loader2 className="w-3 h-3 animate-spin text-gray-500" />
                <span>Loading...</span>
              </>
            ) : error ? (
              <>
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span>Error</span>
              </>
            ) : (
              <>
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Ready</span>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
