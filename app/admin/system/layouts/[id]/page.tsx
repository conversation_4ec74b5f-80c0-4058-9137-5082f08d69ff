"use client"

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { PageData } from '@/lib/page-builder/types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import {
  ArrowLeft,
  Edit,
  Eye,
  Copy,
  ExternalLink,
  FileText,
  Layers,
  Palette,
  Share2,
  Trash2,
  MoreHorizontal,
  History
} from 'lucide-react'
import { toast } from 'sonner'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'

interface PageDetailsData extends PageData {
  viewCount: number
  shareCount: number
  versions: Array<{
    id: string
    versionNumber: number
    title: string
    description: string
    isPublished: boolean
    publishedAt: string | null
    createdBy: string | null
    createdAt: string
  }>
}

export default function PageDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [pageData, setPageData] = useState<PageDetailsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [deleting, setDeleting] = useState(false)

  const pageId = params.id as string

  // Load page data
  useEffect(() => {
    const fetchPageData = async () => {
      if (!pageId || pageId === 'new') {
        router.push('/admin/system/pages/new/edit')
        return
      }

      try {
        setLoading(true)
        const response = await fetch(`/api/e-commerce/pages/${pageId}`)
        const data = await response.json()

        if (data.success && data.data) {
          setPageData(data.data)
        } else {
          toast.error('Failed to load page')
          router.push('/admin/system/pages')
        }
      } catch (error) {
        console.error('Error loading page:', error)
        toast.error('Failed to load page')
        router.push('/admin/system/pages')
      } finally {
        setLoading(false)
      }
    }

    fetchPageData()
  }, [pageId, router])

  // Handle delete page
  const handleDelete = async () => {
    if (!pageData) return
    
    if (!confirm(`Are you sure you want to delete "${pageData.title}"? This action cannot be undone.`)) {
      return
    }

    try {
      setDeleting(true)
      const response = await fetch(`/api/e-commerce/pages/${pageId}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Page deleted successfully')
        router.push('/admin/system/pages')
      } else {
        toast.error(data.error || 'Failed to delete page')
      }
    } catch (error) {
      console.error('Error deleting page:', error)
      toast.error('Failed to delete page')
    } finally {
      setDeleting(false)
    }
  }

  // Handle duplicate page
  const handleDuplicate = async () => {
    if (!pageData) return

    try {
      const response = await fetch('/api/e-commerce/pages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: `${pageData.title} (Copy)`,
          slug: `${pageData.slug}-copy`,
          description: pageData.description,
          type: pageData.type,
          status: 'draft',
          blocks: pageData.blocks,
          settings: pageData.settings,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Page duplicated successfully')
        router.push(`/admin/system/pages/${data.data.id}`)
      } else {
        toast.error(data.error || 'Failed to duplicate page')
      }
    } catch (error) {
      console.error('Error duplicating page:', error)
      toast.error('Failed to duplicate page')
    }
  }

  // Copy page URL to clipboard
  const copyPageUrl = () => {
    if (!pageData) return
    const url = `${window.location.origin}/${pageData.slug}`
    navigator.clipboard.writeText(url)
    toast.success('Page URL copied to clipboard')
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-9 w-24" />
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-9" />
          </div>
        </div>
        
        {/* Content Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
        </div>
      </div>
    )
  }

  if (!pageData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Page not found</h2>
          <p className="text-muted-foreground mb-4">
            The page you're looking for doesn't exist or has been deleted.
          </p>
          <Button onClick={() => router.push('/admin/system/pages')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Pages
          </Button>
        </div>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'archived':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'landing':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'product':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'category':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/admin/system/pages')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Pages
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{pageData.title}</h1>
            <p className="text-muted-foreground">
              /{pageData.slug} • {pageData.type} page
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={copyPageUrl}
          >
            <Copy className="h-4 w-4 mr-2" />
            Copy URL
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            asChild
          >
            <Link href={`/preview/${pageData.slug}`} target="_blank">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Link>
          </Button>

          <Button
            size="sm"
            asChild
          >
            <Link href={`/admin/system/pages/${pageId}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Open Editor
            </Link>
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleDuplicate}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Page
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/admin/system/pages/${pageId}/edit`}>
                  <Palette className="h-4 w-4 mr-2" />
                  AI Editor
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={handleDelete}
                disabled={deleting}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {deleting ? 'Deleting...' : 'Delete Page'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Page Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Page Overview
                <div className="flex items-center space-x-2">
                  <Badge className={getStatusColor(pageData.status)}>
                    {pageData.status}
                  </Badge>
                  <Badge className={getTypeColor(pageData.type)}>
                    {pageData.type}
                  </Badge>
                </div>
              </CardTitle>
              <CardDescription>
                {pageData.description || 'No description provided'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Layers className="h-6 w-6 mx-auto mb-2 text-blue-600" />
                  <div className="text-2xl font-bold">{pageData.blocks?.length || 0}</div>
                  <div className="text-sm text-muted-foreground">Blocks</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Eye className="h-6 w-6 mx-auto mb-2 text-green-600" />
                  <div className="text-2xl font-bold">{pageData.viewCount || 0}</div>
                  <div className="text-sm text-muted-foreground">Views</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Share2 className="h-6 w-6 mx-auto mb-2 text-purple-600" />
                  <div className="text-2xl font-bold">{pageData.shareCount || 0}</div>
                  <div className="text-sm text-muted-foreground">Shares</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <History className="h-6 w-6 mx-auto mb-2 text-orange-600" />
                  <div className="text-2xl font-bold">{pageData.versions?.length || 0}</div>
                  <div className="text-sm text-muted-foreground">Versions</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabs for detailed information */}
          <Tabs defaultValue="blocks" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="blocks">Blocks</TabsTrigger>
              <TabsTrigger value="seo">SEO</TabsTrigger>
              <TabsTrigger value="versions">Versions</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="blocks" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Page Blocks</CardTitle>
                  <CardDescription>
                    Content blocks that make up this page
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {pageData.blocks && pageData.blocks.length > 0 ? (
                    <div className="space-y-3">
                      {pageData.blocks.map((block, index) => (
                        <div
                          key={block.id}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center text-sm font-medium">
                              {index + 1}
                            </div>
                            <div>
                              <div className="font-medium capitalize">
                                {block.type.replace('-', ' ')}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                Position: {block.position}
                                {!block.isVisible && ' • Hidden'}
                              </div>
                            </div>
                          </div>
                          <Badge variant={block.isVisible ? 'default' : 'secondary'}>
                            {block.isVisible ? 'Visible' : 'Hidden'}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Layers className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No blocks added to this page yet</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="seo" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>SEO Settings</CardTitle>
                  <CardDescription>
                    Search engine optimization settings for this page
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">SEO Title</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {pageData.seoTitle || pageData.title}
                    </p>
                  </div>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium">SEO Description</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {pageData.seoDescription || pageData.description || 'No SEO description set'}
                    </p>
                  </div>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium">Keywords</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {pageData.seoKeywords && pageData.seoKeywords.length > 0 ? (
                        pageData.seoKeywords.map((keyword, index) => (
                          <Badge key={index} variant="outline">
                            {keyword}
                          </Badge>
                        ))
                      ) : (
                        <p className="text-sm text-muted-foreground">No keywords set</p>
                      )}
                    </div>
                  </div>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium">Open Graph Image</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {pageData.ogImage || 'No OG image set'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="versions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Page Versions</CardTitle>
                  <CardDescription>
                    Version history and published versions of this page
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {pageData.versions && pageData.versions.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Version</TableHead>
                          <TableHead>Title</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {pageData.versions.map((version) => (
                          <TableRow key={version.id}>
                            <TableCell className="font-medium">
                              v{version.versionNumber}
                            </TableCell>
                            <TableCell>{version.title}</TableCell>
                            <TableCell>
                              <Badge variant={version.isPublished ? 'default' : 'secondary'}>
                                {version.isPublished ? 'Published' : 'Draft'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {new Date(version.createdAt).toLocaleDateString()}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No versions available</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Page Settings</CardTitle>
                  <CardDescription>
                    Advanced settings and configurations for this page
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Requires Authentication</label>
                      <p className="text-sm text-muted-foreground">
                        {pageData.requiresAuth ? 'Yes' : 'No'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Allow Comments</label>
                      <p className="text-sm text-muted-foreground">
                        {pageData.allowComments ? 'Yes' : 'No'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Home Page</label>
                      <p className="text-sm text-muted-foreground">
                        {pageData.isHomePage ? 'Yes' : 'No'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Landing Page</label>
                      <p className="text-sm text-muted-foreground">
                        {pageData.isLandingPage ? 'Yes' : 'No'}
                      </p>
                    </div>
                  </div>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium">Template</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {pageData.template || 'Default template'}
                    </p>
                  </div>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium">Custom CSS</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {pageData.customCss ? 'Custom styles applied' : 'No custom CSS'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Custom JavaScript</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {pageData.customJs ? 'Custom scripts applied' : 'No custom JavaScript'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Quick Info & Actions */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full" asChild>
                <Link href={`/admin/system/pages/${pageId}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Page
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/preview/${pageData.slug}`} target="_blank">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview Page
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/${pageData.slug}`} target="_blank">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Live
                </Link>
              </Button>
              <Button variant="outline" className="w-full" onClick={handleDuplicate}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </Button>
            </CardContent>
          </Card>

          {/* Page Information */}
          <Card>
            <CardHeader>
              <CardTitle>Page Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Created</label>
                <p className="text-sm">
                  {new Date(pageData.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                <p className="text-sm">
                  {new Date(pageData.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
              {pageData.publishedAt && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Published</label>
                    <p className="text-sm">
                      {new Date(pageData.publishedAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </>
              )}
              {pageData.scheduledAt && (
                <>
                  <Separator />
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Scheduled</label>
                    <p className="text-sm">
                      {new Date(pageData.scheduledAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </>
              )}
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Page URL</label>
                <div className="flex items-center space-x-2 mt-1">
                  <code className="text-xs bg-muted px-2 py-1 rounded flex-1 truncate">
                    /{pageData.slug}
                  </code>
                  <Button size="sm" variant="ghost" onClick={copyPageUrl}>
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Page Views</span>
                  <span className="font-medium">{pageData.viewCount || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Shares</span>
                  <span className="font-medium">{pageData.shareCount || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Blocks</span>
                  <span className="font-medium">{pageData.blocks?.length || 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Versions</span>
                  <span className="font-medium">{pageData.versions?.length || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
