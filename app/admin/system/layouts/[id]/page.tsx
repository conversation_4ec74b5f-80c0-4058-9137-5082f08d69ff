"use client"

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { NextJSLayout } from '@/lib/layout-builder/types/nextjs-types'
import { Layout } from '@/lib/layout-builder/types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Switch } from '@/components/ui/switch'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  ArrowLeft,
  Edit,
  Eye,
  Copy,
  ExternalLink,
  FileCode,
  Layers,
  Palette,
  Share2,
  Trash2,
  MoreHorizontal,
  History,
  Globe,
  Route,
  Settings,
  Code,
  Zap,
  Shield,
  Clock,
  Database,
  Monitor,
  Smartphone,
  Tablet,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react'
import { toast } from 'sonner'
import { Skeleton } from '@/components/ui/skeleton'
import Link from 'next/link'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface LayoutDetailsData extends NextJSLayout {
  usageCount: number
  registeredRoutes: string[]
  renderingStats: {
    totalRenders: number
    averageRenderTime: number
    cacheHitRate: number
    errorRate: number
  }
  assignments: Array<{
    id: string
    targetType: 'global' | 'page' | 'post-type' | 'specific' | 'conditional'
    targetId?: string
    targetSlug?: string
    priority: number
    isActive: boolean
    createdAt: string
  }>
  versions: Array<{
    id: string
    versionNumber: number
    name: string
    description: string
    isActive: boolean
    createdAt: string
    createdBy?: string
  }>
}

export default function LayoutDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [layoutData, setLayoutData] = useState<LayoutDetailsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [deleting, setDeleting] = useState(false)
  const [saving, setSaving] = useState(false)
  const [editingSettings, setEditingSettings] = useState(false)

  const layoutId = params.id as string

  // Load layout data
  useEffect(() => {
    const fetchLayoutData = async () => {
      if (!layoutId || layoutId === 'new') {
        router.push('/admin/system/layouts/new/edit')
        return
      }

      try {
        setLoading(true)
        const response = await fetch(`/api/layouts/${layoutId}`)
        const data = await response.json()

        if (data.success && data.layout) {
          // Mock additional data for demonstration
          const layoutWithDetails: LayoutDetailsData = {
            ...data.layout,
            usageCount: Math.floor(Math.random() * 100),
            registeredRoutes: [
              data.layout.nextjs?.route || 'app/layout.tsx',
              ...(data.layout.nextjs?.type === 'root' ? ['app/page.tsx'] : [])
            ],
            renderingStats: {
              totalRenders: Math.floor(Math.random() * 10000),
              averageRenderTime: Math.floor(Math.random() * 100) + 50,
              cacheHitRate: Math.floor(Math.random() * 30) + 70,
              errorRate: Math.floor(Math.random() * 5)
            },
            assignments: [
              {
                id: 'assignment-1',
                targetType: 'global',
                priority: 1,
                isActive: true,
                createdAt: new Date().toISOString()
              }
            ],
            versions: [
              {
                id: 'version-1',
                versionNumber: 1,
                name: 'Initial Version',
                description: 'Initial layout version',
                isActive: true,
                createdAt: new Date().toISOString()
              }
            ]
          }
          setLayoutData(layoutWithDetails)
        } else {
          toast.error('Failed to load layout')
          router.push('/admin/system/layouts')
        }
      } catch (error) {
        console.error('Error loading layout:', error)
        toast.error('Failed to load layout')
        router.push('/admin/system/layouts')
      } finally {
        setLoading(false)
      }
    }

    fetchLayoutData()
  }, [layoutId, router])

  // Handle delete layout
  const handleDelete = async () => {
    if (!layoutData) return

    if (!confirm(`Are you sure you want to delete "${layoutData.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      setDeleting(true)
      const response = await fetch(`/api/layouts/${layoutId}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Layout deleted successfully')
        router.push('/admin/system/layouts')
      } else {
        toast.error(data.error || 'Failed to delete layout')
      }
    } catch (error) {
      console.error('Error deleting layout:', error)
      toast.error('Failed to delete layout')
    } finally {
      setDeleting(false)
    }
  }

  // Handle duplicate layout
  const handleDuplicate = async () => {
    if (!layoutData) return

    try {
      const response = await fetch('/api/layouts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          layout: {
            ...layoutData,
            id: `${layoutData.id}-copy`,
            name: `${layoutData.name} (Copy)`,
            nextjs: {
              ...layoutData.nextjs,
              route: layoutData.nextjs.route.replace('.tsx', '-copy.tsx')
            }
          }
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Layout duplicated successfully')
        router.push(`/admin/system/layouts/${data.layout.id}`)
      } else {
        toast.error(data.error || 'Failed to duplicate layout')
      }
    } catch (error) {
      console.error('Error duplicating layout:', error)
      toast.error('Failed to duplicate layout')
    }
  }

  // Copy layout route to clipboard
  const copyLayoutRoute = () => {
    if (!layoutData) return
    const route = layoutData.nextjs.route
    navigator.clipboard.writeText(route)
    toast.success('Layout route copied to clipboard')
  }

  // Handle save layout settings
  const handleSaveSettings = async () => {
    if (!layoutData) return

    try {
      setSaving(true)
      const response = await fetch(`/api/layouts/${layoutId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ layout: layoutData }),
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Layout settings saved successfully')
        setEditingSettings(false)
      } else {
        toast.error(data.error || 'Failed to save layout settings')
      }
    } catch (error) {
      console.error('Error saving layout settings:', error)
      toast.error('Failed to save layout settings')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-9 w-24" />
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-48" />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-32" />
            <Skeleton className="h-9 w-9" />
          </div>
        </div>
        
        {/* Content Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-48 w-full" />
          </div>
        </div>
      </div>
    )
  }

  if (!layoutData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <FileCode className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Layout not found</h2>
          <p className="text-muted-foreground mb-4">
            The layout you're looking for doesn't exist or has been deleted.
          </p>
          <Button onClick={() => router.push('/admin/system/layouts')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Layouts
          </Button>
        </div>
      </div>
    )
  }

  const getLayoutTypeColor = (type: string) => {
    switch (type) {
      case 'root':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'nested':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'template':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'group':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = (isActive: boolean) => {
    return isActive
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-gray-100 text-gray-800 border-gray-200'
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/admin/system/layouts')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Layouts
          </Button>
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Globe className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">{layoutData.name}</h1>
              <p className="text-muted-foreground">
                {layoutData.nextjs.route} • {layoutData.nextjs.type} layout
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={copyLayoutRoute}
          >
            <Copy className="h-4 w-4 mr-2" />
            Copy Route
          </Button>

          <Button
            variant="outline"
            size="sm"
            asChild
          >
            <Link href={`/admin/system/layouts/${layoutId}/preview`} target="_blank">
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Link>
          </Button>

          <Button
            size="sm"
            asChild
          >
            <Link href={`/admin/system/layouts/${layoutId}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Open Editor
            </Link>
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleDuplicate}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Layout
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/admin/system/layouts/${layoutId}/edit`}>
                  <Palette className="h-4 w-4 mr-2" />
                  Visual Editor
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setEditingSettings(!editingSettings)}>
                <Settings className="h-4 w-4 mr-2" />
                {editingSettings ? 'View Mode' : 'Edit Settings'}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                disabled={deleting}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {deleting ? 'Deleting...' : 'Delete Layout'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Layout Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Layout Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Layout Overview
                <div className="flex items-center space-x-2">
                  <Badge className={getStatusColor(layoutData.isActive)}>
                    {layoutData.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                  <Badge className={getLayoutTypeColor(layoutData.nextjs.type)}>
                    {layoutData.nextjs.type}
                  </Badge>
                </div>
              </CardTitle>
              <CardDescription>
                {layoutData.description || 'No description provided'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Route className="h-6 w-6 mx-auto mb-2 text-blue-600" />
                  <div className="text-2xl font-bold">{layoutData.registeredRoutes.length}</div>
                  <div className="text-sm text-muted-foreground">Routes</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Globe className="h-6 w-6 mx-auto mb-2 text-green-600" />
                  <div className="text-2xl font-bold">{layoutData.usageCount}</div>
                  <div className="text-sm text-muted-foreground">Usage</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Zap className="h-6 w-6 mx-auto mb-2 text-purple-600" />
                  <div className="text-2xl font-bold">{layoutData.renderingStats.totalRenders}</div>
                  <div className="text-sm text-muted-foreground">Renders</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <History className="h-6 w-6 mx-auto mb-2 text-orange-600" />
                  <div className="text-2xl font-bold">{layoutData.versions?.length || 0}</div>
                  <div className="text-sm text-muted-foreground">Versions</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabs for detailed information */}
          <Tabs defaultValue="routing" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="routing">Routing</TabsTrigger>
              <TabsTrigger value="rendering">Rendering</TabsTrigger>
              <TabsTrigger value="assignments">Assignments</TabsTrigger>
              <TabsTrigger value="versions">Versions</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="routing" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Routing Configuration
                    {editingSettings && (
                      <Button size="sm" onClick={handleSaveSettings} disabled={saving}>
                        {saving ? <RefreshCw className="h-4 w-4 animate-spin mr-2" /> : <Save className="h-4 w-4 mr-2" />}
                        {saving ? 'Saving...' : 'Save'}
                      </Button>
                    )}
                  </CardTitle>
                  <CardDescription>
                    NextJS App Router routing configuration and registered routes
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="route">Route Path</Label>
                      {editingSettings ? (
                        <Input
                          id="route"
                          value={layoutData.nextjs.route}
                          onChange={(e) => setLayoutData({
                            ...layoutData,
                            nextjs: { ...layoutData.nextjs, route: e.target.value }
                          })}
                          placeholder="app/layout.tsx"
                        />
                      ) : (
                        <p className="text-sm font-mono bg-muted p-2 rounded">{layoutData.nextjs.route}</p>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="type">Layout Type</Label>
                      {editingSettings ? (
                        <Select
                          value={layoutData.nextjs.type}
                          onValueChange={(value) => setLayoutData({
                            ...layoutData,
                            nextjs: { ...layoutData.nextjs, type: value as any }
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="root">Root Layout</SelectItem>
                            <SelectItem value="nested">Nested Layout</SelectItem>
                            <SelectItem value="template">Template</SelectItem>
                            <SelectItem value="group">Route Group</SelectItem>
                          </SelectContent>
                        </Select>
                      ) : (
                        <p className="text-sm bg-muted p-2 rounded capitalize">{layoutData.nextjs.type}</p>
                      )}
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <Label>Registered Routes</Label>
                    <div className="mt-2 space-y-2">
                      {layoutData.registeredRoutes.map((route, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                          <code className="text-sm">{route}</code>
                          <Badge variant="outline">Active</Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <Label>App Router Features</Label>
                    <div className="mt-2 grid grid-cols-2 gap-2">
                      {['metadata', 'loading', 'error', 'not-found', 'template', 'parallel-routes'].map((feature) => (
                        <div key={feature} className="flex items-center justify-between p-2 border rounded">
                          <span className="text-sm capitalize">{feature.replace('-', ' ')}</span>
                          <Switch
                            checked={layoutData.nextjs.appRouterFeatures.includes(feature as any)}
                            onCheckedChange={(checked) => {
                              const features = checked
                                ? [...layoutData.nextjs.appRouterFeatures, feature as any]
                                : layoutData.nextjs.appRouterFeatures.filter(f => f !== feature)
                              setLayoutData({
                                ...layoutData,
                                nextjs: { ...layoutData.nextjs, appRouterFeatures: features }
                              })
                            }}
                            disabled={!editingSettings}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="rendering" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Rendering Performance</CardTitle>
                  <CardDescription>
                    Universal Renderer performance metrics and caching statistics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <Zap className="h-6 w-6 mx-auto mb-2 text-blue-600" />
                      <div className="text-2xl font-bold">{layoutData.renderingStats.totalRenders}</div>
                      <div className="text-sm text-muted-foreground">Total Renders</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <Clock className="h-6 w-6 mx-auto mb-2 text-green-600" />
                      <div className="text-2xl font-bold">{layoutData.renderingStats.averageRenderTime}ms</div>
                      <div className="text-sm text-muted-foreground">Avg Render Time</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <Database className="h-6 w-6 mx-auto mb-2 text-purple-600" />
                      <div className="text-2xl font-bold">{layoutData.renderingStats.cacheHitRate}%</div>
                      <div className="text-sm text-muted-foreground">Cache Hit Rate</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 rounded-lg">
                      <AlertCircle className="h-6 w-6 mx-auto mb-2 text-red-600" />
                      <div className="text-2xl font-bold">{layoutData.renderingStats.errorRate}%</div>
                      <div className="text-sm text-muted-foreground">Error Rate</div>
                    </div>
                  </div>

                  <Separator className="my-6" />

                  <div className="space-y-4">
                    <h4 className="font-medium">Rendering Configuration</h4>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertTitle>Universal Renderer Integration</AlertTitle>
                      <AlertDescription>
                        This layout is integrated with the Universal Rendering System for dynamic content rendering.
                        Performance metrics are updated in real-time.
                      </AlertDescription>
                    </Alert>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Responsive Breakpoints</Label>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between p-2 bg-muted rounded">
                            <div className="flex items-center gap-2">
                              <Smartphone className="h-4 w-4" />
                              <span className="text-sm">Mobile</span>
                            </div>
                            <Badge variant="outline">≤ 768px</Badge>
                          </div>
                          <div className="flex items-center justify-between p-2 bg-muted rounded">
                            <div className="flex items-center gap-2">
                              <Tablet className="h-4 w-4" />
                              <span className="text-sm">Tablet</span>
                            </div>
                            <Badge variant="outline">769px - 1024px</Badge>
                          </div>
                          <div className="flex items-center justify-between p-2 bg-muted rounded">
                            <div className="flex items-center gap-2">
                              <Monitor className="h-4 w-4" />
                              <span className="text-sm">Desktop</span>
                            </div>
                            <Badge variant="outline">≥ 1025px</Badge>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Caching Strategy</Label>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between p-2 bg-muted rounded">
                            <span className="text-sm">Layout Cache</span>
                            <Badge variant="default">Enabled</Badge>
                          </div>
                          <div className="flex items-center justify-between p-2 bg-muted rounded">
                            <span className="text-sm">Component Cache</span>
                            <Badge variant="default">Enabled</Badge>
                          </div>
                          <div className="flex items-center justify-between p-2 bg-muted rounded">
                            <span className="text-sm">Route Cache</span>
                            <Badge variant="default">Enabled</Badge>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="assignments" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Layout Assignments</CardTitle>
                  <CardDescription>
                    Configure where and when this layout should be used
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {layoutData.assignments.map((assignment) => (
                      <div key={assignment.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="capitalize">
                              {assignment.targetType.replace('-', ' ')}
                            </Badge>
                            <span className="text-sm font-medium">
                              Priority: {assignment.priority}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {assignment.targetType === 'global' && 'Applied to all pages by default'}
                            {assignment.targetType === 'page' && `Applied to page: ${assignment.targetSlug}`}
                            {assignment.targetType === 'post-type' && `Applied to post type: ${assignment.targetId}`}
                            {assignment.targetType === 'specific' && `Applied to specific content: ${assignment.targetId}`}
                            {assignment.targetType === 'conditional' && 'Applied based on conditions'}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Created: {new Date(assignment.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={assignment.isActive}
                            onCheckedChange={(checked) => {
                              // Handle assignment toggle
                              console.log('Toggle assignment:', assignment.id, checked)
                            }}
                            disabled={!editingSettings}
                          />
                          <Badge variant={assignment.isActive ? 'default' : 'secondary'}>
                            {assignment.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                      </div>
                    ))}

                    {editingSettings && (
                      <Button variant="outline" className="w-full">
                        <Plus className="h-4 w-4 mr-2" />
                        Add New Assignment
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="versions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Page Versions</CardTitle>
                  <CardDescription>
                    Version history and published versions of this page
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {layoutData.versions && layoutData.versions.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Version</TableHead>
                          <TableHead>Name</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {layoutData.versions.map((version) => (
                          <TableRow key={version.id}>
                            <TableCell className="font-medium">
                              v{version.versionNumber}
                            </TableCell>
                            <TableCell>{version.name}</TableCell>
                            <TableCell>
                              <Badge variant={version.isActive ? 'default' : 'secondary'}>
                                {version.isActive ? 'Active' : 'Inactive'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {new Date(version.createdAt).toLocaleDateString()}
                            </TableCell>
                            <TableCell>
                              <Button variant="ghost" size="sm">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No version history available</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Page Settings</CardTitle>
                  <CardDescription>
                    Advanced settings and configurations for this page
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Layout Name</Label>
                      <p className="text-sm text-muted-foreground">{layoutData.name}</p>
                    </div>
                    <div>
                      <Label>Category</Label>
                      <p className="text-sm text-muted-foreground capitalize">{layoutData.category}</p>
                    </div>
                    <div>
                      <Label>Type</Label>
                      <p className="text-sm text-muted-foreground capitalize">{layoutData.nextjs.type}</p>
                    </div>
                    <div>
                      <Label>Status</Label>
                      <p className="text-sm text-muted-foreground">
                        {layoutData.isActive ? 'Active' : 'Inactive'}
                      </p>
                    </div>
                  </div>
                  <Separator />
                  <div>
                    <Label>Route Path</Label>
                    <p className="text-sm text-muted-foreground font-mono mt-1">
                      {layoutData.nextjs.route}
                    </p>
                  </div>
                  <Separator />
                  <div>
                    <Label>App Router Features</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {layoutData.nextjs.appRouterFeatures.map((feature) => (
                        <Badge key={feature} variant="outline" className="text-xs">
                          {feature.replace('-', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <Label>Description</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {layoutData.description || 'No description'}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Quick Info & Actions */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full" asChild>
                <Link href={`/admin/system/layouts/${layoutId}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Layout
                </Link>
              </Button>
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/admin/system/layouts/${layoutId}/preview`} target="_blank">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview Layout
                </Link>
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setEditingSettings(!editingSettings)}
              >
                <Settings className="h-4 w-4 mr-2" />
                {editingSettings ? 'View Mode' : 'Edit Settings'}
              </Button>
              <Button variant="outline" className="w-full" onClick={handleDuplicate}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate Layout
              </Button>
            </CardContent>
          </Card>

          {/* Layout Information */}
          <Card>
            <CardHeader>
              <CardTitle>Layout Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Created</label>
                <p className="text-sm">
                  {new Date(layoutData.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                <p className="text-sm">
                  {new Date(layoutData.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Layout Route</label>
                <div className="flex items-center space-x-2 mt-1">
                  <code className="text-xs bg-muted px-2 py-1 rounded flex-1 truncate">
                    {layoutData.nextjs.route}
                  </code>
                  <Button size="sm" variant="ghost" onClick={copyLayoutRoute}>
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
              <Separator />
              <div>
                <label className="text-sm font-medium text-muted-foreground">Universal Renderer</label>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-sm">Registered</span>
                  <Badge variant="default" className="gap-1">
                    <CheckCircle className="w-3 h-3" />
                    Active
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Total Renders</span>
                  <span className="font-medium">{layoutData.renderingStats.totalRenders}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Avg Render Time</span>
                  <span className="font-medium">{layoutData.renderingStats.averageRenderTime}ms</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Cache Hit Rate</span>
                  <span className="font-medium">{layoutData.renderingStats.cacheHitRate}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Usage Count</span>
                  <span className="font-medium">{layoutData.usageCount}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Versions</span>
                  <span className="font-medium">{layoutData.versions?.length || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
