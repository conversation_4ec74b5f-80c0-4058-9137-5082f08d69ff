'use client'

import React, { useState, useEffect } from 'react'
import { NextJSLayoutEditor } from '@/lib/layout-builder/components/nextjs-layout-editor'
import { NextJSLayout } from '@/lib/layout-builder/types/nextjs-types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  ArrowLeft,
  Globe,
  FileCode,
  Layout,
  Sparkles,
  Save,
  Eye,
  Download,
  Settings
} from 'lucide-react'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

export default function NextJSLayoutEditorPage() {
  const router = useRouter()
  const [currentLayout, setCurrentLayout] = useState<NextJSLayout | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [registeredLayouts, setRegisteredLayouts] = useState<any[]>([])

  // Initialize with a default NextJS layout
  useEffect(() => {
    const defaultLayout: NextJSLayout = {
      id: 'nextjs-layout-' + Date.now(),
      name: 'New NextJS Layout',
      description: 'A new NextJS layout created with the visual editor',
      type: 'page',
      category: 'custom',
      structure: {
        main: {
          id: 'main-section',
          type: 'main',
          name: 'Main Content',
          position: 1,
          blocks: [],
          configuration: {
            layout: 'block',
            alignment: 'left',
            spacing: { top: 0, right: 0, bottom: 0, left: 0 },
            background: { type: 'color', color: 'transparent' },
            container: {
              maxWidth: 1200,
              padding: { top: 16, right: 16, bottom: 16, left: 16 },
              margin: { top: 0, right: 0, bottom: 0, left: 0 },
              centered: true
            }
          },
          styling: {
            background: { type: 'color', color: 'transparent' },
            border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
            spacing: { top: 0, right: 0, bottom: 0, left: 0 },
            shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
          },
          responsive: {
            mobile: { isVisible: true },
            tablet: { isVisible: true },
            desktop: { isVisible: true },
            large: { isVisible: true }
          },
          isVisible: true
        }
      },
      styling: {
        background: { type: 'none' },
        border: { width: '0', style: 'none', color: 'transparent' },
        spacing: { top: '0', right: '0', bottom: '0', left: '0' },
        shadow: { type: 'none' }
      },
      responsive: {
        mobile: { isVisible: true },
        tablet: { isVisible: true },
        desktop: { isVisible: true },
        large: { isVisible: true }
      },
      conditions: {},
      isTemplate: false,
      isSystem: false,
      isActive: true,
      usageCount: 0,
      tags: ['nextjs', 'layout', 'universal-renderer'],
      createdAt: new Date(),
      updatedAt: new Date(),
      nextjs: {
        type: 'root',
        route: 'app/layout.tsx',
        metadata: {
          title: { default: 'My App', template: '%s | My App' },
          description: 'A modern web application built with NextJS and Universal Renderer'
        },
        imports: ['import React from "react"'],
        exports: [
          {
            name: 'default',
            type: 'default',
            isAsync: false,
            returnType: 'JSX.Element'
          }
        ],
        appRouterFeatures: ['metadata', 'loading']
      }
    }

    setCurrentLayout(defaultLayout)
    loadRegisteredLayouts()
  }, [])

  // Load registered layouts
  const loadRegisteredLayouts = async () => {
    try {
      const response = await fetch('/api/universal-renderer/register-layout')
      if (response.ok) {
        const layouts = await response.json()
        setRegisteredLayouts(layouts)
      }
    } catch (error) {
      console.error('Failed to load registered layouts:', error)
    }
  }

  // Handle save layout
  const handleSaveLayout = async (layout: NextJSLayout) => {
    setIsLoading(true)
    try {
      // Register with Universal Renderer
      const response = await fetch('/api/universal-renderer/register-layout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ layout })
      })

      if (!response.ok) {
        throw new Error('Failed to register layout')
      }

      const result = await response.json()
      toast.success(`Layout saved and registered with Universal Renderer`)
      
      // Reload registered layouts
      await loadRegisteredLayouts()
      
      // Update current layout
      setCurrentLayout(layout)
    } catch (error) {
      toast.error('Failed to save layout')
      console.error('Save error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle preview
  const handlePreview = () => {
    if (currentLayout) {
      // Open preview in new tab
      const previewUrl = `/preview/layout/${currentLayout.id}`
      window.open(previewUrl, '_blank')
    }
  }

  // Handle back navigation
  const handleBack = () => {
    router.push('/admin/system/layouts')
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Layouts
            </Button>
            
            <Separator orientation="vertical" className="h-6" />
            
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Globe className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  NextJS Layout Editor
                </h1>
                <p className="text-sm text-muted-foreground">
                  Create NextJS layouts with Universal Renderer integration
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Badge variant="secondary" className="gap-1">
              <Sparkles className="w-3 h-3" />
              AI-Powered
            </Badge>
            <Badge variant="outline" className="gap-1">
              <Globe className="w-3 h-3" />
              Universal Renderer
            </Badge>
          </div>
        </div>
      </div>

      {/* Info Panel */}
      <div className="bg-blue-50 border-b border-blue-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-blue-800">
            <Globe className="w-4 h-4" />
            <span>
              This layout will be automatically integrated with the Universal Rendering System for dynamic content rendering.
            </span>
          </div>
          
          <div className="flex items-center gap-4 text-sm text-blue-700">
            <div className="flex items-center gap-1">
              <Layout className="w-4 h-4" />
              <span>{registeredLayouts.length} Registered Layouts</span>
            </div>
            <div className="flex items-center gap-1">
              <FileCode className="w-4 h-4" />
              <span>NextJS App Router</span>
            </div>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 overflow-hidden">
        {currentLayout ? (
          <NextJSLayoutEditor
            initialLayout={currentLayout}
            onSave={handleSaveLayout}
            onPreview={handlePreview}
            onBack={handleBack}
          />
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <Layout className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Loading NextJS Layout Editor
              </h3>
              <p className="text-sm text-muted-foreground">
                Initializing the visual layout editor...
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="bg-white border-t border-gray-200 px-6 py-2">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>NextJS Layout Editor v1.0</span>
            <span>•</span>
            <span>Universal Renderer Compatible</span>
            <span>•</span>
            <span>App Router Ready</span>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>System Ready</span>
          </div>
        </div>
      </div>
    </div>
  )
}
