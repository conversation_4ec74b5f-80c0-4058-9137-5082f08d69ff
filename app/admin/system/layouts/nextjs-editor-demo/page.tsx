'use client'

import React from 'react'
import { NextJSLayoutEditor } from '@/lib/layout-builder/components/nextjs-layout-editor'
import { NextJSLayout } from '@/lib/layout-builder/types/nextjs-types'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Globe, Sparkles } from 'lucide-react'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'

export default function NextJSLayoutEditorDemoPage() {
  const router = useRouter()

  // Demo NextJS layout with resizable panels
  const demoLayout: NextJSLayout = {
    id: 'demo-nextjs-layout',
    name: 'Demo NextJS Layout',
    description: 'A demonstration layout showcasing ResizablePanels integration',
    type: 'page',
    category: 'demo',
    structure: {
      header: {
        id: 'header-section',
        type: 'header',
        name: 'Header',
        position: 1,
        blocks: [
          {
            id: 'logo-block',
            type: 'logo',
            name: '<PERSON><PERSON>',
            position: 1,
            content: {
              text: 'Demo App',
              style: { fontSize: '24px', fontWeight: 'bold' }
            },
            styling: {
              background: { type: 'none' },
              border: { width: '0', style: 'none', color: 'transparent' },
              spacing: { top: '0', right: '0', bottom: '0', left: '0' },
              shadow: { type: 'none' }
            },
            responsive: {
              mobile: { isVisible: true },
              tablet: { isVisible: true },
              desktop: { isVisible: true },
              large: { isVisible: true }
            },
            isVisible: true
          }
        ],
        configuration: {
          layout: 'flex',
          alignment: 'space-between',
          spacing: { top: '1rem', right: '2rem', bottom: '1rem', left: '2rem' },
          background: { type: 'solid', color: '#ffffff' },
          container: { maxWidth: '1200px', padding: '0' }
        },
        styling: {
          background: { type: 'solid', color: '#ffffff' },
          border: { width: '1px', style: 'solid', color: '#e5e7eb' },
          spacing: { top: '0', right: '0', bottom: '0', left: '0' },
          shadow: { type: 'sm' }
        },
        responsive: {
          mobile: { isVisible: true },
          tablet: { isVisible: true },
          desktop: { isVisible: true },
          large: { isVisible: true }
        },
        isVisible: true
      },
      main: {
        id: 'main-section',
        type: 'main',
        name: 'Main Content',
        position: 2,
        blocks: [],
        configuration: {
          layout: 'block',
          alignment: 'left',
          spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' },
          background: { type: 'none' },
          container: { maxWidth: '1200px', padding: '1rem' }
        },
        styling: {
          background: { type: 'none' },
          border: { width: '0', style: 'none', color: 'transparent' },
          spacing: { top: '0', right: '0', bottom: '0', left: '0' },
          shadow: { type: 'none' }
        },
        responsive: {
          mobile: { isVisible: true },
          tablet: { isVisible: true },
          desktop: { isVisible: true },
          large: { isVisible: true }
        },
        isVisible: true
      },
      footer: {
        id: 'footer-section',
        type: 'footer',
        name: 'Footer',
        position: 3,
        blocks: [],
        configuration: {
          layout: 'flex',
          alignment: 'center',
          spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' },
          background: { type: 'solid', color: '#f9fafb' },
          container: { maxWidth: '1200px', padding: '1rem' }
        },
        styling: {
          background: { type: 'solid', color: '#f9fafb' },
          border: { width: '1px', style: 'solid', color: '#e5e7eb' },
          spacing: { top: '0', right: '0', bottom: '0', left: '0' },
          shadow: { type: 'none' }
        },
        responsive: {
          mobile: { isVisible: true },
          tablet: { isVisible: true },
          desktop: { isVisible: true },
          large: { isVisible: true }
        },
        isVisible: true
      }
    },
    styling: {
      background: { type: 'solid', color: '#ffffff' },
      border: { width: '0', style: 'none', color: 'transparent' },
      spacing: { top: '0', right: '0', bottom: '0', left: '0' },
      shadow: { type: 'none' }
    },
    responsive: {
      mobile: { isVisible: true },
      tablet: { isVisible: true },
      desktop: { isVisible: true },
      large: { isVisible: true }
    },
    conditions: {},
    isTemplate: false,
    isSystem: false,
    isActive: true,
    usageCount: 0,
    tags: ['demo', 'nextjs', 'resizable-panels'],
    createdAt: new Date(),
    updatedAt: new Date(),
    nextjs: {
      type: 'root',
      route: 'app/layout.tsx',
      metadata: {
        title: { default: 'Demo App', template: '%s | Demo App' },
        description: 'A demonstration of NextJS Layout Editor with ResizablePanels'
      },
      imports: [
        'import React from "react"',
        'import type { Metadata } from "next"'
      ],
      exports: [
        {
          name: 'default',
          type: 'default',
          isAsync: false,
          returnType: 'JSX.Element'
        }
      ],
      appRouterFeatures: ['metadata', 'loading', 'error']
    }
  }

  const handleSave = async (layout: NextJSLayout) => {
    try {
      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Demo layout saved successfully!')
    } catch (error) {
      toast.error('Failed to save demo layout')
    }
  }

  const handlePreview = () => {
    toast.info('Preview functionality would open in a new tab')
  }

  const handleBack = () => {
    router.push('/admin/system/layouts')
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Demo Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Layouts
            </Button>
            
            <div className="h-6 w-px bg-gray-300" />
            
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Globe className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  NextJS Layout Editor Demo
                </h1>
                <p className="text-sm text-muted-foreground">
                  Featuring ResizablePanels for optimal workspace management
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Badge variant="secondary" className="gap-1">
              <Sparkles className="w-3 h-3" />
              ResizablePanels
            </Badge>
            <Badge variant="outline" className="gap-1">
              <Globe className="w-3 h-3" />
              Universal Renderer
            </Badge>
          </div>
        </div>
      </div>

      {/* Demo Info Panel */}
      <div className="bg-blue-50 border-b border-blue-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-blue-800">
            <Globe className="w-4 h-4" />
            <span>
              <strong>Demo Features:</strong> ResizablePanels for flexible workspace • 
              Drag panel handles to resize • Minimum and maximum panel sizes • 
              Persistent layout preferences
            </span>
          </div>
          
          <div className="text-sm text-blue-700">
            <span className="font-medium">Try resizing the panels!</span>
          </div>
        </div>
      </div>

      {/* NextJS Layout Editor with ResizablePanels */}
      <div className="flex-1 overflow-hidden">
        <NextJSLayoutEditor
          initialLayout={demoLayout}
          onSave={handleSave}
          onPreview={handlePreview}
          onBack={handleBack}
        />
      </div>

      {/* Demo Status Bar */}
      <div className="bg-white border-t border-gray-200 px-6 py-2">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center gap-4">
            <span>NextJS Layout Editor Demo v1.0</span>
            <span>•</span>
            <span>ResizablePanels Integration</span>
            <span>•</span>
            <span>Universal Renderer Compatible</span>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Demo Mode Active</span>
          </div>
        </div>
      </div>
    </div>
  )
}
