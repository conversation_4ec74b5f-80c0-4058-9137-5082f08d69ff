'use client'

import React from 'react'
import { <PERSON><PERSON>ageE<PERSON>or, StylesManager, DataManager } from '@/lib/craft-page-editor'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ArrowLeft, ExternalLink, Github, Palette, Database, Sparkles } from 'lucide-react'
import Link from 'next/link'

export default function CraftPageEditorDemo() {
  const [activeDemo, setActiveDemo] = React.useState('editor')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  AI-Powered Craft.js Page Editor
                </h1>
                <p className="text-sm text-gray-600">
                  Production-ready page builder with AI, dynamic data, and styles management
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Badge variant="outline" className="text-xs">
                v2.0.0 - Production Ready
              </Badge>
              <Button variant="outline" size="sm" asChild>
                <a
                  href="https://craft.js.org"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Craft.js Docs
                </a>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Demo Info */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <span>🎨 Craft.js Page Editor</span>
            </CardTitle>
            <CardDescription>
              This demo showcases a complete page editor built with Craft.js, integrating shadcn/ui components 
              and the existing custom fields system. The editor follows Craft.js documentation patterns exactly.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">✨ Features</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Drag & drop components</li>
                  <li>• Real-time editing</li>
                  <li>• Properties panel</li>
                  <li>• Undo/Redo support</li>
                  <li>• Save/Load functionality</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium text-sm">🧩 Components</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Container (Canvas)</li>
                  <li>• Text (Editable)</li>
                  <li>• Button</li>
                  <li>• Card with sections</li>
                  <li>• Heading</li>
                  <li>• Image</li>
                </ul>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium text-sm">⚡ Integration</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Shadcn/ui components</li>
                  <li>• Custom fields system</li>
                  <li>• HOC patterns</li>
                  <li>• TypeScript support</li>
                  <li>• Responsive design</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Editor with AI and Data Management */}
      <div className="h-[calc(100vh-200px)]">
        <CraftPageEditor
          config={{
            enabled: true,
            indicator: true
          }}
          eventHandlers={{
            onSelect: (nodeId) => {
              console.log('Selected node:', nodeId)
            },
            onUpdate: (nodeId, props) => {
              console.log('Updated node:', nodeId, props)
            },
            onCreate: (nodeId) => {
              console.log('Created node:', nodeId)
            },
            onDelete: (nodeId) => {
              console.log('Deleted node:', nodeId)
            }
          }}
          showToolbox={true}
          showSettings={true}
          showTopbar={true}
          showAI={true}
          showTemplates={false}
          toolboxPosition="left"
          settingsPosition="right"
          aiPosition="top"
          mode="builder"
        />
      </div>
    </div>
  )
}
