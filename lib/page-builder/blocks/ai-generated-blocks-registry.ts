import { BlockTypeDefinition } from '../types'
import { blockRegistry } from './registry'
import { GeneratedComponent } from '@/lib/ai-visual-editor/types'

/**
 * Registry for AI-generated page builder blocks
 * Manages dynamic registration and lifecycle of AI-generated blocks
 */
class AIGeneratedBlocksRegistry {
  private aiBlocks: Map<string, AIGeneratedBlock> = new Map()
  private registeredBlockIds: Set<string> = new Set()

  /**
   * Register an AI-generated component as a page builder block
   */
  async registerAIBlock(component: GeneratedComponent, blockDefinition: BlockTypeDefinition): Promise<boolean> {
    try {
      const blockId = this.generateBlockId(component.name)
      
      // Create AI block wrapper
      const aiBlock: AIGeneratedBlock = {
        id: blockId,
        component,
        blockDefinition: {
          ...blockDefinition,
          id: blockId,
          name: blockId
        },
        registeredAt: new Date(),
        isActive: true,
        version: '1.0.0'
      }

      // Store in AI blocks registry
      this.aiBlocks.set(blockId, aiBlock)

      // Register with main block registry
      blockRegistry.register(aiBlock.blockDefinition)
      this.registeredBlockIds.add(blockId)

      console.log(`AI block registered: ${blockId}`)
      return true
    } catch (error) {
      console.error('Failed to register AI block:', error)
      return false
    }
  }

  /**
   * Unregister an AI-generated block
   */
  unregisterAIBlock(blockId: string): boolean {
    try {
      const aiBlock = this.aiBlocks.get(blockId)
      if (!aiBlock) {
        console.warn(`AI block not found: ${blockId}`)
        return false
      }

      // Unregister from main block registry
      blockRegistry.unregister(blockId)
      this.registeredBlockIds.delete(blockId)

      // Remove from AI blocks registry
      this.aiBlocks.delete(blockId)

      console.log(`AI block unregistered: ${blockId}`)
      return true
    } catch (error) {
      console.error('Failed to unregister AI block:', error)
      return false
    }
  }

  /**
   * Update an existing AI-generated block
   */
  async updateAIBlock(blockId: string, updates: Partial<GeneratedComponent>): Promise<boolean> {
    try {
      const aiBlock = this.aiBlocks.get(blockId)
      if (!aiBlock) {
        console.warn(`AI block not found for update: ${blockId}`)
        return false
      }

      // Update component
      aiBlock.component = {
        ...aiBlock.component,
        ...updates,
        updatedAt: new Date()
      }

      // Update block definition if needed
      if (updates.name || updates.description || updates.category) {
        const updatedDefinition: BlockTypeDefinition = {
          ...aiBlock.blockDefinition,
          displayName: updates.name || aiBlock.blockDefinition.displayName,
          description: updates.description || aiBlock.blockDefinition.description,
          category: updates.category || aiBlock.blockDefinition.category
        }

        aiBlock.blockDefinition = updatedDefinition

        // Re-register with updated definition
        blockRegistry.unregister(blockId)
        blockRegistry.register(updatedDefinition)
      }

      console.log(`AI block updated: ${blockId}`)
      return true
    } catch (error) {
      console.error('Failed to update AI block:', error)
      return false
    }
  }

  /**
   * Get an AI-generated block by ID
   */
  getAIBlock(blockId: string): AIGeneratedBlock | undefined {
    return this.aiBlocks.get(blockId)
  }

  /**
   * Get all AI-generated blocks
   */
  getAllAIBlocks(): AIGeneratedBlock[] {
    return Array.from(this.aiBlocks.values())
  }

  /**
   * Get AI blocks by category
   */
  getAIBlocksByCategory(category: string): AIGeneratedBlock[] {
    return this.getAllAIBlocks().filter(block => 
      block.blockDefinition.category === category
    )
  }

  /**
   * Check if a block is AI-generated
   */
  isAIGeneratedBlock(blockId: string): boolean {
    return this.registeredBlockIds.has(blockId)
  }

  /**
   * Get statistics about AI-generated blocks
   */
  getStatistics(): AIBlockStatistics {
    const blocks = this.getAllAIBlocks()
    const activeBlocks = blocks.filter(block => block.isActive)
    
    const categoryDistribution = blocks.reduce((acc, block) => {
      const category = block.blockDefinition.category
      acc[category] = (acc[category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const complexityDistribution = blocks.reduce((acc, block) => {
      const complexity = block.component.metadata?.complexity || 'unknown'
      acc[complexity] = (acc[complexity] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      totalBlocks: blocks.length,
      activeBlocks: activeBlocks.length,
      categoryDistribution,
      complexityDistribution,
      averageGenerationTime: this.calculateAverageGenerationTime(blocks),
      oldestBlock: this.getOldestBlock(blocks),
      newestBlock: this.getNewestBlock(blocks)
    }
  }

  /**
   * Clean up inactive or old AI blocks
   */
  cleanup(options: CleanupOptions = {}): CleanupResult {
    const {
      maxAge = 30 * 24 * 60 * 60 * 1000, // 30 days
      removeInactive = true,
      dryRun = false
    } = options

    const now = Date.now()
    const blocksToRemove: string[] = []
    const blocks = this.getAllAIBlocks()

    blocks.forEach(block => {
      const age = now - block.registeredAt.getTime()
      const shouldRemove = (
        (removeInactive && !block.isActive) ||
        (age > maxAge)
      )

      if (shouldRemove) {
        blocksToRemove.push(block.id)
      }
    })

    if (!dryRun) {
      blocksToRemove.forEach(blockId => {
        this.unregisterAIBlock(blockId)
      })
    }

    return {
      removedCount: blocksToRemove.length,
      removedBlocks: blocksToRemove,
      dryRun
    }
  }

  /**
   * Export AI blocks for backup or migration
   */
  exportAIBlocks(): AIBlockExport {
    const blocks = this.getAllAIBlocks()
    
    return {
      version: '1.0.0',
      exportedAt: new Date(),
      totalBlocks: blocks.length,
      blocks: blocks.map(block => ({
        id: block.id,
        component: block.component,
        blockDefinition: block.blockDefinition,
        registeredAt: block.registeredAt,
        isActive: block.isActive,
        version: block.version
      }))
    }
  }

  /**
   * Import AI blocks from export data
   */
  async importAIBlocks(exportData: AIBlockExport): Promise<ImportResult> {
    const results: ImportResult = {
      totalBlocks: exportData.blocks.length,
      successfulImports: 0,
      failedImports: 0,
      errors: []
    }

    for (const blockData of exportData.blocks) {
      try {
        const success = await this.registerAIBlock(
          blockData.component,
          blockData.blockDefinition
        )

        if (success) {
          results.successfulImports++
        } else {
          results.failedImports++
          results.errors.push(`Failed to import block: ${blockData.id}`)
        }
      } catch (error) {
        results.failedImports++
        results.errors.push(`Error importing block ${blockData.id}: ${error}`)
      }
    }

    return results
  }

  // Private helper methods
  private generateBlockId(componentName: string): string {
    const baseId = componentName
      .toLowerCase()
      .replace(/([A-Z])/g, '-$1')
      .replace(/^-/, '')
      .replace(/[^a-z0-9-]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')

    // Ensure uniqueness
    let counter = 1
    let blockId = `ai-${baseId}`
    
    while (this.aiBlocks.has(blockId) || blockRegistry.hasBlockType(blockId)) {
      blockId = `ai-${baseId}-${counter}`
      counter++
    }

    return blockId
  }

  private calculateAverageGenerationTime(blocks: AIGeneratedBlock[]): number {
    const times = blocks
      .map(block => block.component.metadata?.generationTime)
      .filter((time): time is number => typeof time === 'number')

    if (times.length === 0) return 0
    
    return times.reduce((sum, time) => sum + time, 0) / times.length
  }

  private getOldestBlock(blocks: AIGeneratedBlock[]): AIGeneratedBlock | null {
    if (blocks.length === 0) return null
    
    return blocks.reduce((oldest, block) => 
      block.registeredAt < oldest.registeredAt ? block : oldest
    )
  }

  private getNewestBlock(blocks: AIGeneratedBlock[]): AIGeneratedBlock | null {
    if (blocks.length === 0) return null
    
    return blocks.reduce((newest, block) => 
      block.registeredAt > newest.registeredAt ? block : newest
    )
  }
}

// Types
export interface AIGeneratedBlock {
  id: string
  component: GeneratedComponent
  blockDefinition: BlockTypeDefinition
  registeredAt: Date
  isActive: boolean
  version: string
}

export interface AIBlockStatistics {
  totalBlocks: number
  activeBlocks: number
  categoryDistribution: Record<string, number>
  complexityDistribution: Record<string, number>
  averageGenerationTime: number
  oldestBlock: AIGeneratedBlock | null
  newestBlock: AIGeneratedBlock | null
}

export interface CleanupOptions {
  maxAge?: number // milliseconds
  removeInactive?: boolean
  dryRun?: boolean
}

export interface CleanupResult {
  removedCount: number
  removedBlocks: string[]
  dryRun: boolean
}

export interface AIBlockExport {
  version: string
  exportedAt: Date
  totalBlocks: number
  blocks: AIGeneratedBlock[]
}

export interface ImportResult {
  totalBlocks: number
  successfulImports: number
  failedImports: number
  errors: string[]
}

// Create singleton instance
export const aiGeneratedBlocksRegistry = new AIGeneratedBlocksRegistry()

// Export utility functions
export function registerAIGeneratedBlock(
  component: GeneratedComponent,
  blockDefinition: BlockTypeDefinition
): Promise<boolean> {
  return aiGeneratedBlocksRegistry.registerAIBlock(component, blockDefinition)
}

export function unregisterAIGeneratedBlock(blockId: string): boolean {
  return aiGeneratedBlocksRegistry.unregisterAIBlock(blockId)
}

export function getAIGeneratedBlock(blockId: string): AIGeneratedBlock | undefined {
  return aiGeneratedBlocksRegistry.getAIBlock(blockId)
}

export function isAIGeneratedBlock(blockId: string): boolean {
  return aiGeneratedBlocksRegistry.isAIGeneratedBlock(blockId)
}

export function getAIBlockStatistics(): AIBlockStatistics {
  return aiGeneratedBlocksRegistry.getStatistics()
}
