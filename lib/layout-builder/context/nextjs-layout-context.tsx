'use client'

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react'
import { useLayoutBuilder } from '../context'
import { 
  NextJSLayout, 
  NextJSLayoutBuilderState, 
  NextJSLayoutBuilderAction,
  NextJSLayoutGenerationParams,
  NextJSCodeGenerationResult,
  NextJSLayoutTemplate,
  NextJSMetadata,
  AppRouterFeature
} from '../types/nextjs-types'
import { Layout } from '../types'

// Initial NextJS layout builder state
const initialNextJSState: NextJSLayoutBuilderState = {
  nextjsLayout: null,
  generatedCode: null,
  isGeneratingCode: false,
  availableTemplates: [],
  selectedTemplate: null,
  previewUrl: null,
  isPreviewLoading: false,
  exportFormat: 'typescript',
  includeTypes: true,
  includeStyles: true
}

// NextJS Layout Builder reducer
function nextjsLayoutBuilderReducer(
  state: NextJSLayoutBuilderState, 
  action: NextJSLayoutBuilderAction
): NextJSLayoutBuilderState {
  switch (action.type) {
    case 'SET_NEXTJS_LAYOUT':
      return {
        ...state,
        nextjsLayout: action.payload
      }

    case 'UPDATE_NEXTJS_METADATA':
      if (!state.nextjsLayout) return state
      return {
        ...state,
        nextjsLayout: {
          ...state.nextjsLayout,
          nextjs: {
            ...state.nextjsLayout.nextjs,
            metadata: {
              ...state.nextjsLayout.nextjs.metadata,
              ...action.payload
            }
          }
        }
      }

    case 'SET_GENERATED_CODE':
      return {
        ...state,
        generatedCode: action.payload,
        isGeneratingCode: false
      }

    case 'SET_GENERATING_CODE':
      return {
        ...state,
        isGeneratingCode: action.payload
      }

    case 'SET_SELECTED_TEMPLATE':
      return {
        ...state,
        selectedTemplate: action.payload
      }

    case 'SET_PREVIEW_URL':
      return {
        ...state,
        previewUrl: action.payload
      }

    case 'SET_PREVIEW_LOADING':
      return {
        ...state,
        isPreviewLoading: action.payload
      }

    case 'SET_EXPORT_FORMAT':
      return {
        ...state,
        exportFormat: action.payload
      }

    case 'SET_INCLUDE_TYPES':
      return {
        ...state,
        includeTypes: action.payload
      }

    case 'SET_INCLUDE_STYLES':
      return {
        ...state,
        includeStyles: action.payload
      }

    default:
      return state
  }
}

// NextJS Layout Builder Context Type
interface NextJSLayoutBuilderContextType {
  // State
  state: NextJSLayoutBuilderState
  
  // Base layout builder context
  baseLayoutBuilder: ReturnType<typeof useLayoutBuilder>
  
  // NextJS specific actions
  setNextJSLayout: (layout: NextJSLayout) => void
  updateNextJSMetadata: (metadata: Partial<NextJSMetadata>) => void
  generateNextJSCode: () => Promise<NextJSCodeGenerationResult>
  applyTemplate: (template: NextJSLayoutTemplate) => void
  toggleFeature: (feature: AppRouterFeature) => void
  setPreviewUrl: (url: string | null) => void
  exportLayout: (format: 'typescript' | 'javascript') => Promise<string>
  
  // Layout conversion
  convertToNextJSLayout: (baseLayout: Layout) => NextJSLayout
  convertFromNextJSLayout: (nextjsLayout: NextJSLayout) => Layout
  
  // Template management
  loadTemplates: () => Promise<void>
  saveAsTemplate: (name: string, description: string) => Promise<void>
  
  // Universal Rendering integration
  registerWithUniversalRenderer: () => Promise<void>
  generateLayoutFile: (route: string) => Promise<string>
}

const NextJSLayoutBuilderContext = createContext<NextJSLayoutBuilderContextType | undefined>(undefined)

// Provider
interface NextJSLayoutBuilderProviderProps {
  children: React.ReactNode
  initialNextJSLayout?: NextJSLayout
}

export function NextJSLayoutBuilderProvider({ 
  children, 
  initialNextJSLayout 
}: NextJSLayoutBuilderProviderProps) {
  const [state, dispatch] = useReducer(nextjsLayoutBuilderReducer, {
    ...initialNextJSState,
    nextjsLayout: initialNextJSLayout || null
  })
  
  // Get base layout builder context
  const baseLayoutBuilder = useLayoutBuilder()

  // Initialize with initial NextJS layout if provided
  useEffect(() => {
    if (initialNextJSLayout) {
      dispatch({ type: 'SET_NEXTJS_LAYOUT', payload: initialNextJSLayout })
    }
  }, [initialNextJSLayout])

  // Sync base layout with NextJS layout
  useEffect(() => {
    if (state.nextjsLayout && baseLayoutBuilder.state.layout.id !== state.nextjsLayout.id) {
      const baseLayout = convertFromNextJSLayout(state.nextjsLayout)
      baseLayoutBuilder.updateLayout(baseLayout)
    }
  }, [state.nextjsLayout])

  // NextJS specific actions
  const setNextJSLayout = useCallback((layout: NextJSLayout) => {
    dispatch({ type: 'SET_NEXTJS_LAYOUT', payload: layout })
  }, [])

  const updateNextJSMetadata = useCallback((metadata: Partial<NextJSMetadata>) => {
    dispatch({ type: 'UPDATE_NEXTJS_METADATA', payload: metadata })
  }, [])

  const generateNextJSCode = useCallback(async (): Promise<NextJSCodeGenerationResult> => {
    if (!state.nextjsLayout) {
      throw new Error('No NextJS layout to generate code for')
    }

    dispatch({ type: 'SET_GENERATING_CODE', payload: true })

    try {
      const response = await fetch('/api/nextjs-layout-generator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          layout: state.nextjsLayout,
          exportFormat: state.exportFormat,
          includeTypes: state.includeTypes,
          includeStyles: state.includeStyles
        })
      })

      if (!response.ok) {
        throw new Error('Failed to generate NextJS code')
      }

      const result = await response.json()
      dispatch({ type: 'SET_GENERATED_CODE', payload: result })
      return result
    } catch (error) {
      dispatch({ type: 'SET_GENERATING_CODE', payload: false })
      throw error
    }
  }, [state.nextjsLayout, state.exportFormat, state.includeTypes, state.includeStyles])

  const applyTemplate = useCallback((template: NextJSLayoutTemplate) => {
    dispatch({ type: 'SET_SELECTED_TEMPLATE', payload: template })
    
    // Convert template to NextJS layout
    const nextjsLayout: NextJSLayout = {
      ...baseLayoutBuilder.state.layout,
      nextjs: {
        type: template.type,
        route: `app/${template.type === 'root' ? '' : '(group)/'}layout.tsx`,
        metadata: template.metadata,
        imports: ['import React from "react"'],
        exports: [
          {
            name: 'default',
            type: 'default',
            isAsync: false,
            returnType: 'JSX.Element'
          }
        ],
        appRouterFeatures: template.features
      }
    }
    
    setNextJSLayout(nextjsLayout)
  }, [baseLayoutBuilder.state.layout])

  const toggleFeature = useCallback((feature: AppRouterFeature) => {
    if (!state.nextjsLayout) return
    
    const currentFeatures = state.nextjsLayout.nextjs.appRouterFeatures || []
    const updatedFeatures = currentFeatures.includes(feature)
      ? currentFeatures.filter(f => f !== feature)
      : [...currentFeatures, feature]
    
    const updatedLayout = {
      ...state.nextjsLayout,
      nextjs: {
        ...state.nextjsLayout.nextjs,
        appRouterFeatures: updatedFeatures
      }
    }
    
    setNextJSLayout(updatedLayout)
  }, [state.nextjsLayout])

  const setPreviewUrl = useCallback((url: string | null) => {
    dispatch({ type: 'SET_PREVIEW_URL', payload: url })
  }, [])

  const exportLayout = useCallback(async (format: 'typescript' | 'javascript'): Promise<string> => {
    dispatch({ type: 'SET_EXPORT_FORMAT', payload: format })
    const result = await generateNextJSCode()
    return result.layout.code
  }, [generateNextJSCode])

  // Layout conversion functions
  const convertToNextJSLayout = useCallback((baseLayout: Layout): NextJSLayout => {
    return {
      ...baseLayout,
      nextjs: {
        type: 'root',
        route: 'app/layout.tsx',
        metadata: {
          title: baseLayout.name,
          description: baseLayout.description
        },
        imports: ['import React from "react"'],
        exports: [
          {
            name: 'default',
            type: 'default',
            isAsync: false,
            returnType: 'JSX.Element'
          }
        ],
        appRouterFeatures: ['metadata']
      }
    }
  }, [])

  const convertFromNextJSLayout = useCallback((nextjsLayout: NextJSLayout): Layout => {
    const { nextjs, ...baseLayout } = nextjsLayout
    return baseLayout
  }, [])

  // Template management
  const loadTemplates = useCallback(async () => {
    try {
      const response = await fetch('/api/nextjs-layout-templates')
      if (response.ok) {
        const templates = await response.json()
        // Update available templates in state
      }
    } catch (error) {
      console.error('Failed to load templates:', error)
    }
  }, [])

  const saveAsTemplate = useCallback(async (name: string, description: string) => {
    if (!state.nextjsLayout) return
    
    try {
      await fetch('/api/nextjs-layout-templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name,
          description,
          layout: state.nextjsLayout
        })
      })
    } catch (error) {
      console.error('Failed to save template:', error)
    }
  }, [state.nextjsLayout])

  // Universal Rendering integration
  const registerWithUniversalRenderer = useCallback(async () => {
    if (!state.nextjsLayout) return
    
    try {
      await fetch('/api/universal-renderer/register-layout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          layout: state.nextjsLayout
        })
      })
    } catch (error) {
      console.error('Failed to register with Universal Renderer:', error)
    }
  }, [state.nextjsLayout])

  const generateLayoutFile = useCallback(async (route: string): Promise<string> => {
    if (!state.nextjsLayout) {
      throw new Error('No NextJS layout to generate file for')
    }
    
    const result = await generateNextJSCode()
    return result.layout.code
  }, [state.nextjsLayout, generateNextJSCode])

  const value: NextJSLayoutBuilderContextType = {
    state,
    baseLayoutBuilder,
    setNextJSLayout,
    updateNextJSMetadata,
    generateNextJSCode,
    applyTemplate,
    toggleFeature,
    setPreviewUrl,
    exportLayout,
    convertToNextJSLayout,
    convertFromNextJSLayout,
    loadTemplates,
    saveAsTemplate,
    registerWithUniversalRenderer,
    generateLayoutFile
  }

  return (
    <NextJSLayoutBuilderContext.Provider value={value}>
      {children}
    </NextJSLayoutBuilderContext.Provider>
  )
}

// Hook
export function useNextJSLayoutBuilder() {
  const context = useContext(NextJSLayoutBuilderContext)
  if (context === undefined) {
    throw new Error('useNextJSLayoutBuilder must be used within a NextJSLayoutBuilderProvider')
  }
  return context
}
