# NextJS Layout Editor System

A comprehensive NextJS Layout Editor that integrates seamlessly with the Universal Rendering System for dynamic content rendering. This system allows you to visually create NextJS App Router layouts that automatically work with the existing Universal Renderer.

## 🎯 Overview

The NextJS Layout Editor is an AI-powered visual editor that creates production-ready NextJS layouts following App Router conventions. It integrates with the existing Universal Rendering System to provide dynamic layout resolution and content rendering.

## 🏗️ Architecture

### Core Components

1. **NextJS Layout Editor** (`nextjs-layout-editor.tsx`)
   - Main visual editor interface
   - Drag-and-drop layout composition
   - Real-time preview with responsive breakpoints
   - AI-powered layout generation

2. **NextJS Layout Sidebar** (`nextjs-layout-sidebar.tsx`)
   - Template library with pre-built layouts
   - Section-based editing (Header, Footer, Sidebar, Main)
   - App Router feature toggles
   - Template categories (Website, Admin, Landing, etc.)

3. **NextJS Properties Panel** (`nextjs-properties-panel.tsx`)
   - Dynamic properties editor using existing custom fields
   - NextJS-specific metadata configuration
   - SEO and Open Graph settings
   - App Router feature management

4. **NextJS Code Generator** (`nextjs-code-generator.tsx`)
   - Real-time TypeScript/JavaScript code generation
   - Component file generation
   - Type definitions and styles
   - Download and copy functionality

### Universal Renderer Integration

The system integrates with the Universal Rendering System through:

- **Layout Resolution**: Automatic layout resolution based on route patterns
- **Dynamic Rendering**: Seamless content rendering with custom layouts
- **Route Integration**: Support for nested layouts and route groups
- **Content Management**: Integration with existing CMS builders

## 🚀 Features

### NextJS App Router Compliance
- ✅ Root layouts (`app/layout.tsx`)
- ✅ Nested layouts (`app/(group)/layout.tsx`)
- ✅ Templates (`app/template.tsx`)
- ✅ Route groups for organization
- ✅ Parallel and intercepting routes support

### Section-Based Editing
- **Header Components**: Navigation, branding, search
- **Footer Components**: Links, copyright, social media
- **Sidebar Layouts**: Navigation, widgets, filters
- **Main Content Areas**: Dynamic content rendering
- **Custom Sections**: Flexible section types

### Advanced Features
- **Multi-Purpose Templates**: Website, admin, landing, blog layouts
- **Responsive Design**: Built-in breakpoint management
- **AI Integration**: Intelligent layout suggestions
- **Component Integration**: Seamless shadcn/ui integration
- **Production Ready**: DRY principles and best practices

### Universal Renderer Features
- **Dynamic Layout Resolution**: Automatic layout selection
- **Content Type Support**: Pages, posts, archives, custom types
- **Route-Based Layouts**: Different layouts for different routes
- **Fallback Handling**: Graceful degradation to default layouts

## 📁 File Structure

```
lib/layout-builder/
├── components/
│   ├── nextjs-layout-editor.tsx       # Main editor component
│   ├── nextjs-layout-sidebar.tsx      # Template and section library
│   ├── nextjs-properties-panel.tsx    # Properties editor
│   ├── nextjs-code-generator.tsx      # Code generation
│   └── layout-builder-editor.tsx      # Enhanced base editor
├── context/
│   └── nextjs-layout-context.tsx      # NextJS layout state management
├── types/
│   └── nextjs-types.ts                # NextJS-specific type definitions
├── services/
│   └── universal-renderer-integration.ts # Universal Renderer integration
└── README-NEXTJS-LAYOUT-EDITOR.md

app/api/
├── nextjs-layout-generator/
│   └── route.ts                       # Code generation API
└── universal-renderer/
    └── register-layout/
        └── route.ts                   # Layout registration API

app/admin/system/layouts/
└── nextjs-editor/
    └── page.tsx                       # Editor page
```

## 🛠️ Usage

### Basic Usage

```tsx
import { NextJSLayoutEditor } from '@/lib/layout-builder/components/nextjs-layout-editor'

function LayoutEditorPage() {
  const handleSave = async (layout: NextJSLayout) => {
    // Save and register with Universal Renderer
    await fetch('/api/universal-renderer/register-layout', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ layout })
    })
  }

  return (
    <NextJSLayoutEditor
      initialLayout={initialLayout}
      onSave={handleSave}
      onPreview={() => window.open('/preview', '_blank')}
    />
  )
}
```

### Universal Renderer Integration

```tsx
// The Universal Renderer automatically uses registered NextJS layouts
import { UniversalRenderer } from '@/lib/rendering/universal-renderer'

export default async function DynamicPage({ params, searchParams }) {
  return (
    <UniversalRenderer
      pathname={pathname}
      searchParams={searchParams}
    />
  )
}
```

### Custom Layout Registration

```tsx
import { UniversalRendererIntegration } from '@/lib/layout-builder/services/universal-renderer-integration'

// Register a NextJS layout with the Universal Renderer
await UniversalRendererIntegration.registerLayout(nextjsLayout)
```

## 🎨 Templates

### Available Templates

1. **Basic Root Layout**
   - Simple header and footer
   - Main content area
   - Responsive design

2. **Admin Dashboard**
   - Top navigation
   - Sidebar navigation
   - Content area with breadcrumbs

3. **Modern Landing**
   - Hero section
   - Feature sections
   - Call-to-action areas

4. **Blog Layout**
   - Article header
   - Content area
   - Sidebar with widgets

5. **E-commerce Layout**
   - Product navigation
   - Shopping cart integration
   - Product showcase areas

### Creating Custom Templates

```tsx
const customTemplate: NextJSLayoutTemplate = {
  id: 'custom-template',
  name: 'Custom Layout',
  description: 'A custom layout for specific needs',
  category: 'custom',
  type: 'root',
  sections: [
    { type: 'header', name: 'Header', enabled: true },
    { type: 'main', name: 'Main Content', enabled: true },
    { type: 'footer', name: 'Footer', enabled: true }
  ],
  features: ['metadata', 'loading', 'error'],
  metadata: {
    title: 'Custom Layout',
    description: 'A custom layout template'
  },
  tags: ['custom', 'template']
}
```

## 🔧 Configuration

### App Router Features

Enable NextJS App Router features:

- **Metadata API**: SEO and meta tags
- **Loading UI**: Loading states and skeletons
- **Error Boundaries**: Error handling
- **Not Found Pages**: 404 handling
- **Templates**: Re-render on navigation
- **Parallel Routes**: Multiple pages in one layout
- **Intercepting Routes**: Modal-like behavior
- **Route Groups**: Organize routes without affecting URL

### Responsive Breakpoints

```tsx
const breakpoints = {
  mobile: '375px',
  tablet: '768px',
  desktop: '1024px',
  large: '1440px'
}
```

## 🔌 API Endpoints

### Layout Generation
```
POST /api/nextjs-layout-generator
```
Generates NextJS layout code from visual layout definition.

### Universal Renderer Registration
```
POST /api/universal-renderer/register-layout
GET /api/universal-renderer/register-layout
DELETE /api/universal-renderer/register-layout
```
Manages layout registration with the Universal Rendering System.

## 🎯 Integration Points

### With Existing Systems

1. **Page Builder**: Seamless integration with existing page builder
2. **CMS Builders**: Works with unified CMS builder system
3. **Custom Fields**: Uses existing custom field components
4. **Zustand Stores**: Consistent state management patterns
5. **shadcn/ui**: Built on existing design system

### With Universal Renderer

1. **Route Resolution**: Automatic layout resolution
2. **Content Rendering**: Dynamic content integration
3. **Layout Fallbacks**: Graceful degradation
4. **Performance**: Optimized rendering pipeline

## 🚀 Getting Started

1. **Access the Editor**
   ```
   /admin/system/layouts/nextjs-editor
   ```

2. **Create a Layout**
   - Choose a template or start from scratch
   - Configure sections and properties
   - Preview in different breakpoints

3. **Generate Code**
   - Click "Generate" to create NextJS files
   - Copy or download the generated code
   - Files are automatically TypeScript-ready

4. **Register with Universal Renderer**
   - Save the layout to register automatically
   - Layout becomes available for dynamic rendering
   - Test with different content types

## 🎨 Customization

### Custom Sections
Add custom section types by extending the section configuration:

```tsx
const customSection: NextJSSectionConfig = {
  type: 'custom',
  name: 'Custom Section',
  enabled: true,
  template: 'custom-template',
  props: {
    backgroundColor: '#ffffff',
    padding: '2rem'
  }
}
```

### Custom Components
Integrate custom components with the layout system:

```tsx
const customComponent: NextJSComponent = {
  name: 'CustomComponent',
  path: 'components/custom/custom-component',
  imports: ['import { CustomComponent } from "@/components/custom"'],
  props: [
    { name: 'title', type: 'string', required: true },
    { name: 'variant', type: 'string', required: false, defaultValue: 'default' }
  ],
  children: true,
  isClientComponent: true
}
```

## 📈 Performance

- **Code Splitting**: Automatic component code splitting
- **Lazy Loading**: Dynamic imports for better performance
- **Caching**: Layout resolution caching
- **Optimization**: Production-ready code generation

## 🔒 Security

- **Input Validation**: All inputs are validated
- **XSS Protection**: Safe code generation
- **CSRF Protection**: API endpoint protection
- **Access Control**: Admin-only access

## 🧪 Testing

The system includes comprehensive testing for:
- Layout generation
- Universal Renderer integration
- Code generation accuracy
- Responsive behavior
- API endpoints

## 📚 Documentation

For more detailed documentation, see:
- [Universal Rendering System](../rendering/README.md)
- [Layout Builder Architecture](./docs/layout-builder-architecture.md)
- [API Documentation](./docs/api-documentation.md)

## 🤝 Contributing

1. Follow existing code patterns
2. Add comprehensive tests
3. Update documentation
4. Ensure Universal Renderer compatibility
