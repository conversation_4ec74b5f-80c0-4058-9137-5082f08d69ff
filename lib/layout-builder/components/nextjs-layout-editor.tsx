'use client'

import React, { useState, useEffect } from 'react'
import { LayoutBuilderProvider } from '../context'
import { NextJSLayoutBuilderProvider, useNextJSLayoutBuilder } from '../context/nextjs-layout-context'
import { LayoutBuilderCanvas } from './layout-builder-canvas'
import { NextJSLayoutSidebar } from './nextjs-layout-sidebar'
import { NextJSPropertiesPanel } from './nextjs-properties-panel'
import { NextJSCodeGenerator } from './nextjs-code-generator'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable'
import {
  ArrowLeft,
  Eye,
  EyeOff,
  Smartphone,
  Tablet,
  Monitor,
  Undo,
  Redo,
  <PERSON>tings,
  Save,
  Download,
  Upload,
  Layers,
  Palette,
  Code,
  Play,
  Pause,
  Brain,
  Sparkles,
  Wand2,
  Plus,
  FileCode,
  Layout,
  Globe,
  RefreshCw
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { toast } from 'sonner'
import { NextJSLayout, NextJSLayoutTemplate, NextJSSectionConfig, AppRouterFeature } from '../types/nextjs-types'

interface NextJSLayoutEditorProps {
  initialLayout?: NextJSLayout
  onSave?: (layout: NextJSLayout) => Promise<void>
  onPreview?: () => void
  onBack?: () => void
  className?: string
  isSaving?: boolean
}

// Inner component that uses the NextJS layout builder context
function NextJSLayoutEditorInner({
  onSave: _onSave,
  onPreview,
  onBack: _onBack,
  className,
  isSaving
}: Omit<NextJSLayoutEditorProps, 'initialLayout'>) {
  const {
    state,
    baseLayoutBuilder,
    setNextJSLayout,
    generateNextJSCode,
    applyTemplate,
    toggleFeature,
    registerWithUniversalRenderer
  } = useNextJSLayoutBuilder()

  const [activeRightTab, setActiveRightTab] = useState('properties')

  const {
    state: baseState,
    setPreviewMode,
    setDevicePreview,
    undo,
    redo,
    canUndo,
    canRedo,
    updateSection,
    updateBlock
  } = baseLayoutBuilder

  const {
    isPreviewMode,
    devicePreview,
    hasUnsavedChanges,
    isSaving: baseSaving,
    selectedSectionId,
    selectedBlockId
  } = baseState

  // Use the passed isSaving prop or fall back to base saving state
  const isCurrentlySaving = isSaving || baseSaving

  // Handle save
  const handleSave = async () => {
    if (_onSave && state.nextjsLayout) {
      try {
        await _onSave(state.nextjsLayout)
        // Register with Universal Renderer after saving
        await registerWithUniversalRenderer()
        toast.success('Layout saved and registered with Universal Renderer')
      } catch (error) {
        toast.error('Failed to save layout')
      }
    }
  }

  // Handle preview
  const handlePreview = () => {
    setPreviewMode(!isPreviewMode)
    if (onPreview) {
      onPreview()
    }
  }

  // Handle template selection
  const handleTemplateSelect = (template: NextJSLayoutTemplate) => {
    applyTemplate(template)
    toast.success(`Applied template: ${template.name}`)
  }

  // Handle section addition
  const handleSectionAdd = (section: NextJSSectionConfig) => {
    // This would integrate with the base layout builder to add sections
    toast.success(`Added ${section.name} section`)
  }

  // Handle feature toggle
  const handleFeatureToggle = (feature: AppRouterFeature) => {
    toggleFeature(feature)
    toast.success(`Toggled ${feature} feature`)
  }

  // Handle layout updates
  const handleLayoutUpdate = (updates: Partial<NextJSLayout>) => {
    if (state.nextjsLayout) {
      const updatedLayout = { ...state.nextjsLayout, ...updates }
      setNextJSLayout(updatedLayout)
    }
  }

  // Handle code generation
  const handleGenerateCode = async () => {
    try {
      await generateNextJSCode()
      toast.success('NextJS code generated successfully')
    } catch (error) {
      toast.error('Failed to generate code')
    }
  }

  // Handle code copy
  const handleCodeCopy = (code: string) => {
    // Copy handled by the code generator component
  }

  // Handle code download
  const handleCodeDownload = (files: any) => {
    // Create and download zip file with all generated files
    toast.success('Files downloaded')
  }

  // Get device preview width
  const getCanvasWidth = () => {
    switch (devicePreview) {
      case 'mobile':
        return '375px'
      case 'tablet':
        return '768px'
      default:
        return '100%'
    }
  }

  return (
    <div className={cn('h-full flex flex-col bg-gray-50', className)}>
      {/* Top Toolbar */}
      <div className="flex items-center justify-between p-4 bg-white border-b border-gray-200 shadow-sm">
        <div className="flex items-center space-x-4">
          {/* Layout Info */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Globe className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h1 className="font-semibold text-gray-900">
                {state.nextjsLayout?.name || 'NextJS Layout'}
              </h1>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Badge variant="outline" className="text-xs">
                  {state.nextjsLayout?.nextjs.type || 'root'}
                </Badge>
                <span>•</span>
                <span>{state.nextjsLayout?.nextjs.route || 'app/layout.tsx'}</span>
              </div>
            </div>
          </div>

          <Separator orientation="vertical" className="h-8" />

          {/* Status Indicator */}
          <div className="flex items-center gap-2">
            <div className={cn(
              'w-2 h-2 rounded-full',
              isCurrentlySaving ? 'bg-blue-500' : hasUnsavedChanges ? 'bg-orange-500' : 'bg-green-500'
            )} />
            <span className="text-sm text-muted-foreground">
              {isCurrentlySaving ? 'Saving...' : hasUnsavedChanges ? 'Unsaved changes' : 'All changes saved'}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Undo/Redo */}
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={undo}
              disabled={!canUndo}
              className="h-8 w-8 p-0"
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={redo}
              disabled={!canRedo}
              className="h-8 w-8 p-0"
            >
              <Redo className="h-4 w-4" />
            </Button>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Device Preview */}
          <div className="flex items-center gap-1 p-1 bg-gray-100 rounded-lg">
            {(['desktop', 'tablet', 'mobile'] as const).map((device) => {
              const Icon = device === 'desktop' ? Monitor : device === 'tablet' ? Tablet : Smartphone
              return (
                <Button
                  key={device}
                  variant={devicePreview === device ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setDevicePreview(device)}
                  className="h-8 w-8 p-0"
                >
                  <Icon className="h-4 w-4" />
                </Button>
              )
            })}
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Preview Mode */}
          <Button
            variant={isPreviewMode ? 'default' : 'outline'}
            size="sm"
            onClick={handlePreview}
            className="gap-2"
          >
            {isPreviewMode ? (
              <>
                <Pause className="h-4 w-4" />
                Exit Preview
              </>
            ) : (
              <>
                <Play className="h-4 w-4" />
                Preview
              </>
            )}
          </Button>

          {/* Generate Code */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleGenerateCode}
            disabled={state.isGeneratingCode}
            className="gap-2"
          >
            {state.isGeneratingCode ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <FileCode className="h-4 w-4" />
            )}
            Generate
          </Button>

          {/* Actions Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleSave} disabled={isSaving || !hasUnsavedChanges}>
                <Save className="h-4 w-4 mr-2" />
                Save Layout
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => registerWithUniversalRenderer()}>
                <Globe className="h-4 w-4 mr-2" />
                Register with Universal Renderer
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Export Layout
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                Import Layout
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Code className="h-4 w-4 mr-2" />
                View Generated Code
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Save Button */}
          <Button
            onClick={handleSave}
            disabled={isCurrentlySaving || !hasUnsavedChanges}
            size="sm"
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {isCurrentlySaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>

      {/* Editor Content with Resizable Panels */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full">
          {/* Left Sidebar Panel */}
          <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
            <div className="h-full">
              <NextJSLayoutSidebar
                onTemplateSelect={handleTemplateSelect}
                onSectionAdd={handleSectionAdd}
                onFeatureToggle={handleFeatureToggle}
              />
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Main Canvas Panel */}
          <ResizablePanel defaultSize={60} minSize={40}>
            <div className="h-full flex flex-col">
              {/* Canvas Header */}
              <div className="flex items-center justify-between p-4 bg-white border-b">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Canvas</span>
                  <Badge variant="outline" className="text-xs">
                    {devicePreview}
                  </Badge>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">
                    {getCanvasWidth()}
                  </span>
                </div>
              </div>

              {/* Canvas */}
              <div className="flex-1 overflow-auto bg-gray-100 p-6">
                <div className="flex justify-center">
                  <div
                    className="bg-white shadow-lg rounded-lg overflow-hidden transition-all duration-300"
                    style={{
                      width: getCanvasWidth(),
                      minHeight: '600px',
                      maxWidth: '100%'
                    }}
                  >
                    <LayoutBuilderCanvas
                      devicePreview={devicePreview}
                      isPreviewMode={isPreviewMode}
                    />
                  </div>
                </div>
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle withHandle />

          {/* Right Sidebar Panel */}
          <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
            <div className="h-full">
              <Tabs value={activeRightTab} onValueChange={setActiveRightTab} className="h-full">
                <div className="border-b border-gray-200 px-4 py-2">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="properties">Properties</TabsTrigger>
                    <TabsTrigger value="code">Code</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="properties" className="m-0 h-full">
                  <NextJSPropertiesPanel
                    layout={state.nextjsLayout || undefined}
                    selectedSectionId={selectedSectionId}
                    selectedBlockId={selectedBlockId}
                    onLayoutUpdate={handleLayoutUpdate}
                    onSectionUpdate={updateSection}
                    onBlockUpdate={updateBlock}
                    onFeatureToggle={handleFeatureToggle}
                  />
                </TabsContent>

                <TabsContent value="code" className="m-0 h-full">
                  <NextJSCodeGenerator
                    layout={state.nextjsLayout || undefined}
                    generatedCode={state.generatedCode}
                    isGenerating={state.isGeneratingCode}
                    onGenerate={handleGenerateCode}
                    onCopy={handleCodeCopy}
                    onDownload={handleCodeDownload}
                  />
                </TabsContent>
              </Tabs>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  )
}

// Main component with providers
export function NextJSLayoutEditor({
  initialLayout,
  onSave,
  onPreview,
  onBack,
  className,
  isSaving
}: NextJSLayoutEditorProps) {
  return (
    <LayoutBuilderProvider initialLayout={initialLayout}>
      <NextJSLayoutBuilderProvider initialNextJSLayout={initialLayout}>
        <NextJSLayoutEditorInner
          onSave={onSave}
          onPreview={onPreview}
          onBack={onBack}
          className={className}
          isSaving={isSaving}
        />
      </NextJSLayoutBuilderProvider>
    </LayoutBuilderProvider>
  )
}
