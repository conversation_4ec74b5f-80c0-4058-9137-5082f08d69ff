'use client'

import React, { useState } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Layout,
  FileCode,
  Globe,
  Layers,
  Navigation,
  FileText,
  Package,
  Copyright,
  Search,
  Plus,

} from 'lucide-react'
import { cn } from '@/lib/utils'
import { NextJSLayoutTemplate, NextJSSectionConfig, AppRouterFeature } from '../types/nextjs-types'

interface NextJSLayoutSidebarProps {
  className?: string
  onTemplateSelect?: (template: NextJSLayoutTemplate) => void
  onSectionAdd?: (section: NextJSSectionConfig) => void
  onFeatureToggle?: (feature: AppRouterFeature) => void
}

// Mock templates data - in real implementation, this would come from a service
const layoutTemplates: NextJSLayoutTemplate[] = [
  {
    id: 'root-basic',
    name: 'Basic Root Layout',
    description: 'Simple root layout with header and footer',
    category: 'website',
    type: 'root',
    preview: '/templates/root-basic.png',
    sections: [
      { type: 'header', name: 'Header', enabled: true },
      { type: 'main', name: 'Main Content', enabled: true },
      { type: 'footer', name: 'Footer', enabled: true }
    ],
    features: ['metadata', 'loading'],
    metadata: {
      title: { default: 'My App', template: '%s | My App' },
      description: 'A modern web application'
    },
    tags: ['basic', 'website']
  },
  {
    id: 'dashboard-admin',
    name: 'Admin Dashboard',
    description: 'Dashboard layout with sidebar navigation',
    category: 'admin',
    type: 'nested',
    preview: '/templates/dashboard-admin.png',
    sections: [
      { type: 'header', name: 'Top Navigation', enabled: true },
      { type: 'sidebar', name: 'Sidebar', enabled: true },
      { type: 'main', name: 'Content Area', enabled: true }
    ],
    features: ['metadata', 'loading', 'error'],
    metadata: {
      title: 'Admin Dashboard',
      description: 'Administrative interface'
    },
    tags: ['dashboard', 'admin', 'sidebar']
  },
  {
    id: 'landing-modern',
    name: 'Modern Landing',
    description: 'Contemporary landing page layout',
    category: 'landing',
    type: 'root',
    preview: '/templates/landing-modern.png',
    sections: [
      { type: 'header', name: 'Navigation', enabled: true },
      { type: 'main', name: 'Hero & Content', enabled: true },
      { type: 'footer', name: 'Footer', enabled: true }
    ],
    features: ['metadata', 'loading'],
    metadata: {
      title: 'Landing Page',
      description: 'Convert visitors into customers'
    },
    tags: ['landing', 'marketing', 'modern']
  }
]

const sectionTypes = [
  {
    type: 'header' as const,
    name: 'Header',
    description: 'Top navigation and branding',
    icon: Navigation,
    color: 'bg-blue-100 text-blue-600'
  },
  {
    type: 'main' as const,
    name: 'Main Content',
    description: 'Primary content area',
    icon: FileText,
    color: 'bg-green-100 text-green-600'
  },
  {
    type: 'sidebar' as const,
    name: 'Sidebar',
    description: 'Side navigation or content',
    icon: Package,
    color: 'bg-purple-100 text-purple-600'
  },
  {
    type: 'footer' as const,
    name: 'Footer',
    description: 'Bottom content and links',
    icon: Copyright,
    color: 'bg-orange-100 text-orange-600'
  },
  {
    type: 'custom' as const,
    name: 'Custom Section',
    description: 'Custom layout section',
    icon: Layers,
    color: 'bg-gray-100 text-gray-600'
  }
]

const appRouterFeatures: Array<{ feature: AppRouterFeature; name: string; description: string }> = [
  { feature: 'metadata', name: 'Metadata', description: 'SEO and meta tags' },
  { feature: 'loading', name: 'Loading UI', description: 'Loading states' },
  { feature: 'error', name: 'Error Handling', description: 'Error boundaries' },
  { feature: 'not-found', name: 'Not Found', description: '404 pages' },
  { feature: 'template', name: 'Templates', description: 'Re-render on navigation' },
  { feature: 'parallel-routes', name: 'Parallel Routes', description: 'Multiple pages in one layout' },
  { feature: 'intercepting-routes', name: 'Intercepting Routes', description: 'Modal-like behavior' },
  { feature: 'route-groups', name: 'Route Groups', description: 'Organize routes' }
]

export function NextJSLayoutSidebar({
  className,
  onTemplateSelect,
  onSectionAdd,
  onFeatureToggle
}: NextJSLayoutSidebarProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const filteredTemplates = layoutTemplates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const categories = ['all', ...Array.from(new Set(layoutTemplates.map(t => t.category)))]

  return (
    <div className={cn('h-full bg-white border-r border-gray-200 flex flex-col', className)}>
      <Tabs defaultValue="templates" className="h-full flex flex-col">
        <div className="p-3 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center space-x-2 mb-3">
            <Layout className="w-4 h-4 text-purple-600" />
            <h2 className="text-sm font-semibold text-gray-900">NextJS Layouts</h2>
          </div>

          <TabsList className="grid w-full grid-cols-3 h-8">
            <TabsTrigger value="templates" className="text-xs">Templates</TabsTrigger>
            <TabsTrigger value="sections" className="text-xs">Sections</TabsTrigger>
            <TabsTrigger value="features" className="text-xs">Features</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="templates" className="flex-1 flex flex-col m-0 min-h-0">
          {/* Search and Filter */}
          <div className="p-3 border-b border-gray-100 flex-shrink-0">
            <div className="relative mb-2">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
              <Input
                placeholder="Search templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-7 h-7 text-xs"
              />
            </div>

            <div className="flex flex-wrap gap-1">
              {categories.map(category => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="text-xs h-6 px-2"
                >
                  {category === 'all' ? 'All' : category.charAt(0).toUpperCase() + category.slice(1)}
                </Button>
              ))}
            </div>
          </div>

          {/* Templates List */}
          <ScrollArea className="flex-1 min-h-0">
            <div className="p-3 space-y-2">
              {filteredTemplates.map(template => (
                <Card
                  key={template.id}
                  className="cursor-pointer hover:shadow-sm transition-shadow"
                  onClick={() => onTemplateSelect?.(template)}
                >
                  <CardHeader className="pb-1 p-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-xs font-medium">{template.name}</CardTitle>
                      <Badge variant="outline" className="text-xs h-4 px-1">
                        {template.type}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground leading-tight">{template.description}</p>
                  </CardHeader>
                  <CardContent className="pt-0 p-3">
                    <div className="flex flex-wrap gap-1 mb-1">
                      {template.tags.slice(0, 2).map(tag => (
                        <Badge key={tag} variant="secondary" className="text-xs h-4 px-1">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {template.sections.length} sections • {template.features.length} features
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="sections" className="flex-1 flex flex-col m-0 min-h-0">
          <div className="p-3 border-b border-gray-100 flex-shrink-0">
            <h3 className="text-xs font-medium text-gray-900 mb-1">Layout Sections</h3>
            <p className="text-xs text-muted-foreground">
              Drag sections to the canvas or click to add
            </p>
          </div>

          <ScrollArea className="flex-1 min-h-0">
            <div className="p-3 space-y-2">
              {sectionTypes.map(section => {
                const Icon = section.icon
                return (
                  <Card
                    key={section.type}
                    className="cursor-pointer hover:shadow-sm transition-shadow"
                    onClick={() => onSectionAdd?.({
                      type: section.type,
                      name: section.name,
                      enabled: true
                    })}
                  >
                    <CardContent className="p-2">
                      <div className="flex items-center space-x-2">
                        <div className={cn('p-1 rounded', section.color)}>
                          <Icon className="w-3 h-3" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-xs font-medium text-gray-900">{section.name}</h4>
                          <p className="text-xs text-muted-foreground truncate">
                            {section.description}
                          </p>
                        </div>
                        <Plus className="w-3 h-3 text-gray-400" />
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="features" className="flex-1 flex flex-col m-0 min-h-0">
          <div className="p-3 border-b border-gray-100 flex-shrink-0">
            <h3 className="text-xs font-medium text-gray-900 mb-1">App Router Features</h3>
            <p className="text-xs text-muted-foreground">
              Enable Next.js App Router features
            </p>
          </div>

          <ScrollArea className="flex-1 min-h-0">
            <div className="p-3 space-y-2">
              {appRouterFeatures.map(({ feature, name, description }) => (
                <Card
                  key={feature}
                  className="cursor-pointer hover:shadow-sm transition-shadow"
                  onClick={() => onFeatureToggle?.(feature)}
                >
                  <CardContent className="p-2">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-xs font-medium text-gray-900">{name}</h4>
                        <p className="text-xs text-muted-foreground">{description}</p>
                      </div>
                      <div className="w-3 h-3 border border-gray-300 rounded"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  )
}
