'use client'

import React, { useState } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import {
  FileCode,
  Copy,
  Download,
  Eye,
  Code,
  Folder,
  File,
  Check,
  Loader2,
  RefreshCw,
  Settings
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'
import { NextJSCodeGenerationResult, NextJSLayout } from '../types/nextjs-types'

interface NextJSCodeGeneratorProps {
  layout?: NextJSLayout
  generatedCode?: NextJSCodeGenerationResult | null
  isGenerating?: boolean
  onGenerate?: () => Promise<void>
  onCopy?: (code: string) => void
  onDownload?: (files: NextJSCodeGenerationResult) => void
  className?: string
}

export function NextJSCodeGenerator({
  layout,
  generatedCode,
  isGenerating = false,
  onGenerate,
  onCopy,
  onDownload,
  className
}: NextJSCodeGeneratorProps) {
  const [activeTab, setActiveTab] = useState('layout')
  const [copiedFile, setCopiedFile] = useState<string | null>(null)

  // Handle copy to clipboard
  const handleCopy = async (code: string, fileName: string) => {
    try {
      await navigator.clipboard.writeText(code)
      setCopiedFile(fileName)
      onCopy?.(code)
      toast.success(`Copied ${fileName} to clipboard`)
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedFile(null), 2000)
    } catch (error) {
      toast.error('Failed to copy to clipboard')
    }
  }

  // Handle download
  const handleDownload = () => {
    if (generatedCode && onDownload) {
      onDownload(generatedCode)
      toast.success('Files downloaded successfully')
    }
  }

  // Handle generate
  const handleGenerate = async () => {
    if (onGenerate) {
      try {
        await onGenerate()
        toast.success('Code generated successfully')
      } catch (error) {
        toast.error('Failed to generate code')
      }
    }
  }

  if (!layout) {
    return (
      <div className={cn('h-full bg-white border-l border-gray-200 flex items-center justify-center', className)}>
        <div className="text-center text-gray-500">
          <FileCode className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <div className="text-lg font-medium mb-2">No Layout Selected</div>
          <div className="text-sm">
            Select a NextJS layout to generate code
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('h-full bg-white border-l border-gray-200 flex flex-col', className)}>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="p-3 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <FileCode className="w-4 h-4 text-purple-600" />
              <h2 className="text-sm font-semibold text-gray-900">Code Generator</h2>
            </div>
            
            <div className="flex items-center space-x-1">
              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerate}
                disabled={isGenerating}
                className="gap-1 h-7 px-2 text-xs"
              >
                {isGenerating ? (
                  <Loader2 className="w-3 h-3 animate-spin" />
                ) : (
                  <RefreshCw className="w-3 h-3" />
                )}
                Generate
              </Button>

              {generatedCode && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={handleDownload}
                  className="gap-1 h-7 px-2 text-xs"
                >
                  <Download className="w-3 h-3" />
                  Download
                </Button>
              )}
            </div>
          </div>

          {/* Layout Info */}
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Badge variant="outline" className="text-xs h-4 px-1">{layout.nextjs.type}</Badge>
            <span>•</span>
            <span className="truncate">{layout.nextjs.route}</span>
            <span>•</span>
            <span>{layout.nextjs.appRouterFeatures.length} features</span>
          </div>
        </div>

        {/* Content */}
        {isGenerating ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Loader2 className="w-8 h-8 mx-auto mb-4 animate-spin text-purple-600" />
              <div className="text-lg font-medium mb-2">Generating Code...</div>
              <div className="text-sm text-muted-foreground">
                Creating NextJS layout files
              </div>
            </div>
          </div>
        ) : !generatedCode ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Code className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <div className="text-lg font-medium mb-2">Ready to Generate</div>
              <div className="text-sm text-muted-foreground mb-4">
                Click Generate to create NextJS layout code
              </div>
              <Button onClick={handleGenerate} className="gap-2">
                <FileCode className="w-4 h-4" />
                Generate Code
              </Button>
            </div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col min-h-0">
            <div className="px-3 py-2 border-b border-gray-100 flex-shrink-0">
              <TabsList className="grid w-full grid-cols-4 h-7">
                <TabsTrigger value="layout" className="text-xs">Layout</TabsTrigger>
                <TabsTrigger value="components" className="text-xs">Components</TabsTrigger>
                <TabsTrigger value="types" className="text-xs">Types</TabsTrigger>
                <TabsTrigger value="structure" className="text-xs">Structure</TabsTrigger>
              </TabsList>
            </div>

            <ScrollArea className="flex-1 min-h-0">
              <TabsContent value="layout" className="m-0 p-3">
                <Card>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm font-medium flex items-center gap-2">
                        <File className="w-4 h-4" />
                        {generatedCode.layout.path}
                      </CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleCopy(generatedCode.layout.code, 'layout.tsx')}
                        className="gap-2"
                      >
                        {copiedFile === 'layout.tsx' ? (
                          <Check className="w-4 h-4 text-green-600" />
                        ) : (
                          <Copy className="w-4 h-4" />
                        )}
                        Copy
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <pre className="text-xs bg-gray-50 p-3 rounded-lg overflow-x-auto">
                      <code>{generatedCode.layout.code}</code>
                    </pre>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="components" className="m-0 p-3 space-y-3">
                {generatedCode.components.map((component, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <File className="w-4 h-4" />
                          {component.path}
                        </CardTitle>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopy(component.code, component.name)}
                          className="gap-2"
                        >
                          {copiedFile === component.name ? (
                            <Check className="w-4 h-4 text-green-600" />
                          ) : (
                            <Copy className="w-4 h-4" />
                          )}
                          Copy
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <pre className="text-xs bg-gray-50 p-3 rounded-lg overflow-x-auto">
                        <code>{component.code}</code>
                      </pre>
                    </CardContent>
                  </Card>
                ))}
                
                {generatedCode.components.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    <File className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                    <div className="text-sm">No additional components generated</div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="types" className="m-0 p-3">
                {generatedCode.types ? (
                  <Card>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm font-medium flex items-center gap-2">
                          <File className="w-4 h-4" />
                          {generatedCode.types.path}
                        </CardTitle>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopy(generatedCode.types!.code, 'types.ts')}
                          className="gap-2"
                        >
                          {copiedFile === 'types.ts' ? (
                            <Check className="w-4 h-4 text-green-600" />
                          ) : (
                            <Copy className="w-4 h-4" />
                          )}
                          Copy
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <pre className="text-xs bg-gray-50 p-3 rounded-lg overflow-x-auto">
                        <code>{generatedCode.types.code}</code>
                      </pre>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    <File className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                    <div className="text-sm">No type definitions generated</div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="structure" className="m-0 p-3">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Folder className="w-4 h-4" />
                      File Structure
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm font-mono">
                      <div className="flex items-center gap-2">
                        <Folder className="w-4 h-4 text-blue-500" />
                        <span>app/</span>
                      </div>
                      <div className="ml-6 flex items-center gap-2">
                        <File className="w-4 h-4 text-green-500" />
                        <span>layout.tsx</span>
                        <Badge variant="secondary" className="text-xs">Main</Badge>
                      </div>
                      
                      {generatedCode.components.map((component, index) => (
                        <div key={index} className="ml-6 flex items-center gap-2">
                          <File className="w-4 h-4 text-blue-500" />
                          <span>{component.name}.tsx</span>
                          <Badge variant="outline" className="text-xs">Component</Badge>
                        </div>
                      ))}
                      
                      {generatedCode.types && (
                        <div className="ml-6 flex items-center gap-2">
                          <File className="w-4 h-4 text-purple-500" />
                          <span>types.ts</span>
                          <Badge variant="outline" className="text-xs">Types</Badge>
                        </div>
                      )}
                      
                      {generatedCode.styles && (
                        <div className="ml-6 flex items-center gap-2">
                          <File className="w-4 h-4 text-pink-500" />
                          <span>styles.css</span>
                          <Badge variant="outline" className="text-xs">Styles</Badge>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </ScrollArea>
          </Tabs>
        )}
      </div>
    </div>
  )
}
