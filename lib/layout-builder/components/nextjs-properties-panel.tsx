'use client'

import React, { useState } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Settings,
  Globe,
  Code,
  Smartphone,
  Monitor,
  Palette,
  FileCode,
  Search,
  Eye,
  EyeOff,
  Copy,
  Save
} from 'lucide-react'
import { cn } from '@/lib/utils'

// Import custom fields from existing system
import {
  TextField,
  TextareaField,
  BooleanField,
  SelectField,
  ObjectField,
  FieldGroup,
  FieldSection
} from '@/lib/core/builders/components/properties-panel/custom-fields'

import { NextJSLayout, NextJSMetadata, AppRouterFeature } from '../types/nextjs-types'

interface NextJSPropertiesPanelProps {
  layout?: NextJSLayout
  selectedSectionId?: string | null
  selectedBlockId?: string | null
  onLayoutUpdate?: (updates: Partial<NextJSLayout>) => void
  onSectionUpdate?: (sectionId: string, updates: any) => void
  onBlockUpdate?: (blockId: string, updates: any) => void
  onFeatureToggle?: (feature: AppRouterFeature) => void
  className?: string
}

export function NextJSPropertiesPanel({
  layout,
  selectedSectionId,
  selectedBlockId,
  onLayoutUpdate,
  onSectionUpdate,
  onBlockUpdate,
  onFeatureToggle,
  className
}: NextJSPropertiesPanelProps) {
  const [activeTab, setActiveTab] = useState('layout')

  // Get current selection context
  const selectedSection = selectedSectionId && layout ? 
    Object.values(layout.structure).find(section => section?.id === selectedSectionId) : null
  
  const selectedBlock = selectedBlockId && selectedSection ?
    selectedSection.blocks.find(block => block.id === selectedBlockId) : null

  // Determine what to show based on selection
  const showLayoutProperties = !selectedSectionId && !selectedBlockId
  const showSectionProperties = selectedSectionId && !selectedBlockId
  const showBlockProperties = selectedBlockId

  // Handle metadata updates
  const handleMetadataUpdate = (field: string, value: any) => {
    if (!layout) return
    
    const updatedMetadata = {
      ...layout.nextjs.metadata,
      [field]: value
    }
    
    onLayoutUpdate?.({
      nextjs: {
        ...layout.nextjs,
        metadata: updatedMetadata
      }
    })
  }

  // Handle layout type change
  const handleLayoutTypeChange = (type: string) => {
    if (!layout) return
    
    onLayoutUpdate?.({
      nextjs: {
        ...layout.nextjs,
        type: type as any
      }
    })
  }

  // Handle feature toggle
  const handleFeatureToggle = (feature: AppRouterFeature) => {
    if (!layout) return
    
    const currentFeatures = layout.nextjs.appRouterFeatures || []
    const updatedFeatures = currentFeatures.includes(feature)
      ? currentFeatures.filter(f => f !== feature)
      : [...currentFeatures, feature]
    
    onLayoutUpdate?.({
      nextjs: {
        ...layout.nextjs,
        appRouterFeatures: updatedFeatures
      }
    })
  }

  if (!layout) {
    return (
      <div className={cn('h-full bg-white border-l border-gray-200 flex items-center justify-center', className)}>
        <div className="text-center text-gray-500">
          <Settings className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <div className="text-lg font-medium mb-2">No Layout Selected</div>
          <div className="text-sm">
            Select a layout to edit its properties
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('h-full bg-white border-l border-gray-200 flex flex-col', className)}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
        <div className="p-3 border-b border-gray-200 flex-shrink-0">
          <div className="flex items-center space-x-2 mb-2">
            <Settings className="w-4 h-4 text-purple-600" />
            <h2 className="text-sm font-semibold text-gray-900">Properties</h2>
          </div>

          <TabsList className="grid w-full grid-cols-3 h-8">
            <TabsTrigger value="layout" className="text-xs">Layout</TabsTrigger>
            <TabsTrigger value="metadata" className="text-xs">Metadata</TabsTrigger>
            <TabsTrigger value="advanced" className="text-xs">Advanced</TabsTrigger>
          </TabsList>
        </div>

        <ScrollArea className="flex-1 min-h-0">
          <TabsContent value="layout" className="m-0 p-3 space-y-3">
            {showLayoutProperties && (
              <>
                <FieldSection title="Layout Configuration" icon={<FileCode className="w-4 h-4" />}>
                  <FieldGroup>
                    <TextField
                      label="Layout Name"
                      value={layout.name}
                      onChange={(value) => onLayoutUpdate?.({ name: value })}
                      placeholder="Enter layout name"
                    />
                    
                    <TextareaField
                      label="Description"
                      value={layout.description || ''}
                      onChange={(value) => onLayoutUpdate?.({ description: value })}
                      placeholder="Describe this layout"
                      rows={3}
                    />
                    
                    <SelectField
                      label="Layout Type"
                      value={layout.nextjs.type}
                      onChange={handleLayoutTypeChange}
                      options={[
                        { value: 'root', label: 'Root Layout (app/layout.tsx)' },
                        { value: 'nested', label: 'Nested Layout' },
                        { value: 'template', label: 'Template (re-renders)' },
                        { value: 'group', label: 'Route Group' }
                      ]}
                    />
                    
                    <TextField
                      label="Route Path"
                      value={layout.nextjs.route}
                      onChange={(value) => onLayoutUpdate?.({
                        nextjs: { ...layout.nextjs, route: value }
                      })}
                      placeholder="app/layout.tsx"
                    />
                  </FieldGroup>
                </FieldSection>

                <FieldSection title="App Router Features" icon={<Globe className="w-4 h-4" />}>
                  <div className="space-y-2">
                    {[
                      { feature: 'metadata' as AppRouterFeature, label: 'Metadata API', description: 'SEO and meta tags' },
                      { feature: 'loading' as AppRouterFeature, label: 'Loading UI', description: 'Loading states' },
                      { feature: 'error' as AppRouterFeature, label: 'Error Boundaries', description: 'Error handling' },
                      { feature: 'not-found' as AppRouterFeature, label: 'Not Found Pages', description: '404 handling' }
                    ].map(({ feature, label, description }) => (
                      <div key={feature} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-900">{label}</div>
                          <div className="text-xs text-muted-foreground">{description}</div>
                        </div>
                        <BooleanField
                          value={layout.nextjs.appRouterFeatures?.includes(feature) || false}
                          onChange={(enabled) => onFeatureToggle?.(feature)}
                        />
                      </div>
                    ))}
                  </div>
                </FieldSection>
              </>
            )}

            {showSectionProperties && selectedSection && (
              <FieldSection title="Section Properties" icon={<Code className="w-4 h-4" />}>
                <FieldGroup>
                  <TextField
                    label="Section Name"
                    value={selectedSection.name}
                    onChange={(value) => onSectionUpdate?.(selectedSection.id, { name: value })}
                  />
                  
                  <SelectField
                    label="Section Type"
                    value={selectedSection.type}
                    onChange={(value) => onSectionUpdate?.(selectedSection.id, { type: value })}
                    options={[
                      { value: 'header', label: 'Header' },
                      { value: 'main', label: 'Main Content' },
                      { value: 'sidebar', label: 'Sidebar' },
                      { value: 'footer', label: 'Footer' },
                      { value: 'custom', label: 'Custom' }
                    ]}
                  />
                  
                  <BooleanField
                    label="Visible"
                    value={selectedSection.isVisible}
                    onChange={(value) => onSectionUpdate?.(selectedSection.id, { isVisible: value })}
                  />
                </FieldGroup>
              </FieldSection>
            )}

            {showBlockProperties && selectedBlock && (
              <FieldSection title="Block Properties" icon={<Code className="w-4 h-4" />}>
                <FieldGroup>
                  <TextField
                    label="Block Name"
                    value={selectedBlock.name}
                    onChange={(value) => onBlockUpdate?.(selectedBlock.id, { name: value })}
                  />
                  
                  <SelectField
                    label="Block Type"
                    value={selectedBlock.type}
                    onChange={(value) => onBlockUpdate?.(selectedBlock.id, { type: value })}
                    options={[
                      { value: 'logo', label: 'Logo' },
                      { value: 'navigation', label: 'Navigation' },
                      { value: 'content', label: 'Content' },
                      { value: 'widget', label: 'Widget' },
                      { value: 'custom', label: 'Custom' }
                    ]}
                  />
                  
                  <BooleanField
                    label="Visible"
                    value={selectedBlock.isVisible}
                    onChange={(value) => onBlockUpdate?.(selectedBlock.id, { isVisible: value })}
                  />
                </FieldGroup>
              </FieldSection>
            )}
          </TabsContent>

          <TabsContent value="metadata" className="m-0 p-3 space-y-3">
            <FieldSection title="SEO Metadata" icon={<Search className="w-4 h-4" />}>
              <FieldGroup>
                <ObjectField
                  label="Title"
                  value={layout.nextjs.metadata?.title || ''}
                  onChange={(value) => handleMetadataUpdate('title', value)}
                  schema={{
                    type: 'object',
                    properties: {
                      default: { type: 'string', title: 'Default Title' },
                      template: { type: 'string', title: 'Title Template' }
                    }
                  }}
                />
                
                <TextareaField
                  label="Description"
                  value={layout.nextjs.metadata?.description || ''}
                  onChange={(value) => handleMetadataUpdate('description', value)}
                  placeholder="Page description for SEO"
                  rows={3}
                />
                
                <TextField
                  label="Keywords (comma-separated)"
                  value={layout.nextjs.metadata?.keywords?.join(', ') || ''}
                  onChange={(value) => handleMetadataUpdate('keywords', value.split(',').map(k => k.trim()))}
                  placeholder="keyword1, keyword2, keyword3"
                />
              </FieldGroup>
            </FieldSection>

            <FieldSection title="Open Graph" icon={<Globe className="w-4 h-4" />}>
              <FieldGroup>
                <TextField
                  label="OG Title"
                  value={layout.nextjs.metadata?.openGraph?.title || ''}
                  onChange={(value) => handleMetadataUpdate('openGraph', {
                    ...layout.nextjs.metadata?.openGraph,
                    title: value
                  })}
                />
                
                <TextareaField
                  label="OG Description"
                  value={layout.nextjs.metadata?.openGraph?.description || ''}
                  onChange={(value) => handleMetadataUpdate('openGraph', {
                    ...layout.nextjs.metadata?.openGraph,
                    description: value
                  })}
                  rows={2}
                />
              </FieldGroup>
            </FieldSection>
          </TabsContent>

          <TabsContent value="advanced" className="m-0 p-3 space-y-3">
            <FieldSection title="Code Generation" icon={<Code className="w-4 h-4" />}>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">TypeScript</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">App Router</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Tailwind CSS</span>
                  <Badge variant="default">Enabled</Badge>
                </div>
              </div>
            </FieldSection>

            <FieldSection title="Export Options" icon={<Copy className="w-4 h-4" />}>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <FileCode className="w-4 h-4 mr-2" />
                  Export Layout Code
                </Button>
                
                <Button variant="outline" className="w-full justify-start">
                  <Copy className="w-4 h-4 mr-2" />
                  Copy to Clipboard
                </Button>
                
                <Button variant="outline" className="w-full justify-start">
                  <Save className="w-4 h-4 mr-2" />
                  Save as Template
                </Button>
              </div>
            </FieldSection>
          </TabsContent>
        </ScrollArea>
      </Tabs>
    </div>
  )
}
