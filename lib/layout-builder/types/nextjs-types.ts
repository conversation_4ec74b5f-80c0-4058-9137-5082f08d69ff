// NextJS Layout Builder Type Definitions
// Extends the base layout builder types with Next.js App Router specific functionality

import { Layout, LayoutSection, LayoutBlock } from '../types'

export interface NextJSLayout extends Layout {
  // Next.js specific properties
  nextjs: {
    type: NextJSLayoutType
    route: string // e.g., 'app/layout.tsx', 'app/(dashboard)/layout.tsx'
    metadata?: NextJSMetadata
    imports: string[]
    exports: NextJSExport[]
    appRouterFeatures: AppRouterFeature[]
  }
}

export type NextJSLayoutType = 'root' | 'nested' | 'template' | 'group'

export interface NextJSMetadata {
  title?: string | { default: string; template: string }
  description?: string
  keywords?: string[]
  authors?: Array<{ name: string; url?: string }>
  creator?: string
  publisher?: string
  openGraph?: {
    type?: string
    locale?: string
    url?: string
    siteName?: string
    title?: string
    description?: string
    images?: Array<{
      url: string
      width?: number
      height?: number
      alt?: string
    }>
  }
  twitter?: {
    card?: string
    site?: string
    creator?: string
  }
  robots?: {
    index?: boolean
    follow?: boolean
    googleBot?: Record<string, any>
  }
  verification?: {
    google?: string
    yandex?: string
    yahoo?: string
    other?: Record<string, string>
  }
}

export interface NextJSExport {
  name: string
  type: 'function' | 'const' | 'default'
  isAsync: boolean
  parameters?: string[]
  returnType?: string
}

export type AppRouterFeature = 
  | 'metadata'
  | 'loading'
  | 'error'
  | 'not-found'
  | 'global-error'
  | 'template'
  | 'parallel-routes'
  | 'intercepting-routes'
  | 'route-groups'

export interface NextJSSection extends LayoutSection {
  // Next.js specific section properties
  nextjs: {
    component: NextJSComponent
    slot?: string // For parallel routes
    conditional?: NextJSConditional
  }
}

export interface NextJSComponent {
  name: string
  path: string
  imports: string[]
  props: NextJSComponentProp[]
  children?: boolean
  isAsync?: boolean
  isClientComponent?: boolean
}

export interface NextJSComponentProp {
  name: string
  type: string
  required: boolean
  defaultValue?: any
  description?: string
}

export interface NextJSConditional {
  type: 'route' | 'device' | 'auth' | 'feature'
  condition: string
  fallback?: NextJSComponent
}

export interface NextJSBlock extends LayoutBlock {
  // Next.js specific block properties
  nextjs: {
    component: NextJSComponent
    hydration: 'static' | 'dynamic' | 'client'
    caching?: NextJSCaching
  }
}

export interface NextJSCaching {
  revalidate?: number | false
  tags?: string[]
  dynamic?: 'auto' | 'force-dynamic' | 'error' | 'force-static'
}

// Layout generation parameters
export interface NextJSLayoutGenerationParams {
  name: string
  description: string
  type: NextJSLayoutType
  route: string
  features: AppRouterFeature[]
  styling: 'modern' | 'minimal' | 'bold' | 'elegant' | 'playful'
  sections: NextJSSectionConfig[]
  metadata?: Partial<NextJSMetadata>
  responsive: boolean
  accessibility: boolean
  seo: boolean
}

export interface NextJSSectionConfig {
  type: 'header' | 'main' | 'sidebar' | 'footer' | 'custom'
  name: string
  enabled: boolean
  template?: string
  props?: Record<string, any>
}

// Code generation types
export interface NextJSCodeGenerationResult {
  layout: {
    path: string
    code: string
  }
  components: Array<{
    name: string
    path: string
    code: string
  }>
  types?: {
    path: string
    code: string
  }
  styles?: {
    path: string
    code: string
  }
}

// Template types
export interface NextJSLayoutTemplate {
  id: string
  name: string
  description: string
  category: 'website' | 'admin' | 'landing' | 'blog' | 'ecommerce' | 'dashboard'
  type: NextJSLayoutType
  preview: string
  sections: NextJSSectionConfig[]
  features: AppRouterFeature[]
  metadata: Partial<NextJSMetadata>
  tags: string[]
}

// Builder state extensions
export interface NextJSLayoutBuilderState {
  // Current NextJS layout being edited
  nextjsLayout: NextJSLayout | null
  
  // Code generation
  generatedCode: NextJSCodeGenerationResult | null
  isGeneratingCode: boolean
  
  // Templates
  availableTemplates: NextJSLayoutTemplate[]
  selectedTemplate: NextJSLayoutTemplate | null
  
  // Preview
  previewUrl: string | null
  isPreviewLoading: boolean
  
  // Export
  exportFormat: 'typescript' | 'javascript'
  includeTypes: boolean
  includeStyles: boolean
}

// Action types for NextJS layout builder
export type NextJSLayoutBuilderAction =
  | { type: 'SET_NEXTJS_LAYOUT'; payload: NextJSLayout }
  | { type: 'UPDATE_NEXTJS_METADATA'; payload: Partial<NextJSMetadata> }
  | { type: 'ADD_NEXTJS_SECTION'; payload: NextJSSectionConfig }
  | { type: 'UPDATE_NEXTJS_SECTION'; payload: { id: string; updates: Partial<NextJSSection> } }
  | { type: 'DELETE_NEXTJS_SECTION'; payload: string }
  | { type: 'SET_GENERATED_CODE'; payload: NextJSCodeGenerationResult }
  | { type: 'SET_GENERATING_CODE'; payload: boolean }
  | { type: 'SET_SELECTED_TEMPLATE'; payload: NextJSLayoutTemplate | null }
  | { type: 'SET_PREVIEW_URL'; payload: string | null }
  | { type: 'SET_PREVIEW_LOADING'; payload: boolean }
  | { type: 'SET_EXPORT_FORMAT'; payload: 'typescript' | 'javascript' }
  | { type: 'SET_INCLUDE_TYPES'; payload: boolean }
  | { type: 'SET_INCLUDE_STYLES'; payload: boolean }

// Utility types
export interface NextJSRouteInfo {
  segments: string[]
  isDynamic: boolean
  isGroup: boolean
  isParallel: boolean
  isIntercepting: boolean
}

export interface NextJSFileStructure {
  path: string
  type: 'layout' | 'page' | 'loading' | 'error' | 'not-found' | 'template' | 'component'
  content: string
  dependencies: string[]
}
