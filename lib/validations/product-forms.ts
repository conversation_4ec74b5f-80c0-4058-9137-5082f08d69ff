import * as z from 'zod'

// Shared validation rules
const requiredString = (fieldName: string, maxLength = 255) =>
  z.string()
    .min(1, `${fieldName} is required`)
    .max(maxLength, `${fieldName} is too long (max ${maxLength} characters)`)
    .refine(val => val.trim().length > 0, `${fieldName} cannot be empty`)

const optionalString = (maxLength = 255) =>
  z.string()
    .max(maxLength, `Field is too long (max ${maxLength} characters)`)
    .optional()

const positiveNumber = (fieldName: string, min = 0.01, max = 999999.99) =>
  z.number()
    .min(min, `${fieldName} must be at least ${min}`)
    .max(max, `${fieldName} cannot exceed ${max}`)

const optionalPositiveNumber = (max = 999999.99) =>
  z.number()
    .min(0, 'Value cannot be negative')
    .max(max, `Value cannot exceed ${max}`)
    .optional()
    .nullable()

// URL slug validation
const slugValidation = z.string()
  .optional()
  .refine(
    val => !val || /^[a-z0-9-]+$/.test(val),
    'Slug can only contain lowercase letters, numbers, and hyphens'
  )

// General form schema
export const generalFormSchema = z.object({
  title: requiredString('Product title'),
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(5000, 'Description is too long'),
  slug: slugValidation,
  vendor: optionalString(100),
  productType: optionalString(100),
  status: z.enum(['active', 'draft', 'archived']),
  weight: optionalPositiveNumber(),
  weightUnit: z.string().optional(),
  requiresShipping: z.boolean(),
  isTaxable: z.boolean(),
})

// Pricing form schema with cross-field validation
export const pricingFormSchema = z.object({
  price: positiveNumber('Price'),
  compareAtPrice: optionalPositiveNumber(),
  costPerItem: optionalPositiveNumber(),
}).refine(
  (data) => {
    if (data.compareAtPrice && data.compareAtPrice > 0) {
      return data.compareAtPrice > data.price
    }
    return true
  },
  {
    message: 'Compare price should be higher than regular price',
    path: ['compareAtPrice']
  }
).refine(
  (data) => {
    if (data.costPerItem && data.costPerItem > 0) {
      return data.costPerItem < data.price
    }
    return true
  },
  {
    message: 'Cost should be lower than selling price for profit',
    path: ['costPerItem']
  }
)

// Inventory form schema
export const inventoryFormSchema = z.object({
  trackQuantity: z.boolean(),
  inventoryQuantity: z.number()
    .min(0, 'Inventory must be non-negative')
    .int('Inventory must be a whole number'),
  continueSellingWhenOutOfStock: z.boolean(),
})

// Media form schema
export const mediaFormSchema = z.object({
  images: z.array(z.object({
    url: z.string().url('Invalid image URL'),
    altText: z.string(),
    position: z.number()
  })).max(10, 'Maximum 10 images allowed').default([])
})

// SEO form schema
export const seoFormSchema = z.object({
  seoTitle: z.string()
    .max(60, 'SEO title should be under 60 characters')
    .optional(),
  seoDescription: z.string()
    .max(160, 'SEO description should be under 160 characters')
    .optional(),
  seoKeywords: z.array(z.string())
    .max(10, 'Maximum 10 keywords allowed')
    .optional(),
})

// Type exports
export type GeneralFormData = z.infer<typeof generalFormSchema>
export type PricingFormData = z.infer<typeof pricingFormSchema>
export type InventoryFormData = z.infer<typeof inventoryFormSchema>
export type MediaFormData = z.infer<typeof mediaFormSchema>
export type SeoFormData = z.infer<typeof seoFormSchema>

// Validation helpers
export const validateProductCompletion = (product: any) => {
  const completionStatus = {
    general: !!(product?.title && product?.description),
    pricing: !!(product?.price?.amount && product?.price.amount > 0),
    inventory: true, // Always considered complete
    variants: true, // Optional, so always complete
    media: !!(product?.images && product?.images.length > 0),
    seo: !!(product?.seo?.title || product?.seo?.description)
  }

  const completedCount = Object.values(completionStatus).filter(Boolean).length
  const totalCount = Object.keys(completionStatus).length
  const percentage = (completedCount / totalCount) * 100

  return {
    completionStatus,
    completedCount,
    totalCount,
    percentage: Math.round(percentage),
    isComplete: completedCount === totalCount
  }
}

// Form field validation helpers
export const getFieldError = (errors: any, fieldName: string): string | undefined => {
  return errors[fieldName]?.message
}

export const hasFieldError = (errors: any, fieldName: string): boolean => {
  return !!errors[fieldName]
}

// Character count helper
export const getCharacterCount = (value: string | undefined, maxLength: number) => {
  const current = value?.length || 0
  return {
    current,
    max: maxLength,
    remaining: maxLength - current,
    isOverLimit: current > maxLength,
    isNearLimit: current > maxLength * 0.8
  }
}

// Price calculation helpers
export const calculateProfitMetrics = (price: number, cost: number, comparePrice?: number) => {
  const profit = price - cost
  const profitMargin = price > 0 ? (profit / price) * 100 : 0
  const markup = cost > 0 ? (profit / cost) * 100 : 0
  const discount = comparePrice && comparePrice > 0 ? ((comparePrice - price) / comparePrice) * 100 : 0

  return {
    profit,
    profitMargin,
    markup,
    discount: discount || 0,
    savings: comparePrice ? comparePrice - price : 0,
    isProfit: profit > 0,
    isProfitable: profitMargin > 0,
    profitStatus: profitMargin > 50 ? 'excellent' : profitMargin > 20 ? 'good' : profitMargin > 0 ? 'low' : 'loss'
  }
}

// Form state helpers
export const createFormDefaults = (product?: any) => ({
  general: {
    title: product?.title || '',
    description: product?.description || '',
    slug: product?.slug || '',
    vendor: product?.vendor || '',
    productType: product?.productType || '',
    status: product?.status || 'draft',
    weight: product?.weight || null,
    weightUnit: product?.weightUnit || 'kg',
    requiresShipping: product?.requiresShipping ?? true,
    isTaxable: product?.isTaxable ?? true,
  },
  pricing: {
    price: product?.price?.amount || 0,
    compareAtPrice: product?.compareAtPrice?.amount || null,
    costPerItem: product?.costPerItem?.amount || null,
  },
  inventory: {
    trackQuantity: product?.trackQuantity ?? true,
    inventoryQuantity: product?.inventoryQuantity || 0,
    continueSellingWhenOutOfStock: product?.continueSellingWhenOutOfStock ?? false,
  },
  media: {
    images: product?.images || []
  },
  seo: {
    seoTitle: product?.seo?.title || '',
    seoDescription: product?.seo?.description || '',
    seoKeywords: product?.seo?.keywords || [],
  }
})

// Auto-slug generation
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

// Validation error formatting
export const formatValidationErrors = (errors: any): string[] => {
  return Object.entries(errors).map(([field, error]: [string, any]) => {
    const fieldName = field
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .replace(/([a-z])([A-Z])/g, '$1 $2')
    
    return `${fieldName}: ${error.message}`
  })
}
