import * as z from 'zod'

// Shared validation rules
const requiredString = (fieldName: string, maxLength = 255) =>
  z.string()
    .min(1, `${fieldName} is required`)
    .max(maxLength, `${fieldName} is too long (max ${maxLength} characters)`)
    .refine(val => val.trim().length > 0, `${fieldName} cannot be empty`)

const optionalString = (maxLength = 255) =>
  z.string()
    .max(maxLength, `Field is too long (max ${maxLength} characters)`)
    .optional()

const positiveNumber = (fieldName: string, min = 0.01, max = 999999.99) =>
  z.number()
    .min(min, `${fieldName} must be at least ${min}`)
    .max(max, `${fieldName} cannot exceed ${max}`)

const optionalPositiveNumber = (max = 999999.99) =>
  z.number()
    .min(0, 'Value cannot be negative')
    .max(max, `Value cannot exceed ${max}`)
    .optional()
    .nullable()

// Email validation
const emailValidation = z.string().email('Invalid email address')

// Phone validation
const phoneValidation = z.string()
  .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format')
  .optional()

// URL validation
const urlValidation = z.string().url('Invalid URL format').optional()

// Address validation
const addressSchema = z.object({
  street: requiredString('Street address'),
  city: requiredString('City'),
  state: requiredString('State/Province'),
  postalCode: requiredString('Postal code'),
  country: requiredString('Country'),
})

// Customer form schemas
export const customerFormSchema = z.object({
  firstName: requiredString('First name', 50),
  lastName: requiredString('Last name', 50),
  email: emailValidation,
  phone: phoneValidation,
  dateOfBirth: z.date().optional(),
  status: z.enum(['active', 'inactive', 'suspended']),
  tags: z.array(z.string()).optional(),
  notes: optionalString(1000),
  billingAddress: addressSchema.optional(),
  shippingAddress: addressSchema.optional(),
  marketingOptIn: z.boolean(),
})

// Order form schemas
export const orderFormSchema = z.object({
  customerId: z.string().min(1, 'Customer is required'),
  status: z.enum(['pending', 'processing', 'shipped', 'delivered', 'cancelled']),
  items: z.array(z.object({
    productId: z.string().min(1, 'Product is required'),
    variantId: z.string().optional(),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
    price: positiveNumber('Price'),
  })).min(1, 'At least one item is required'),
  shippingAddress: addressSchema,
  billingAddress: addressSchema.optional(),
  notes: optionalString(500),
  discountCode: optionalString(50),
  shippingMethod: z.string().optional(),
})

// Inventory form schemas
export const inventoryFormSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  variantId: z.string().optional(),
  quantity: z.number().int('Quantity must be a whole number'),
  location: requiredString('Location'),
  reorderPoint: z.number().min(0, 'Reorder point cannot be negative').optional(),
  maxStock: z.number().min(0, 'Max stock cannot be negative').optional(),
  cost: optionalPositiveNumber(),
  notes: optionalString(500),
})

// Fulfillment form schemas
export const fulfillmentFormSchema = z.object({
  orderId: z.string().min(1, 'Order is required'),
  items: z.array(z.object({
    orderItemId: z.string().min(1, 'Order item is required'),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
  })).min(1, 'At least one item is required'),
  carrier: requiredString('Carrier'),
  trackingNumber: optionalString(100),
  trackingUrl: urlValidation,
  shippingCost: optionalPositiveNumber(),
  estimatedDelivery: z.date().optional(),
  notes: optionalString(500),
})

// Category form schemas
export const categoryFormSchema = z.object({
  name: requiredString('Category name'),
  slug: z.string()
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .optional(),
  description: optionalString(1000),
  parentId: z.string().optional(),
  image: urlValidation,
  isActive: z.boolean(),
  sortOrder: z.number().min(0, 'Sort order cannot be negative').optional(),
  seo: z.object({
    title: optionalString(60),
    description: optionalString(160),
    keywords: z.array(z.string()).optional(),
  }).optional(),
})

// Collection form schemas
export const collectionFormSchema = z.object({
  name: requiredString('Collection name'),
  slug: z.string()
    .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
    .optional(),
  description: optionalString(1000),
  image: urlValidation,
  isActive: z.boolean(),
  sortOrder: z.number().min(0, 'Sort order cannot be negative').optional(),
  conditions: z.array(z.object({
    field: z.string(),
    operator: z.string(),
    value: z.string(),
  })).optional(),
  seo: z.object({
    title: optionalString(60),
    description: optionalString(160),
    keywords: z.array(z.string()).optional(),
  }).optional(),
})

// Discount form schemas
export const discountFormSchema = z.object({
  code: requiredString('Discount code', 50),
  name: requiredString('Discount name'),
  description: optionalString(500),
  type: z.enum(['percentage', 'fixed_amount', 'free_shipping']),
  value: positiveNumber('Discount value'),
  minimumOrderAmount: optionalPositiveNumber(),
  maximumDiscountAmount: optionalPositiveNumber(),
  usageLimit: z.number().min(1, 'Usage limit must be at least 1').optional(),
  usagePerCustomer: z.number().min(1, 'Usage per customer must be at least 1').optional(),
  startDate: z.date(),
  endDate: z.date().optional(),
  isActive: z.boolean(),
  applicableProducts: z.array(z.string()).optional(),
  applicableCategories: z.array(z.string()).optional(),
}).refine(
  (data) => {
    if (data.endDate) {
      return data.endDate > data.startDate
    }
    return true
  },
  {
    message: 'End date must be after start date',
    path: ['endDate']
  }
)

// Type exports
export type CustomerFormData = z.infer<typeof customerFormSchema>
export type OrderFormData = z.infer<typeof orderFormSchema>
export type InventoryFormData = z.infer<typeof inventoryFormSchema>
export type FulfillmentFormData = z.infer<typeof fulfillmentFormSchema>
export type CategoryFormData = z.infer<typeof categoryFormSchema>
export type CollectionFormData = z.infer<typeof collectionFormSchema>
export type DiscountFormData = z.infer<typeof discountFormSchema>

// Validation helpers
export const validateFormData = <T>(schema: z.ZodSchema<T>, data: unknown): { success: boolean; data?: T; errors?: string[] } => {
  try {
    const validatedData = schema.parse(data)
    return { success: true, data: validatedData }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      return { success: false, errors }
    }
    return { success: false, errors: ['Validation failed'] }
  }
}

// Form field validation helpers
export const getFieldError = (errors: any, fieldName: string): string | undefined => {
  return errors[fieldName]?.message
}

export const hasFieldError = (errors: any, fieldName: string): boolean => {
  return !!errors[fieldName]
}

// Character count helper
export const getCharacterCount = (value: string | undefined, maxLength: number) => {
  const current = value?.length || 0
  return {
    current,
    max: maxLength,
    remaining: maxLength - current,
    isOverLimit: current > maxLength,
    isNearLimit: current > maxLength * 0.8
  }
}

// Auto-slug generation
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

// Validation error formatting
export const formatValidationErrors = (errors: any): string[] => {
  return Object.entries(errors).map(([field, error]: [string, any]) => {
    const fieldName = field
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .replace(/([a-z])([A-Z])/g, '$1 $2')
    
    return `${fieldName}: ${error.message}`
  })
}

// Form state helpers
export const createFormDefaults = (type: string, data?: any) => {
  const defaults: Record<string, any> = {
    customer: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      status: 'active',
      marketingOptIn: false,
      tags: [],
      notes: '',
    },
    order: {
      customerId: '',
      status: 'pending',
      items: [],
      notes: '',
      discountCode: '',
    },
    inventory: {
      productId: '',
      quantity: 0,
      location: '',
      reorderPoint: 10,
      maxStock: 100,
      notes: '',
    },
    fulfillment: {
      orderId: '',
      items: [],
      carrier: '',
      trackingNumber: '',
      notes: '',
    },
    category: {
      name: '',
      description: '',
      isActive: true,
      sortOrder: 0,
    },
    collection: {
      name: '',
      description: '',
      isActive: true,
      sortOrder: 0,
      conditions: [],
    },
    discount: {
      code: '',
      name: '',
      description: '',
      type: 'percentage',
      value: 0,
      startDate: new Date(),
      isActive: true,
    }
  }

  return { ...defaults[type], ...data }
}

// Status helpers
export const getStatusColor = (status: string, type: string): string => {
  const statusColors: Record<string, Record<string, string>> = {
    order: {
      pending: 'warning',
      processing: 'default',
      shipped: 'secondary',
      delivered: 'success',
      cancelled: 'destructive'
    },
    customer: {
      active: 'success',
      inactive: 'secondary',
      suspended: 'destructive'
    },
    fulfillment: {
      pending: 'warning',
      'in-transit': 'default',
      delivered: 'success',
      failed: 'destructive'
    }
  }

  return statusColors[type]?.[status] || 'default'
}

// Currency formatting
export const formatCurrency = (amount: number, currency = 'ZAR'): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency,
  }).format(amount)
}

// Date formatting
export const formatDate = (date: Date | string): string => {
  const d = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat('en-ZA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(d)
}

// Percentage formatting
export const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`
}
