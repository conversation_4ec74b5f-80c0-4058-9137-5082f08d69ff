// Enhanced Product Variant Service
import { PrismaClient } from '@prisma/client'
import { ApiResponse, Money } from '../../types/base'
import {
  ProductVariant,
  CreateVariantInput as BaseCreateVariantInput
} from '../../types/product'

const prisma = new PrismaClient()

// Extended interfaces for service-specific needs
export interface CreateVariantInput extends Omit<BaseCreateVariantInput, 'price' | 'compareAtPrice' | 'costPerItem'> {
  productId: string
  price: number | Money
  compareAtPrice?: number | Money
  costPerItem?: number | Money
  currency?: string
  imageId?: string
  available?: boolean
  position?: number
}

export interface UpdateVariantInput extends Partial<CreateVariantInput> {
  id: string
}

export class EnhancedVariantService {
  private extractAmount(value: number | Money | undefined): number | undefined {
    if (value === undefined) return undefined
    if (typeof value === 'number') return value
    if (typeof value === 'object' && 'amount' in value) return (value as any).amount
    return Number(value)
  }
  async getVariantsByProduct(productId: string): Promise<ApiResponse<ProductVariant[]>> {
    try {
      const variants = await prisma.productVariant.findMany({
        where: { productId },
        orderBy: { position: 'asc' },
        include: {
          options: true
        }
      })

      return {
        success: true,
        data: variants.map(this.mapToProductVariant)
      }
    } catch (error) {
      console.error('Get variants error:', error)
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch product variants'
        }
      }
    }
  }

  async getVariantById(id: string): Promise<ApiResponse<ProductVariant>> {
    try {
      const variant = await prisma.productVariant.findUnique({
        where: { id }
      })

      if (!variant) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product variant not found'
          }
        }
      }

      return {
        success: true,
        data: this.mapToProductVariant(variant)
      }
    } catch (error) {
      console.error('Get variant error:', error)
      return {
        success: false,
        error: {
          code: 'FETCH_ERROR',
          message: 'Failed to fetch product variant'
        }
      }
    }
  }

  async createVariant(input: CreateVariantInput): Promise<ApiResponse<ProductVariant>> {
    try {
      // Validate input
      const validation = this.validateVariantInput(input)
      if (!validation.isValid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: validation.error || 'Invalid variant data'
          }
        }
      }

      // Check if product exists
      const product = await prisma.product.findUnique({
        where: { id: input.productId }
      })

      if (!product) {
        return {
          success: false,
          error: {
            code: 'PRODUCT_NOT_FOUND',
            message: 'Product not found'
          }
        }
      }

      // Generate SKU if not provided
      const sku = input.sku || await this.generateSKU(input.productId)

      // Check for duplicate SKU
      const existingSku = await prisma.productVariant.findFirst({
        where: { sku }
      })

      if (existingSku) {
        return {
          success: false,
          error: {
            code: 'DUPLICATE_SKU',
            message: 'A variant with this SKU already exists'
          }
        }
      }

      // Get next position
      const lastVariant = await prisma.productVariant.findFirst({
        where: { productId: input.productId },
        orderBy: { position: 'desc' }
      })
      const position = (lastVariant?.position || 0) + 1

      // Handle price conversion
      const price = this.extractAmount(input.price)!
      const compareAtPrice = this.extractAmount(input.compareAtPrice)
      const costPerItem = this.extractAmount(input.costPerItem)

      const variant = await prisma.productVariant.create({
        data: {
          productId: input.productId,
          sku,
          title: input.title,
          price,
          compareAtPrice,
          currency: input.currency || 'ZAR',
          weight: input.weight,
          weightUnit: input.weightUnit,
          inventoryQuantity: input.inventoryQuantity || 0,
          inventoryPolicy: input.inventoryPolicy || 'deny',
          fulfillmentService: input.fulfillmentService || 'manual',
          inventoryManagement: input.inventoryManagement ?? true,
          imageId: input.imageId,
          available: input.available ?? true,
          position,
          barcode: input.barcode,
          continueSellingWhenOutOfStock: input.continueSellingWhenOutOfStock ?? false,
          costPerItem,
          metafields: input.metafields,
          requiresShipping: input.requiresShipping ?? true,
          taxable: input.taxable ?? true,
          trackQuantity: input.trackQuantity ?? true,
          options: {
            create: input.options?.map(option => ({
              name: option.name,
              value: option.value
            })) || []
          }
        },
        include: {
          options: true
        }
      })

      return {
        success: true,
        data: this.mapToProductVariant(variant)
      }
    } catch (error) {
      console.error('Create variant error:', error)
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create product variant'
        }
      }
    }
  }

  async updateVariant(input: UpdateVariantInput): Promise<ApiResponse<ProductVariant>> {
    try {
      // Check if variant exists
      const existing = await prisma.productVariant.findUnique({
        where: { id: input.id }
      })

      if (!existing) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product variant not found'
          }
        }
      }

      // Validate input if provided
      if (input.title || input.price !== undefined) {
        const validation = this.validateVariantInput({
          productId: existing.productId,
          title: input.title || existing.title,
          price: input.price !== undefined ? input.price : Number(existing.price)
        })
        
        if (!validation.isValid) {
          return {
            success: false,
            error: {
              code: 'VALIDATION_ERROR',
              message: validation.error || 'Invalid variant data'
            }
          }
        }
      }

      // Check for duplicate SKU if SKU is being changed
      if (input.sku && input.sku !== existing.sku) {
        const duplicate = await prisma.productVariant.findFirst({
          where: {
            sku: input.sku,
            id: { not: input.id }
          }
        })

        if (duplicate) {
          return {
            success: false,
            error: {
              code: 'DUPLICATE_SKU',
              message: 'A variant with this SKU already exists'
            }
          }
        }
      }

      const updateData: any = {}
      if (input.sku !== undefined) updateData.sku = input.sku
      if (input.title !== undefined) updateData.title = input.title
      if (input.price !== undefined) updateData.price = input.price
      if (input.compareAtPrice !== undefined) updateData.compareAtPrice = input.compareAtPrice
      if (input.costPerItem !== undefined) updateData.costPerItem = input.costPerItem
      if (input.weight !== undefined) updateData.weight = input.weight
      if (input.weightUnit !== undefined) updateData.weightUnit = input.weightUnit
      if (input.requiresShipping !== undefined) updateData.requiresShipping = input.requiresShipping
      if (input.taxable !== undefined) updateData.taxable = input.taxable
      if (input.inventoryQuantity !== undefined) updateData.inventoryQuantity = input.inventoryQuantity
      if (input.inventoryPolicy !== undefined) updateData.inventoryPolicy = input.inventoryPolicy
      if (input.fulfillmentService !== undefined) updateData.fulfillmentService = input.fulfillmentService
      if (input.inventoryManagement !== undefined) updateData.inventoryManagement = input.inventoryManagement
      if (input.available !== undefined) updateData.available = input.available
      if (input.barcode !== undefined) updateData.barcode = input.barcode
      if (input.continueSellingWhenOutOfStock !== undefined) updateData.continueSellingWhenOutOfStock = input.continueSellingWhenOutOfStock
      if (input.metafields !== undefined) updateData.metafields = input.metafields
      if (input.trackQuantity !== undefined) updateData.trackQuantity = input.trackQuantity
      if (input.imageId !== undefined) updateData.imageId = input.imageId

      // Handle options update separately if provided
      if (input.options !== undefined) {
        // Delete existing options and create new ones
        await prisma.productVariantOption.deleteMany({
          where: { variantId: input.id }
        })
        
        if (input.options.length > 0) {
          updateData.options = {
            create: input.options.map(option => ({
              name: option.name,
              value: option.value
            }))
          }
        }
      }

      const variant = await prisma.productVariant.update({
        where: { id: input.id },
        data: updateData,
        include: {
          options: true
        }
      })

      return {
        success: true,
        data: this.mapToProductVariant(variant)
      }
    } catch (error) {
      console.error('Update variant error:', error)
      return {
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to update product variant'
        }
      }
    }
  }

  async deleteVariant(id: string): Promise<ApiResponse<boolean>> {
    try {
      // Check if variant exists
      const existing = await prisma.productVariant.findUnique({
        where: { id }
      })

      if (!existing) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Product variant not found'
          }
        }
      }

      await prisma.productVariant.delete({
        where: { id }
      })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete variant error:', error)
      return {
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: 'Failed to delete product variant'
        }
      }
    }
  }

  async createVariants(productId: string, variants: CreateVariantInput[]): Promise<ApiResponse<ProductVariant[]>> {
    try {
      // Check if product exists
      const product = await prisma.product.findUnique({
        where: { id: productId }
      })

      if (!product) {
        return {
          success: false,
          error: {
            code: 'PRODUCT_NOT_FOUND',
            message: 'Product not found'
          }
        }
      }

      const createdVariants: ProductVariant[] = []
      const errors: Array<{ index: number; error: string }> = []

      // Get starting position
      const lastVariant = await prisma.productVariant.findFirst({
        where: { productId },
        orderBy: { position: 'desc' }
      })
      let position = (lastVariant?.position || 0) + 1

      for (let i = 0; i < variants.length; i++) {
        const variantInput = { ...variants[i], productId }

        try {
          // Validate each variant
          const validation = this.validateVariantInput(variantInput)
          if (!validation.isValid) {
            errors.push({ index: i, error: validation.error || 'Invalid variant data' })
            continue
          }

          // Generate SKU if not provided
          const sku = variantInput.sku || await this.generateSKU(productId)

          // Check for duplicate SKU
          const existingSku = await prisma.productVariant.findFirst({
            where: { sku }
          })

          if (existingSku) {
            errors.push({ index: i, error: `Duplicate SKU: ${sku}` })
            continue
          }

          // Handle price conversion
          const price = this.extractAmount(variantInput.price)!
          const compareAtPrice = this.extractAmount(variantInput.compareAtPrice)
          const costPerItem = this.extractAmount(variantInput.costPerItem)

          const variant = await prisma.productVariant.create({
            data: {
              productId,
              sku,
              title: variantInput.title,
              price,
              compareAtPrice,
              currency: variantInput.currency || 'ZAR',
              weight: variantInput.weight,
              weightUnit: variantInput.weightUnit,
              inventoryQuantity: variantInput.inventoryQuantity || 0,
              inventoryPolicy: variantInput.inventoryPolicy || 'deny',
              fulfillmentService: variantInput.fulfillmentService || 'manual',
              inventoryManagement: variantInput.inventoryManagement ?? true,
              imageId: variantInput.imageId,
              available: variantInput.available ?? true,
              position: position++,
              barcode: variantInput.barcode,
              continueSellingWhenOutOfStock: variantInput.continueSellingWhenOutOfStock ?? false,
              costPerItem,
              metafields: variantInput.metafields,
              requiresShipping: variantInput.requiresShipping ?? true,
              taxable: variantInput.taxable ?? true,
              trackQuantity: variantInput.trackQuantity ?? true,
              options: {
                create: variantInput.options?.map(option => ({
                  name: option.name,
                  value: option.value
                })) || []
              }
            },
            include: {
              options: true
            }
          })

          createdVariants.push(this.mapToProductVariant(variant))
        } catch (error) {
          console.error(`Error creating variant ${i}:`, error)
          errors.push({ index: i, error: 'Failed to create variant' })
        }
      }

      if (errors.length > 0 && createdVariants.length === 0) {
        return {
          success: false,
          error: {
            code: 'CREATE_ERROR',
            message: `Failed to create variants: ${errors.map(e => `Index ${e.index}: ${e.error}`).join(', ')}`
          }
        }
      }

      return {
        success: true,
        data: createdVariants
      }
    } catch (error) {
      console.error('Create variants error:', error)
      return {
        success: false,
        error: {
          code: 'CREATE_ERROR',
          message: 'Failed to create product variants'
        }
      }
    }
  }

  async deleteVariants(variantIds: string[]): Promise<ApiResponse<boolean>> {
    try {
      if (variantIds.length === 0) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'No variant IDs provided'
          }
        }
      }

      // Check if variants exist
      const existingVariants = await prisma.productVariant.findMany({
        where: { id: { in: variantIds } }
      })

      if (existingVariants.length !== variantIds.length) {
        const foundIds = existingVariants.map(v => v.id)
        const missingIds = variantIds.filter(id => !foundIds.includes(id))
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: `Variants not found: ${missingIds.join(', ')}`
          }
        }
      }

      // Delete all variants
      await prisma.productVariant.deleteMany({
        where: { id: { in: variantIds } }
      })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete variants error:', error)
      return {
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: 'Failed to delete product variants'
        }
      }
    }
  }

  async bulkUpdateVariants(variants: UpdateVariantInput[]): Promise<ApiResponse<ProductVariant[]>> {
    try {
      if (variants.length === 0) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'No variants provided for update'
          }
        }
      }

      const updatedVariants: ProductVariant[] = []
      const errors: Array<{ id: string; error: string }> = []

      for (const variantInput of variants) {
        try {
          const result = await this.updateVariant(variantInput)
          if (result.success) {
            updatedVariants.push(result.data!)
          } else {
            errors.push({
              id: variantInput.id,
              error: result.error?.message || 'Update failed'
            })
          }
        } catch (error) {
          console.error(`Error updating variant ${variantInput.id}:`, error)
          errors.push({
            id: variantInput.id,
            error: 'Failed to update variant'
          })
        }
      }

      if (errors.length > 0 && updatedVariants.length === 0) {
        return {
          success: false,
          error: {
            code: 'UPDATE_ERROR',
            message: `Failed to update variants: ${errors.map(e => `${e.id}: ${e.error}`).join(', ')}`
          }
        }
      }

      return {
        success: true,
        data: updatedVariants
      }
    } catch (error) {
      console.error('Bulk update variants error:', error)
      return {
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to bulk update product variants'
        }
      }
    }
  }

  private async generateSKU(productId: string): Promise<string> {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { title: true }
    })

    const prefix = product?.title
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 3)
      .toUpperCase() || 'VAR'

    const timestamp = Date.now().toString().slice(-6)
    return `${prefix}-${timestamp}`
  }

  private validateVariantInput(input: Partial<CreateVariantInput>): { isValid: boolean; error?: string } {
    if (!input.productId) {
      return { isValid: false, error: 'Product ID is required' }
    }

    if (!input.title || input.title.trim().length === 0) {
      return { isValid: false, error: 'Variant title is required' }
    }

    if (input.price === undefined) {
      return { isValid: false, error: 'Price is required' }
    }

    // Handle price validation for both number and Money types
    const price = this.extractAmount(input.price)
    if (price === undefined || price < 0) {
      return { isValid: false, error: 'Price must be greater than or equal to 0' }
    }

    // Validate compare at price if provided
    if (input.compareAtPrice !== undefined) {
      const compareAtPrice = this.extractAmount(input.compareAtPrice)
      if (compareAtPrice !== undefined && compareAtPrice < price) {
        return { isValid: false, error: 'Compare at price must be greater than or equal to price' }
      }
    }

    return { isValid: true }
  }

  private mapToProductVariant(variant: any): ProductVariant {
    return {
      id: variant.id,
      productId: variant.productId,
      sku: variant.sku,
      title: variant.title,
      price: {
        amount: Number(variant.price),
        currency: variant.currency || 'ZAR'
      },
      compareAtPrice: variant.compareAtPrice ? {
        amount: Number(variant.compareAtPrice),
        currency: variant.currency || 'ZAR'
      } : undefined,
      costPerItem: variant.costPerItem ? {
        amount: Number(variant.costPerItem),
        currency: variant.currency || 'ZAR'
      } : undefined,
      weight: variant.weight ? Number(variant.weight) : undefined,
      weightUnit: variant.weightUnit,
      inventoryQuantity: variant.inventoryQuantity,
      inventoryPolicy: variant.inventoryPolicy,
      fulfillmentService: variant.fulfillmentService,
      inventoryManagement: variant.inventoryManagement,
      options: variant.options?.map((option: any) => ({
        name: option.name,
        value: option.value
      })) || [],
      image: variant.imageId ? {
        id: variant.imageId,
        url: '',
        position: 0
      } : undefined,
      available: variant.available,
      barcode: variant.barcode,
      taxable: variant.taxable,
      requiresShipping: variant.requiresShipping,
      trackQuantity: variant.trackQuantity,
      continueSellingWhenOutOfStock: variant.continueSellingWhenOutOfStock,
      metafields: variant.metafields,
      createdAt: variant.createdAt,
      updatedAt: variant.updatedAt
    }
  }
}

export const enhancedVariantService = new EnhancedVariantService()
