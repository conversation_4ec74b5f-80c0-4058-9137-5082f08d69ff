// React hooks for product variant management
'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  ProductVariant, 
  CreateVariantInput,
  UpdateVariantInput,
  ApiResponse
} from '../types'

export interface UseVariantsOptions {
  productId?: string
  autoFetch?: boolean
}

export interface UseVariantsReturn {
  variants: ProductVariant[]
  loading: boolean
  error: { code: string; message: string } | null
  fetchVariants: () => Promise<void>
  createVariant: (input: CreateVariantInput) => Promise<ProductVariant | null>
  createVariants: (inputs: CreateVariantInput[]) => Promise<ProductVariant[] | null>
  updateVariant: (input: UpdateVariantInput) => Promise<ProductVariant | null>
  updateVariants: (inputs: UpdateVariantInput[]) => Promise<ProductVariant[] | null>
  deleteVariant: (id: string) => Promise<boolean>
  deleteVariants: (ids: string[]) => Promise<boolean>
  refetch: () => Promise<void>
  clearError: () => void
}

export function useVariants({ productId, autoFetch = true }: UseVariantsOptions = {}): UseVariantsReturn {
  const [variants, setVariants] = useState<ProductVariant[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const fetchVariants = useCallback(async () => {
    if (!productId) {
      setVariants([])
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/variants`)
      const result: ApiResponse<ProductVariant[]> = await response.json()

      if (result.success && result.data) {
        setVariants(result.data)
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch variants' })
        setVariants([])
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setVariants([])
    } finally {
      setLoading(false)
    }
  }, [productId])

  const createVariant = useCallback(async (input: CreateVariantInput): Promise<ProductVariant | null> => {
    if (!productId) {
      setError({ code: 'VALIDATION_ERROR', message: 'Product ID is required' })
      return null
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/variants`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          variants: [input]
        }),
      })

      const result: ApiResponse<ProductVariant[]> = await response.json()

      if (result.success && result.data && result.data.length > 0) {
        const newVariant = result.data[0]
        setVariants(prev => [...prev, newVariant])
        return newVariant
      } else {
        setError(result.error || { code: 'CREATE_ERROR', message: 'Failed to create variant' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [productId])

  const createVariants = useCallback(async (inputs: CreateVariantInput[]): Promise<ProductVariant[] | null> => {
    if (!productId) {
      setError({ code: 'VALIDATION_ERROR', message: 'Product ID is required' })
      return null
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/variants`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          variants: inputs
        }),
      })

      const result: ApiResponse<ProductVariant[]> = await response.json()

      if (result.success && result.data) {
        setVariants(prev => [...prev, ...result.data])
        return result.data
      } else {
        setError(result.error || { code: 'CREATE_ERROR', message: 'Failed to create variants' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [productId])

  const updateVariant = useCallback(async (input: UpdateVariantInput): Promise<ProductVariant | null> => {
    if (!productId) {
      setError({ code: 'VALIDATION_ERROR', message: 'Product ID is required' })
      return null
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/variants`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          variants: [input]
        }),
      })

      const result: ApiResponse<ProductVariant[]> = await response.json()

      if (result.success && result.data && result.data.length > 0) {
        const updatedVariant = result.data[0]
        setVariants(prev => prev.map(v => v.id === updatedVariant.id ? updatedVariant : v))
        return updatedVariant
      } else {
        setError(result.error || { code: 'UPDATE_ERROR', message: 'Failed to update variant' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [productId])

  const updateVariants = useCallback(async (inputs: UpdateVariantInput[]): Promise<ProductVariant[] | null> => {
    if (!productId) {
      setError({ code: 'VALIDATION_ERROR', message: 'Product ID is required' })
      return null
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/variants`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          variants: inputs
        }),
      })

      const result: ApiResponse<ProductVariant[]> = await response.json()

      if (result.success && result.data) {
        setVariants(prev => {
          const updated = [...prev]
          result.data.forEach(updatedVariant => {
            const index = updated.findIndex(v => v.id === updatedVariant.id)
            if (index !== -1) {
              updated[index] = updatedVariant
            }
          })
          return updated
        })
        return result.data
      } else {
        setError(result.error || { code: 'UPDATE_ERROR', message: 'Failed to update variants' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [productId])

  const deleteVariant = useCallback(async (id: string): Promise<boolean> => {
    return deleteVariants([id])
  }, [])

  const deleteVariants = useCallback(async (ids: string[]): Promise<boolean> => {
    if (!productId) {
      setError({ code: 'VALIDATION_ERROR', message: 'Product ID is required' })
      return false
    }

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/variants?variantIds=${ids.join(',')}`, {
        method: 'DELETE',
      })

      const result: ApiResponse<boolean> = await response.json()

      if (result.success) {
        setVariants(prev => prev.filter(v => !ids.includes(v.id)))
        return true
      } else {
        setError(result.error || { code: 'DELETE_ERROR', message: 'Failed to delete variants' })
        return false
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [productId])

  const refetch = useCallback(async () => {
    await fetchVariants()
  }, [fetchVariants])

  // Auto-fetch variants when productId changes
  useEffect(() => {
    if (autoFetch && productId) {
      fetchVariants()
    }
  }, [productId, autoFetch, fetchVariants])

  return {
    variants,
    loading,
    error,
    fetchVariants,
    createVariant,
    createVariants,
    updateVariant,
    updateVariants,
    deleteVariant,
    deleteVariants,
    refetch,
    clearError
  }
}
