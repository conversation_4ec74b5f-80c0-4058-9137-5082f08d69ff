// E-commerce types

export interface Customer {
  id: string
  email: string
  firstName?: string
  lastName?: string
  phone?: string
}

export interface Address {
  id?: string
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  state?: string
  postalCode: string
  country: string
  phone?: string
}

export interface OrderItem {
  id: string
  productId: string
  variantId?: string
  sku: string
  name: string
  variantName?: string
  quantity: number
  price: number
  imageUrl?: string
}

export interface Order {
  id: string
  orderNumber: string
  customerId?: string
  customer?: Customer
  email?: string
  status: string
  fulfillmentStatus?: string
  paymentStatus?: string
  currency?: string
  shippingAddress?: Address
  billingAddress?: Address
  items?: OrderItem[]
  itemCount?: number
  total?: {
    amount: number
    currency?: string
  }
  subtotal?: number
  shipping?: number
  tax?: number
  discount?: number
  notes?: string
  tags?: string[]
  createdAt: string | Date
  updatedAt?: string | Date
}

export interface CreateOrderInput {
  customerId?: string
  email: string
  currency: string
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    price: number
  }>
  shippingAddress?: Omit<Address, 'id'>
  billingAddress?: Omit<Address, 'id'>
  shippingMethod?: string
  notes?: string
  tags?: string[]
  metadata?: Record<string, any>
}

export interface UpdateOrderInput {
  id: string
  customerId?: string
  email?: string
  status?: string
  fulfillmentStatus?: string
  paymentStatus?: string
  shippingAddress?: Omit<Address, 'id'>
  billingAddress?: Omit<Address, 'id'>
  notes?: string
  tags?: string[]
  metadata?: Record<string, any>
}

export interface OrderSearchParams {
  page?: number
  limit?: number
  status?: string | string[]
  paymentStatus?: string
  fulfillmentStatus?: string
  customerId?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters?: Record<string, any>
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  success?: boolean
  error?: string
}