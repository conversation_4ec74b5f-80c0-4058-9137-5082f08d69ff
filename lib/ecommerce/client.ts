// Client-safe exports from ecommerce library
// This file excludes server-side only services

// Export all types (safe for client)
export * from './types'

// Export utilities (safe for client)
export * from './utils/api-response'

// Export configuration constants (safe for client)
export * from './config/constants'

// Import and export only client-safe services
import { ProductService } from './services/product-service'
import { CartService } from './services/cart-service'
import { AuthService } from './services/auth-service'
import { CustomerService } from './services/customer-service'
import { AnalyticsService } from './services/analytics-service'

export { ProductService } from './services/product-service'
export { CartService } from './services/cart-service'
export { AuthService } from './services/auth-service'
export { CustomerService } from './services/customer-service'
export { AnalyticsService } from './services/analytics-service'

// Client-safe service factory functions
let _productService: ProductService | null = null
let _cartService: CartService | null = null
let _authService: AuthService | null = null
let _customerService: CustomerService | null = null
let _analyticsService: AnalyticsService | null = null

export const productService = () => _productService || (_productService = new ProductService())
export const cartService = () => _cartService || (_cartService = new CartService())
export const authService = () => _authService || (_authService = new AuthService())
export const customerService = () => _customerService || (_customerService = new CustomerService())
export const analyticsService = () => _analyticsService || (_analyticsService = new AnalyticsService())

// Client-safe E-commerce class
export class ClientEcommerceLibrary {
  public readonly products: ProductService
  public readonly cart: CartService
  public readonly auth: AuthService
  public readonly customers: CustomerService
  public readonly analytics: AnalyticsService

  constructor() {
    this.products = new ProductService()
    this.cart = new CartService()
    this.auth = new AuthService()
    this.customers = new CustomerService()
    this.analytics = new AnalyticsService()
  }
}

// Create and export a default client-safe instance
export const clientEcommerce = new ClientEcommerceLibrary()

// Version information
export const VERSION = '1.0.0'
export const LIBRARY_NAME = 'Custom E-commerce Library (Client)'

// Error handling utilities
export function handleEcommerceError(error: any): {
  code: string
  message: string
  statusCode: number
} {
  if (error.name === 'NotFoundError') {
    return {
      code: 'NOT_FOUND',
      message: error.message,
      statusCode: 404
    }
  }

  if (error.name === 'ValidationError') {
    return {
      code: 'VALIDATION_ERROR',
      message: error.message,
      statusCode: 400
    }
  }

  if (error.name === 'InsufficientStockError') {
    return {
      code: 'INSUFFICIENT_STOCK',
      message: error.message,
      statusCode: 400
    }
  }

  // Default error
  return {
    code: 'INTERNAL_ERROR',
    message: error.message || 'An unexpected error occurred',
    statusCode: 500
  }
}

// Logging utilities (client-safe)
export const logger = {
  info: (message: string, meta?: any) => {
    console.log(`[ECOMMERCE-CLIENT] ${message}`, meta || '')
  },
  warn: (message: string, meta?: any) => {
    console.warn(`[ECOMMERCE-CLIENT] ${message}`, meta || '')
  },
  error: (message: string, meta?: any) => {
    console.error(`[ECOMMERCE-CLIENT] ${message}`, meta || '')
  },
  debug: (message: string, meta?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[ECOMMERCE-CLIENT] ${message}`, meta || '')
    }
  }
}

// Export default instance for convenience
export default clientEcommerce