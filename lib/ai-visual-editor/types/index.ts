import { FieldConfig } from '@/lib/core/builders/components/properties-panel/custom-fields/types'

export interface GeneratedComponent {
  id: string
  name: string
  description: string
  category: 'layout' | 'content' | 'media' | 'form' | 'navigation' | 'data'
  jsx: string
  props: Record<string, any>
  propertiesConfig: ComponentPropertiesConfig
  defaultValues: Record<string, any>
  createdAt: Date
  updatedAt: Date
  metadata?: {
    generationType?: 'intelligent' | 'standard' | 'enhanced'
    learnedFrom?: number
    shadcnComponents?: string[]
    codeStyle?: any
    complexity?: 'simple' | 'moderate' | 'complex'
    [key: string]: any
  }
}

export interface ComponentPropertiesConfig {
  appearance: FieldConfig[]
  content: FieldConfig[]
  behavior: FieldConfig[]
  data: FieldConfig[]
  layout: FieldConfig[]
}

export interface ComponentAnalysis {
  hasBackground: boolean
  hasText: boolean
  hasTitle: boolean
  hasDescription: boolean
  hasImage: boolean
  hasIcon: boolean
  hasLinks: boolean
  canHaveBorder: boolean
  canAnimate: boolean
  isClickable: boolean
  needsData: boolean
  summary: string
}

export interface AIToolResult {
  success: boolean
  data?: any
  error?: string
  message?: string
}

export interface EditorState {
  // Components
  components: GeneratedComponent[]
  selectedComponentId: string | null
  
  // Properties
  propertyValues: Record<string, Record<string, any>>
  
  // UI State
  isGenerating: boolean
  previewMode: 'desktop' | 'tablet' | 'mobile'
  showComponentTree: boolean
  
  // Chat
  chatMessages: any[]
  isAIResponding: boolean
}

export interface EditorActions {
  // Component management
  addComponent: (component: GeneratedComponent) => void
  updateComponent: (id: string, updates: Partial<GeneratedComponent>) => void
  deleteComponent: (id: string) => void
  selectComponent: (id: string | null) => void
  
  // Property management
  updatePropertyValue: (componentId: string, fieldId: string, value: any) => void
  resetPropertyValues: (componentId: string) => void
  
  // UI actions
  setGenerating: (isGenerating: boolean) => void
  setPreviewMode: (mode: 'desktop' | 'tablet' | 'mobile') => void
  toggleComponentTree: () => void
  
  // Chat actions
  addChatMessage: (message: any) => void
  setAIResponding: (isResponding: boolean) => void
  clearChat: () => void
}

export type EditorStore = EditorState & EditorActions

export interface ComponentGenerationParams {
  description: string
  componentType: 'hero' | 'card' | 'button' | 'form' | 'navigation' | 'footer' | 'custom'
  features: string[]
  styling: 'modern' | 'minimal' | 'bold' | 'elegant' | 'playful'
}

export interface PropertiesAnalysisParams {
  componentCode: string
  componentName: string
  focusArea: 'appearance' | 'content' | 'behavior' | 'data' | 'all'
}

export interface FieldGenerationResult {
  componentName: string
  propertiesConfig: ComponentPropertiesConfig
  defaultValues: Record<string, any>
  analysis: ComponentAnalysis
}
