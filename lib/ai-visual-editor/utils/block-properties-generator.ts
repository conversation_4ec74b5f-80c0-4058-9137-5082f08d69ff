import { FieldConfig } from '@/lib/core/builders/components/properties-panel/custom-fields/types'
import { BlockCategory } from '@/lib/page-builder/types'

export interface BlockPropertiesConfig {
  [sectionName: string]: {
    title: string
    description?: string
    fields: Record<string, FieldConfig>
  }
}

/**
 * Generates properties configuration for AI-generated blocks using existing custom fields
 */
export async function generateBlockPropertiesConfig(
  configurationInterface: string,
  defaultConfig: Record<string, any>,
  category: BlockCategory
): Promise<BlockPropertiesConfig> {
  try {
    // Parse the configuration interface to extract property types
    const properties = parseConfigurationInterface(configurationInterface)
    
    // Generate field configurations based on property types and values
    const propertiesConfig: BlockPropertiesConfig = {}
    
    // Group properties into logical sections
    const sections = groupPropertiesIntoSections(properties, defaultConfig, category)
    
    for (const [sectionName, sectionProps] of Object.entries(sections)) {
      propertiesConfig[sectionName] = {
        title: formatSectionTitle(sectionName),
        description: getSectionDescription(sectionName, category),
        fields: {}
      }
      
      for (const [propName, propInfo] of Object.entries(sectionProps)) {
        const fieldConfig = generateFieldConfig(propName, propInfo, defaultConfig[propName])
        if (fieldConfig) {
          propertiesConfig[sectionName].fields[propName] = fieldConfig
        }
      }
    }
    
    return propertiesConfig
  } catch (error) {
    console.error('Error generating block properties config:', error)
    return generateFallbackConfig(defaultConfig, category)
  }
}

/**
 * Parses TypeScript interface to extract property information
 */
function parseConfigurationInterface(interfaceCode: string): Record<string, PropertyInfo> {
  const properties: Record<string, PropertyInfo> = {}
  
  if (!interfaceCode) {
    return properties
  }
  
  // Extract interface content
  const interfaceMatch = interfaceCode.match(/interface\s+\w+\s*{([^}]+)}/s)
  if (!interfaceMatch) {
    return properties
  }
  
  const interfaceContent = interfaceMatch[1]
  
  // Parse property declarations
  const propertyRegex = /(\w+)(\?)?:\s*([^;\n]+)/g
  let match
  
  while ((match = propertyRegex.exec(interfaceContent)) !== null) {
    const [, name, optional, type] = match
    
    properties[name] = {
      name,
      type: type.trim(),
      optional: !!optional,
      description: extractPropertyDescription(interfaceContent, name)
    }
  }
  
  return properties
}

interface PropertyInfo {
  name: string
  type: string
  optional: boolean
  description?: string
}

/**
 * Groups properties into logical sections for better organization
 */
function groupPropertiesIntoSections(
  properties: Record<string, PropertyInfo>,
  defaultConfig: Record<string, any>,
  category: BlockCategory
): Record<string, Record<string, PropertyInfo>> {
  const sections: Record<string, Record<string, PropertyInfo>> = {
    content: {},
    appearance: {},
    layout: {},
    behavior: {},
    advanced: {}
  }
  
  // Category-specific section mappings
  const categoryMappings = getCategorySpecificMappings(category)
  
  for (const [propName, propInfo] of Object.entries(properties)) {
    const sectionName = determineSectionForProperty(propName, propInfo, categoryMappings)
    sections[sectionName][propName] = propInfo
  }
  
  // Remove empty sections
  Object.keys(sections).forEach(sectionName => {
    if (Object.keys(sections[sectionName]).length === 0) {
      delete sections[sectionName]
    }
  })
  
  return sections
}

/**
 * Determines which section a property belongs to
 */
function determineSectionForProperty(
  propName: string,
  propInfo: PropertyInfo,
  categoryMappings: Record<string, string[]>
): string {
  const lowerPropName = propName.toLowerCase()
  
  // Check category-specific mappings first
  for (const [section, keywords] of Object.entries(categoryMappings)) {
    if (keywords.some(keyword => lowerPropName.includes(keyword))) {
      return section
    }
  }
  
  // General mappings
  if (lowerPropName.includes('title') || lowerPropName.includes('text') || 
      lowerPropName.includes('content') || lowerPropName.includes('description')) {
    return 'content'
  }
  
  if (lowerPropName.includes('color') || lowerPropName.includes('background') || 
      lowerPropName.includes('style') || lowerPropName.includes('theme')) {
    return 'appearance'
  }
  
  if (lowerPropName.includes('width') || lowerPropName.includes('height') || 
      lowerPropName.includes('align') || lowerPropName.includes('position')) {
    return 'layout'
  }
  
  if (lowerPropName.includes('auto') || lowerPropName.includes('enable') || 
      lowerPropName.includes('show') || lowerPropName.includes('hide')) {
    return 'behavior'
  }
  
  return 'advanced'
}

/**
 * Generates field configuration based on property type and default value
 */
function generateFieldConfig(
  propName: string,
  propInfo: PropertyInfo,
  defaultValue: any
): FieldConfig | null {
  const baseConfig: Partial<FieldConfig> = {
    label: formatFieldLabel(propName),
    description: propInfo.description || generateFieldDescription(propName, propInfo.type),
    required: !propInfo.optional
  }
  
  // Determine field type based on TypeScript type and default value
  const fieldType = determineFieldType(propInfo.type, defaultValue, propName)
  
  switch (fieldType) {
    case 'text':
      return {
        ...baseConfig,
        type: 'text',
        placeholder: `Enter ${formatFieldLabel(propName).toLowerCase()}...`,
        validation: {
          maxLength: propName.includes('title') ? 100 : 500
        }
      } as FieldConfig
      
    case 'textarea':
      return {
        ...baseConfig,
        type: 'textarea',
        placeholder: `Enter ${formatFieldLabel(propName).toLowerCase()}...`,
        rows: 3,
        validation: {
          maxLength: 1000
        }
      } as FieldConfig
      
    case 'boolean':
      return {
        ...baseConfig,
        type: 'boolean',
        defaultValue: defaultValue || false
      } as FieldConfig
      
    case 'number':
      return {
        ...baseConfig,
        type: 'number',
        min: 0,
        max: propName.includes('opacity') ? 1 : 1000,
        step: propName.includes('opacity') ? 0.1 : 1
      } as FieldConfig
      
    case 'select':
      return {
        ...baseConfig,
        type: 'select',
        options: generateSelectOptions(propInfo.type, propName)
      } as FieldConfig
      
    case 'color':
      return {
        ...baseConfig,
        type: 'color',
        defaultValue: defaultValue || '#000000'
      } as FieldConfig
      
    case 'image':
      return {
        ...baseConfig,
        type: 'image',
        accept: 'image/*',
        maxSize: '5MB'
      } as FieldConfig
      
    case 'link':
      return {
        ...baseConfig,
        type: 'link',
        allowExternal: true,
        allowInternal: true
      } as FieldConfig
      
    case 'spacing':
      return {
        ...baseConfig,
        type: 'spacing',
        units: ['px', 'rem', '%'],
        defaultUnit: 'px'
      } as FieldConfig
      
    case 'object':
      return {
        ...baseConfig,
        type: 'object',
        properties: generateObjectProperties(propInfo.type, defaultValue)
      } as FieldConfig
      
    default:
      return {
        ...baseConfig,
        type: 'text'
      } as FieldConfig
  }
}

/**
 * Determines the appropriate field type based on TypeScript type and context
 */
function determineFieldType(tsType: string, defaultValue: any, propName: string): string {
  const lowerPropName = propName.toLowerCase()
  
  // Check for specific property name patterns
  if (lowerPropName.includes('color')) return 'color'
  if (lowerPropName.includes('image') || lowerPropName.includes('src')) return 'image'
  if (lowerPropName.includes('url') || lowerPropName.includes('link') || lowerPropName.includes('href')) return 'link'
  if (lowerPropName.includes('padding') || lowerPropName.includes('margin')) return 'spacing'
  if (lowerPropName.includes('description') || lowerPropName.includes('content')) return 'textarea'
  
  // Check TypeScript type
  if (tsType.includes('boolean')) return 'boolean'
  if (tsType.includes('number')) return 'number'
  if (tsType.includes('|') && !tsType.includes('string')) return 'select'
  if (tsType.includes('string') && tsType.includes('|')) return 'select'
  if (tsType.includes('{') || tsType.includes('object')) return 'object'
  
  // Check default value type
  if (typeof defaultValue === 'boolean') return 'boolean'
  if (typeof defaultValue === 'number') return 'number'
  if (typeof defaultValue === 'object' && defaultValue !== null) return 'object'
  
  return 'text'
}

/**
 * Generates select options from TypeScript union types
 */
function generateSelectOptions(tsType: string, propName: string): Array<{value: string, label: string}> {
  // Extract union type values
  const unionMatch = tsType.match(/'([^']+)'|"([^"]+)"/g)
  if (unionMatch) {
    return unionMatch.map(match => {
      const value = match.replace(/['"]/g, '')
      return {
        value,
        label: formatOptionLabel(value)
      }
    })
  }
  
  // Fallback options based on property name
  const fallbackOptions = getFallbackOptions(propName)
  return fallbackOptions.map(option => ({
    value: option,
    label: formatOptionLabel(option)
  }))
}

/**
 * Helper functions
 */
function extractPropertyDescription(interfaceContent: string, propName: string): string | undefined {
  const commentRegex = new RegExp(`//\\s*([^\\n]+)\\s*\\n\\s*${propName}`, 'i')
  const match = interfaceContent.match(commentRegex)
  return match?.[1]?.trim()
}

function getCategorySpecificMappings(category: BlockCategory): Record<string, string[]> {
  const mappings: Record<BlockCategory, Record<string, string[]>> = {
    content: {
      content: ['title', 'subtitle', 'text', 'content', 'description'],
      appearance: ['color', 'font', 'size', 'weight'],
      layout: ['align', 'spacing', 'width', 'height']
    },
    ecommerce: {
      content: ['title', 'description', 'price', 'product'],
      behavior: ['cart', 'checkout', 'payment', 'shipping'],
      appearance: ['currency', 'badge', 'label']
    },
    marketing: {
      content: ['message', 'cta', 'headline', 'copy'],
      behavior: ['tracking', 'analytics', 'conversion'],
      appearance: ['brand', 'theme', 'style']
    },
    layout: {
      layout: ['grid', 'flex', 'container', 'responsive'],
      behavior: ['breakpoint', 'collapse', 'stack'],
      appearance: ['gap', 'spacing', 'border']
    },
    media: {
      content: ['src', 'alt', 'caption', 'title'],
      behavior: ['autoplay', 'controls', 'loop'],
      appearance: ['aspect', 'fit', 'quality']
    }
  }
  
  return mappings[category] || {}
}

function formatSectionTitle(sectionName: string): string {
  return sectionName.charAt(0).toUpperCase() + sectionName.slice(1)
}

function getSectionDescription(sectionName: string, category: BlockCategory): string {
  const descriptions: Record<string, string> = {
    content: 'Configure the text content and messaging',
    appearance: 'Customize the visual styling and colors',
    layout: 'Adjust positioning and spacing',
    behavior: 'Control interactive features and functionality',
    advanced: 'Advanced configuration options'
  }
  
  return descriptions[sectionName] || `Configure ${sectionName} settings`
}

function formatFieldLabel(propName: string): string {
  return propName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim()
}

function generateFieldDescription(propName: string, tsType: string): string {
  const baseDescription = `Configure the ${formatFieldLabel(propName).toLowerCase()}`
  
  if (tsType.includes('boolean')) {
    return `${baseDescription} (enable/disable)`
  }
  
  if (tsType.includes('number')) {
    return `${baseDescription} (numeric value)`
  }
  
  if (tsType.includes('|')) {
    return `${baseDescription} (select from options)`
  }
  
  return baseDescription
}

function generateObjectProperties(tsType: string, defaultValue: any): Record<string, FieldConfig> {
  // This would need more sophisticated parsing for complex objects
  // For now, return basic structure
  if (defaultValue && typeof defaultValue === 'object') {
    const properties: Record<string, FieldConfig> = {}
    
    Object.keys(defaultValue).forEach(key => {
      properties[key] = {
        type: 'text',
        label: formatFieldLabel(key),
        required: false
      } as FieldConfig
    })
    
    return properties
  }
  
  return {}
}

function getFallbackOptions(propName: string): string[] {
  const optionMaps: Record<string, string[]> = {
    align: ['left', 'center', 'right'],
    size: ['small', 'medium', 'large'],
    style: ['default', 'primary', 'secondary'],
    position: ['top', 'bottom', 'left', 'right'],
    layout: ['horizontal', 'vertical', 'grid']
  }
  
  const lowerPropName = propName.toLowerCase()
  
  for (const [key, options] of Object.entries(optionMaps)) {
    if (lowerPropName.includes(key)) {
      return options
    }
  }
  
  return ['option1', 'option2', 'option3']
}

function formatOptionLabel(value: string): string {
  return value
    .split(/[-_]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

function generateFallbackConfig(defaultConfig: Record<string, any>, category: BlockCategory): BlockPropertiesConfig {
  const config: BlockPropertiesConfig = {
    content: {
      title: 'Content',
      description: 'Configure the content and messaging',
      fields: {}
    }
  }
  
  // Generate basic fields from default config
  Object.entries(defaultConfig).forEach(([key, value]) => {
    const fieldType = typeof value === 'boolean' ? 'boolean' : 
                     typeof value === 'number' ? 'number' : 'text'
    
    config.content.fields[key] = {
      type: fieldType,
      label: formatFieldLabel(key),
      required: false
    } as FieldConfig
  })
  
  return config
}
