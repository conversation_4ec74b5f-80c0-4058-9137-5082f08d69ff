'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Activity, 
  Brain, 
  Shield, 
  Zap, 
  Users, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Target
} from 'lucide-react'
import { userExperienceService } from '../services/user-experience-service'
import { monitoringAnalyticsService } from '../services/monitoring-analytics-service'

interface EnhancedAIVisualEditorProps {
  userId?: string
  sessionId?: string
  onComponentGenerated?: (component: any) => void
  onError?: (error: any) => void
}

interface GenerationRequest {
  description: string
  blockType: string
  quality: 'standard' | 'high' | 'premium'
  requirements: {
    accessibility: boolean
    performance: boolean
    security: boolean
    errorHandling: boolean
    uxOptimization: boolean
    testing: boolean
  }
  preferences: {
    agentMode: 'single' | 'multi-agent' | 'orchestrated'
    validationLevel: 'basic' | 'comprehensive' | 'premium'
    optimizationLevel: 'none' | 'standard' | 'aggressive'
  }
}

export function EnhancedAIVisualEditor({ 
  userId, 
  sessionId = `session_${Date.now()}`,
  onComponentGenerated,
  onError 
}: EnhancedAIVisualEditorProps) {
  const [request, setRequest] = useState<GenerationRequest>({
    description: '',
    blockType: 'hero',
    quality: 'high',
    requirements: {
      accessibility: true,
      performance: true,
      security: false,
      errorHandling: true,
      uxOptimization: true,
      testing: false
    },
    preferences: {
      agentMode: 'multi-agent',
      validationLevel: 'comprehensive',
      optimizationLevel: 'standard'
    }
  })

  const [isGenerating, setIsGenerating] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [assistance, setAssistance] = useState<any>(null)
  const [systemHealth, setSystemHealth] = useState<'healthy' | 'warning' | 'critical'>('healthy')
  const [metrics, setMetrics] = useState<any>(null)
  const [insights, setInsights] = useState<any>(null)

  // Track user interactions for UX analysis
  const trackInteraction = useCallback((action: string, context: any = {}) => {
    userExperienceService.trackInteraction(sessionId, {
      action,
      context,
      duration: 0,
      success: true
    })
  }, [sessionId])

  // Load personalized experience
  useEffect(() => {
    if (userId) {
      userExperienceService.personalizeExperience(userId, { sessionId })
        .then(personalization => {
          // Apply personalized settings
          console.log('Personalization loaded:', personalization)
        })
        .catch(console.error)
    }
  }, [userId, sessionId])

  // Monitor system health
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        const anomalies = await monitoringAnalyticsService.detectAnomalies()
        setSystemHealth(anomalies.systemHealth)
        
        const dashboardData = monitoringAnalyticsService.getDashboardData('hour')
        setMetrics(dashboardData)
      } catch (error) {
        console.error('Health monitoring failed:', error)
      }
    }, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [])

  // Generate insights periodically
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        const generatedInsights = await monitoringAnalyticsService.generateInsights('hour')
        setInsights(generatedInsights)
      } catch (error) {
        console.error('Insights generation failed:', error)
      }
    }, 60000) // Generate insights every minute

    return () => clearInterval(interval)
  }, [])

  const handleGenerate = async () => {
    if (!request.description.trim()) {
      onError?.({ message: 'Please provide a component description' })
      return
    }

    setIsGenerating(true)
    trackInteraction('generation-started', { 
      blockType: request.blockType, 
      quality: request.quality 
    })

    try {
      const response = await fetch('/api/ai-visual-editor/enhanced-generation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...request,
          context: { sessionId, userId }
        })
      })

      const data = await response.json()

      if (data.success) {
        setResult(data.result)
        setAssistance(data.assistance)
        onComponentGenerated?.(data.result)
        trackInteraction('generation-completed', { 
          success: true, 
          quality: data.result.quality.overallScore 
        })
      } else {
        throw new Error(data.error?.message || 'Generation failed')
      }
    } catch (error) {
      console.error('Generation error:', error)
      onError?.(error)
      trackInteraction('generation-failed', { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      })
    } finally {
      setIsGenerating(false)
    }
  }

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getQualityColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-blue-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className="space-y-6">
      {/* System Health Status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Status
            </CardTitle>
            <Badge variant={systemHealth === 'healthy' ? 'default' : 'destructive'}>
              <div className={`w-2 h-2 rounded-full mr-2 ${
                systemHealth === 'healthy' ? 'bg-green-500' : 
                systemHealth === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
              }`} />
              {systemHealth.toUpperCase()}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {metrics?.summary?.successRate?.toFixed(1) || '0'}%
              </div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {metrics?.summary?.averageResponseTime?.toFixed(0) || '0'}ms
              </div>
              <div className="text-sm text-gray-600">Avg Response</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {metrics?.summary?.activeUsers || '0'}
              </div>
              <div className="text-sm text-gray-600">Active Users</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${getQualityColor(metrics?.summary?.qualityScore || 0)}`}>
                {metrics?.summary?.qualityScore?.toFixed(0) || '0'}
              </div>
              <div className="text-sm text-gray-600">Quality Score</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Generation Interface */}
      <Tabs defaultValue="generate" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generate" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Generate
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Insights
          </TabsTrigger>
          <TabsTrigger value="assistance" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Assistance
          </TabsTrigger>
          <TabsTrigger value="monitoring" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Monitoring
          </TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Enhanced AI Component Generation
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Generation Form */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Component Description
                  </label>
                  <textarea
                    value={request.description}
                    onChange={(e) => setRequest(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe the component you want to generate..."
                    className="w-full p-3 border rounded-lg resize-none"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Block Type</label>
                    <select
                      value={request.blockType}
                      onChange={(e) => setRequest(prev => ({ ...prev, blockType: e.target.value }))}
                      className="w-full p-2 border rounded-lg"
                    >
                      <option value="hero">Hero Section</option>
                      <option value="feature">Feature Section</option>
                      <option value="pricing">Pricing Section</option>
                      <option value="contact">Contact Section</option>
                      <option value="product">Product Section</option>
                      <option value="content">Content Section</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Quality Level</label>
                    <select
                      value={request.quality}
                      onChange={(e) => setRequest(prev => ({ ...prev, quality: e.target.value as any }))}
                      className="w-full p-2 border rounded-lg"
                    >
                      <option value="standard">Standard</option>
                      <option value="high">High Quality</option>
                      <option value="premium">Premium</option>
                    </select>
                  </div>
                </div>

                {/* Requirements Checkboxes */}
                <div>
                  <label className="block text-sm font-medium mb-2">Requirements</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {Object.entries(request.requirements).map(([key, value]) => (
                      <label key={key} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={(e) => setRequest(prev => ({
                            ...prev,
                            requirements: { ...prev.requirements, [key]: e.target.checked }
                          }))}
                          className="rounded"
                        />
                        <span className="text-sm capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                <Button 
                  onClick={handleGenerate} 
                  disabled={isGenerating || !request.description.trim()}
                  className="w-full"
                  size="lg"
                >
                  {isGenerating ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Zap className="h-4 w-4 mr-2" />
                      Generate Enhanced Component
                    </>
                  )}
                </Button>
              </div>

              {/* Generation Result */}
              {result && (
                <div className="mt-6 space-y-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h3 className="text-lg font-semibold">Generation Complete</h3>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                    <div className="text-center">
                      <div className={`text-2xl font-bold ${getQualityColor(result.quality.overallScore)}`}>
                        {result.quality.overallScore}
                      </div>
                      <div className="text-sm text-gray-600">Overall Score</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {result.quality.accessibility}
                      </div>
                      <div className="text-sm text-gray-600">Accessibility</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {result.quality.performance}
                      </div>
                      <div className="text-sm text-gray-600">Performance</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {result.quality.usability}
                      </div>
                      <div className="text-sm text-gray-600">Usability</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {result.metrics.agentsUsed}
                      </div>
                      <div className="text-sm text-gray-600">Agents Used</div>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium mb-2">Applied Enhancements</h4>
                    <div className="flex flex-wrap gap-2">
                      {result.enhancements.applied.map((enhancement: string, index: number) => (
                        <Badge key={index} variant="secondary">
                          {enhancement}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {result.enhancements.recommendations.length > 0 && (
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Recommendations
                      </h4>
                      <ul className="space-y-1">
                        {result.enhancements.recommendations.slice(0, 3).map((rec: string, index: number) => (
                          <li key={index} className="text-sm text-blue-700">
                            • {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                System Insights & Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              {insights ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-blue-600">
                        {insights.trends.performance}
                      </div>
                      <div className="text-sm text-gray-600">Performance Trend</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-green-600">
                        {insights.trends.quality}
                      </div>
                      <div className="text-sm text-gray-600">Quality Trend</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-purple-600">
                        {insights.trends.usage}
                      </div>
                      <div className="text-sm text-gray-600">Usage Trend</div>
                    </div>
                  </div>

                  {insights.insights.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">Key Insights</h4>
                      <div className="space-y-2">
                        {insights.insights.slice(0, 3).map((insight: any, index: number) => (
                          <Alert key={index}>
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                              <strong>{insight.category}:</strong> {insight.insight}
                            </AlertDescription>
                          </Alert>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  Loading insights...
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assistance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Contextual Assistance
              </CardTitle>
            </CardHeader>
            <CardContent>
              {assistance ? (
                <div className="space-y-4">
                  {assistance.assistance.map((item: any, index: number) => (
                    <Alert key={index}>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>{item.type}:</strong> {item.message}
                      </AlertDescription>
                    </Alert>
                  ))}

                  {assistance.predictions.nextLikelyActions.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">Suggested Next Actions</h4>
                      <div className="flex flex-wrap gap-2">
                        {assistance.predictions.nextLikelyActions.map((action: string, index: number) => (
                          <Badge key={index} variant="outline">
                            {action}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  No assistance available yet. Start generating components to receive contextual help.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                System Monitoring
              </CardTitle>
            </CardHeader>
            <CardContent>
              {metrics ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium mb-2">Performance Metrics</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Response Time</span>
                          <span>{metrics.summary.averageResponseTime?.toFixed(0)}ms</span>
                        </div>
                        <Progress value={Math.min(100, (metrics.summary.averageResponseTime || 0) / 50)} />
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium mb-2">Quality Metrics</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span>Quality Score</span>
                          <span>{metrics.summary.qualityScore?.toFixed(0)}</span>
                        </div>
                        <Progress value={metrics.summary.qualityScore || 0} />
                      </div>
                    </div>
                  </div>

                  {metrics.alerts.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">Active Alerts</h4>
                      <div className="space-y-2">
                        {metrics.alerts.slice(0, 3).map((alert: any, index: number) => (
                          <Alert key={index} variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                              <strong>{alert.severity}:</strong> {alert.message}
                            </AlertDescription>
                          </Alert>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center text-gray-500 py-8">
                  Loading monitoring data...
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
