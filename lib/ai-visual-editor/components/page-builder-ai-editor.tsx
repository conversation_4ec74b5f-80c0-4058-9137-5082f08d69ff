'use client'

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  MessageSquare, 
  Settings, 
  Eye, 
  Layers,
  Sparkles,
  RotateCcw,
  Download,
  Share,
  Plus,
  Grid,
  Blocks,
  Wand2
} from 'lucide-react'
import { AiChatPanel } from './ai-chat-panel'
import { PropertiesPanel } from './properties-panel'
import { LivePreview } from './live-preview'
import { ComponentTree } from './component-tree'
import { PageBuilderBlockRenderer } from './page-builder-block-renderer'
import { useEditorStore } from '../stores/editor-store'
import { usePageBuilderBlocks } from '../hooks/use-page-builder-blocks'
import { BlockCategory } from '@/lib/page-builder/types'
import { toast } from 'sonner'

interface PageBuilderAIEditorProps {
  onBlockGenerated?: (blockId: string) => void
  onBlockRegistered?: (blockId: string) => void
  initialCategory?: BlockCategory
  className?: string
}

export function PageBuilderAIEditor({
  onBlockGenerated,
  onBlockRegistered,
  initialCategory = 'content',
  className = ''
}: PageBuilderAIEditorProps) {
  const { 
    components, 
    selectedComponentId, 
    isGenerating, 
    isAIResponding,
    clearChat,
    showComponentTree,
    toggleComponentTree
  } = useEditorStore()

  const {
    generatePageBuilderBlock,
    registerBlockInPageBuilder,
    getRegisteredBlocks,
    isRegistering
  } = usePageBuilderBlocks()

  const [activeTab, setActiveTab] = useState<'chat' | 'properties' | 'preview' | 'blocks'>('chat')
  const [selectedCategory, setSelectedCategory] = useState<BlockCategory>(initialCategory)
  const [showBlockLibrary, setShowBlockLibrary] = useState(false)

  const selectedComponent = components.find(c => c.id === selectedComponentId)
  const registeredBlocks = getRegisteredBlocks()

  // Handle AI block generation
  const handleGenerateBlock = useCallback(async (prompt: string) => {
    try {
      const result = await generatePageBuilderBlock({
        description: prompt,
        category: selectedCategory,
        blockType: 'custom',
        complexity: 'moderate',
        learnFromExisting: true,
        includeConfiguration: true
      })

      if (result.success && result.block) {
        toast.success(`Generated ${result.block.displayName} block!`)
        onBlockGenerated?.(result.block.id)
        setActiveTab('preview')
      } else {
        toast.error(result.error || 'Failed to generate block')
      }
    } catch (error) {
      console.error('Block generation error:', error)
      toast.error('Failed to generate block')
    }
  }, [selectedCategory, generatePageBuilderBlock, onBlockGenerated])

  // Handle block registration in page builder
  const handleRegisterBlock = useCallback(async (componentId: string) => {
    try {
      const component = components.find(c => c.id === componentId)
      if (!component) {
        toast.error('Component not found')
        return
      }

      const result = await registerBlockInPageBuilder(component)
      if (result.success) {
        toast.success(`Registered ${component.name} in page builder!`)
        onBlockRegistered?.(result.blockId!)
      } else {
        toast.error(result.error || 'Failed to register block')
      }
    } catch (error) {
      console.error('Block registration error:', error)
      toast.error('Failed to register block')
    }
  }, [components, registerBlockInPageBuilder, onBlockRegistered])

  // Category options
  const categories: { value: BlockCategory; label: string; icon: string }[] = [
    { value: 'content', label: 'Content', icon: '📝' },
    { value: 'ecommerce', label: 'E-commerce', icon: '🛒' },
    { value: 'marketing', label: 'Marketing', icon: '📢' },
    { value: 'layout', label: 'Layout', icon: '📐' },
    { value: 'media', label: 'Media', icon: '🖼️' }
  ]

  return (
    <div className={`h-full flex flex-col bg-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            <Wand2 className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">Page Builder AI</h1>
            <p className="text-sm text-gray-600">Generate blocks for your page builder</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Category Selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700">Category:</span>
            <div className="flex space-x-1">
              {categories.map(category => (
                <Button
                  key={category.value}
                  size="sm"
                  variant={selectedCategory === category.value ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory(category.value)}
                  className="text-xs"
                >
                  <span className="mr-1">{category.icon}</span>
                  {category.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowBlockLibrary(!showBlockLibrary)}
          >
            <Blocks className="w-4 h-4 mr-2" />
            Library ({registeredBlocks.length})
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={clearChat}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Clear
          </Button>
        </div>
      </div>

      {/* Status Bar */}
      <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{components.length} Components</Badge>
              <Badge variant="outline">{registeredBlocks.length} Registered</Badge>
              {selectedComponent && (
                <Badge variant="secondary">
                  {selectedComponent.name} Selected
                </Badge>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {(isGenerating || isAIResponding) && (
              <Badge variant="outline" className="animate-pulse">
                <Sparkles className="w-3 h-3 mr-1" />
                Generating...
              </Badge>
            )}
            
            {isRegistering && (
              <Badge variant="outline" className="animate-pulse">
                <Plus className="w-3 h-3 mr-1" />
                Registering...
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Block Library */}
      {showBlockLibrary && (
        <div className="p-4 bg-blue-50 border-b border-blue-200">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Registered Page Builder Blocks</h3>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowBlockLibrary(false)}
            >
              ×
            </Button>
          </div>
          
          {registeredBlocks.length === 0 ? (
            <Alert>
              <AlertDescription>
                No blocks registered yet. Generate and register some blocks to see them here.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {registeredBlocks.map(block => (
                <Card key={block.id} className="p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-sm">{block.blockDefinition.displayName}</h4>
                      <p className="text-xs text-gray-600">{block.blockDefinition.category}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {block.blockDefinition.icon}
                    </Badge>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Sidebar */}
        {showComponentTree && (
          <div className="w-80 border-r border-gray-200">
            <ComponentTree />
          </div>
        )}

        {/* Center Panel */}
        <div className="flex-1 flex flex-col">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-4 mx-4 mt-4">
              <TabsTrigger value="chat" className="flex items-center space-x-2">
                <MessageSquare className="w-4 h-4" />
                <span>AI Chat</span>
              </TabsTrigger>
              <TabsTrigger value="properties" className="flex items-center space-x-2">
                <Settings className="w-4 h-4" />
                <span>Properties</span>
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center space-x-2">
                <Eye className="w-4 h-4" />
                <span>Preview</span>
              </TabsTrigger>
              <TabsTrigger value="blocks" className="flex items-center space-x-2">
                <Grid className="w-4 h-4" />
                <span>Blocks</span>
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 p-4">
              <TabsContent value="chat" className="h-full">
                <AiChatPanel 
                  onGenerateBlock={handleGenerateBlock}
                  selectedCategory={selectedCategory}
                />
              </TabsContent>

              <TabsContent value="properties" className="h-full">
                {selectedComponent ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Block Properties</h3>
                      <Button
                        size="sm"
                        onClick={() => handleRegisterBlock(selectedComponent.id)}
                        disabled={isRegistering}
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Register in Page Builder
                      </Button>
                    </div>
                    <PropertiesPanel />
                  </div>
                ) : (
                  <Alert>
                    <AlertDescription>
                      Select a component to view and edit its properties.
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>

              <TabsContent value="preview" className="h-full">
                {selectedComponent ? (
                  <PageBuilderBlockRenderer
                    component={selectedComponent}
                    isEditing={true}
                    showControls={true}
                  />
                ) : (
                  <Alert>
                    <AlertDescription>
                      Select a component to preview how it will look in the page builder.
                    </AlertDescription>
                  </Alert>
                )}
              </TabsContent>

              <TabsContent value="blocks" className="h-full">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Generated Blocks</h3>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={toggleComponentTree}
                      >
                        <Layers className="w-4 h-4 mr-2" />
                        {showComponentTree ? 'Hide' : 'Show'} Tree
                      </Button>
                    </div>
                  </div>

                  {components.length === 0 ? (
                    <Alert>
                      <AlertDescription>
                        No blocks generated yet. Use the AI chat to generate your first block.
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      {components.map(component => (
                        <PageBuilderBlockRenderer
                          key={component.id}
                          component={component}
                          isEditing={false}
                          showControls={true}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
