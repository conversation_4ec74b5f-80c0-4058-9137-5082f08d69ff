'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { PageBlock } from '@/lib/page-builder/types'
import { GeneratedComponent } from '../types'
import { useEditorStore } from '../stores/editor-store'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  AlertTriangle, 
  CheckCircle, 
  Code, 
  Eye, 
  EyeOff, 
  RefreshCw,
  Download,
  Trash2
} from 'lucide-react'
import { toast } from 'sonner'

interface PageBuilderBlockRendererProps {
  component: GeneratedComponent
  block?: PageBlock
  isEditing?: boolean
  showControls?: boolean
  onBlockUpdate?: (blockId: string, updates: Partial<PageBlock>) => void
  onBlockDelete?: (blockId: string) => void
  className?: string
}

export function PageBuilderBlockRenderer({
  component,
  block,
  isEditing = false,
  showControls = true,
  onBlockUpdate,
  onBlockDelete,
  className = ''
}: PageBuilderBlockRendererProps) {
  const [renderError, setRenderError] = useState<string | null>(null)
  const [isVisible, setIsVisible] = useState(true)
  const [isCompiling, setIsCompiling] = useState(false)
  const { propertyValues } = useEditorStore()

  // Get current property values for this component
  const currentProps = propertyValues[component.id] || component.defaultValues || {}

  // Create a mock PageBlock for rendering if not provided
  const mockBlock: PageBlock = useMemo(() => {
    if (block) return block

    return {
      id: component.id,
      type: component.name.toLowerCase().replace(/([A-Z])/g, '-$1').replace(/^-/, ''),
      position: 0,
      isVisible: true,
      configuration: currentProps,
      styling: {},
      responsive: {},
      animation: { type: 'none' }
    }
  }, [block, component, currentProps])

  // Compile and render the component
  const RenderedComponent = useMemo(() => {
    try {
      setRenderError(null)
      setIsCompiling(true)

      // Create a safe component renderer
      const ComponentRenderer = () => {
        try {
          // This is a simplified version - in a real implementation,
          // you would need a proper JSX compiler/transformer
          return (
            <div className="ai-generated-block-wrapper">
              <div className="p-6 border rounded-lg bg-gradient-to-br from-blue-50 to-purple-50">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <Code className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {component.displayName || component.name}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {component.description}
                    </p>
                    <div className="flex flex-wrap gap-2 justify-center">
                      <Badge variant="secondary">{component.category}</Badge>
                      {component.metadata?.complexity && (
                        <Badge variant="outline">{component.metadata.complexity}</Badge>
                      )}
                      <Badge variant="outline">AI Generated</Badge>
                    </div>
                  </div>
                  
                  {/* Show current configuration */}
                  {Object.keys(currentProps).length > 0 && (
                    <div className="mt-4 p-3 bg-white rounded border">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Current Configuration:</h4>
                      <div className="text-xs text-gray-600 space-y-1">
                        {Object.entries(currentProps).slice(0, 3).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="font-medium">{key}:</span>
                            <span className="truncate ml-2 max-w-32">
                              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            </span>
                          </div>
                        ))}
                        {Object.keys(currentProps).length > 3 && (
                          <div className="text-center text-gray-400">
                            +{Object.keys(currentProps).length - 3} more properties
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  <div className="text-xs text-gray-500">
                    Generated: {component.createdAt.toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          )
        } catch (error) {
          console.error('Component rendering error:', error)
          throw error
        }
      }

      setIsCompiling(false)
      return ComponentRenderer
    } catch (error) {
      setIsCompiling(false)
      setRenderError(error instanceof Error ? error.message : 'Unknown rendering error')
      return null
    }
  }, [component, currentProps])

  // Handle block visibility toggle
  const handleVisibilityToggle = () => {
    setIsVisible(!isVisible)
    if (onBlockUpdate && block) {
      onBlockUpdate(block.id, { isVisible: !isVisible })
    }
  }

  // Handle block deletion
  const handleDelete = () => {
    if (onBlockDelete && block) {
      onBlockDelete(block.id)
      toast.success('Block deleted successfully')
    }
  }

  // Handle block export
  const handleExport = () => {
    const exportData = {
      component,
      block: mockBlock,
      properties: currentProps
    }
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${component.name}-block.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success('Block exported successfully')
  }

  // Handle refresh/recompile
  const handleRefresh = () => {
    setRenderError(null)
    setIsCompiling(true)
    // Force re-render by updating a state
    setTimeout(() => setIsCompiling(false), 500)
    toast.success('Block refreshed')
  }

  return (
    <div className={`relative group ${className}`}>
      {/* Controls */}
      {showControls && (
        <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="flex space-x-1 bg-white rounded-lg shadow-lg border p-1">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleVisibilityToggle}
              title={isVisible ? 'Hide block' : 'Show block'}
            >
              {isVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={handleRefresh}
              title="Refresh block"
              disabled={isCompiling}
            >
              <RefreshCw className={`w-4 h-4 ${isCompiling ? 'animate-spin' : ''}`} />
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={handleExport}
              title="Export block"
            >
              <Download className="w-4 h-4" />
            </Button>
            
            {onBlockDelete && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleDelete}
                title="Delete block"
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Block Status Indicator */}
      <div className="absolute top-2 left-2 z-10">
        <div className="flex space-x-1">
          {component.metadata?.generationType === 'page-builder-block' && (
            <Badge variant="secondary" className="text-xs">
              Page Builder
            </Badge>
          )}
          
          {renderError && (
            <Badge variant="destructive" className="text-xs">
              Error
            </Badge>
          )}
          
          {isCompiling && (
            <Badge variant="outline" className="text-xs">
              Compiling...
            </Badge>
          )}
        </div>
      </div>

      {/* Error Display */}
      {renderError && (
        <Alert className="mb-4" variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Rendering Error:</strong> {renderError}
            <Button
              size="sm"
              variant="outline"
              onClick={handleRefresh}
              className="ml-2"
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {isCompiling && (
        <Card className="p-8">
          <div className="flex items-center justify-center space-x-2">
            <RefreshCw className="w-5 h-5 animate-spin" />
            <span>Compiling block...</span>
          </div>
        </Card>
      )}

      {/* Rendered Component */}
      {!isCompiling && !renderError && RenderedComponent && (
        <div 
          className={`transition-opacity duration-200 ${isVisible ? 'opacity-100' : 'opacity-50'}`}
          style={{ display: isVisible ? 'block' : 'block' }}
        >
          <ErrorBoundary
            fallback={
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  This block encountered an error during rendering. 
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleRefresh}
                    className="ml-2"
                  >
                    Retry
                  </Button>
                </AlertDescription>
              </Alert>
            }
            onError={(error) => setRenderError(error.message)}
          >
            <RenderedComponent />
          </ErrorBoundary>
        </div>
      )}

      {/* Block Metadata (Development Mode) */}
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-2 text-xs text-gray-500">
          <summary className="cursor-pointer">Debug Info</summary>
          <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto">
            {JSON.stringify({
              componentId: component.id,
              blockId: mockBlock.id,
              properties: Object.keys(currentProps),
              metadata: component.metadata
            }, null, 2)}
          </pre>
        </details>
      )}
    </div>
  )
}

// Error Boundary Component
interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback: React.ReactNode
  onError?: (error: Error) => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Block rendering error:', error, errorInfo)
    this.props.onError?.(error)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }

    return this.props.children
  }
}
