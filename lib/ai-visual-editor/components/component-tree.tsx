'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { 
  Layers, 
  Search, 
  MoreVertical, 
  Eye, 
  EyeOff, 
  Copy, 
  Trash2,
  Edit,
  ChevronDown,
  ChevronRight,
  Plus
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useEditorStore } from '../stores/editor-store'
import { GeneratedComponent } from '../types'
import { toast } from 'sonner'

const categoryIcons = {
  layout: '📐',
  content: '📝',
  media: '🖼️',
  form: '📋',
  navigation: '🧭',
  data: '📊'
}

const categoryColors = {
  layout: 'bg-blue-50 text-blue-700 border-blue-200',
  content: 'bg-green-50 text-green-700 border-green-200',
  media: 'bg-purple-50 text-purple-700 border-purple-200',
  form: 'bg-orange-50 text-orange-700 border-orange-200',
  navigation: 'bg-indigo-50 text-indigo-700 border-indigo-200',
  data: 'bg-red-50 text-red-700 border-red-200'
}

export function ComponentTree() {
  const { 
    components, 
    selectedComponentId, 
    selectComponent, 
    deleteComponent, 
    updateComponent 
  } = useEditorStore()
  
  const [searchQuery, setSearchQuery] = useState('')
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['layout', 'content', 'media', 'form', 'navigation', 'data'])
  )
  const [hiddenComponents, setHiddenComponents] = useState<Set<string>>(new Set())

  const filteredComponents = components.filter(component =>
    component.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    component.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    component.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const componentsByCategory = filteredComponents.reduce((acc, component) => {
    if (!acc[component.category]) {
      acc[component.category] = []
    }
    acc[component.category].push(component)
    return acc
  }, {} as Record<string, GeneratedComponent[]>)

  const toggleCategory = (category: string) => {
    const newExpanded = new Set(expandedCategories)
    if (newExpanded.has(category)) {
      newExpanded.delete(category)
    } else {
      newExpanded.add(category)
    }
    setExpandedCategories(newExpanded)
  }

  const toggleComponentVisibility = (componentId: string) => {
    const newHidden = new Set(hiddenComponents)
    if (newHidden.has(componentId)) {
      newHidden.delete(componentId)
    } else {
      newHidden.add(componentId)
    }
    setHiddenComponents(newHidden)
  }

  const handleDuplicateComponent = (component: GeneratedComponent) => {
    const duplicatedComponent = {
      ...component,
      id: `${component.id}_copy_${Date.now()}`,
      name: `${component.name} Copy`,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    // This would need to be implemented in the store
    // addComponent(duplicatedComponent)
    toast.success(`Duplicated ${component.name}`)
  }

  const handleDeleteComponent = (component: GeneratedComponent) => {
    deleteComponent(component.id)
    toast.success(`Deleted ${component.name}`)
  }

  const handleRenameComponent = (component: GeneratedComponent, newName: string) => {
    updateComponent(component.id, { name: newName })
    toast.success(`Renamed to ${newName}`)
  }

  const renderComponent = (component: GeneratedComponent) => {
    const isSelected = selectedComponentId === component.id
    const isHidden = hiddenComponents.has(component.id)
    
    return (
      <Card
        key={component.id}
        className={`p-3 cursor-pointer builder-card builder-selection-ring ${
          isSelected
            ? 'selected ring-2 ring-blue-500 border-blue-200 bg-blue-50'
            : 'border-gray-200 hover:border-gray-300'
        } ${isHidden ? 'opacity-50' : ''}`}
        onClick={() => selectComponent(component.id)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 flex-1 min-w-0">
            <div className="text-lg">
              {categoryIcons[component.category]}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <h4 className="font-medium text-gray-900 truncate">
                  {component.name}
                </h4>
                <Badge 
                  variant="outline" 
                  className={`text-xs ${categoryColors[component.category]}`}
                >
                  {component.category}
                </Badge>
              </div>
              <p className="text-xs text-gray-500 truncate mt-1">
                {component.description}
              </p>
              <div className="flex items-center space-x-2 mt-2 text-xs text-gray-400">
                <span>
                  {Object.values(component.propertiesConfig).flat().length} properties
                </span>
                <span>•</span>
                <span>
                  {new Date(component.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                toggleComponentVisibility(component.id)
              }}
              className="h-8 w-8 p-0"
            >
              {isHidden ? (
                <EyeOff className="w-4 h-4 text-gray-400" />
              ) : (
                <Eye className="w-4 h-4 text-gray-600" />
              )}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => e.stopPropagation()}
                  className="h-8 w-8 p-0"
                >
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    const newName = prompt('Enter new name:', component.name)
                    if (newName && newName !== component.name) {
                      handleRenameComponent(component, newName)
                    }
                  }}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    handleDuplicateComponent(component)
                  }}
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation()
                    if (confirm(`Delete ${component.name}?`)) {
                      handleDeleteComponent(component)
                    }
                  }}
                  className="text-red-600"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </Card>
    )
  }

  const renderCategory = (category: string, categoryComponents: GeneratedComponent[]) => {
    const isExpanded = expandedCategories.has(category)
    const categoryIcon = categoryIcons[category as keyof typeof categoryIcons]
    
    return (
      <div key={category} className="mb-4">
        <Button
          variant="ghost"
          onClick={() => toggleCategory(category)}
          className="w-full justify-start p-2 h-auto"
        >
          <div className="flex items-center space-x-2">
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
            <span className="text-lg">{categoryIcon}</span>
            <span className="font-medium capitalize">{category}</span>
            <Badge variant="secondary" className="ml-auto">
              {categoryComponents.length}
            </Badge>
          </div>
        </Button>
        
        {isExpanded && (
          <div className="ml-4 space-y-2 mt-2">
            {categoryComponents.map(renderComponent)}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white border-r border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-2">
          <Layers className="w-5 h-5 text-purple-600" />
          <h2 className="text-lg font-semibold text-gray-900">Components</h2>
        </div>
        
        {/* Search */}
        <div className="mt-3 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search components..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        
        {/* Stats */}
        <div className="mt-2 text-sm text-gray-600">
          {components.length} total • {Object.keys(componentsByCategory).length} categories
        </div>
      </div>

      {/* Component List */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {components.length === 0 ? (
            <div className="text-center text-gray-500 mt-8">
              <Layers className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <div className="text-lg font-medium mb-2">No Components</div>
              <div className="text-sm">
                Generate components with AI to see them here
              </div>
            </div>
          ) : Object.keys(componentsByCategory).length === 0 ? (
            <div className="text-center text-gray-500 mt-8">
              <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <div className="text-lg font-medium mb-2">No Results</div>
              <div className="text-sm">
                Try a different search term
              </div>
            </div>
          ) : (
            Object.entries(componentsByCategory).map(([category, categoryComponents]) =>
              renderCategory(category, categoryComponents)
            )
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          {hiddenComponents.size > 0 && (
            <span>{hiddenComponents.size} hidden • </span>
          )}
          {selectedComponentId ? 'Component selected' : 'No selection'}
        </div>
      </div>
    </div>
  )
}
