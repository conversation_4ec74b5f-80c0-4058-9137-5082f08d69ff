'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Sparkles, 
  Blocks, 
  Code, 
  Eye, 
  Settings,
  Download,
  Upload,
  BarChart3,
  Lightbulb,
  Zap,
  Target
} from 'lucide-react'
import { PageBuilderAIEditor } from './page-builder-ai-editor'
import { usePageBuilderBlocks } from '../hooks/use-page-builder-blocks'
import { toast } from 'sonner'

/**
 * Comprehensive demo showcasing the AI Visual Editor's page builder integration
 */
export function PageBuilderIntegrationDemo() {
  const [activeDemo, setActiveDemo] = useState<'editor' | 'statistics' | 'examples'>('editor')
  const [selectedExample, setSelectedExample] = useState<string | null>(null)

  const {
    registeredBlocks,
    blockStatistics,
    exportBlocks,
    importBlocks,
    generatePageBuilderBlock,
    isGenerating
  } = usePageBuilderBlocks()

  // Example prompts for different block types
  const examplePrompts = [
    {
      id: 'hero-ecommerce',
      title: 'E-commerce Hero Section',
      description: 'A compelling hero section for an online store',
      prompt: 'Create a hero section for an e-commerce store selling organic skincare products. Include a large background image, compelling headline, subtitle, and a prominent "Shop Now" button. Make it mobile-responsive with overlay text.',
      category: 'ecommerce' as const,
      blockType: 'hero' as const,
      complexity: 'moderate' as const
    },
    {
      id: 'testimonials-marketing',
      title: 'Customer Testimonials',
      description: 'Social proof section with customer reviews',
      prompt: 'Design a testimonials section that displays customer reviews in a carousel format. Include customer photos, star ratings, review text, and customer names. Make it trustworthy and visually appealing.',
      category: 'marketing' as const,
      blockType: 'testimonial' as const,
      complexity: 'moderate' as const
    },
    {
      id: 'pricing-content',
      title: 'Pricing Table',
      description: 'Clear pricing options for services',
      prompt: 'Create a pricing table with three tiers: Basic, Pro, and Enterprise. Include features list, pricing, and call-to-action buttons. Make it easy to compare options and highlight the recommended plan.',
      category: 'content' as const,
      blockType: 'pricing' as const,
      complexity: 'complex' as const
    },
    {
      id: 'contact-marketing',
      title: 'Contact Form Section',
      description: 'Professional contact form with company info',
      prompt: 'Design a contact section with a form on one side and company information on the other. Include fields for name, email, subject, and message. Add contact details like phone, email, and address.',
      category: 'marketing' as const,
      blockType: 'contact' as const,
      complexity: 'moderate' as const
    }
  ]

  const handleGenerateExample = async (example: typeof examplePrompts[0]) => {
    try {
      setSelectedExample(example.id)
      const result = await generatePageBuilderBlock({
        description: example.prompt,
        blockType: example.blockType,
        category: example.category,
        complexity: example.complexity,
        learnFromExisting: true,
        includeConfiguration: true,
        responsive: true,
        accessibility: true
      })

      if (result.success) {
        toast.success(`Generated ${example.title} successfully!`)
        setActiveDemo('editor')
      } else {
        toast.error(result.error || 'Failed to generate example')
      }
    } catch (error) {
      console.error('Example generation error:', error)
      toast.error('Failed to generate example')
    } finally {
      setSelectedExample(null)
    }
  }

  const handleFileImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const success = await importBlocks(file)
      if (success) {
        toast.success('Blocks imported successfully!')
      }
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl flex items-center justify-center">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">AI Visual Editor</h1>
            <p className="text-lg text-gray-600">Page Builder Integration</p>
          </div>
        </div>
        
        <p className="text-gray-600 max-w-2xl mx-auto">
          Generate intelligent page builder blocks that follow your existing patterns and integrate seamlessly 
          with your page builder system. Create blocks with dynamic properties panels and live editing capabilities.
        </p>

        {/* Quick Stats */}
        <div className="flex justify-center space-x-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{registeredBlocks.length}</div>
            <div className="text-sm text-gray-600">Registered Blocks</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{blockStatistics.activeBlocks}</div>
            <div className="text-sm text-gray-600">Active Blocks</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Object.keys(blockStatistics.categoryDistribution).length}
            </div>
            <div className="text-sm text-gray-600">Categories</div>
          </div>
        </div>
      </div>

      {/* Main Demo Tabs */}
      <Tabs value={activeDemo} onValueChange={(value) => setActiveDemo(value as any)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="editor" className="flex items-center space-x-2">
            <Code className="w-4 h-4" />
            <span>AI Editor</span>
          </TabsTrigger>
          <TabsTrigger value="statistics" className="flex items-center space-x-2">
            <BarChart3 className="w-4 h-4" />
            <span>Statistics</span>
          </TabsTrigger>
          <TabsTrigger value="examples" className="flex items-center space-x-2">
            <Lightbulb className="w-4 h-4" />
            <span>Examples</span>
          </TabsTrigger>
        </TabsList>

        {/* AI Editor Tab */}
        <TabsContent value="editor" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Sparkles className="w-5 h-5" />
                <span>AI Page Builder Editor</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <PageBuilderAIEditor
                onBlockGenerated={(blockId) => {
                  console.log('Block generated:', blockId)
                }}
                onBlockRegistered={(blockId) => {
                  console.log('Block registered:', blockId)
                  toast.success('Block registered in page builder!')
                }}
                className="h-[600px]"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Statistics Tab */}
        <TabsContent value="statistics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Block Statistics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Blocks className="w-5 h-5" />
                  <span>Block Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Total Blocks:</span>
                    <Badge variant="outline">{blockStatistics.totalBlocks}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Blocks:</span>
                    <Badge variant="outline">{blockStatistics.activeBlocks}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg Generation Time:</span>
                    <Badge variant="outline">{Math.round(blockStatistics.averageGenerationTime)}ms</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Category Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Category Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(blockStatistics.categoryDistribution).map(([category, count]) => (
                    <div key={category} className="flex justify-between items-center">
                      <span className="capitalize">{category}:</span>
                      <Badge variant="secondary">{count}</Badge>
                    </div>
                  ))}
                  {Object.keys(blockStatistics.categoryDistribution).length === 0 && (
                    <p className="text-gray-500 text-sm">No blocks generated yet</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button
                  onClick={exportBlocks}
                  className="w-full"
                  variant="outline"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Export Blocks
                </Button>
                
                <div>
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleFileImport}
                    className="hidden"
                    id="import-blocks"
                  />
                  <Button
                    onClick={() => document.getElementById('import-blocks')?.click()}
                    className="w-full"
                    variant="outline"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Import Blocks
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Blocks */}
          {registeredBlocks.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recent Blocks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {registeredBlocks.slice(0, 6).map(block => (
                    <Card key={block.id} className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{block.blockDefinition.displayName}</h4>
                        <Badge variant="outline" className="text-xs">
                          {block.blockDefinition.category}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-600 mb-2">
                        {block.blockDefinition.description}
                      </p>
                      <div className="text-xs text-gray-500">
                        {block.registeredAt.toLocaleDateString()}
                      </div>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Examples Tab */}
        <TabsContent value="examples" className="space-y-6">
          <Alert>
            <Lightbulb className="h-4 w-4" />
            <AlertDescription>
              Try these example prompts to see how the AI generates different types of page builder blocks.
              Each example demonstrates different complexity levels and use cases.
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {examplePrompts.map(example => (
              <Card key={example.id} className="relative">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{example.title}</CardTitle>
                    <div className="flex space-x-2">
                      <Badge variant="outline">{example.category}</Badge>
                      <Badge variant="secondary">{example.complexity}</Badge>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">{example.description}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <strong>Prompt:</strong>
                    <p className="mt-1 text-gray-700">{example.prompt}</p>
                  </div>
                  
                  <Button
                    onClick={() => handleGenerateExample(example)}
                    disabled={isGenerating || selectedExample === example.id}
                    className="w-full"
                  >
                    {selectedExample === example.id ? (
                      <>
                        <Zap className="w-4 h-4 mr-2 animate-pulse" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Target className="w-4 h-4 mr-2" />
                        Generate This Block
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
