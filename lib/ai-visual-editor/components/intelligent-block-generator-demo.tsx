'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Brain, 
  Code, 
  Search, 
  Zap,
  CheckCircle,
  AlertCircle,
  Info,
  TrendingUp,
  FileCode,
  Layers,
  Target
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface IntelligentBlockGeneratorDemoProps {
  className?: string
}

export function IntelligentBlockGeneratorDemo({ className }: IntelligentBlockGeneratorDemoProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [codebaseAnalysis, setCodebaseAnalysis] = useState<any>(null)
  const [generatedBlock, setGeneratedBlock] = useState<any>(null)
  const [formData, setFormData] = useState({
    description: 'Modern pricing section with three tiers and feature comparison',
    blockType: 'pricing',
    contextPath: 'components/ui',
    learnFromCodebase: true,
    matchExistingStyle: true,
    complexity: 'moderate'
  })

  const blockTypes = [
    { value: 'hero', label: 'Hero Section' },
    { value: 'feature', label: 'Feature Section' },
    { value: 'testimonial', label: 'Testimonial' },
    { value: 'pricing', label: 'Pricing' },
    { value: 'contact', label: 'Contact Form' },
    { value: 'product', label: 'Product Showcase' },
    { value: 'content', label: 'Content Block' },
    { value: 'navigation', label: 'Navigation' },
    { value: 'footer', label: 'Footer' }
  ]

  const complexityLevels = [
    { value: 'simple', label: 'Simple' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'complex', label: 'Complex' }
  ]

  // Auto-analyze codebase on mount
  useEffect(() => {
    if (formData.learnFromCodebase) {
      handleAnalyzeCodebase()
    }
  }, [])

  const handleAnalyzeCodebase = async () => {
    setIsAnalyzing(true)
    
    try {
      const response = await fetch('/api/ai-visual-editor/intelligent-blocks?action=stats')
      const data = await response.json()
      
      if (data.success) {
        setCodebaseAnalysis(data.stats)
        toast.success('Codebase analyzed successfully!')
      } else {
        toast.error('Failed to analyze codebase')
      }
    } catch (error) {
      toast.error('Error analyzing codebase')
      console.error('Analysis error:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleGenerateBlock = async () => {
    setIsGenerating(true)
    
    try {
      const response = await fetch('/api/ai-visual-editor/intelligent-blocks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          mode: 'direct',
          action: 'generateBlock',
          ...formData
        })
      })
      
      const result = await response.json()
      
      if (result.success) {
        setGeneratedBlock(result)
        toast.success('Intelligent block generated successfully!')
      } else {
        toast.error(result.error || 'Failed to generate block')
      }
    } catch (error) {
      toast.error('Error generating block')
      console.error('Generation error:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2">
          <Brain className="w-6 h-6 text-purple-500" />
          <h2 className="text-2xl font-bold">Intelligent Block Generator</h2>
        </div>
        <p className="text-muted-foreground">
          Generate blocks by learning from your existing codebase patterns and architecture
        </p>
      </div>

      {/* Codebase Analysis Status */}
      {codebaseAnalysis && (
        <Alert>
          <TrendingUp className="h-4 w-4" />
          <AlertDescription>
            Analyzed {codebaseAnalysis.totalComponents} components, found {codebaseAnalysis.shadcnComponentsUsed} shadcn components in use.
            Most used: {codebaseAnalysis.mostUsedComponents?.slice(0, 3).map((c: any) => c.component).join(', ')}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5" />
              Intelligent Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Block Description</Label>
              <Textarea
                id="description"
                placeholder="Describe the block you want to generate..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            {/* Block Type */}
            <div className="space-y-2">
              <Label>Block Type</Label>
              <Select value={formData.blockType} onValueChange={(value) => setFormData(prev => ({ ...prev, blockType: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {blockTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Context Path */}
            <div className="space-y-2">
              <Label htmlFor="contextPath">Context Path</Label>
              <Select value={formData.contextPath} onValueChange={(value) => setFormData(prev => ({ ...prev, contextPath: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="components/ui">components/ui</SelectItem>
                  <SelectItem value="components">components</SelectItem>
                  <SelectItem value="app">app</SelectItem>
                  <SelectItem value="lib">lib</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Complexity */}
            <div className="space-y-2">
              <Label>Complexity</Label>
              <Select value={formData.complexity} onValueChange={(value) => setFormData(prev => ({ ...prev, complexity: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {complexityLevels.map(level => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Intelligence Options */}
            <div className="space-y-4">
              <Label>Intelligence Options</Label>
              
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="learnFromCodebase" className="text-sm font-medium">Learn from Codebase</Label>
                  <p className="text-xs text-muted-foreground">Analyze existing components and patterns</p>
                </div>
                <Switch
                  id="learnFromCodebase"
                  checked={formData.learnFromCodebase}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, learnFromCodebase: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="matchStyle" className="text-sm font-medium">Match Existing Style</Label>
                  <p className="text-xs text-muted-foreground">Follow existing naming and code conventions</p>
                </div>
                <Switch
                  id="matchStyle"
                  checked={formData.matchExistingStyle}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, matchExistingStyle: checked }))}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button 
                onClick={handleAnalyzeCodebase} 
                disabled={isAnalyzing}
                variant="outline"
                className="w-full"
              >
                {isAnalyzing ? (
                  <>
                    <Search className="w-4 h-4 mr-2 animate-spin" />
                    Analyzing Codebase...
                  </>
                ) : (
                  <>
                    <Search className="w-4 h-4 mr-2" />
                    Re-analyze Codebase
                  </>
                )}
              </Button>

              <Button 
                onClick={handleGenerateBlock} 
                disabled={isGenerating || !formData.description.trim()}
                className="w-full"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Brain className="w-4 h-4 mr-2 animate-pulse" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Zap className="w-4 h-4 mr-2" />
                    Generate Intelligent Block
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileCode className="w-5 h-5" />
              Generated Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            {generatedBlock ? (
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="code">Code</TabsTrigger>
                  <TabsTrigger value="analysis">Analysis</TabsTrigger>
                  <TabsTrigger value="recommendations">Tips</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">{generatedBlock.block?.name}</h3>
                      <Badge variant="secondary">{generatedBlock.block?.metadata?.complexity}</Badge>
                    </div>
                    
                    <p className="text-muted-foreground">{generatedBlock.block?.description}</p>
                    
                    {generatedBlock.styleConsistency && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Style Consistency</span>
                          <span className="font-medium">{generatedBlock.styleConsistency}%</span>
                        </div>
                        <Progress value={generatedBlock.styleConsistency} className="h-2" />
                      </div>
                    )}

                    {generatedBlock.block?.metadata?.shadcnComponents && (
                      <div className="space-y-2">
                        <Label>Shadcn Components Used</Label>
                        <div className="flex flex-wrap gap-1">
                          {generatedBlock.block.metadata.shadcnComponents.map((comp: string) => (
                            <Badge key={comp} variant="outline" className="text-xs">{comp}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="code">
                  <ScrollArea className="h-64 w-full rounded border">
                    <pre className="p-4 text-xs">
                      <code>{generatedBlock.block?.jsx || 'No code generated'}</code>
                    </pre>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="analysis" className="space-y-4">
                  {generatedBlock.codebaseAnalysis && (
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <Label>Total Components</Label>
                          <p className="font-medium">{generatedBlock.codebaseAnalysis.totalComponents}</p>
                        </div>
                        <div>
                          <Label>Learned Patterns</Label>
                          <p className="font-medium">{generatedBlock.learnedPatterns?.componentComposition?.length || 0}</p>
                        </div>
                      </div>
                      
                      {generatedBlock.codebaseAnalysis.commonPatterns && (
                        <div className="space-y-2">
                          <Label>Common Patterns Found</Label>
                          <div className="space-y-1">
                            {generatedBlock.codebaseAnalysis.commonPatterns.slice(0, 3).map((pattern: any, index: number) => (
                              <div key={index} className="flex items-center justify-between text-xs">
                                <span className="truncate">{pattern.pattern}</span>
                                <Badge variant="outline" className="text-xs">{pattern.frequency}</Badge>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="recommendations">
                  <div className="space-y-3">
                    {generatedBlock.recommendations?.length > 0 ? (
                      generatedBlock.recommendations.map((rec: string, index: number) => (
                        <div key={index} className="flex items-start gap-2 text-sm">
                          <Info className="w-4 h-4 mt-0.5 text-blue-500 flex-shrink-0" />
                          <span>{rec}</span>
                        </div>
                      ))
                    ) : (
                      <p className="text-muted-foreground text-sm">No specific recommendations at this time.</p>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <Layers className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Configure your intelligent block and click "Generate" to see results</p>
                {!codebaseAnalysis && (
                  <p className="text-xs mt-2">Tip: Analyze your codebase first for better results</p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Codebase Insights */}
      {codebaseAnalysis && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Codebase Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label>Most Used Components</Label>
                <div className="space-y-1">
                  {codebaseAnalysis.mostUsedComponents?.slice(0, 5).map((comp: any, index: number) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span>{comp.component}</span>
                      <Badge variant="outline" className="text-xs">{comp.usage}</Badge>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Common Patterns</Label>
                <div className="space-y-1">
                  {codebaseAnalysis.commonPatterns?.slice(0, 3).map((pattern: any, index: number) => (
                    <div key={index} className="text-sm">
                      <span className="truncate">{pattern.pattern}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Analysis Stats</Label>
                <div className="text-sm space-y-1">
                  <div>Components: {codebaseAnalysis.totalComponents}</div>
                  <div>Shadcn Usage: {codebaseAnalysis.shadcnComponentsUsed}</div>
                  <div>Last Analyzed: {new Date(codebaseAnalysis.lastAnalyzed).toLocaleTimeString()}</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
