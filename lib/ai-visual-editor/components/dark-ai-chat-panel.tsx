'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { 
  Send, 
  Sparkles, 
  Bot, 
  User, 
  Loader2, 
  Copy,
  ThumbsUp,
  ThumbsDown,
  Lightbulb,
  Code,
  Wand2
} from 'lucide-react'
import { useEditorStore } from '../stores/editor-store'
import { toast } from 'sonner'

interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  isGenerating?: boolean
  componentId?: string
}

const quickPrompts = [
  {
    icon: Wand2,
    label: 'Hero Section',
    prompt: 'Create a modern hero section with a title, subtitle, and call-to-action button'
  },
  {
    icon: Code,
    label: 'Card Component',
    prompt: 'Generate a product card with image, title, description, and price'
  },
  {
    icon: Lightbulb,
    label: 'Navigation',
    prompt: 'Build a responsive navigation bar with logo and menu items'
  },
  {
    icon: Sparkles,
    label: 'Contact Form',
    prompt: 'Create a contact form with name, email, message fields and validation'
  }
]

export function DarkAiChatPanel() {
  const { 
    chatMessages, 
    addChatMessage, 
    isAIResponding, 
    setAIResponding,
    addComponent,
    components
  } = useEditorStore()
  
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [chatMessages])

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isAIResponding) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    }

    addChatMessage(userMessage)
    setInputValue('')
    setAIResponding(true)
    setIsTyping(true)

    try {
      // Simulate AI response delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `I'll help you create a component based on your request: "${userMessage.content}". Let me generate that for you with Shadcn/UI components.`,
        timestamp: new Date()
      }

      addChatMessage(aiResponse)
      
      // Simulate component generation
      const newComponent = {
        id: `component-${Date.now()}`,
        name: `Generated Component ${components.length + 1}`,
        description: `Component generated from: ${userMessage.content}`,
        category: 'content' as const,
        jsx: `<div className="p-4 border rounded-lg">
  <h3 className="text-lg font-semibold">Generated Component</h3>
  <p className="text-gray-600">${userMessage.content}</p>
</div>`,
        props: {},
        propertiesConfig: {
          appearance: [],
          content: [],
          behavior: [],
          data: [],
          layout: []
        },
        defaultValues: {},
        createdAt: new Date(),
        updatedAt: new Date()
      }

      addComponent(newComponent)
      toast.success('Component generated successfully!')
      
    } catch (error) {
      console.error('Error generating component:', error)
      toast.error('Failed to generate component')
    } finally {
      setAIResponding(false)
      setIsTyping(false)
    }
  }

  const handleQuickPrompt = (prompt: string) => {
    setInputValue(prompt)
    inputRef.current?.focus()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard')
  }

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Chat Messages */}
      <ScrollArea ref={scrollAreaRef} className="flex-1 p-3">
        <div className="space-y-4">
          {/* Welcome Message */}
          {chatMessages.length === 0 && (
            <div className="text-center py-8">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Bot className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-gray-200 mb-2">AI Component Generator</h3>
              <p className="text-sm text-gray-400 mb-4">
                Describe the component you want to create and I'll generate it using Shadcn/UI
              </p>
              
              {/* Quick Prompts */}
              <div className="grid grid-cols-1 gap-2 max-w-sm mx-auto">
                {quickPrompts.map((prompt, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuickPrompt(prompt.prompt)}
                    className="justify-start h-auto p-3 text-left bg-gray-800 hover:bg-gray-700 border border-gray-700"
                  >
                    <prompt.icon className="w-4 h-4 mr-2 text-blue-400" />
                    <div>
                      <div className="font-medium text-gray-200">{prompt.label}</div>
                      <div className="text-xs text-gray-400 mt-1">{prompt.prompt}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Chat Messages */}
          {chatMessages.map((message) => (
            <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                <div className="flex items-center space-x-2 mb-1">
                  {message.type === 'assistant' ? (
                    <Bot className="w-4 h-4 text-blue-400" />
                  ) : (
                    <User className="w-4 h-4 text-green-400" />
                  )}
                  <span className="text-xs text-gray-400">
                    {message.type === 'assistant' ? 'AI Assistant' : 'You'}
                  </span>
                  <span className="text-xs text-gray-500">
                    {message.timestamp.toLocaleTimeString()}
                  </span>
                </div>
                
                <Card className={`p-3 ${
                  message.type === 'user' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-800 border-gray-700'
                }`}>
                  <div className="text-sm">{message.content}</div>
                  
                  {message.type === 'assistant' && (
                    <div className="flex items-center space-x-2 mt-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(message.content)}
                        className="h-6 px-2 text-gray-400 hover:text-white hover:bg-gray-700"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-gray-400 hover:text-white hover:bg-gray-700"
                      >
                        <ThumbsUp className="w-3 h-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 text-gray-400 hover:text-white hover:bg-gray-700"
                      >
                        <ThumbsDown className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                </Card>
              </div>
            </div>
          ))}

          {/* Typing Indicator */}
          {isTyping && (
            <div className="flex justify-start">
              <div className="flex items-center space-x-2">
                <Bot className="w-4 h-4 text-blue-400" />
                <Card className="p-3 bg-gray-800 border-gray-700">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </Card>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="p-3 border-t border-gray-700 bg-gray-800">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Describe the component you want to create..."
              disabled={isAIResponding}
              className="bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400 pr-10"
            />
            {isAIResponding && (
              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-blue-400 animate-spin" />
            )}
          </div>
          
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isAIResponding}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        
        <div className="flex items-center justify-between mt-2">
          <div className="text-xs text-gray-500">
            Press Enter to send, Shift+Enter for new line
          </div>
          
          <div className="flex items-center space-x-1">
            <Badge variant="outline" className="text-xs border-gray-600 text-gray-400">
              {components.length} components
            </Badge>
            {isAIResponding && (
              <Badge variant="outline" className="text-xs border-blue-400 text-blue-400 bg-blue-900/20">
                Generating...
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
