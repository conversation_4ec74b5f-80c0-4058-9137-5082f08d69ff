# Intelligent Shadcn Block Generation System

A revolutionary, template-free AI system that learns from your existing codebase to generate contextually appropriate shadcn/ui blocks that match your project's architecture, patterns, and style conventions.

## 🧠 Core Philosophy

Instead of relying on static templates, this system:
- **Learns from your codebase** to understand your architectural patterns
- **Analyzes component usage** to select optimal shadcn components
- **Matches your code style** including naming conventions, import patterns, and structure
- **Generates contextually aware** components that feel native to your project

## 🎯 Key Features

### 🔍 **Intelligent Codebase Analysis**
- **Real-time Component Discovery**: Scans your entire codebase to find React components
- **Pattern Recognition**: Identifies common architectural patterns and coding conventions
- **Usage Statistics**: Tracks which shadcn components are actually used in your project
- **Style Analysis**: Learns your naming conventions, import styles, and code structure

### 🎨 **Context-Aware Generation**
- **Template-Free**: No pre-built templates - everything is learned from your codebase
- **Style Consistency**: Generates code that matches your existing patterns
- **Smart Component Selection**: Chooses shadcn components based on actual usage data
- **Architectural Alignment**: Follows your project's file structure and organization

### 📊 **Real-Time Monitoring**
- **Component Change Tracking**: Monitors file changes in real-time
- **Pattern Evolution**: Adapts to changes in your codebase patterns
- **Usage Trend Analysis**: Identifies trending components and patterns
- **Hotspot Detection**: Finds frequently modified components

## 🏗️ System Architecture

### **Core Services**

#### 1. **CodebaseAnalyzer** (`services/codebase-analyzer.ts`)
```typescript
// Discover all components in your codebase
const components = await codebaseAnalyzer.discoverComponents()

// Get usage patterns and architectural insights
const patterns = await codebaseAnalyzer.getComponentUsagePatterns()
const architecture = await codebaseAnalyzer.getArchitecturalPatterns()
```

#### 2. **IntelligentBlockService** (`services/intelligent-block-service.ts`)
```typescript
// Generate blocks using learned patterns
const result = await intelligentBlockService.generateIntelligentBlock({
  description: 'Modern pricing section with feature comparison',
  blockType: 'pricing',
  learnFromCodebase: true,
  matchExistingStyle: true
})
```

### **API Routes**

#### 1. **Component Analysis API** (`/api/codebase/analyze-components`)
- **GET**: Analyze all components in the codebase
- **POST**: Analyze specific component code
- Returns component registry, usage patterns, and architectural insights

#### 2. **Component Monitoring API** (`/api/codebase/monitor-components`)
- **POST**: Start/stop real-time component monitoring
- **GET**: Get monitoring data and change analytics
- Supports Server-Sent Events for real-time updates

#### 3. **Intelligent Generation API** (`/api/ai-visual-editor/intelligent-blocks`)
- **POST**: Generate blocks using AI with codebase learning
- **GET**: Health checks and system statistics
- Supports both chat mode and direct API access

## 🚀 Usage Examples

### **Basic Intelligent Generation**

```typescript
import { intelligentBlockService } from '@/lib/ai-visual-editor'

// Generate a block that learns from your codebase
const result = await intelligentBlockService.generateIntelligentBlock({
  description: 'Hero section with call-to-action and feature highlights',
  blockType: 'hero',
  contextPath: 'components/marketing', // Learn from this specific area
  learnFromCodebase: true,
  matchExistingStyle: true,
  complexity: 'moderate'
})

console.log('Generated block:', result.block)
console.log('Style consistency:', result.styleConsistency + '%')
console.log('Recommendations:', result.recommendations)
```

### **Codebase Analysis**

```typescript
import { codebaseAnalyzer } from '@/lib/ai-visual-editor'

// Discover all components
const components = await codebaseAnalyzer.discoverComponents({
  patterns: ['components/**/*.tsx', 'app/**/*.tsx'],
  excludePatterns: ['**/*.test.tsx', '**/*.stories.tsx']
})

// Get usage patterns
const patterns = await codebaseAnalyzer.getComponentUsagePatterns()
console.log('Most used shadcn components:', patterns.shadcnUsage)
console.log('Common patterns:', patterns.commonPatterns)

// Get architectural patterns
const architecture = await codebaseAnalyzer.getArchitecturalPatterns()
console.log('File structure patterns:', architecture.fileStructure)
console.log('Naming conventions:', architecture.namingConventions)
```

### **Real-Time Monitoring**

```typescript
// Start monitoring component changes
const response = await fetch('/api/codebase/monitor-components', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'start',
    watchPaths: ['components', 'app', 'lib']
  })
})

const { sessionId } = await response.json()

// Get real-time updates via Server-Sent Events
const eventSource = new EventSource(
  `/api/codebase/monitor-components?sessionId=${sessionId}&action=stream`
)

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data)
  console.log('Component changes:', data.recentChanges)
  console.log('Usage patterns:', data.patterns)
}
```

### **AI Chat Integration**

```typescript
// Use with AI chat for natural language generation
const response = await fetch('/api/ai-visual-editor/intelligent-blocks', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [
      {
        role: 'user',
        content: 'Create a pricing section that matches my existing component style. Learn from my codebase and use the most common patterns.'
      }
    ]
  })
})

// The AI will automatically:
// 1. Analyze your codebase
// 2. Learn your patterns
// 3. Generate a matching component
// 4. Provide style consistency scores
// 5. Offer recommendations
```

## 🎛️ Configuration Options

### **Generation Parameters**

```typescript
interface IntelligentBlockRequest {
  description: string                    // What you want to build
  blockType: 'hero' | 'feature' | ...  // Type of block
  contextPath?: string                   // Specific area to learn from
  learnFromCodebase?: boolean           // Enable codebase learning
  matchExistingStyle?: boolean          // Match existing code style
  preferredComponents?: string[]        // Preferred shadcn components
  complexity?: 'simple' | 'moderate' | 'complex'
  requirements?: {
    responsive?: boolean
    accessibility?: boolean
    themeSupport?: boolean
    animations?: boolean
  }
}
```

### **Analysis Options**

```typescript
// Component discovery options
const components = await codebaseAnalyzer.discoverComponents({
  includeNodeModules: false,           // Include node_modules analysis
  patterns: ['components/**/*.tsx'],    // File patterns to analyze
  excludePatterns: ['**/*.test.tsx']   // Patterns to exclude
})
```

## 📊 What Gets Analyzed

### **Component Properties**
- **Imports & Exports**: How components are imported and exported
- **Props & Interfaces**: TypeScript interfaces and prop definitions
- **Shadcn Usage**: Which shadcn/ui components are actually used
- **Patterns**: Common coding patterns and conventions
- **Styling**: How styling is applied (className, cn utility, etc.)
- **Composition**: How components are composed and structured

### **Architectural Patterns**
- **File Structure**: How components are organized in directories
- **Naming Conventions**: PascalCase, camelCase, kebab-case patterns
- **Import Styles**: Named imports, default imports, namespace imports
- **Export Patterns**: Default exports vs named exports
- **Code Complexity**: Simple, moderate, or complex component structures

### **Usage Statistics**
- **Component Frequency**: How often each shadcn component is used
- **Pattern Frequency**: How often specific patterns appear
- **Style Approaches**: Most common styling approaches
- **Composition Patterns**: How components are typically composed

## 🎯 Benefits

### **For Developers**
- **Consistent Code**: Generated components match your existing style
- **Faster Development**: No need to manually analyze patterns
- **Better Architecture**: Components that fit naturally into your codebase
- **Learning Tool**: Understand your own codebase patterns better

### **For Teams**
- **Style Consistency**: Enforces team coding standards automatically
- **Onboarding**: New team members can see established patterns
- **Code Quality**: Maintains architectural consistency
- **Documentation**: Living documentation of your patterns

### **For Projects**
- **Maintainability**: Components that follow established patterns
- **Scalability**: Architecture-aware component generation
- **Quality**: Higher code quality through pattern consistency
- **Evolution**: Adapts as your codebase evolves

## 🔧 Advanced Features

### **Style Consistency Scoring**
The system provides a percentage score showing how well generated components match your existing style:

```typescript
const result = await intelligentBlockService.generateIntelligentBlock(request)
console.log(`Style consistency: ${result.styleConsistency}%`)
// 95% = Excellent match
// 80% = Good match
// 60% = Fair match
// <60% = Needs improvement
```

### **Intelligent Recommendations**
Get actionable recommendations based on your codebase analysis:

```typescript
console.log(result.recommendations)
// [
//   "Consider using Button and Card - commonly used in your codebase",
//   "Your team prefers PascalCase naming - component follows this pattern",
//   "Consider adding TypeScript interfaces for better type safety"
// ]
```

### **Learned Patterns Export**
Access the patterns learned from your codebase:

```typescript
const patterns = result.learnedPatterns
console.log('Component composition patterns:', patterns.componentComposition)
console.log('Styling approaches:', patterns.stylingApproaches)
console.log('Naming conventions:', patterns.namingConventions)
```

## 🚀 Getting Started

1. **Install Dependencies**: Ensure you have the AI Visual Editor installed
2. **Start Analysis**: The system automatically analyzes your codebase
3. **Generate Blocks**: Use the intelligent generation tools
4. **Monitor Changes**: Set up real-time monitoring for continuous learning

```typescript
import { 
  intelligentBlockService,
  codebaseAnalyzer,
  IntelligentBlockGeneratorDemo 
} from '@/lib/ai-visual-editor'

// Start using intelligent block generation
const result = await intelligentBlockService.generateIntelligentBlock({
  description: 'Your component description',
  blockType: 'hero',
  learnFromCodebase: true
})
```

## 🔮 Future Enhancements

- **Machine Learning Models**: Train custom models on your codebase
- **Team Pattern Sharing**: Share learned patterns across team projects
- **Version Control Integration**: Track pattern evolution over time
- **IDE Integration**: Real-time suggestions in your code editor
- **Performance Optimization**: Automatic performance improvements based on patterns

This intelligent system represents the future of component generation - learning from your unique codebase to create components that feel like they were written by your team, following your established patterns and conventions.
