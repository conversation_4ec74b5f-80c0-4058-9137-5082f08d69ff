'use client'

import { useState, useCallback } from 'react'
import type { 
  EnhancedGenerationRequest, 
  EnhancedGenerationResult 
} from '../services/enhanced-generation-service'

interface UseEnhancedGenerationOptions {
  onSuccess?: (result: EnhancedGenerationResult) => void
  onError?: (error: Error) => void
}

export function useEnhancedGeneration(options: UseEnhancedGenerationOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [result, setResult] = useState<EnhancedGenerationResult | null>(null)

  const generateComponent = useCallback(async (request: EnhancedGenerationRequest) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/enhanced-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate enhanced component')
      }

      const data: EnhancedGenerationResult = await response.json()
      setResult(data)
      options.onSuccess?.(data)
      
      return data
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const getCapabilities = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/enhanced-generation?action=capabilities')
      
      if (!response.ok) {
        throw new Error('Failed to get capabilities')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get capabilities')
      setError(error)
      throw error
    }
  }, [])

  const checkHealth = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/enhanced-generation?action=health')
      
      if (!response.ok) {
        throw new Error('Health check failed')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Health check failed')
      setError(error)
      throw error
    }
  }, [])

  const reset = useCallback(() => {
    setResult(null)
    setError(null)
    setIsLoading(false)
  }, [])

  return {
    // State
    isLoading,
    error,
    result,
    
    // Actions
    generateComponent,
    getCapabilities,
    checkHealth,
    reset,
    
    // Computed
    hasResult: !!result,
    hasError: !!error
  }
}

// Hook for batch generation
export function useBatchEnhancedGeneration(options: UseEnhancedGenerationOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [results, setResults] = useState<EnhancedGenerationResult[]>([])
  const [progress, setProgress] = useState(0)

  const generateComponents = useCallback(async (requests: EnhancedGenerationRequest[]) => {
    setIsLoading(true)
    setError(null)
    setResults([])
    setProgress(0)
    
    try {
      const results: EnhancedGenerationResult[] = []
      
      for (let i = 0; i < requests.length; i++) {
        const request = requests[i]
        
        const response = await fetch('/api/node/ai-visual-editor/enhanced-generation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(request)
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || `Failed to generate component ${i + 1}`)
        }

        const data: EnhancedGenerationResult = await response.json()
        results.push(data)
        setResults([...results])
        setProgress(((i + 1) / requests.length) * 100)
      }
      
      options.onSuccess?.(results[results.length - 1]) // Call with last result
      return results
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Batch generation failed')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const reset = useCallback(() => {
    setResults([])
    setError(null)
    setIsLoading(false)
    setProgress(0)
  }, [])

  return {
    // State
    isLoading,
    error,
    results,
    progress,
    
    // Actions
    generateComponents,
    reset,
    
    // Computed
    hasResults: results.length > 0,
    hasError: !!error,
    isComplete: progress === 100 && !isLoading
  }
}
