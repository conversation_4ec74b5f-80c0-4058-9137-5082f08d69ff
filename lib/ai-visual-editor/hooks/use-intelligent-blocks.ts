'use client'

import { useState, useCallback } from 'react'
import type { 
  IntelligentBlockRequest, 
  IntelligentBlockResult 
} from '../services/intelligent-block-service'

interface UseIntelligentBlocksOptions {
  onSuccess?: (result: IntelligentBlockResult) => void
  onError?: (error: Error) => void
  autoLearnFromCodebase?: boolean
}

export function useIntelligentBlocks(options: UseIntelligentBlocksOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [result, setResult] = useState<IntelligentBlockResult | null>(null)
  const [codebaseStats, setCodebaseStats] = useState<any>(null)

  const generateBlock = useCallback(async (request: IntelligentBlockRequest) => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Apply default options
      const enhancedRequest: IntelligentBlockRequest = {
        learnFromCodebase: options.autoLearnFromCodebase ?? true,
        matchExistingStyle: true,
        complexity: 'moderate',
        requirements: {
          responsive: true,
          accessibility: true,
          themeSupport: true,
          animations: false
        },
        ...request
      }

      const response = await fetch('/api/node/ai-visual-editor/intelligent-blocks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(enhancedRequest)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate intelligent block')
      }

      const data: IntelligentBlockResult = await response.json()
      setResult(data)
      options.onSuccess?.(data)
      
      return data
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const getBlockTypes = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/intelligent-blocks?action=block-types')
      
      if (!response.ok) {
        throw new Error('Failed to get block types')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get block types')
      setError(error)
      throw error
    }
  }, [])

  const getCodebaseStats = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/intelligent-blocks?action=codebase-stats')
      
      if (!response.ok) {
        throw new Error('Failed to get codebase stats')
      }

      const data = await response.json()
      setCodebaseStats(data)
      return data
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get codebase stats')
      setError(error)
      throw error
    }
  }, [])

  const checkHealth = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/intelligent-blocks?action=health')
      
      if (!response.ok) {
        throw new Error('Health check failed')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Health check failed')
      setError(error)
      throw error
    }
  }, [])

  const reset = useCallback(() => {
    setResult(null)
    setError(null)
    setIsLoading(false)
  }, [])

  return {
    // State
    isLoading,
    error,
    result,
    codebaseStats,
    
    // Actions
    generateBlock,
    getBlockTypes,
    getCodebaseStats,
    checkHealth,
    reset,
    
    // Computed
    hasResult: !!result,
    hasError: !!error,
    styleConsistency: result?.styleConsistency,
    recommendations: result?.recommendations,
    learnedPatterns: result?.learnedPatterns
  }
}

// Hook for generating multiple related blocks
export function useIntelligentBlockSuite(options: UseIntelligentBlocksOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [results, setResults] = useState<IntelligentBlockResult[]>([])
  const [progress, setProgress] = useState(0)

  const generateBlockSuite = useCallback(async (
    requests: IntelligentBlockRequest[],
    shareCodebaseAnalysis = true
  ) => {
    setIsLoading(true)
    setError(null)
    setResults([])
    setProgress(0)
    
    try {
      const results: IntelligentBlockResult[] = []
      let sharedCodebaseAnalysis: any = null
      
      for (let i = 0; i < requests.length; i++) {
        const request = requests[i]
        
        // Use shared codebase analysis for subsequent requests if enabled
        const enhancedRequest = {
          ...request,
          learnFromCodebase: i === 0 || !shareCodebaseAnalysis,
          // Pass shared analysis context if available
          ...(sharedCodebaseAnalysis && { contextPath: request.contextPath })
        }

        const response = await fetch('/api/node/ai-visual-editor/intelligent-blocks', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(enhancedRequest)
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || `Failed to generate block ${i + 1}`)
        }

        const data: IntelligentBlockResult = await response.json()
        
        // Store codebase analysis from first request
        if (i === 0 && shareCodebaseAnalysis) {
          sharedCodebaseAnalysis = data.codebaseAnalysis
        }
        
        results.push(data)
        setResults([...results])
        setProgress(((i + 1) / requests.length) * 100)
      }
      
      options.onSuccess?.(results[results.length - 1])
      return results
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Block suite generation failed')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const reset = useCallback(() => {
    setResults([])
    setError(null)
    setIsLoading(false)
    setProgress(0)
  }, [])

  return {
    // State
    isLoading,
    error,
    results,
    progress,
    
    // Actions
    generateBlockSuite,
    reset,
    
    // Computed
    hasResults: results.length > 0,
    hasError: !!error,
    isComplete: progress === 100 && !isLoading,
    averageStyleConsistency: results.length > 0 
      ? results.reduce((sum, r) => sum + (r.styleConsistency || 0), 0) / results.length 
      : 0
  }
}
