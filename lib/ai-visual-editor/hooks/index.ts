// AI Visual Editor Hooks
// Comprehensive client-side hooks for all AI Visual Editor services

// Enhanced Generation Hooks
export { 
  useEnhancedGeneration, 
  useBatchEnhancedGeneration 
} from './use-enhanced-generation'

// Intelligent Blocks Hooks
export { 
  useIntelligentBlocks, 
  useIntelligentBlockSuite 
} from './use-intelligent-blocks'

// NextJS Generator Hooks
export { 
  useNextJSGenerator, 
  useProgressiveNextJSBuilder 
} from './use-nextjs-generator'

// Component Persistence Hooks
export { 
  useComponentPersistence, 
  useAutoSave 
} from './use-component-persistence'

// Error Recovery Hooks
export { 
  useErrorRecovery, 
  useAutoErrorRecovery 
} from './use-error-recovery'

// User Experience Hooks
export { 
  useUserExperience 
} from './use-user-experience'

// Monitoring & Analytics Hooks
export { 
  useMonitoringAnalytics 
} from './use-monitoring-analytics'

// Performance Monitoring Hooks (existing)
export {
  usePerformanceMonitor,
  useComponentPerformance,
  useDebouncedUpdate,
  useVirtualization,
  useLazyComponent,
  PerformanceUtils
} from './use-performance-monitor'

// Codebase Analysis Hook (client-safe)
export { useCodebaseAnalysis } from './use-codebase-analysis'

// Re-export types for convenience
export type {
  EnhancedGenerationRequest,
  EnhancedGenerationResult
} from '../services/enhanced-generation-service'

export type {
  IntelligentBlockRequest,
  IntelligentBlockResult,
  CodebaseAnalysis,
  LearnedPatterns
} from '../services/intelligent-block-service'

export type {
  LayoutGenerationParams,
  PageGenerationParams,
  NextJSLayout,
  NextJSPage,
  GeneratedNextJSStructure
} from '../types/nextjs-types'

export type {
  ErrorContext,
  RecoveryStrategy,
  ErrorAnalysis
} from '../services/error-recovery-service'

export type {
  UserInteraction,
  UserJourney,
  UXInsights,
  PersonalizationProfile
} from '../services/user-experience-service'

export type {
  SystemMetrics,
  Alert,
  AnalyticsInsight
} from '../services/monitoring-analytics-service'

export type {
  CodebaseAnalysisOptions,
  ComponentUsagePatterns,
  ArchitecturalPatterns
} from '../services/codebase-analyzer-client'

export type { GeneratedComponent } from '../types'
