'use client'

import { useState, useCallback, useMemo } from 'react'
import { useChat } from 'ai/react'
import { GeneratedComponent } from '../types'
import { BlockTypeDefinition, BlockCategory } from '@/lib/page-builder/types'
import { 
  aiGeneratedBlocksRegistry, 
  registerAIGeneratedBlock,
  getAIGeneratedBlock,
  AIGeneratedBlock 
} from '@/lib/page-builder/blocks/ai-generated-blocks-registry'
import { useEditorStore } from '../stores/editor-store'
import { toast } from 'sonner'

export interface PageBuilderBlockGenerationParams {
  description: string
  blockType: 'hero' | 'feature' | 'testimonial' | 'pricing' | 'contact' | 'product' | 'content' | 'navigation' | 'footer' | 'custom'
  category: BlockCategory
  complexity?: 'simple' | 'moderate' | 'complex'
  learnFromExisting?: boolean
  includeConfiguration?: boolean
  responsive?: boolean
  accessibility?: boolean
}

export interface BlockGenerationResult {
  success: boolean
  block?: GeneratedComponent & {
    blockDefinition: BlockTypeDefinition
    propertiesConfig: any
  }
  error?: string
  message?: string
  analysis?: any
}

export interface BlockRegistrationResult {
  success: boolean
  blockId?: string
  error?: string
  message?: string
}

export function usePageBuilderBlocks() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [isRegistering, setIsRegistering] = useState(false)
  const [lastGeneratedBlock, setLastGeneratedBlock] = useState<GeneratedComponent | null>(null)
  
  const { addComponent } = useEditorStore()

  // Chat integration for AI block generation
  const { messages, append, isLoading } = useChat({
    api: '/api/ai-visual-editor/page-builder-blocks',
    onFinish: (message) => {
      console.log('Page builder block generation completed:', message)
    },
    onError: (error) => {
      console.error('Page builder block generation error:', error)
      toast.error('Failed to generate page builder block')
    }
  })

  /**
   * Generate a new page builder block using AI
   */
  const generatePageBuilderBlock = useCallback(async (
    params: PageBuilderBlockGenerationParams
  ): Promise<BlockGenerationResult> => {
    setIsGenerating(true)
    
    try {
      const response = await fetch('/api/ai-visual-editor/page-builder-blocks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate',
          params
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success && result.block) {
        // Add to editor store
        addComponent(result.block)
        setLastGeneratedBlock(result.block)
        
        return {
          success: true,
          block: result.block,
          message: result.message,
          analysis: result.analysis
        }
      } else {
        return {
          success: false,
          error: result.error || 'Unknown error occurred',
          message: result.message
        }
      }
    } catch (error) {
      console.error('Block generation error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate block',
        message: 'Block generation failed'
      }
    } finally {
      setIsGenerating(false)
    }
  }, [addComponent])

  /**
   * Register a generated component as a page builder block
   */
  const registerBlockInPageBuilder = useCallback(async (
    component: GeneratedComponent
  ): Promise<BlockRegistrationResult> => {
    setIsRegistering(true)

    try {
      // Create block definition from component
      const blockDefinition: BlockTypeDefinition = {
        id: component.name.toLowerCase().replace(/([A-Z])/g, '-$1').replace(/^-/, ''),
        name: component.name.toLowerCase().replace(/([A-Z])/g, '-$1').replace(/^-/, ''),
        displayName: component.name,
        description: component.description,
        category: component.category as BlockCategory,
        icon: getBlockIcon(component.category, component.metadata?.blockType || 'custom'),
        defaultConfig: component.defaultValues || {},
        configSchema: generateConfigSchema(component.propertiesConfig),
        isActive: true,
        isSystem: false,
        version: '1.0.0',
        tags: [component.category, 'ai-generated']
      }

      // Register with AI blocks registry
      const success = await registerAIGeneratedBlock(component, blockDefinition)

      if (success) {
        return {
          success: true,
          blockId: blockDefinition.id,
          message: `Successfully registered ${component.name} as a page builder block`
        }
      } else {
        return {
          success: false,
          error: 'Failed to register block in registry',
          message: 'Block registration failed'
        }
      }
    } catch (error) {
      console.error('Block registration error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed',
        message: 'Failed to register block'
      }
    } finally {
      setIsRegistering(false)
    }
  }, [])

  /**
   * Get all registered AI-generated blocks
   */
  const getRegisteredBlocks = useCallback((): AIGeneratedBlock[] => {
    return aiGeneratedBlocksRegistry.getAllAIBlocks()
  }, [])

  /**
   * Get registered blocks by category
   */
  const getRegisteredBlocksByCategory = useCallback((category: BlockCategory): AIGeneratedBlock[] => {
    return aiGeneratedBlocksRegistry.getAIBlocksByCategory(category)
  }, [])

  /**
   * Check if a block is AI-generated
   */
  const isAIGeneratedBlock = useCallback((blockId: string): boolean => {
    return aiGeneratedBlocksRegistry.isAIGeneratedBlock(blockId)
  }, [])

  /**
   * Get statistics about AI-generated blocks
   */
  const getBlockStatistics = useCallback(() => {
    return aiGeneratedBlocksRegistry.getStatistics()
  }, [])

  /**
   * Unregister a block from the page builder
   */
  const unregisterBlock = useCallback(async (blockId: string): Promise<boolean> => {
    try {
      const success = aiGeneratedBlocksRegistry.unregisterAIBlock(blockId)
      if (success) {
        toast.success('Block unregistered successfully')
      } else {
        toast.error('Failed to unregister block')
      }
      return success
    } catch (error) {
      console.error('Block unregistration error:', error)
      toast.error('Failed to unregister block')
      return false
    }
  }, [])

  /**
   * Export all AI-generated blocks
   */
  const exportBlocks = useCallback(() => {
    try {
      const exportData = aiGeneratedBlocksRegistry.exportAIBlocks()
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `ai-blocks-export-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      
      toast.success('Blocks exported successfully')
      return true
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export blocks')
      return false
    }
  }, [])

  /**
   * Import AI-generated blocks
   */
  const importBlocks = useCallback(async (file: File): Promise<boolean> => {
    try {
      const text = await file.text()
      const exportData = JSON.parse(text)
      
      const result = await aiGeneratedBlocksRegistry.importAIBlocks(exportData)
      
      if (result.successfulImports > 0) {
        toast.success(`Imported ${result.successfulImports} blocks successfully`)
      }
      
      if (result.failedImports > 0) {
        toast.error(`Failed to import ${result.failedImports} blocks`)
        console.error('Import errors:', result.errors)
      }
      
      return result.successfulImports > 0
    } catch (error) {
      console.error('Import error:', error)
      toast.error('Failed to import blocks')
      return false
    }
  }, [])

  // Memoized values
  const registeredBlocks = useMemo(() => getRegisteredBlocks(), [getRegisteredBlocks])
  const blockStatistics = useMemo(() => getBlockStatistics(), [getBlockStatistics])

  return {
    // Generation
    generatePageBuilderBlock,
    isGenerating: isGenerating || isLoading,
    lastGeneratedBlock,
    
    // Registration
    registerBlockInPageBuilder,
    unregisterBlock,
    isRegistering,
    
    // Data access
    getRegisteredBlocks,
    getRegisteredBlocksByCategory,
    registeredBlocks,
    blockStatistics,
    
    // Utilities
    isAIGeneratedBlock,
    exportBlocks,
    importBlocks,
    
    // Chat integration
    messages,
    appendMessage: append
  }
}

// Helper functions
function getBlockIcon(category: string, blockType: string): string {
  const iconMap: Record<string, Record<string, string>> = {
    content: {
      hero: '🎯',
      feature: '✨',
      testimonial: '💬',
      custom: '📝'
    },
    ecommerce: {
      product: '🛍️',
      pricing: '💰',
      custom: '🛒'
    },
    marketing: {
      contact: '📞',
      custom: '📢'
    },
    layout: {
      navigation: '🧭',
      footer: '📄',
      custom: '📐'
    },
    media: {
      custom: '🖼️'
    }
  }

  return iconMap[category]?.[blockType] || iconMap[category]?.custom || '🧩'
}

function generateConfigSchema(propertiesConfig: any): any {
  // Convert properties config to JSON schema format
  const schema: any = {
    type: 'object',
    properties: {}
  }

  if (propertiesConfig) {
    Object.entries(propertiesConfig).forEach(([sectionName, section]: [string, any]) => {
      if (section.fields) {
        Object.entries(section.fields).forEach(([fieldName, field]: [string, any]) => {
          schema.properties[fieldName] = {
            type: field.type === 'boolean' ? 'boolean' : 
                  field.type === 'number' ? 'number' : 'string',
            title: field.label,
            description: field.description
          }

          if (field.options) {
            schema.properties[fieldName].enum = field.options.map((opt: any) => opt.value)
          }
        })
      }
    })
  }

  return schema
}
