'use client'

import { useState, useCallback } from 'react'
import type { GeneratedComponent } from '../types'

interface UseComponentPersistenceOptions {
  onSuccess?: (result: any) => void
  onError?: (error: Error) => void
  autoSave?: boolean
  saveInterval?: number
}

export function useComponentPersistence(options: UseComponentPersistenceOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [savedComponents, setSavedComponents] = useState<string[]>([])
  const [lastSaved, setLastSaved] = useState<Date | null>(null)

  const saveComponent = useCallback(async (
    component: GeneratedComponent, 
    filePath: string
  ) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/component-persistence', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'save-component',
          component,
          filePath
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save component')
      }

      const data = await response.json()
      setSavedComponents(prev => [...prev.filter(p => p !== filePath), filePath])
      setLastSaved(new Date())
      options.onSuccess?.(data.result)
      
      return data.result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const loadComponent = useCallback(async (filePath: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/component-persistence', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'load-component',
          filePath
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to load component')
      }

      const data = await response.json()
      options.onSuccess?.(data.component)
      
      return data.component
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const saveProject = useCallback(async (
    components: GeneratedComponent[], 
    projectPath: string
  ) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/component-persistence', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'save-project',
          components,
          projectPath
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save project')
      }

      const data = await response.json()
      setLastSaved(new Date())
      options.onSuccess?.(data.result)
      
      return data.result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const exportComponent = useCallback(async (
    component: GeneratedComponent, 
    format: 'tsx' | 'jsx' | 'ts' | 'js' = 'tsx'
  ) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/component-persistence', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'export-component',
          component,
          format
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to export component')
      }

      const data = await response.json()
      options.onSuccess?.(data.exported)
      
      return data.exported
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const deleteComponent = useCallback(async (filePath: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/node/ai-visual-editor/component-persistence?filePath=${encodeURIComponent(filePath)}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete component')
      }

      const data = await response.json()
      setSavedComponents(prev => prev.filter(p => p !== filePath))
      options.onSuccess?.(data.result)
      
      return data.result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const listComponents = useCallback(async (projectPath: string) => {
    try {
      const response = await fetch(`/api/node/ai-visual-editor/component-persistence?action=list-components&projectPath=${encodeURIComponent(projectPath)}`)
      
      if (!response.ok) {
        throw new Error('Failed to list components')
      }

      const data = await response.json()
      return data.components
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to list components')
      setError(error)
      throw error
    }
  }, [])

  const getSupportedFormats = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/component-persistence?action=supported-formats')
      
      if (!response.ok) {
        throw new Error('Failed to get supported formats')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get supported formats')
      setError(error)
      throw error
    }
  }, [])

  const checkHealth = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/component-persistence?action=health')
      
      if (!response.ok) {
        throw new Error('Health check failed')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Health check failed')
      setError(error)
      throw error
    }
  }, [])

  const reset = useCallback(() => {
    setSavedComponents([])
    setLastSaved(null)
    setError(null)
    setIsLoading(false)
  }, [])

  return {
    // State
    isLoading,
    error,
    savedComponents,
    lastSaved,
    
    // Actions
    saveComponent,
    loadComponent,
    saveProject,
    exportComponent,
    deleteComponent,
    listComponents,
    getSupportedFormats,
    checkHealth,
    reset,
    
    // Computed
    hasSavedComponents: savedComponents.length > 0,
    hasError: !!error,
    timeSinceLastSave: lastSaved ? Date.now() - lastSaved.getTime() : null
  }
}

// Hook for auto-save functionality
export function useAutoSave(
  component: GeneratedComponent | null,
  filePath: string | null,
  options: { interval?: number; enabled?: boolean } = {}
) {
  const { interval = 30000, enabled = true } = options
  const [lastAutoSave, setLastAutoSave] = useState<Date | null>(null)
  const [autoSaveError, setAutoSaveError] = useState<Error | null>(null)
  
  const { saveComponent } = useComponentPersistence({
    onSuccess: () => {
      setLastAutoSave(new Date())
      setAutoSaveError(null)
    },
    onError: (error) => {
      setAutoSaveError(error)
    }
  })

  const performAutoSave = useCallback(async () => {
    if (!component || !filePath || !enabled) return
    
    try {
      await saveComponent(component, filePath)
    } catch (error) {
      // Error is handled by the persistence hook
    }
  }, [component, filePath, enabled, saveComponent])

  // Auto-save effect would go here in a real implementation
  // useEffect(() => {
  //   if (!enabled) return
  //   const timer = setInterval(performAutoSave, interval)
  //   return () => clearInterval(timer)
  // }, [performAutoSave, interval, enabled])

  return {
    lastAutoSave,
    autoSaveError,
    performAutoSave,
    isAutoSaveEnabled: enabled
  }
}
