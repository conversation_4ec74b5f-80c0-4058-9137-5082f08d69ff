'use client'

import { useState, useCallback } from 'react'
import type { ErrorContext, RecoveryStrategy, ErrorAnalysis } from '../services/error-recovery-service'

interface UseErrorRecoveryOptions {
  onRecovery?: (result: any) => void
  onError?: (error: Error) => void
  autoRetry?: boolean
  maxRetries?: number
}

export function useErrorRecovery(options: UseErrorRecoveryOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [recovery, setRecovery] = useState<any>(null)
  const [analysis, setAnalysis] = useState<ErrorAnalysis | null>(null)
  const [retryCount, setRetryCount] = useState(0)

  const handleError = useCallback(async (errorContext: ErrorContext) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/error-recovery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'handle-error',
          ...errorContext
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to handle error')
      }

      const data = await response.json()
      setRecovery(data.recovery)
      options.onRecovery?.(data.recovery)
      
      return data.recovery
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const analyzeError = useCallback(async (error: any, context?: any) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/error-recovery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'analyze-error',
          error,
          context
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to analyze error')
      }

      const data = await response.json()
      setAnalysis(data.analysis)
      
      return data.analysis
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const suggestFixes = useCallback(async (
    errorType: string,
    errorMessage: string,
    componentCode?: string
  ) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/error-recovery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'suggest-fixes',
          errorType,
          errorMessage,
          componentCode
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to suggest fixes')
      }

      const data = await response.json()
      return data.suggestions
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const applyFix = useCallback(async (
    fixStrategy: RecoveryStrategy,
    componentCode: string,
    errorContext?: ErrorContext
  ) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/error-recovery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'apply-fix',
          fixStrategy,
          componentCode,
          errorContext
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to apply fix')
      }

      const data = await response.json()
      options.onRecovery?.(data.result)
      
      return data.result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      
      // Auto-retry logic
      if (options.autoRetry && retryCount < (options.maxRetries || 3)) {
        setRetryCount(prev => prev + 1)
        setTimeout(() => {
          applyFix(fixStrategy, componentCode, errorContext)
        }, 1000 * Math.pow(2, retryCount)) // Exponential backoff
      }
      
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options, retryCount])

  const getErrorTypes = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/error-recovery?action=error-types')
      
      if (!response.ok) {
        throw new Error('Failed to get error types')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get error types')
      setError(error)
      throw error
    }
  }, [])

  const getRecoveryStats = useCallback(async (timeframe = '24h') => {
    try {
      const response = await fetch(`/api/node/ai-visual-editor/error-recovery?action=recovery-stats&timeframe=${timeframe}`)
      
      if (!response.ok) {
        throw new Error('Failed to get recovery stats')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get recovery stats')
      setError(error)
      throw error
    }
  }, [])

  const checkHealth = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/error-recovery?action=health')
      
      if (!response.ok) {
        throw new Error('Health check failed')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Health check failed')
      setError(error)
      throw error
    }
  }, [])

  const reset = useCallback(() => {
    setRecovery(null)
    setAnalysis(null)
    setError(null)
    setIsLoading(false)
    setRetryCount(0)
  }, [])

  return {
    // State
    isLoading,
    error,
    recovery,
    analysis,
    retryCount,
    
    // Actions
    handleError,
    analyzeError,
    suggestFixes,
    applyFix,
    getErrorTypes,
    getRecoveryStats,
    checkHealth,
    reset,
    
    // Computed
    hasRecovery: !!recovery,
    hasAnalysis: !!analysis,
    hasError: !!error,
    canRetry: retryCount < (options.maxRetries || 3)
  }
}

// Hook for automatic error handling
export function useAutoErrorRecovery(
  onError?: (error: Error, recovery?: any) => void
) {
  const [autoRecoveryEnabled, setAutoRecoveryEnabled] = useState(true)
  const [recoveryHistory, setRecoveryHistory] = useState<any[]>([])
  
  const { handleError, analyzeError } = useErrorRecovery({
    onRecovery: (recovery) => {
      setRecoveryHistory(prev => [...prev, { 
        timestamp: new Date(), 
        recovery 
      }].slice(-10)) // Keep last 10 recoveries
    }
  })

  const autoRecover = useCallback(async (error: Error, context?: any) => {
    if (!autoRecoveryEnabled) {
      onError?.(error)
      return
    }

    try {
      // First analyze the error
      const analysis = await analyzeError(error, context)
      
      // Then attempt recovery based on analysis
      const errorContext: ErrorContext = {
        errorType: analysis.type || 'unknown',
        message: error.message,
        severity: analysis.severity || 'medium',
        timestamp: new Date(),
        componentContext: context
      }
      
      const recovery = await handleError(errorContext)
      onError?.(error, recovery)
      
      return recovery
    } catch (recoveryError) {
      onError?.(error) // Original error if recovery fails
      throw recoveryError
    }
  }, [autoRecoveryEnabled, handleError, analyzeError, onError])

  return {
    autoRecoveryEnabled,
    setAutoRecoveryEnabled,
    recoveryHistory,
    autoRecover,
    clearHistory: () => setRecoveryHistory([])
  }
}
