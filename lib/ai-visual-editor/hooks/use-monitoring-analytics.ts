'use client'

import { useState, useCallback, useEffect } from 'react'
import type { 
  SystemMetrics, 
  Alert, 
  AnalyticsInsight 
} from '../services/monitoring-analytics-service'

interface UseMonitoringAnalyticsOptions {
  autoRefresh?: boolean
  refreshInterval?: number
  onAlert?: (alert: Alert) => void
  onError?: (error: Error) => void
}

export function useMonitoringAnalytics(options: UseMonitoringAnalyticsOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null)
  const [insights, setInsights] = useState<AnalyticsInsight[]>([])
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)

  const collectMetrics = useCallback(async (metricsData: SystemMetrics) => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/monitoring-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'collect-metrics',
          ...metricsData
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to collect metrics')
      }

      return true
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    }
  }, [options])

  const getSystemMetrics = useCallback(async (timeframe = '1h') => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/node/ai-visual-editor/monitoring-analytics?action=system-metrics&timeframe=${timeframe}`)
      
      if (!response.ok) {
        throw new Error('Failed to get system metrics')
      }

      const data = await response.json()
      setMetrics(data.metrics)
      setLastRefresh(new Date())
      
      return data.metrics
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get system metrics')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const getPerformanceInsights = useCallback(async (timeframe = '24h') => {
    try {
      const response = await fetch(`/api/node/ai-visual-editor/monitoring-analytics?action=performance-insights&timeframe=${timeframe}`)
      
      if (!response.ok) {
        throw new Error('Failed to get performance insights')
      }

      const data = await response.json()
      return data.insights
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get performance insights')
      setError(error)
      throw error
    }
  }, [])

  const getQualityMetrics = useCallback(async (timeframe = '7d') => {
    try {
      const response = await fetch(`/api/node/ai-visual-editor/monitoring-analytics?action=quality-metrics&timeframe=${timeframe}`)
      
      if (!response.ok) {
        throw new Error('Failed to get quality metrics')
      }

      const data = await response.json()
      return data.quality
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get quality metrics')
      setError(error)
      throw error
    }
  }, [])

  const getAlerts = useCallback(async (
    status?: 'active' | 'resolved' | 'all',
    severity?: 'low' | 'medium' | 'high' | 'critical'
  ) => {
    try {
      const params = new URLSearchParams({ action: 'alerts' })
      if (status) params.append('status', status)
      if (severity) params.append('severity', severity)

      const response = await fetch(`/api/node/ai-visual-editor/monitoring-analytics?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to get alerts')
      }

      const data = await response.json()
      setAlerts(data.alerts)
      
      // Trigger alert callbacks for new alerts
      if (options.onAlert) {
        data.alerts.forEach((alert: Alert) => {
          if (alert.status === 'active') {
            options.onAlert!(alert)
          }
        })
      }
      
      return data.alerts
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get alerts')
      setError(error)
      throw error
    }
  }, [options])

  const generateInsights = useCallback(async (timeframe = '7d') => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/node/ai-visual-editor/monitoring-analytics?action=insights&timeframe=${timeframe}`)
      
      if (!response.ok) {
        throw new Error('Failed to generate insights')
      }

      const data = await response.json()
      setInsights(data.insights)
      
      return data.insights
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to generate insights')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const getDashboardData = useCallback(async (timeframe = '24h') => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/node/ai-visual-editor/monitoring-analytics?action=dashboard-data&timeframe=${timeframe}`)
      
      if (!response.ok) {
        throw new Error('Failed to get dashboard data')
      }

      const data = await response.json()
      const dashboard = data.dashboard
      
      setMetrics(dashboard.metrics)
      setInsights(dashboard.insights)
      setAlerts(dashboard.alerts)
      setLastRefresh(new Date())
      
      return dashboard
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get dashboard data')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const createAlert = useCallback(async (alertConfig: any) => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/monitoring-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'create-alert',
          alertConfig
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create alert')
      }

      const data = await response.json()
      return data.alert
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    }
  }, [options])

  const triggerAlert = useCallback(async (
    alertId: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    message: string,
    context?: any
  ) => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/monitoring-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'trigger-alert',
          alertId,
          severity,
          message,
          context
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to trigger alert')
      }

      return true
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    }
  }, [options])

  const logPerformance = useCallback(async (
    operation: string,
    duration: number,
    success = true,
    metadata?: any
  ) => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/monitoring-analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'log-performance',
          operation,
          duration,
          success,
          metadata
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to log performance')
      }

      return true
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    }
  }, [options])

  const checkHealth = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/monitoring-analytics?action=health')
      
      if (!response.ok) {
        throw new Error('Health check failed')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Health check failed')
      setError(error)
      throw error
    }
  }, [])

  const reset = useCallback(() => {
    setMetrics(null)
    setInsights([])
    setAlerts([])
    setLastRefresh(null)
    setError(null)
    setIsLoading(false)
  }, [])

  // Auto-refresh functionality
  useEffect(() => {
    if (!options.autoRefresh) return

    const interval = options.refreshInterval || 30000 // 30 seconds default
    const timer = setInterval(() => {
      getDashboardData()
    }, interval)

    return () => clearInterval(timer)
  }, [options.autoRefresh, options.refreshInterval, getDashboardData])

  return {
    // State
    isLoading,
    error,
    metrics,
    insights,
    alerts,
    lastRefresh,
    
    // Actions
    collectMetrics,
    getSystemMetrics,
    getPerformanceInsights,
    getQualityMetrics,
    getAlerts,
    generateInsights,
    getDashboardData,
    createAlert,
    triggerAlert,
    logPerformance,
    checkHealth,
    reset,
    
    // Computed
    hasMetrics: !!metrics,
    hasInsights: insights.length > 0,
    hasAlerts: alerts.length > 0,
    activeAlerts: alerts.filter(alert => alert.status === 'active'),
    criticalAlerts: alerts.filter(alert => alert.severity === 'critical'),
    hasError: !!error,
    timeSinceLastRefresh: lastRefresh ? Date.now() - lastRefresh.getTime() : null
  }
}
