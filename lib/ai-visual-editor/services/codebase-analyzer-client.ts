import type {
  CodebaseComponent,
  ComponentCategory
} from './codebase-analyzer'

export interface CodebaseAnalysisOptions {
  includeNodeModules?: boolean
  patterns?: string[]
  excludePatterns?: string[]
}

export interface ComponentUsagePatterns {
  shadcnUsage: Record<string, number>
  commonPatterns: Array<{ pattern: string; frequency: number; examples: string[] }>
  styleApproaches: Record<string, number>
  compositionPatterns: Record<string, number>
}

export interface ArchitecturalPatterns {
  fileStructure: Record<string, number>
  namingConventions: Record<string, number>
  importPatterns: Record<string, number>
  exportPatterns: Record<string, number>
}

/**
 * Client-safe wrapper for codebase analysis
 * Uses API routes to perform server-side analysis
 */
export class CodebaseAnalyzerClient {
  private baseUrl = '/api/node/codebase/analyze'

  /**
   * Discover all components in the codebase
   */
  async discoverComponents(options: CodebaseAnalysisOptions = {}): Promise<CodebaseComponent[]> {
    const params = new URLSearchParams({
      action: 'discover-components',
      ...(options.includeNodeModules !== undefined && { 
        includeNodeModules: options.includeNodeModules.toString() 
      }),
      ...(options.patterns && { patterns: options.patterns.join(',') }),
      ...(options.excludePatterns && { excludePatterns: options.excludePatterns.join(',') })
    })

    const response = await fetch(`${this.baseUrl}?${params}`)
    
    if (!response.ok) {
      throw new Error(`Failed to discover components: ${response.statusText}`)
    }

    const data = await response.json()
    return data.components
  }

  /**
   * Analyze a specific component file
   */
  async analyzeComponentFile(filePath: string): Promise<CodebaseComponent | null> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'analyze-component',
        filePath
      })
    })

    if (!response.ok) {
      throw new Error(`Failed to analyze component: ${response.statusText}`)
    }

    const data = await response.json()
    return data.component
  }

  /**
   * Get component usage patterns across the codebase
   */
  async getComponentUsagePatterns(): Promise<ComponentUsagePatterns> {
    const response = await fetch(`${this.baseUrl}?action=usage-patterns`)
    
    if (!response.ok) {
      throw new Error(`Failed to get usage patterns: ${response.statusText}`)
    }

    const data = await response.json()
    return data.patterns
  }

  /**
   * Get architectural patterns from the codebase
   */
  async getArchitecturalPatterns(): Promise<ArchitecturalPatterns> {
    const response = await fetch(`${this.baseUrl}?action=architectural-patterns`)
    
    if (!response.ok) {
      throw new Error(`Failed to get architectural patterns: ${response.statusText}`)
    }

    const data = await response.json()
    return data.patterns
  }
}

// Export singleton instance for client use
export const codebaseAnalyzerClient = new CodebaseAnalyzerClient()
