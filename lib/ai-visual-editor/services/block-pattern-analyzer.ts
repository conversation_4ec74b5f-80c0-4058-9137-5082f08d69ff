import fs from 'fs/promises'
import path from 'path'
import { BlockCategory } from '@/lib/page-builder/types'

export interface BlockPattern {
  name: string
  category: BlockCategory
  structure: {
    hasBaseBlock: boolean
    hasConfiguration: boolean
    hasProps: boolean
    hasDefaults: boolean
  }
  imports: string[]
  shadcnComponents: string[]
  stylingPatterns: {
    containerClasses: string[]
    responsiveClasses: string[]
    spacingPatterns: string[]
  }
  configurationPatterns: {
    sections: string[]
    fieldTypes: string[]
    validationPatterns: string[]
  }
  codeStyle: {
    namingConvention: string
    exportStyle: string
    indentation: string
  }
}

export interface BlockAnalysis {
  totalBlocks: number
  categoryDistribution: Record<BlockCategory, number>
  commonPatterns: {
    mostUsedShadcnComponents: string[]
    commonImports: string[]
    stylingConventions: string[]
    configurationPatterns: string[]
  }
  recommendations: string[]
}

/**
 * Analyzes existing page builder blocks to extract patterns and conventions
 */
export async function analyzeExistingBlocks(
  targetCategory?: BlockCategory,
  targetType?: string
): Promise<BlockAnalysis> {
  try {
    const blocksDir = path.join(process.cwd(), 'lib/page-builder/blocks')
    const files = await fs.readdir(blocksDir)
    
    // Filter for TypeScript block files
    const blockFiles = files.filter(file => 
      file.endsWith('-block.tsx') && 
      file !== 'base-block.tsx' &&
      !file.startsWith('index.')
    )

    const patterns: BlockPattern[] = []
    
    for (const file of blockFiles) {
      try {
        const filePath = path.join(blocksDir, file)
        const content = await fs.readFile(filePath, 'utf-8')
        const pattern = await analyzeBlockFile(content, file)
        
        if (pattern) {
          patterns.push(pattern)
        }
      } catch (error) {
        console.warn(`Failed to analyze block file ${file}:`, error)
      }
    }

    // Filter patterns by target category if specified
    const relevantPatterns = targetCategory 
      ? patterns.filter(p => p.category === targetCategory)
      : patterns

    return generateAnalysis(relevantPatterns, targetType)
  } catch (error) {
    console.error('Error analyzing existing blocks:', error)
    return {
      totalBlocks: 0,
      categoryDistribution: {} as Record<BlockCategory, number>,
      commonPatterns: {
        mostUsedShadcnComponents: [],
        commonImports: [],
        stylingConventions: [],
        configurationPatterns: []
      },
      recommendations: ['Unable to analyze existing blocks. Using default patterns.']
    }
  }
}

/**
 * Analyzes a single block file to extract patterns
 */
async function analyzeBlockFile(content: string, filename: string): Promise<BlockPattern | null> {
  try {
    const name = filename.replace('-block.tsx', '')
    
    // Extract imports
    const imports = extractImports(content)
    
    // Extract shadcn components
    const shadcnComponents = extractShadcnComponents(imports, content)
    
    // Analyze structure
    const structure = analyzeBlockStructure(content)
    
    // Extract styling patterns
    const stylingPatterns = extractStylingPatterns(content)
    
    // Extract configuration patterns
    const configurationPatterns = extractConfigurationPatterns(content)
    
    // Analyze code style
    const codeStyle = analyzeCodeStyle(content)
    
    // Determine category based on filename and content
    const category = determineCategory(name, content)

    return {
      name,
      category,
      structure,
      imports,
      shadcnComponents,
      stylingPatterns,
      configurationPatterns,
      codeStyle
    }
  } catch (error) {
    console.warn(`Failed to analyze block file ${filename}:`, error)
    return null
  }
}

/**
 * Extracts import statements from block code
 */
function extractImports(content: string): string[] {
  const importRegex = /import\s+.*?\s+from\s+['"`]([^'"`]+)['"`]/g
  const imports: string[] = []
  let match

  while ((match = importRegex.exec(content)) !== null) {
    imports.push(match[1])
  }

  return imports
}

/**
 * Extracts shadcn/ui components used in the block
 */
function extractShadcnComponents(imports: string[], content: string): string[] {
  const shadcnImports = imports.filter(imp => imp.includes('@/components/ui/'))
  const components: string[] = []

  shadcnImports.forEach(imp => {
    const componentMatch = imp.match(/@\/components\/ui\/(.+)/)
    if (componentMatch) {
      components.push(componentMatch[1])
    }
  })

  // Also extract from destructured imports
  const destructuredRegex = /import\s+\{([^}]+)\}\s+from\s+['"`]@\/components\/ui/g
  let match

  while ((match = destructuredRegex.exec(content)) !== null) {
    const componentNames = match[1]
      .split(',')
      .map(name => name.trim())
      .filter(name => name.length > 0)
    
    components.push(...componentNames)
  }

  return [...new Set(components)]
}

/**
 * Analyzes the structural patterns of a block
 */
function analyzeBlockStructure(content: string): BlockPattern['structure'] {
  return {
    hasBaseBlock: content.includes('<BaseBlock'),
    hasConfiguration: content.includes('Config({') || content.includes('ConfigProps'),
    hasProps: content.includes('Props {') || content.includes('interface'),
    hasDefaults: content.includes('= {') || content.includes('default')
  }
}

/**
 * Extracts styling patterns from block code
 */
function extractStylingPatterns(content: string): BlockPattern['stylingPatterns'] {
  const containerClasses = extractClassPatterns(content, /className.*?["'`]([^"'`]*container[^"'`]*)["'`]/g)
  const responsiveClasses = extractClassPatterns(content, /className.*?["'`]([^"'`]*(?:md:|lg:|xl:)[^"'`]*)["'`]/g)
  const spacingPatterns = extractClassPatterns(content, /className.*?["'`]([^"'`]*(?:p-|m-|px-|py-|mx-|my-)[^"'`]*)["'`]/g)

  return {
    containerClasses,
    responsiveClasses,
    spacingPatterns
  }
}

/**
 * Extracts configuration patterns from block code
 */
function extractConfigurationPatterns(content: string): BlockPattern['configurationPatterns'] {
  const sections = extractConfigSections(content)
  const fieldTypes = extractFieldTypes(content)
  const validationPatterns = extractValidationPatterns(content)

  return {
    sections,
    fieldTypes,
    validationPatterns
  }
}

/**
 * Analyzes code style patterns
 */
function analyzeCodeStyle(content: string): BlockPattern['codeStyle'] {
  const namingConvention = content.includes('function ') && content.match(/function\s+([A-Z][a-zA-Z]*)/)?.[1] 
    ? 'PascalCase' : 'camelCase'
  
  const exportStyle = content.includes('export default') ? 'default' : 'named'
  
  const indentationMatch = content.match(/\n(\s+)/)?.[1]
  const indentation = indentationMatch || '  '

  return {
    namingConvention,
    exportStyle,
    indentation
  }
}

/**
 * Determines block category based on name and content
 */
function determineCategory(name: string, content: string): BlockCategory {
  const categoryKeywords = {
    content: ['text', 'hero', 'feature', 'testimonial'],
    ecommerce: ['product', 'cart', 'checkout', 'wishlist'],
    marketing: ['newsletter', 'contact', 'social'],
    layout: ['container', 'grid', 'columns', 'spacer'],
    media: ['image', 'video', 'gallery']
  }

  for (const [category, keywords] of Object.entries(categoryKeywords)) {
    if (keywords.some(keyword => name.includes(keyword) || content.includes(keyword))) {
      return category as BlockCategory
    }
  }

  return 'content' // default category
}

/**
 * Generates comprehensive analysis from extracted patterns
 */
function generateAnalysis(patterns: BlockPattern[], targetType?: string): BlockAnalysis {
  const totalBlocks = patterns.length
  
  // Category distribution
  const categoryDistribution = patterns.reduce((acc, pattern) => {
    acc[pattern.category] = (acc[pattern.category] || 0) + 1
    return acc
  }, {} as Record<BlockCategory, number>)

  // Common patterns
  const allShadcnComponents = patterns.flatMap(p => p.shadcnComponents)
  const componentCounts = allShadcnComponents.reduce((acc, comp) => {
    acc[comp] = (acc[comp] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const mostUsedShadcnComponents = Object.entries(componentCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([comp]) => comp)

  const commonImports = [...new Set(patterns.flatMap(p => p.imports))]
    .filter(imp => patterns.filter(p => p.imports.includes(imp)).length > 1)

  const stylingConventions = extractCommonStylingConventions(patterns)
  const configurationPatterns = extractCommonConfigurationPatterns(patterns)

  // Generate recommendations
  const recommendations = generateRecommendations(patterns, targetType)

  return {
    totalBlocks,
    categoryDistribution,
    commonPatterns: {
      mostUsedShadcnComponents,
      commonImports,
      stylingConventions,
      configurationPatterns
    },
    recommendations
  }
}

// Helper functions
function extractClassPatterns(content: string, regex: RegExp): string[] {
  const matches: string[] = []
  let match
  while ((match = regex.exec(content)) !== null) {
    matches.push(match[1])
  }
  return [...new Set(matches)]
}

function extractConfigSections(content: string): string[] {
  const sectionRegex = /<h3[^>]*>([^<]+)<\/h3>/g
  const sections: string[] = []
  let match
  while ((match = sectionRegex.exec(content)) !== null) {
    sections.push(match[1].trim())
  }
  return sections
}

function extractFieldTypes(content: string): string[] {
  const fieldTypes = ['Input', 'Textarea', 'Select', 'Checkbox', 'Switch', 'Slider']
  return fieldTypes.filter(type => content.includes(`<${type}`))
}

function extractValidationPatterns(content: string): string[] {
  const patterns: string[] = []
  if (content.includes('required')) patterns.push('required')
  if (content.includes('min=') || content.includes('max=')) patterns.push('range')
  if (content.includes('pattern=')) patterns.push('pattern')
  return patterns
}

function extractCommonStylingConventions(patterns: BlockPattern[]): string[] {
  const conventions: string[] = []
  
  // Check for common container patterns
  const hasContainer = patterns.filter(p => 
    p.stylingPatterns.containerClasses.some(cls => cls.includes('container'))
  ).length > patterns.length * 0.5

  if (hasContainer) conventions.push('Uses container mx-auto pattern')

  // Check for responsive patterns
  const hasResponsive = patterns.filter(p => 
    p.stylingPatterns.responsiveClasses.length > 0
  ).length > patterns.length * 0.7

  if (hasResponsive) conventions.push('Mobile-first responsive design')

  return conventions
}

function extractCommonConfigurationPatterns(patterns: BlockPattern[]): string[] {
  const configPatterns: string[] = []
  
  const hasUpdateConfig = patterns.filter(p => 
    p.configurationPatterns.sections.length > 0
  ).length > patterns.length * 0.5

  if (hasUpdateConfig) configPatterns.push('Uses updateConfig pattern')

  return configPatterns
}

function generateRecommendations(patterns: BlockPattern[], targetType?: string): string[] {
  const recommendations: string[] = []

  if (patterns.length === 0) {
    recommendations.push('No existing blocks found for pattern analysis')
    return recommendations
  }

  // Component usage recommendations
  const topComponents = patterns
    .flatMap(p => p.shadcnComponents)
    .reduce((acc, comp) => {
      acc[comp] = (acc[comp] || 0) + 1
      return acc
    }, {} as Record<string, number>)

  const mostUsed = Object.entries(topComponents)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 3)
    .map(([comp]) => comp)

  if (mostUsed.length > 0) {
    recommendations.push(`Consider using commonly used components: ${mostUsed.join(', ')}`)
  }

  // Styling recommendations
  const hasConsistentStyling = patterns.filter(p => 
    p.stylingPatterns.containerClasses.some(cls => cls.includes('container mx-auto'))
  ).length > patterns.length * 0.7

  if (hasConsistentStyling) {
    recommendations.push('Follow the container mx-auto px-4 md:px-6 pattern')
  }

  // Configuration recommendations
  const hasConfigSections = patterns.filter(p => 
    p.configurationPatterns.sections.length > 2
  ).length > patterns.length * 0.5

  if (hasConfigSections) {
    recommendations.push('Organize configuration into logical sections')
  }

  return recommendations
}
