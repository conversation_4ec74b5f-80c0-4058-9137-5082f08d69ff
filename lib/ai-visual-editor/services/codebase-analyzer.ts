import { promises as fs } from 'fs'
import path from 'path'

export interface CodebaseComponent {
  name: string
  filePath: string
  code: string
  imports: ImportInfo[]
  exports: ExportInfo[]
  props: PropDefinition[]
  shadcnComponents: string[]
  patterns: UsagePattern[]
  stylePatterns: StylePattern[]
  compositionPatterns: CompositionPattern[]
  complexity: 'simple' | 'moderate' | 'complex'
  category: ComponentCategory
  relationships: ComponentRelationship[]
}

export interface ImportInfo {
  source: string
  imports: string[]
  type: 'default' | 'named' | 'namespace'
}

export interface ExportInfo {
  name: string
  type: 'function' | 'const' | 'class' | 'interface' | 'type'
  isDefault: boolean
}

export interface PropDefinition {
  name: string
  type: string
  required: boolean
  defaultValue?: string
  description?: string
  examples?: string[]
}

export interface UsagePattern {
  type: 'hook' | 'prop' | 'jsx' | 'styling' | 'state'
  pattern: string
  frequency: number
  context: string[]
  examples: string[]
}

export interface StylePattern {
  type: 'className' | 'inline' | 'css-in-js' | 'css-modules'
  pattern: string
  frequency: number
  examples: string[]
}

export interface CompositionPattern {
  type: 'children' | 'render-prop' | 'hoc' | 'compound' | 'forwarded-ref'
  pattern: string
  examples: string[]
}

export interface ComponentRelationship {
  type: 'imports' | 'exports' | 'uses' | 'extends' | 'wraps'
  target: string
  context: string
}

export type ComponentCategory = 
  | 'layout' | 'form' | 'navigation' | 'data' | 'feedback' 
  | 'media' | 'utility' | 'page' | 'feature' | 'ui'

export class CodebaseAnalyzer {
  private projectRoot: string
  private componentCache: Map<string, CodebaseComponent> = new Map()
  private analysisCache: Map<string, any> = new Map()

  constructor(projectRoot?: string) {
    this.projectRoot = projectRoot || process.cwd()
  }

  /**
   * Discover all components in the codebase
   */
  async discoverComponents(options: {
    includeNodeModules?: boolean
    patterns?: string[]
    excludePatterns?: string[]
  } = {}): Promise<CodebaseComponent[]> {
    const {
      includeNodeModules = false,
      patterns = [
        'components/**/*.{tsx,jsx}',
        'app/**/*.{tsx,jsx}',
        'lib/**/*.{tsx,jsx}',
        'src/**/*.{tsx,jsx}'
      ],
      excludePatterns = [
        '**/node_modules/**',
        '**/.next/**',
        '**/dist/**',
        '**/build/**',
        '**/*.test.{tsx,jsx}',
        '**/*.spec.{tsx,jsx}',
        '**/*.stories.{tsx,jsx}'
      ]
    } = options

    const allPatterns = includeNodeModules 
      ? [...patterns, 'node_modules/@/**/*.{tsx,jsx}']
      : patterns

    const componentFiles: string[] = []

    // Use simple directory traversal instead of glob
    for (const pattern of patterns) {
      const dirPath = pattern.split('/')[0] // Get the base directory
      const fullDirPath = path.join(this.projectRoot, dirPath)

      try {
        const files = await this.findFilesRecursively(fullDirPath, /\.(tsx|jsx)$/)
        componentFiles.push(...files.map(f => path.relative(this.projectRoot, f)))
      } catch (error) {
        // Directory doesn't exist, skip it
        continue
      }
    }

    const components: CodebaseComponent[] = []
    
    for (const filePath of componentFiles) {
      try {
        const component = await this.analyzeComponentFile(filePath)
        if (component) {
          components.push(component)
          this.componentCache.set(filePath, component)
        }
      } catch (error) {
        console.warn(`Failed to analyze ${filePath}:`, error)
      }
    }

    // Build relationships between components
    this.buildComponentRelationships(components)

    return components
  }

  /**
   * Analyze a specific component file
   */
  async analyzeComponentFile(filePath: string): Promise<CodebaseComponent | null> {
    const fullPath = path.join(this.projectRoot, filePath)
    
    try {
      const code = await fs.readFile(fullPath, 'utf-8')
      
      // Skip non-React component files
      if (!this.isReactComponent(code)) {
        return null
      }

      const componentName = this.extractComponentName(filePath, code)
      
      return {
        name: componentName,
        filePath,
        code,
        imports: this.extractImports(code),
        exports: this.extractExports(code),
        props: this.extractProps(code),
        shadcnComponents: this.extractShadcnComponents(code),
        patterns: this.extractUsagePatterns(code),
        stylePatterns: this.extractStylePatterns(code),
        compositionPatterns: this.extractCompositionPatterns(code),
        complexity: this.calculateComplexity(code),
        category: this.categorizeComponent(code, componentName),
        relationships: [] // Will be populated later
      }
    } catch (error) {
      console.error(`Error analyzing ${filePath}:`, error)
      return null
    }
  }

  /**
   * Get component usage patterns across the codebase
   */
  async getComponentUsagePatterns(): Promise<{
    shadcnUsage: Record<string, number>
    commonPatterns: Array<{ pattern: string; frequency: number; examples: string[] }>
    styleApproaches: Record<string, number>
    compositionPatterns: Record<string, number>
  }> {
    const cacheKey = 'usage-patterns'
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)
    }

    const components = await this.discoverComponents()
    
    const shadcnUsage: Record<string, number> = {}
    const patternFrequency: Record<string, { frequency: number; examples: string[] }> = {}
    const styleApproaches: Record<string, number> = {}
    const compositionPatterns: Record<string, number> = {}

    components.forEach(component => {
      // Track shadcn component usage
      component.shadcnComponents.forEach(comp => {
        shadcnUsage[comp] = (shadcnUsage[comp] || 0) + 1
      })

      // Track usage patterns
      component.patterns.forEach(pattern => {
        const key = `${pattern.type}:${pattern.pattern}`
        if (!patternFrequency[key]) {
          patternFrequency[key] = { frequency: 0, examples: [] }
        }
        patternFrequency[key].frequency += pattern.frequency
        patternFrequency[key].examples.push(...pattern.examples.slice(0, 2))
      })

      // Track style approaches
      component.stylePatterns.forEach(style => {
        styleApproaches[style.type] = (styleApproaches[style.type] || 0) + style.frequency
      })

      // Track composition patterns
      component.compositionPatterns.forEach(comp => {
        compositionPatterns[comp.type] = (compositionPatterns[comp.type] || 0) + 1
      })
    })

    const commonPatterns = Object.entries(patternFrequency)
      .sort(([, a], [, b]) => b.frequency - a.frequency)
      .slice(0, 20)
      .map(([pattern, data]) => ({
        pattern,
        frequency: data.frequency,
        examples: [...new Set(data.examples)].slice(0, 3)
      }))

    const result = {
      shadcnUsage,
      commonPatterns,
      styleApproaches,
      compositionPatterns
    }

    this.analysisCache.set(cacheKey, result)
    return result
  }

  /**
   * Get architectural patterns from the codebase
   */
  async getArchitecturalPatterns(): Promise<{
    fileStructure: Record<string, number>
    namingConventions: Record<string, number>
    importPatterns: Record<string, number>
    exportPatterns: Record<string, number>
  }> {
    const components = await this.discoverComponents()
    
    const fileStructure: Record<string, number> = {}
    const namingConventions: Record<string, number> = {}
    const importPatterns: Record<string, number> = {}
    const exportPatterns: Record<string, number> = {}

    components.forEach(component => {
      // Analyze file structure
      const dir = path.dirname(component.filePath)
      const dirPattern = dir.split('/').slice(0, 2).join('/')
      fileStructure[dirPattern] = (fileStructure[dirPattern] || 0) + 1

      // Analyze naming conventions
      const namingPattern = this.analyzeNamingPattern(component.name)
      namingConventions[namingPattern] = (namingConventions[namingPattern] || 0) + 1

      // Analyze import patterns
      component.imports.forEach(imp => {
        if (imp.source.startsWith('@/')) {
          const pattern = imp.source.split('/').slice(0, 2).join('/')
          importPatterns[pattern] = (importPatterns[pattern] || 0) + 1
        }
      })

      // Analyze export patterns
      component.exports.forEach(exp => {
        const pattern = `${exp.type}${exp.isDefault ? ':default' : ':named'}`
        exportPatterns[pattern] = (exportPatterns[pattern] || 0) + 1
      })
    })

    return {
      fileStructure,
      namingConventions,
      importPatterns,
      exportPatterns
    }
  }

  // Private helper methods
  private async findFilesRecursively(dir: string, pattern: RegExp): Promise<string[]> {
    const files: string[] = []

    try {
      const entries = await fs.readdir(dir, { withFileTypes: true })

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name)

        if (entry.isDirectory()) {
          // Skip certain directories
          if (['.next', 'dist', 'build', 'node_modules'].includes(entry.name)) {
            continue
          }
          const subFiles = await this.findFilesRecursively(fullPath, pattern)
          files.push(...subFiles)
        } else if (entry.isFile() && pattern.test(entry.name)) {
          // Skip test and spec files
          if (!entry.name.includes('.test.') && !entry.name.includes('.spec.')) {
            files.push(fullPath)
          }
        }
      }
    } catch (error) {
      // Directory doesn't exist or can't be read
    }

    return files
  }

  private isReactComponent(code: string): boolean {
    const reactPatterns = [
      /import\s+.*React/,
      /from\s+['"]react['"]/,
      /export\s+(default\s+)?function\s+[A-Z]/,
      /export\s+(default\s+)?const\s+[A-Z].*=.*=>/,
      /function\s+[A-Z].*\(.*\).*{.*return.*</,
      /const\s+[A-Z].*=.*\(.*\).*=>/,
      /<[A-Z]/
    ]

    return reactPatterns.some(pattern => pattern.test(code))
  }

  private extractComponentName(filePath: string, code: string): string {
    // Try to extract from export default
    const defaultExportMatch = code.match(/export\s+default\s+(?:function\s+)?(\w+)/)
    if (defaultExportMatch) {
      return defaultExportMatch[1]
    }

    // Try to extract from function declaration
    const functionMatch = code.match(/export\s+(?:default\s+)?function\s+(\w+)/)
    if (functionMatch) {
      return functionMatch[1]
    }

    // Try to extract from const declaration
    const constMatch = code.match(/export\s+(?:default\s+)?const\s+(\w+)/)
    if (constMatch) {
      return constMatch[1]
    }

    // Fallback to filename
    return path.basename(filePath, path.extname(filePath))
  }

  private extractImports(code: string): ImportInfo[] {
    const imports: ImportInfo[] = []
    const importRegex = /import\s+(?:(\w+)(?:\s*,\s*)?)?(?:\{\s*([^}]+)\s*\})?(?:\s*,\s*(\w+))?\s+from\s+['"]([^'"]+)['"]/g
    
    let match
    while ((match = importRegex.exec(code)) !== null) {
      const [, defaultImport, namedImports, namespaceImport, source] = match
      
      const importInfo: ImportInfo = {
        source,
        imports: [],
        type: 'named'
      }

      if (defaultImport) {
        importInfo.imports.push(defaultImport)
        importInfo.type = 'default'
      }

      if (namedImports) {
        const named = namedImports.split(',').map(imp => imp.trim()).filter(Boolean)
        importInfo.imports.push(...named)
        importInfo.type = 'named'
      }

      if (namespaceImport) {
        importInfo.imports.push(namespaceImport)
        importInfo.type = 'namespace'
      }

      imports.push(importInfo)
    }

    return imports
  }

  private extractExports(code: string): ExportInfo[] {
    const exports: ExportInfo[] = []
    
    // Export patterns
    const patterns = [
      { regex: /export\s+default\s+(function|const|class)\s+(\w+)/, isDefault: true },
      { regex: /export\s+(function|const|class|interface|type)\s+(\w+)/, isDefault: false },
      { regex: /export\s+\{\s*([^}]+)\s*\}/, isDefault: false }
    ]

    patterns.forEach(({ regex, isDefault }) => {
      let match
      while ((match = regex.exec(code)) !== null) {
        if (isDefault || !match[0].includes('{')) {
          exports.push({
            name: match[2],
            type: match[1] as any,
            isDefault
          })
        } else {
          // Handle named exports in braces
          const namedExports = match[1].split(',').map(exp => exp.trim())
          namedExports.forEach(exp => {
            exports.push({
              name: exp,
              type: 'const',
              isDefault: false
            })
          })
        }
      }
    })

    return exports
  }

  private extractProps(code: string): PropDefinition[] {
    const props: PropDefinition[] = []
    
    // Extract interface/type definitions
    const interfaceRegex = /(?:interface|type)\s+(\w+Props)\s*=?\s*\{([^}]+)\}/g
    let match

    while ((match = interfaceRegex.exec(code)) !== null) {
      const propsContent = match[2]
      const propMatches = propsContent.match(/(\w+)(\?)?:\s*([^;\n,]+)/g)
      
      if (propMatches) {
        propMatches.forEach(propMatch => {
          const propParts = propMatch.match(/(\w+)(\?)?:\s*([^;\n,]+)/)
          if (propParts) {
            const [, name, optional, type] = propParts
            props.push({
              name,
              type: type.trim(),
              required: !optional,
              description: this.extractPropDescription(code, name)
            })
          }
        })
      }
    }

    return props
  }

  private extractShadcnComponents(code: string): string[] {
    const shadcnRegex = /import\s*\{([^}]+)\}\s*from\s*['"]@\/components\/ui(?:\/[^'"]*)?['"]/g
    const components: string[] = []
    let match

    while ((match = shadcnRegex.exec(code)) !== null) {
      const imported = match[1]
        .split(',')
        .map(comp => comp.trim())
        .filter(Boolean)
      components.push(...imported)
    }

    return [...new Set(components)]
  }

  private extractUsagePatterns(code: string): UsagePattern[] {
    const patterns: UsagePattern[] = []
    
    const patternChecks = [
      { type: 'hook', pattern: 'useState', regex: /useState\([^)]*\)/g },
      { type: 'hook', pattern: 'useEffect', regex: /useEffect\([^)]*\)/g },
      { type: 'hook', pattern: 'useCallback', regex: /useCallback\([^)]*\)/g },
      { type: 'hook', pattern: 'useMemo', regex: /useMemo\([^)]*\)/g },
      { type: 'styling', pattern: 'cn utility', regex: /cn\([^)]*\)/g },
      { type: 'styling', pattern: 'className', regex: /className\s*=\s*[^>\s]+/g },
      { type: 'prop', pattern: 'children', regex: /\bchildren\b/g },
      { type: 'jsx', pattern: 'conditional rendering', regex: /\{[^}]*\?\s*[^:}]+\s*:\s*[^}]+\}/g }
    ]

    patternChecks.forEach(({ type, pattern, regex }) => {
      const matches = [...code.matchAll(regex)]
      if (matches.length > 0) {
        patterns.push({
          type: type as any,
          pattern,
          frequency: matches.length,
          context: this.extractContext(code, matches),
          examples: matches.slice(0, 3).map(m => m[0])
        })
      }
    })

    return patterns
  }

  private extractStylePatterns(code: string): StylePattern[] {
    const patterns: StylePattern[] = []
    
    const styleChecks = [
      { type: 'className', regex: /className\s*=\s*["'`][^"'`]*["'`]/g },
      { type: 'className', regex: /className\s*=\s*\{[^}]+\}/g },
      { type: 'inline', regex: /style\s*=\s*\{[^}]+\}/g }
    ]

    styleChecks.forEach(({ type, regex }) => {
      const matches = [...code.matchAll(regex)]
      if (matches.length > 0) {
        patterns.push({
          type: type as any,
          pattern: type,
          frequency: matches.length,
          examples: matches.slice(0, 3).map(m => m[0])
        })
      }
    })

    return patterns
  }

  private extractCompositionPatterns(code: string): CompositionPattern[] {
    const patterns: CompositionPattern[] = []
    
    const compositionChecks = [
      { type: 'children', regex: /\bchildren\b/g },
      { type: 'render-prop', regex: /render\s*=\s*\{[^}]+\}/g },
      { type: 'forwarded-ref', regex: /forwardRef/g },
      { type: 'compound', regex: /\w+\.\w+/g }
    ]

    compositionChecks.forEach(({ type, regex }) => {
      const matches = [...code.matchAll(regex)]
      if (matches.length > 0) {
        patterns.push({
          type: type as any,
          pattern: type,
          examples: matches.slice(0, 2).map(m => m[0])
        })
      }
    })

    return patterns
  }

  private calculateComplexity(code: string): 'simple' | 'moderate' | 'complex' {
    const lines = code.split('\n').length
    const hooks = (code.match(/use\w+/g) || []).length
    const conditionals = (code.match(/if\s*\(|switch\s*\(|\?\s*:/g) || []).length
    const loops = (code.match(/for\s*\(|while\s*\(|\.map\(|\.forEach\(/g) || []).length
    const jsx = (code.match(/<\w+/g) || []).length
    
    const score = lines * 0.1 + hooks * 3 + conditionals * 2 + loops * 2 + jsx * 0.5
    
    if (score < 25) return 'simple'
    if (score < 75) return 'moderate'
    return 'complex'
  }

  private categorizeComponent(code: string, name: string): ComponentCategory {
    const categories = {
      'form': /input|form|field|button|select|checkbox|radio|submit/i,
      'layout': /layout|container|grid|flex|wrapper|section|header|footer/i,
      'navigation': /nav|menu|breadcrumb|pagination|link|router/i,
      'data': /table|list|card|item|row|column|data/i,
      'feedback': /alert|toast|notification|modal|dialog|popup/i,
      'media': /image|video|avatar|icon|logo|gallery/i,
      'page': /page|screen|view|template/i,
      'feature': /feature|widget|component|block/i
    }

    for (const [category, regex] of Object.entries(categories)) {
      if (regex.test(name) || regex.test(code)) {
        return category as ComponentCategory
      }
    }

    return 'ui'
  }

  private buildComponentRelationships(components: CodebaseComponent[]): void {
    const componentMap = new Map(components.map(c => [c.name, c]))

    components.forEach(component => {
      component.imports.forEach(imp => {
        if (imp.source.startsWith('@/')) {
          imp.imports.forEach(importName => {
            const target = componentMap.get(importName)
            if (target) {
              component.relationships.push({
                type: 'imports',
                target: importName,
                context: imp.source
              })
            }
          })
        }
      })
    })
  }

  private extractContext(code: string, matches: RegExpMatchArray[]): string[] {
    return matches.map(match => {
      const index = match.index || 0
      const start = Math.max(0, index - 50)
      const end = Math.min(code.length, index + match[0].length + 50)
      return code.slice(start, end).trim()
    }).slice(0, 3)
  }

  private extractPropDescription(code: string, propName: string): string | undefined {
    const descRegex = new RegExp(`\\*\\s*${propName}\\s*-\\s*([^\\n]+)`, 'i')
    const match = code.match(descRegex)
    return match ? match[1].trim() : undefined
  }

  private analyzeNamingPattern(name: string): string {
    if (/^[A-Z][a-z]+(?:[A-Z][a-z]+)*$/.test(name)) return 'PascalCase'
    if (/^[a-z]+(?:[A-Z][a-z]+)*$/.test(name)) return 'camelCase'
    if (/^[a-z]+(?:-[a-z]+)*$/.test(name)) return 'kebab-case'
    if (/^[a-z]+(?:_[a-z]+)*$/.test(name)) return 'snake_case'
    return 'mixed'
  }
}

// Export singleton instance
export const codebaseAnalyzer = new CodebaseAnalyzer()
