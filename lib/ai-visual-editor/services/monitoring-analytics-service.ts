import { openai } from '@ai-sdk/openai'
import { generateObject } from 'ai'
import { z } from 'zod'

export interface SystemMetrics {
  timestamp: Date
  performance: {
    responseTime: number
    throughput: number
    errorRate: number
    cpuUsage: number
    memoryUsage: number
  }
  quality: {
    averageScore: number
    accessibilityCompliance: number
    performanceScore: number
    userSatisfaction: number
  }
  usage: {
    activeUsers: number
    totalRequests: number
    successfulGenerations: number
    failedGenerations: number
  }
  agents: {
    activeAgents: number
    averageExecutionTime: number
    successRate: number
    resourceUtilization: number
  }
}

export interface Alert {
  id: string
  type: 'performance' | 'quality' | 'error' | 'security' | 'usage'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  details: any
  timestamp: Date
  resolved: boolean
  actions: string[]
}

export interface AnalyticsInsight {
  category: 'performance' | 'quality' | 'usage' | 'user-behavior' | 'system-health'
  insight: string
  impact: 'positive' | 'negative' | 'neutral'
  confidence: number
  recommendations: string[]
  data: any
}

export class MonitoringAnalyticsService {
  private model = openai('gpt-4o')
  private metrics: SystemMetrics[] = []
  private alerts: Alert[] = []
  private thresholds = {
    responseTime: 5000, // 5 seconds
    errorRate: 0.05, // 5%
    qualityScore: 80,
    memoryUsage: 0.85 // 85%
  }

  /**
   * Collect and store system metrics
   */
  collectMetrics(metrics: Partial<SystemMetrics>): void {
    const fullMetrics: SystemMetrics = {
      timestamp: new Date(),
      performance: {
        responseTime: 0,
        throughput: 0,
        errorRate: 0,
        cpuUsage: 0,
        memoryUsage: 0,
        ...metrics.performance
      },
      quality: {
        averageScore: 0,
        accessibilityCompliance: 0,
        performanceScore: 0,
        userSatisfaction: 0,
        ...metrics.quality
      },
      usage: {
        activeUsers: 0,
        totalRequests: 0,
        successfulGenerations: 0,
        failedGenerations: 0,
        ...metrics.usage
      },
      agents: {
        activeAgents: 0,
        averageExecutionTime: 0,
        successRate: 0,
        resourceUtilization: 0,
        ...metrics.agents
      }
    }

    this.metrics.push(fullMetrics)
    this.checkThresholds(fullMetrics)
    
    // Keep only last 1000 metrics for memory management
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }
  }

  /**
   * Generate comprehensive analytics insights
   */
  async generateInsights(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<{
    insights: AnalyticsInsight[]
    trends: {
      performance: 'improving' | 'stable' | 'declining'
      quality: 'improving' | 'stable' | 'declining'
      usage: 'growing' | 'stable' | 'declining'
    }
    predictions: {
      nextHourLoad: number
      qualityTrend: number
      resourceNeeds: string[]
    }
    recommendations: string[]
  }> {
    const relevantMetrics = this.getMetricsForTimeRange(timeRange)
    
    const { object: analysis } = await generateObject({
      model: this.model,
      schema: z.object({
        insights: z.array(z.object({
          category: z.enum(['performance', 'quality', 'usage', 'user-behavior', 'system-health']),
          insight: z.string(),
          impact: z.enum(['positive', 'negative', 'neutral']),
          confidence: z.number().min(0).max(100),
          recommendations: z.array(z.string()),
          priority: z.number().min(1).max(10)
        })),
        trends: z.object({
          performance: z.enum(['improving', 'stable', 'declining']),
          quality: z.enum(['improving', 'stable', 'declining']),
          usage: z.enum(['growing', 'stable', 'declining']),
          reasoning: z.string()
        }),
        predictions: z.object({
          nextHourLoad: z.number(),
          qualityTrend: z.number(),
          resourceNeeds: z.array(z.string()),
          confidence: z.number().min(0).max(100)
        }),
        recommendations: z.array(z.string()),
        anomalies: z.array(z.string()),
        optimizationOpportunities: z.array(z.string())
      }),
      system: `You are an advanced analytics specialist for AI-powered systems.
      
      Analyze system metrics to provide:
      1. Actionable insights about performance, quality, and usage
      2. Trend analysis and pattern recognition
      3. Predictive analytics for capacity planning
      4. Optimization recommendations
      5. Anomaly detection and root cause analysis
      
      Focus on business impact and operational excellence.`,
      prompt: `Analyze these system metrics for the ${timeRange} period:
      
      Metrics Summary:
      - Total Data Points: ${relevantMetrics.length}
      - Average Response Time: ${this.calculateAverage(relevantMetrics, 'performance.responseTime')}ms
      - Average Quality Score: ${this.calculateAverage(relevantMetrics, 'quality.averageScore')}
      - Error Rate: ${this.calculateAverage(relevantMetrics, 'performance.errorRate') * 100}%
      - Active Users: ${this.calculateAverage(relevantMetrics, 'usage.activeUsers')}
      
      Recent Alerts: ${this.alerts.slice(-10).map(a => a.message).join(', ')}
      
      Detailed Metrics:
      ${JSON.stringify(relevantMetrics.slice(-20), null, 2)}
      
      Provide comprehensive analysis with actionable insights and recommendations.`
    })

    return {
      insights: analysis.insights.map(insight => ({
        ...insight,
        data: relevantMetrics
      })),
      trends: analysis.trends,
      predictions: analysis.predictions,
      recommendations: analysis.recommendations
    }
  }

  /**
   * Real-time anomaly detection
   */
  async detectAnomalies(): Promise<{
    anomalies: Array<{
      type: string
      description: string
      severity: 'low' | 'medium' | 'high'
      affectedMetrics: string[]
      suggestedActions: string[]
    }>
    systemHealth: 'healthy' | 'warning' | 'critical'
    recommendations: string[]
  }> {
    const recentMetrics = this.metrics.slice(-50) // Last 50 data points
    
    if (recentMetrics.length < 10) {
      return {
        anomalies: [],
        systemHealth: 'healthy',
        recommendations: ['Collect more data for accurate anomaly detection']
      }
    }

    const { object: detection } = await generateObject({
      model: this.model,
      schema: z.object({
        anomalies: z.array(z.object({
          type: z.string(),
          description: z.string(),
          severity: z.enum(['low', 'medium', 'high']),
          affectedMetrics: z.array(z.string()),
          suggestedActions: z.array(z.string()),
          confidence: z.number().min(0).max(100)
        })),
        systemHealth: z.enum(['healthy', 'warning', 'critical']),
        recommendations: z.array(z.string()),
        patterns: z.array(z.string()),
        rootCauses: z.array(z.string())
      }),
      system: `You are an anomaly detection specialist for AI systems.
      
      Detect anomalies by analyzing:
      1. Statistical deviations from normal patterns
      2. Performance degradation trends
      3. Quality score fluctuations
      4. Resource utilization spikes
      5. Error rate increases
      
      Provide actionable detection results with clear severity assessment.`,
      prompt: `Detect anomalies in these recent system metrics:
      
      Metrics: ${JSON.stringify(recentMetrics, null, 2)}
      
      Thresholds:
      - Response Time: ${this.thresholds.responseTime}ms
      - Error Rate: ${this.thresholds.errorRate * 100}%
      - Quality Score: ${this.thresholds.qualityScore}
      - Memory Usage: ${this.thresholds.memoryUsage * 100}%
      
      Identify any anomalies, patterns, or concerning trends.`
    })

    return detection
  }

  /**
   * Generate performance optimization recommendations
   */
  async generateOptimizationRecommendations(): Promise<{
    recommendations: Array<{
      category: 'performance' | 'quality' | 'cost' | 'reliability'
      title: string
      description: string
      impact: 'low' | 'medium' | 'high'
      effort: 'low' | 'medium' | 'high'
      implementation: string[]
      metrics: string[]
    }>
    prioritizedActions: string[]
    estimatedImpact: {
      performanceGain: number
      qualityImprovement: number
      costReduction: number
    }
  }> {
    const { object: optimization } = await generateObject({
      model: this.model,
      schema: z.object({
        recommendations: z.array(z.object({
          category: z.enum(['performance', 'quality', 'cost', 'reliability']),
          title: z.string(),
          description: z.string(),
          impact: z.enum(['low', 'medium', 'high']),
          effort: z.enum(['low', 'medium', 'high']),
          implementation: z.array(z.string()),
          metrics: z.array(z.string()),
          roi: z.number()
        })),
        prioritizedActions: z.array(z.string()),
        estimatedImpact: z.object({
          performanceGain: z.number(),
          qualityImprovement: z.number(),
          costReduction: z.number(),
          timeframe: z.string()
        }),
        riskAssessment: z.array(z.string())
      }),
      system: `You are an optimization specialist for AI-powered systems.
      
      Provide optimization recommendations based on:
      1. Performance bottlenecks and inefficiencies
      2. Quality improvement opportunities
      3. Cost optimization potential
      4. Reliability and stability enhancements
      5. ROI analysis and prioritization
      
      Focus on practical, implementable solutions with measurable benefits.`,
      prompt: `Generate optimization recommendations based on current system performance:
      
      Current Metrics:
      ${JSON.stringify(this.getLatestMetrics(), null, 2)}
      
      Recent Trends:
      ${JSON.stringify(this.calculateTrends(), null, 2)}
      
      Active Alerts:
      ${this.alerts.filter(a => !a.resolved).map(a => a.message).join(', ')}
      
      Provide actionable optimization recommendations with clear ROI analysis.`
    })

    return optimization
  }

  /**
   * Create custom dashboard data
   */
  getDashboardData(timeRange: 'hour' | 'day' | 'week' = 'day') {
    const metrics = this.getMetricsForTimeRange(timeRange)
    
    return {
      summary: {
        totalRequests: metrics.reduce((sum, m) => sum + m.usage.totalRequests, 0),
        averageResponseTime: this.calculateAverage(metrics, 'performance.responseTime'),
        successRate: this.calculateSuccessRate(metrics),
        qualityScore: this.calculateAverage(metrics, 'quality.averageScore'),
        activeUsers: Math.max(...metrics.map(m => m.usage.activeUsers))
      },
      charts: {
        responseTime: metrics.map(m => ({ time: m.timestamp, value: m.performance.responseTime })),
        qualityScore: metrics.map(m => ({ time: m.timestamp, value: m.quality.averageScore })),
        errorRate: metrics.map(m => ({ time: m.timestamp, value: m.performance.errorRate })),
        usage: metrics.map(m => ({ time: m.timestamp, value: m.usage.totalRequests }))
      },
      alerts: this.alerts.filter(a => !a.resolved).slice(-10),
      health: this.calculateSystemHealth()
    }
  }

  // Helper methods
  private checkThresholds(metrics: SystemMetrics): void {
    const alerts: Alert[] = []

    if (metrics.performance.responseTime > this.thresholds.responseTime) {
      alerts.push(this.createAlert('performance', 'high', 'Response time exceeded threshold', {
        current: metrics.performance.responseTime,
        threshold: this.thresholds.responseTime
      }))
    }

    if (metrics.performance.errorRate > this.thresholds.errorRate) {
      alerts.push(this.createAlert('error', 'high', 'Error rate exceeded threshold', {
        current: metrics.performance.errorRate,
        threshold: this.thresholds.errorRate
      }))
    }

    if (metrics.quality.averageScore < this.thresholds.qualityScore) {
      alerts.push(this.createAlert('quality', 'medium', 'Quality score below threshold', {
        current: metrics.quality.averageScore,
        threshold: this.thresholds.qualityScore
      }))
    }

    this.alerts.push(...alerts)
  }

  private createAlert(type: Alert['type'], severity: Alert['severity'], message: string, details: any): Alert {
    return {
      id: `alert_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type,
      severity,
      message,
      details,
      timestamp: new Date(),
      resolved: false,
      actions: this.getRecommendedActions(type, severity)
    }
  }

  private getRecommendedActions(type: Alert['type'], severity: Alert['severity']): string[] {
    const actions = {
      performance: ['Check system resources', 'Review recent deployments', 'Analyze slow queries'],
      quality: ['Review generation parameters', 'Check validation rules', 'Analyze user feedback'],
      error: ['Check error logs', 'Review recent changes', 'Monitor system health'],
      security: ['Review access logs', 'Check for suspicious activity', 'Update security measures'],
      usage: ['Monitor capacity', 'Check for unusual patterns', 'Review scaling policies']
    }

    return actions[type] || ['Investigate issue', 'Monitor closely', 'Contact support if needed']
  }

  private getMetricsForTimeRange(timeRange: string): SystemMetrics[] {
    const now = Date.now()
    const ranges = {
      hour: 60 * 60 * 1000,
      day: 24 * 60 * 60 * 1000,
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000
    }

    const cutoff = now - (ranges[timeRange as keyof typeof ranges] || ranges.day)
    return this.metrics.filter(m => m.timestamp.getTime() > cutoff)
  }

  private calculateAverage(metrics: SystemMetrics[], path: string): number {
    if (metrics.length === 0) return 0
    
    const values = metrics.map(m => this.getNestedValue(m, path)).filter(v => typeof v === 'number')
    return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  private calculateSuccessRate(metrics: SystemMetrics[]): number {
    const total = metrics.reduce((sum, m) => sum + m.usage.totalRequests, 0)
    const successful = metrics.reduce((sum, m) => sum + m.usage.successfulGenerations, 0)
    return total > 0 ? (successful / total) * 100 : 0
  }

  private getLatestMetrics(): SystemMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null
  }

  private calculateTrends(): any {
    if (this.metrics.length < 2) return {}
    
    const recent = this.metrics.slice(-10)
    const older = this.metrics.slice(-20, -10)
    
    return {
      responseTime: this.calculateTrend(older, recent, 'performance.responseTime'),
      qualityScore: this.calculateTrend(older, recent, 'quality.averageScore'),
      errorRate: this.calculateTrend(older, recent, 'performance.errorRate')
    }
  }

  private calculateTrend(older: SystemMetrics[], recent: SystemMetrics[], path: string): string {
    const olderAvg = this.calculateAverage(older, path)
    const recentAvg = this.calculateAverage(recent, path)
    
    if (recentAvg > olderAvg * 1.1) return 'increasing'
    if (recentAvg < olderAvg * 0.9) return 'decreasing'
    return 'stable'
  }

  private calculateSystemHealth(): 'healthy' | 'warning' | 'critical' {
    const activeAlerts = this.alerts.filter(a => !a.resolved)
    const criticalAlerts = activeAlerts.filter(a => a.severity === 'critical')
    const highAlerts = activeAlerts.filter(a => a.severity === 'high')
    
    if (criticalAlerts.length > 0) return 'critical'
    if (highAlerts.length > 2) return 'critical'
    if (activeAlerts.length > 5) return 'warning'
    return 'healthy'
  }
}

export const monitoringAnalyticsService = new MonitoringAnalyticsService()
