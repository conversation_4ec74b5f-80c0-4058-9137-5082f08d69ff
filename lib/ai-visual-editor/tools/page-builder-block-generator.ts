import { tool } from 'ai'
import { z } from 'zod'
import { generateObject } from 'ai'
import { openai } from '@ai-sdk/openai'
import { blockRegistry } from '@/lib/page-builder/blocks/registry'
import { BlockTypeDefinition, BlockCategory } from '@/lib/page-builder/types'
import { analyzeExistingBlocks } from '../services/block-pattern-analyzer'
import { generateBlockPropertiesConfig } from '../utils/block-properties-generator'
import { validateBlockCode, sanitizeBlockCode } from '../utils/component-analyzer'

// Schema for AI-generated page builder blocks
const pageBuilderBlockSchema = z.object({
  name: z.string(),
  displayName: z.string(),
  description: z.string(),
  category: z.enum(['content', 'ecommerce', 'marketing', 'layout', 'media']),
  jsx: z.string(),
  configurationInterface: z.string(),
  defaultConfig: z.record(z.any()),
  configSchema: z.record(z.any()),
  imports: z.array(z.string()).optional(),
  dependencies: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional()
})

export const generatePageBuilderBlockTool = tool({
  description: 'Generate page builder blocks that follow existing patterns and integrate with the block system',
  parameters: z.object({
    description: z.string().describe('Description of the block to generate'),
    blockType: z.enum(['hero', 'feature', 'testimonial', 'pricing', 'contact', 'product', 'content', 'navigation', 'footer', 'custom']),
    category: z.enum(['content', 'ecommerce', 'marketing', 'layout', 'media']),
    complexity: z.enum(['simple', 'moderate', 'complex']).default('moderate'),
    learnFromExisting: z.boolean().default(true).describe('Whether to analyze existing blocks for patterns'),
    includeConfiguration: z.boolean().default(true).describe('Whether to generate configuration component'),
    responsive: z.boolean().default(true),
    accessibility: z.boolean().default(true)
  }),
  execute: async ({ 
    description, 
    blockType, 
    category, 
    complexity, 
    learnFromExisting, 
    includeConfiguration, 
    responsive, 
    accessibility 
  }) => {
    try {
      const startTime = Date.now()

      // Analyze existing blocks if requested
      let blockPatterns = null
      let existingBlocks: BlockTypeDefinition[] = []
      
      if (learnFromExisting) {
        blockPatterns = await analyzeExistingBlocks(category, blockType)
        existingBlocks = blockRegistry.getBlocksByCategory(category)
      }

      // Generate the block using AI with learned patterns
      const { object } = await generateObject({
        model: openai('gpt-4o'),
        schema: pageBuilderBlockSchema,
        prompt: `
          Generate a page builder block component that follows these exact patterns:

          **Requirements:**
          - Description: ${description}
          - Type: ${blockType}
          - Category: ${category}
          - Complexity: ${complexity}
          - Responsive: ${responsive ? 'Required' : 'Optional'}
          - Accessibility: ${accessibility ? 'Required' : 'Optional'}

          **Page Builder Integration Requirements:**
          1. Must use BaseBlock wrapper from '@/lib/page-builder/blocks/base-block'
          2. Must accept PageBlock as prop with proper typing
          3. Must include isEditing prop for edit mode handling
          4. Must follow the exact pattern of existing blocks
          5. Configuration must be typed interface extending BlockConfiguration

          **Existing Block Patterns to Follow:**
          ${blockPatterns ? JSON.stringify(blockPatterns, null, 2) : 'Use standard page builder patterns'}

          **Code Structure Requirements:**
          \`\`\`tsx
          'use client'

          import React from 'react'
          import { PageBlock, [ConfigInterface] } from '../types'
          import { BaseBlock } from './base-block'
          import { [ShadcnComponents] } from '@/components/ui/[component]'
          import { cn } from '@/lib/utils'

          interface [BlockName]Props {
            block: PageBlock
            isEditing?: boolean
          }

          export function [BlockName]({ block, isEditing = false }: [BlockName]Props) {
            const config = block.configuration as [ConfigInterface]
            
            // Extract configuration with defaults
            const {
              // config properties with defaults
            } = config

            return (
              <BaseBlock block={block} isEditing={isEditing}>
                <section className="[responsive-classes]">
                  <div className="container mx-auto px-4 md:px-6">
                    {/* Block content following existing patterns */}
                  </div>
                </section>
              </BaseBlock>
            )
          }

          // Configuration Component (if includeConfiguration is true)
          interface [BlockName]ConfigProps {
            config: [ConfigInterface]
            onChange: (config: [ConfigInterface]) => void
          }

          export function [BlockName]Config({ config, onChange }: [BlockName]ConfigProps) {
            const updateConfig = (updates: Partial<[ConfigInterface]>) => {
              onChange({ ...config, ...updates })
            }

            return (
              <div className="space-y-6">
                {/* Configuration sections using existing patterns */}
              </div>
            )
          }
          \`\`\`

          **Styling Guidelines:**
          - Use Tailwind CSS with responsive classes
          - Follow existing block styling patterns
          - Use shadcn/ui components consistently
          - Include proper hover states and transitions
          - Ensure mobile-first responsive design

          **Accessibility Requirements:**
          ${accessibility ? `
          - Include proper ARIA labels and roles
          - Ensure keyboard navigation support
          - Use semantic HTML elements
          - Include screen reader support
          - Follow WCAG 2.1 guidelines
          ` : 'Basic accessibility is sufficient'}

          Generate a production-ready block that seamlessly integrates with the existing page builder system.
        `
      })

      // Validate the generated code
      const validation = validateBlockCode(object.jsx)
      if (!validation.isValid) {
        throw new Error(`Block validation failed: ${validation.errors.join(', ')}`)
      }

      const sanitizedCode = sanitizeBlockCode(object.jsx)

      // Generate properties configuration for the block
      const propertiesConfig = await generateBlockPropertiesConfig(
        object.configurationInterface || '',
        object.defaultConfig,
        category
      )

      // Create block type definition for registry
      const blockDefinition: BlockTypeDefinition = {
        id: object.name.toLowerCase().replace(/([A-Z])/g, '-$1').replace(/^-/, ''),
        name: object.name.toLowerCase().replace(/([A-Z])/g, '-$1').replace(/^-/, ''),
        displayName: object.displayName,
        description: object.description,
        category: object.category,
        icon: getBlockIcon(object.category, blockType),
        defaultConfig: object.defaultConfig,
        configSchema: object.configSchema,
        isActive: true,
        isSystem: false,
        version: '1.0.0',
        tags: object.tags || [blockType, object.category]
      }

      const generationTime = Date.now() - startTime

      return {
        success: true,
        block: {
          id: `ai-generated-${Date.now()}`,
          name: object.name,
          displayName: object.displayName,
          description: object.description,
          category: object.category,
          jsx: sanitizedCode,
          configurationComponent: includeConfiguration ? object.configurationInterface : null,
          blockDefinition,
          propertiesConfig,
          defaultConfig: object.defaultConfig,
          metadata: {
            generationType: 'page-builder-block',
            complexity,
            learnedFrom: existingBlocks.length,
            blockType,
            category,
            generationTime,
            responsive,
            accessibility
          }
        },
        analysis: {
          learnedPatterns: blockPatterns,
          usedExistingPatterns: learnFromExisting,
          similarBlocks: existingBlocks.map(b => b.name),
          complexity
        },
        message: `Generated ${object.displayName} block for page builder with ${complexity} complexity`,
        performance: {
          generationTime,
          codeQuality: validation.score || 85
        }
      }
    } catch (error) {
      console.error('Page builder block generation error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate page builder block',
        message: 'Block generation failed. Please check your requirements and try again.',
        suggestions: [
          'Ensure the description is clear and specific',
          'Try a simpler complexity level',
          'Check if the block type matches the category',
          'Verify existing blocks are available for pattern learning'
        ]
      }
    }
  }
})

// Helper function to get appropriate icon for block category and type
function getBlockIcon(category: BlockCategory, blockType: string): string {
  const iconMap: Record<string, Record<string, string>> = {
    content: {
      hero: '🎯',
      feature: '✨',
      testimonial: '💬',
      custom: '📝'
    },
    ecommerce: {
      product: '🛍️',
      pricing: '💰',
      custom: '🛒'
    },
    marketing: {
      contact: '📞',
      custom: '📢'
    },
    layout: {
      navigation: '🧭',
      footer: '📄',
      custom: '📐'
    },
    media: {
      custom: '🖼️'
    }
  }

  return iconMap[category]?.[blockType] || iconMap[category]?.custom || '🧩'
}
