import { tool } from 'ai'
import { z } from 'zod'
import { codebaseAnalyzerClient } from '../services/codebase-analyzer-client'
import type { CodebaseComponent } from '../services/codebase-analyzer'

// Intelligent block generation tool that learns from codebase patterns
export const generateIntelligentBlockTool = tool({
  description: 'Generate blocks by analyzing existing codebase patterns and component usage',
  parameters: z.object({
    description: z.string().describe('Description of the block to generate'),
    blockType: z.enum(['hero', 'feature', 'testimonial', 'pricing', 'contact', 'product', 'content', 'navigation', 'footer']),
    learnFromCodebase: z.boolean().default(true).describe('Whether to analyze codebase patterns'),
    matchExistingStyle: z.boolean().default(true).describe('Whether to match existing code style'),
    preferredComponents: z.array(z.string()).optional().describe('Preferred shadcn components to use'),
    complexity: z.enum(['simple', 'moderate', 'complex']).default('moderate')
  }),
  execute: async ({ description, blockType, learnFromCodebase, matchExistingStyle, preferredComponents, complexity }) => {
    try {
      let codebasePatterns = null
      let existingComponents: CodebaseComponent[] = []
      
      if (learnFromCodebase) {
        // Analyze codebase to learn patterns
        codebasePatterns = await analyzeCodebaseForPatterns()
        existingComponents = await codebaseAnalyzerClient.discoverComponents()
      }

      // Generate block based on learned patterns
      const generatedBlock = await generateBlockFromPatterns({
        description,
        blockType,
        codebasePatterns,
        existingComponents,
        matchExistingStyle,
        preferredComponents,
        complexity
      })

      return {
        success: true,
        block: generatedBlock,
        learnedPatterns: codebasePatterns,
        usedComponents: generatedBlock.shadcnComponents,
        styleConsistency: calculateStyleConsistency(generatedBlock, existingComponents),
        recommendations: generateIntelligentRecommendations(generatedBlock, codebasePatterns),
        message: `Generated ${blockType} block using ${generatedBlock.shadcnComponents.length} components based on codebase analysis`
      }
    } catch (error) {
      console.error('Intelligent block generation error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate intelligent block',
        message: 'Block generation failed. Please check your requirements.'
      }
    }
  }
})

// Tool for analyzing codebase component patterns
export const analyzeCodebasePatternsToolTool = tool({
  description: 'Analyze existing codebase to discover component patterns and usage',
  parameters: z.object({
    analysisType: z.enum(['components', 'patterns', 'architecture', 'comprehensive']).default('comprehensive'),
    includeNodeModules: z.boolean().default(false),
    focusArea: z.enum(['shadcn', 'styling', 'composition', 'all']).default('all')
  }),
  execute: async ({ analysisType, includeNodeModules, focusArea }) => {
    try {
      const components = await codebaseAnalyzerClient.discoverComponents({ includeNodeModules })
      
      let analysis: any = {}
      
      if (analysisType === 'components' || analysisType === 'comprehensive') {
        analysis.componentRegistry = buildComponentRegistry(components)
        analysis.shadcnUsage = analyzeShadcnUsage(components)
      }
      
      if (analysisType === 'patterns' || analysisType === 'comprehensive') {
        analysis.usagePatterns = await codebaseAnalyzerClient.getComponentUsagePatterns()
      }
      
      if (analysisType === 'architecture' || analysisType === 'comprehensive') {
        analysis.architecturalPatterns = await codebaseAnalyzerClient.getArchitecturalPatterns()
      }

      return {
        success: true,
        analysis,
        totalComponents: components.length,
        shadcnComponents: extractUniqueShadcnComponents(components),
        recommendations: generateCodebaseRecommendations(analysis),
        message: `Analyzed ${components.length} components and discovered ${Object.keys(analysis.componentRegistry || {}).length} patterns`
      }
    } catch (error) {
      console.error('Codebase analysis error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to analyze codebase',
        message: 'Codebase analysis failed.'
      }
    }
  }
})

// Tool for generating context-aware components
export const generateContextAwareComponentTool = tool({
  description: 'Generate components that match existing codebase architecture and patterns',
  parameters: z.object({
    componentType: z.string().describe('Type of component to generate'),
    contextPath: z.string().describe('File path context where component will be used'),
    requirements: z.string().describe('Specific requirements for the component'),
    inheritPatterns: z.boolean().default(true).describe('Whether to inherit patterns from similar components')
  }),
  execute: async ({ componentType, contextPath, requirements, inheritPatterns }) => {
    try {
      // Analyze context area
      const contextAnalysis = await analyzeContextArea(contextPath)
      
      // Find similar components
      const similarComponents = await findSimilarComponents(componentType, contextAnalysis)
      
      // Generate component based on context
      const component = await generateContextualComponent({
        componentType,
        contextPath,
        requirements,
        contextAnalysis,
        similarComponents,
        inheritPatterns
      })

      return {
        success: true,
        component,
        contextAnalysis,
        similarComponents: similarComponents.map(c => ({ name: c.name, similarity: c.similarity })),
        inheritedPatterns: component.inheritedPatterns,
        message: `Generated ${componentType} component matching codebase patterns`
      }
    } catch (error) {
      console.error('Context-aware generation error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate context-aware component',
        message: 'Context-aware generation failed.'
      }
    }
  }
})

// Helper functions
async function analyzeCodebaseForPatterns() {
  const usagePatterns = await codebaseAnalyzerClient.getComponentUsagePatterns()
  const architecturalPatterns = await codebaseAnalyzerClient.getArchitecturalPatterns()
  
  return {
    shadcnUsage: usagePatterns.shadcnUsage,
    commonPatterns: usagePatterns.commonPatterns,
    styleApproaches: usagePatterns.styleApproaches,
    compositionPatterns: usagePatterns.compositionPatterns,
    fileStructure: architecturalPatterns.fileStructure,
    namingConventions: architecturalPatterns.namingConventions,
    importPatterns: architecturalPatterns.importPatterns,
    exportPatterns: architecturalPatterns.exportPatterns
  }
}

async function generateBlockFromPatterns(config: {
  description: string
  blockType: string
  codebasePatterns: any
  existingComponents: CodebaseComponent[]
  matchExistingStyle: boolean
  preferredComponents?: string[]
  complexity: string
}) {
  const {
    description,
    blockType,
    codebasePatterns,
    existingComponents,
    matchExistingStyle,
    preferredComponents,
    complexity
  } = config

  // Select components based on codebase usage
  const selectedComponents = selectComponentsFromPatterns(
    blockType,
    codebasePatterns?.shadcnUsage || {},
    preferredComponents
  )

  // Generate code following existing patterns
  const codeStyle = matchExistingStyle ? extractCodeStyle(existingComponents) : getDefaultCodeStyle()
  
  // Find similar existing components for reference
  const similarComponents = findSimilarComponentsByType(existingComponents, blockType)
  
  const jsx = generateIntelligentJSX({
    blockType,
    description,
    components: selectedComponents,
    codeStyle,
    similarComponents,
    complexity
  })

  const props = generateIntelligentProps(selectedComponents, similarComponents)
  
  return {
    id: `intelligent-${blockType}-${Date.now()}`,
    name: generateComponentName(blockType, description, codeStyle.namingConvention),
    description,
    jsx,
    props,
    shadcnComponents: selectedComponents,
    codeStyle,
    inheritedPatterns: extractInheritedPatterns(similarComponents),
    complexity
  }
}

function selectComponentsFromPatterns(
  blockType: string,
  shadcnUsage: Record<string, number>,
  preferredComponents?: string[]
): string[] {
  // Start with preferred components if provided
  let components = preferredComponents ? [...preferredComponents] : []
  
  // Add most used components from codebase
  const mostUsed = Object.entries(shadcnUsage)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([comp]) => comp)
  
  // Add block-type specific components
  const blockSpecific = getBlockSpecificComponents(blockType)
  
  // Combine and deduplicate
  components = [...new Set([...components, ...mostUsed, ...blockSpecific])]
  
  return components.slice(0, 6) // Limit to 6 components
}

function extractCodeStyle(components: CodebaseComponent[]) {
  const styles = {
    namingConvention: 'PascalCase',
    importStyle: 'named',
    exportStyle: 'default',
    indentation: '  ',
    quotesStyle: 'single',
    semicolons: true,
    trailingCommas: true
  }

  // Analyze existing components to extract style preferences
  if (components.length > 0) {
    // Extract naming convention
    const namingPatterns = components.map(c => analyzeNamingPattern(c.name))
    styles.namingConvention = getMostCommon(namingPatterns)
    
    // Extract import/export patterns
    const importStyles = components.flatMap(c => c.imports.map(i => i.type))
    styles.importStyle = getMostCommon(importStyles)
    
    const exportStyles = components.flatMap(c => c.exports.map(e => e.isDefault ? 'default' : 'named'))
    styles.exportStyle = getMostCommon(exportStyles)
  }

  return styles
}

function generateIntelligentJSX(config: {
  blockType: string
  description: string
  components: string[]
  codeStyle: any
  similarComponents: CodebaseComponent[]
  complexity: string
}): string {
  const { blockType, description, components, codeStyle, similarComponents, complexity } = config
  
  const componentName = generateComponentName(blockType, description, codeStyle.namingConvention)
  const imports = generateImports(components, codeStyle)
  const interfaceName = `${componentName}Props`
  
  // Extract patterns from similar components
  const patterns = extractPatternsFromSimilar(similarComponents)
  
  return `${imports}

interface ${interfaceName} {
  className?: string
  ${generatePropsInterface(components, patterns)}
}

export ${codeStyle.exportStyle === 'default' ? 'default ' : ''}function ${componentName}({
  className,
  ...props
}: ${interfaceName}) {
  return (
    <div className={cn("${generateBaseClasses(blockType, complexity)}", className)}>
      ${generateJSXContent(blockType, components, patterns, complexity)}
    </div>
  )
}`
}

// Utility functions
function buildComponentRegistry(components: CodebaseComponent[]) {
  return components.reduce((registry, comp) => {
    registry[comp.name] = {
      filePath: comp.filePath,
      category: comp.category,
      complexity: comp.complexity,
      shadcnComponents: comp.shadcnComponents,
      patterns: comp.patterns.map(p => p.pattern)
    }
    return registry
  }, {} as Record<string, any>)
}

function analyzeShadcnUsage(components: CodebaseComponent[]) {
  const usage: Record<string, number> = {}
  
  components.forEach(comp => {
    comp.shadcnComponents.forEach(shadcnComp => {
      usage[shadcnComp] = (usage[shadcnComp] || 0) + 1
    })
  })
  
  return Object.entries(usage)
    .sort(([, a], [, b]) => b - a)
    .reduce((sorted, [comp, count]) => {
      sorted[comp] = count
      return sorted
    }, {} as Record<string, number>)
}

function extractUniqueShadcnComponents(components: CodebaseComponent[]): string[] {
  const allComponents = components.flatMap(c => c.shadcnComponents)
  return [...new Set(allComponents)].sort()
}

function calculateStyleConsistency(block: any, existingComponents: CodebaseComponent[]): number {
  // Calculate how well the generated block matches existing style patterns
  let score = 0
  const totalChecks = 5
  
  // Check naming convention consistency
  const existingNaming = existingComponents.map(c => analyzeNamingPattern(c.name))
  const blockNaming = analyzeNamingPattern(block.name)
  if (existingNaming.includes(blockNaming)) score++
  
  // Check component usage consistency
  const commonComponents = existingComponents.flatMap(c => c.shadcnComponents)
  const usedCommonComponents = block.shadcnComponents.filter((comp: string) => commonComponents.includes(comp))
  if (usedCommonComponents.length > 0) score++
  
  // Add more consistency checks...
  score += 3 // Placeholder for additional checks
  
  return Math.round((score / totalChecks) * 100)
}

function generateIntelligentRecommendations(block: any, patterns: any): string[] {
  const recommendations: string[] = []
  
  if (patterns?.shadcnUsage) {
    const topComponents = Object.keys(patterns.shadcnUsage).slice(0, 3)
    const unusedTopComponents = topComponents.filter(comp => !block.shadcnComponents.includes(comp))
    
    if (unusedTopComponents.length > 0) {
      recommendations.push(`Consider using ${unusedTopComponents.join(', ')} - commonly used in your codebase`)
    }
  }
  
  if (block.complexity === 'complex') {
    recommendations.push('Consider breaking this complex component into smaller, reusable pieces')
  }
  
  return recommendations
}

function generateCodebaseRecommendations(analysis: any): string[] {
  const recommendations: string[] = []
  
  if (analysis.shadcnUsage) {
    const totalUsage = Object.values(analysis.shadcnUsage).reduce((sum: number, count: any) => sum + count, 0)
    if (totalUsage < 10) {
      recommendations.push('Consider using more shadcn/ui components for consistency')
    }
  }
  
  return recommendations
}

// Helper function implementations
async function analyzeContextArea(_contextPath: string) { return {} }
async function findSimilarComponents(_type: string, _context: any) { return [] }
async function generateContextualComponent(_config: any) { return {} }
function getDefaultCodeStyle() { return { namingConvention: 'PascalCase' } }
function findSimilarComponentsByType(_components: CodebaseComponent[], _type: string) { return [] }
function generateComponentName(type: string, _desc: string, _naming: string) { return `${type}Block` }
function generateIntelligentProps(_components: string[], _similar: any[]) { return {} }
function extractInheritedPatterns(_similar: any[]) { return [] }
function getBlockSpecificComponents(_type: string): string[] { return ['Card', 'Button'] }
function analyzeNamingPattern(_name: string): string { return 'PascalCase' }
function getMostCommon<T>(items: T[]): T { return items[0] }
function generateImports(components: string[], _style: any): string { return `import { ${components.join(', ')} } from '@/components/ui'` }
function generatePropsInterface(_components: string[], _patterns: any): string { return '' }
function extractPatternsFromSimilar(_similar: any[]): any { return {} }
function generateBaseClasses(_type: string, _complexity: string): string { return 'p-6 rounded-lg' }
function generateJSXContent(_type: string, _components: string[], _patterns: any, _complexity: string): string { return '/* Generated content */' }
