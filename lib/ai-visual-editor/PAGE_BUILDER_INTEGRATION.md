# AI Visual Editor - Page Builder Integration

This document describes the enhanced AI Visual Editor system that generates blocks following your existing page builder patterns and integrates seamlessly with the page builder system.

## 🎯 Overview

The Page Builder Integration extends the AI Visual Editor to:

1. **Generate Similar Block Types**: Create blocks that follow the same architectural patterns, component structure, and coding conventions as existing blocks
2. **Dynamic Rendering**: Render AI-generated blocks within the visual editor interface with seamless integration
3. **Live Editing Capabilities**: Provide real-time editing with properties panel integration and visual drag-and-drop
4. **Block-Specific Editors**: Generate appropriate property editors for each block type with contextual editing experiences

## 🏗️ Architecture

### Core Components

#### 1. Page Builder Block Generator (`tools/page-builder-block-generator.ts`)
- Analyzes existing blocks in `/lib/page-builder/blocks/`
- Generates blocks with proper `BaseBlock` integration
- Creates matching configuration components
- Follows block registry patterns

#### 2. Block Pattern Analyzer (`services/block-pattern-analyzer.ts`)
- Extracts patterns from existing blocks
- Identifies styling conventions
- Understands configuration structures
- Provides intelligent recommendations

#### 3. Block Properties Generator (`utils/block-properties-generator.ts`)
- Generates properties panels using existing custom fields
- Analyzes block props and creates appropriate field types
- Uses custom fields from `@lib/core/builders/components/properties-panel/custom-fields/`
- Generates validation schemas

#### 4. AI-Generated Blocks Registry (`lib/page-builder/blocks/ai-generated-blocks-registry.ts`)
- Manages dynamic registration of AI-generated blocks
- Maintains block metadata and lifecycle
- Provides statistics and cleanup utilities
- Handles import/export functionality

#### 5. Page Builder Block Renderer (`components/page-builder-block-renderer.tsx`)
- Renders AI-generated blocks within the page builder
- Provides error boundaries and loading states
- Includes block controls and debugging features
- Handles live property updates

## 🚀 Usage

### Basic Usage

```tsx
import { PageBuilderAIEditor } from '@/lib/ai-visual-editor'

function MyPageBuilder() {
  return (
    <PageBuilderAIEditor
      onBlockGenerated={(blockId) => {
        console.log('New block generated:', blockId)
      }}
      onBlockRegistered={(blockId) => {
        console.log('Block registered in page builder:', blockId)
      }}
      initialCategory="content"
    />
  )
}
```

### Using the Hook

```tsx
import { usePageBuilderBlocks } from '@/lib/ai-visual-editor'

function MyComponent() {
  const {
    generatePageBuilderBlock,
    registerBlockInPageBuilder,
    registeredBlocks,
    isGenerating
  } = usePageBuilderBlocks()

  const handleGenerate = async () => {
    const result = await generatePageBuilderBlock({
      description: 'Create a hero section for an e-commerce store',
      blockType: 'hero',
      category: 'ecommerce',
      complexity: 'moderate'
    })

    if (result.success) {
      await registerBlockInPageBuilder(result.block)
    }
  }

  return (
    <button onClick={handleGenerate} disabled={isGenerating}>
      Generate Block
    </button>
  )
}
```

### API Integration

```typescript
// Generate a page builder block via API
const response = await fetch('/api/ai-visual-editor/page-builder-blocks', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'generate',
    params: {
      description: 'Create a testimonials section',
      blockType: 'testimonial',
      category: 'marketing',
      complexity: 'moderate',
      learnFromExisting: true,
      includeConfiguration: true
    }
  })
})

const result = await response.json()
```

## 🎨 Block Generation Process

### 1. Pattern Analysis
The system analyzes existing blocks to understand:
- Component structure and naming conventions
- Styling patterns and responsive design
- Configuration interfaces and property types
- Import/export patterns and dependencies

### 2. AI Generation
Using learned patterns, the AI generates:
- React component following existing conventions
- TypeScript interfaces with proper typing
- Configuration component with properties panel
- Default configuration and validation schema

### 3. Integration
Generated blocks are:
- Validated for code quality and structure
- Registered in the block registry
- Made available in the page builder
- Equipped with live editing capabilities

## 📋 Block Structure

Generated blocks follow this exact pattern:

```tsx
'use client'

import React from 'react'
import { PageBlock, BlockConfiguration } from '../types'
import { BaseBlock } from './base-block'
import { Button, Card } from '@/components/ui/[components]'
import { cn } from '@/lib/utils'

interface MyBlockConfig extends BlockConfiguration {
  title: string
  description?: string
  // ... other properties
}

interface MyBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function MyBlock({ block, isEditing = false }: MyBlockProps) {
  const config = block.configuration as MyBlockConfig
  
  return (
    <BaseBlock block={block} isEditing={isEditing}>
      <section className="py-8">
        <div className="container mx-auto px-4 md:px-6">
          {/* Block content */}
        </div>
      </section>
    </BaseBlock>
  )
}

// Configuration Component
export function MyBlockConfig({ config, onChange }: ConfigProps) {
  // Configuration interface using custom fields
}
```

## 🔧 Configuration

### Block Categories
- `content`: Text, hero, feature sections
- `ecommerce`: Product grids, pricing, cart blocks
- `marketing`: Testimonials, contact forms, newsletters
- `layout`: Containers, grids, navigation
- `media`: Images, videos, galleries

### Complexity Levels
- `simple`: Basic structure, minimal interactions
- `moderate`: Some state management, multiple sections
- `complex`: Advanced interactions, data handling

### Custom Fields Integration
The system automatically generates properties panels using:
- `text-field`: For text inputs
- `textarea-field`: For longer text content
- `boolean-field`: For toggles and checkboxes
- `select-field`: For dropdown options
- `color-field`: For color pickers
- `spacing-field`: For padding/margin controls
- `image-field`: For image uploads
- `link-field`: For URL inputs

## 📊 Statistics and Management

### Block Statistics
```tsx
const { blockStatistics } = usePageBuilderBlocks()

console.log(blockStatistics)
// {
//   totalBlocks: 15,
//   activeBlocks: 12,
//   categoryDistribution: { content: 8, ecommerce: 4, marketing: 3 },
//   complexityDistribution: { simple: 5, moderate: 8, complex: 2 },
//   averageGenerationTime: 2500
// }
```

### Export/Import
```tsx
const { exportBlocks, importBlocks } = usePageBuilderBlocks()

// Export all blocks
exportBlocks() // Downloads JSON file

// Import blocks from file
const file = event.target.files[0]
await importBlocks(file)
```

## 🛠️ Advanced Features

### Pattern Learning
The system learns from your existing blocks:
- Analyzes component usage patterns
- Extracts styling conventions
- Understands configuration structures
- Adapts to your coding style

### Live Editing
Generated blocks support:
- Real-time property updates
- Visual feedback during editing
- Drag-and-drop positioning
- Responsive preview modes

### Error Handling
Robust error handling includes:
- Code validation before registration
- Runtime error boundaries
- Graceful fallbacks for failed blocks
- Detailed error reporting

## 🎯 Best Practices

1. **Consistent Patterns**: Maintain consistent patterns in your existing blocks for better AI learning
2. **Clear Descriptions**: Provide detailed descriptions when generating blocks
3. **Iterative Refinement**: Use the properties panel to refine generated blocks
4. **Regular Cleanup**: Use the cleanup utilities to manage old or unused blocks
5. **Testing**: Always test generated blocks in different contexts before production use

## 🔍 Troubleshooting

### Common Issues

**Block Generation Fails**
- Check if existing blocks follow consistent patterns
- Ensure the description is clear and specific
- Try reducing complexity level

**Properties Panel Not Working**
- Verify custom fields are properly imported
- Check if configuration interface is correctly typed
- Ensure default values are provided

**Block Not Rendering**
- Check for JavaScript errors in console
- Verify all required dependencies are available
- Ensure block is properly registered

### Debug Mode
Enable debug mode in development:
```tsx
<PageBuilderBlockRenderer
  component={component}
  isEditing={true}
  showControls={true}
  // Debug info will be shown in development
/>
```

## 🚀 Future Enhancements

- **Visual Block Editor**: Drag-and-drop block composition
- **Template System**: Pre-built block templates
- **Version Control**: Block versioning and rollback
- **Collaboration**: Multi-user block editing
- **Performance Optimization**: Lazy loading and caching
- **Advanced Analytics**: Usage tracking and optimization suggestions
