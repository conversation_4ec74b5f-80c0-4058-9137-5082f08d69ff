// Advanced Scraping Examples
// Complex data extraction scenarios and techniques

import { ScrapingService } from '../services'
import fs from 'fs'

const scrapingService = new ScrapingService({
  apiToken: process.env.BROWSERLESS_API_TOKEN || 'your-token-here'
})

/**
 * E-commerce Product Scraping
 */
export async function scrapeEcommerceProduct() {
  console.log('Scraping e-commerce product data...')
  
  const result = await scrapingService.scrapeWithTemplate(
    'https://example-shop.com/product/123',
    'ecommerce'
  )

  if (result.success && result.data) {
    console.log('Product data extracted:', JSON.stringify(result.data, null, 2))
    
    // Save to JSON file
    fs.writeFileSync('product-data.json', JSON.stringify(result.data, null, 2))
    console.log('Product data saved to product-data.json')
  } else {
    console.error('Product scraping failed:', result.error?.message)
  }
}

/**
 * Structured Data Extraction with Transformations
 */
export async function structuredDataExtraction() {
  console.log('Extracting structured data with transformations...')
  
  const result = await scrapingService.scrapeStructuredData({
    url: 'https://example-shop.com/product/123',
    rules: [
      {
        name: 'title',
        selector: 'h1, .product-title',
        required: true,
        transform: (text) => text.trim().replace(/\s+/g, ' ')
      },
      {
        name: 'price',
        selector: '.price, .product-price',
        required: true,
        transform: (text) => {
          const match = text.match(/[\d,]+\.?\d*/);
          return match ? parseFloat(match[0].replace(',', '')) : null;
        }
      },
      {
        name: 'rating',
        selector: '.rating, .stars',
        transform: (text) => {
          const match = text.match(/(\d+\.?\d*)/);
          return match ? parseFloat(match[1]) : null;
        }
      },
      {
        name: 'availability',
        selector: '.stock, .availability',
        transform: (text) => text.toLowerCase().includes('in stock')
      },
      {
        name: 'images',
        selector: '.product-images img',
        attribute: 'src',
        transform: (src) => src.startsWith('http') ? src : `https://example-shop.com${src}`
      }
    ],
    waitForContent: true,
    cleanData: true
  })

  if (result.success && result.data) {
    console.log('Structured data:', JSON.stringify(result.data, null, 2))
    
    // Save structured data
    fs.writeFileSync('structured-product-data.json', JSON.stringify(result.data, null, 2))
  } else {
    console.error('Structured extraction failed:', result.error?.message)
  }
}

/**
 * Bulk Product Scraping
 */
export async function bulkProductScraping() {
  console.log('Performing bulk product scraping...')
  
  const productUrls = [
    'https://example-shop.com/product/1',
    'https://example-shop.com/product/2',
    'https://example-shop.com/product/3',
    'https://example-shop.com/product/4',
    'https://example-shop.com/product/5'
  ]

  const results = await scrapingService.scrapeBulk({
    urls: productUrls,
    elements: [
      { selector: 'h1, .product-title' },
      { selector: '.price, .product-price' },
      { selector: '.rating, .stars' },
      { selector: '.description, .product-description' }
    ],
    options: {
      waitForSelector: {
        selector: '.price',
        timeout: 10000
      }
    },
    concurrency: 2, // Limit concurrent requests
    delay: 2000 // 2 second delay between batches
  })

  console.log(`Scraped ${results.length} products`)
  
  const successfulResults = results.filter(r => r.success)
  const failedResults = results.filter(r => !r.success)
  
  console.log(`Successful: ${successfulResults.length}, Failed: ${failedResults.length}`)
  
  if (failedResults.length > 0) {
    console.log('Failed URLs:', failedResults.map(r => r.url))
  }
  
  // Save all results
  fs.writeFileSync('bulk-scraping-results.json', JSON.stringify(results, null, 2))
}

/**
 * News Article Scraping with Content Cleaning
 */
export async function scrapeNewsArticles() {
  console.log('Scraping news articles...')
  
  const newsUrls = [
    'https://news.ycombinator.com',
    'https://techcrunch.com',
    'https://arstechnica.com'
  ]

  for (const url of newsUrls) {
    console.log(`Scraping ${url}...`)
    
    const result = await scrapingService.scrapeWithTemplate(url, 'news')
    
    if (result.success && result.data) {
      // Extract article links for further processing
      const articleLinks = result.data.data
        .filter(item => item.selector.includes('a'))
        .flatMap(item => item.results)
        .map(result => result.attributes.find(attr => attr.name === 'href')?.value)
        .filter(Boolean)
        .slice(0, 5) // Limit to first 5 articles
      
      console.log(`Found ${articleLinks.length} article links from ${url}`)
      
      // Scrape individual articles
      for (const articleUrl of articleLinks) {
        try {
          const articleResult = await scrapingService.scrapeWithTemplate(
            articleUrl,
            'news'
          )
          
          if (articleResult.success) {
            console.log(`✓ Scraped article: ${articleUrl}`)
          }
        } catch (error) {
          console.log(`✗ Failed to scrape: ${articleUrl}`)
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
  }
}

/**
 * Job Listings Scraping
 */
export async function scrapeJobListings() {
  console.log('Scraping job listings...')
  
  const result = await scrapingService.scrapeWithTemplate(
    'https://jobs.example.com/search?q=developer',
    'job'
  )

  if (result.success && result.data) {
    // Process job data
    const jobs = result.data.data.map(item => ({
      selector: item.selector,
      count: item.results.length,
      data: item.results.map(r => r.text).filter(Boolean)
    }))
    
    console.log('Job listings extracted:', JSON.stringify(jobs, null, 2))
    
    // Save job data
    fs.writeFileSync('job-listings.json', JSON.stringify(jobs, null, 2))
  } else {
    console.error('Job scraping failed:', result.error?.message)
  }
}

/**
 * Social Media Scraping
 */
export async function scrapeSocialMedia() {
  console.log('Scraping social media posts...')
  
  const result = await scrapingService.scrapeWithTemplate(
    'https://twitter.com/browserless',
    'social',
    {
      waitForSelector: {
        selector: '[data-testid="tweet"]',
        timeout: 15000
      }
    },
    {
      stealth: true, // Use stealth mode for social media
      blockAds: true
    }
  )

  if (result.success && result.data) {
    console.log('Social media data:', JSON.stringify(result.data, null, 2))
    
    // Save social data
    fs.writeFileSync('social-media-posts.json', JSON.stringify(result.data, null, 2))
  } else {
    console.error('Social media scraping failed:', result.error?.message)
  }
}

/**
 * Scraping with Pagination
 */
export async function scrapeWithPagination() {
  console.log('Scraping with pagination support...')
  
  const result = await scrapingService.scrapeWithPagination(
    'https://example.com/products',
    [
      { selector: '.product-title' },
      { selector: '.product-price' },
      { selector: '.product-rating' }
    ],
    '.pagination .next', // Next page selector
    5, // Maximum pages
    {
      waitForSelector: {
        selector: '.product-list',
        timeout: 10000
      }
    }
  )

  if (result.success && result.data) {
    console.log(`Scraped ${result.data.length} pages of data`)
    
    // Combine all page data
    const allProducts = result.data.flatMap(page => page.data)
    console.log(`Total products found: ${allProducts.length}`)
    
    // Save paginated results
    fs.writeFileSync('paginated-results.json', JSON.stringify(result.data, null, 2))
  } else {
    console.error('Pagination scraping failed:', result.error?.message)
  }
}

/**
 * Custom Scraping with Complex Selectors
 */
export async function customComplexScraping() {
  console.log('Performing custom complex scraping...')
  
  const result = await scrapingService.scrape(
    'https://example.com/complex-page',
    [
      // Complex CSS selectors
      { selector: 'article:nth-child(odd) h2' },
      { selector: '.content-area > div:not(.advertisement)' },
      { selector: '[data-component="product-card"] .price' },
      { selector: 'img[src*="product"]', attribute: 'src' },
      { selector: 'a[href^="/category/"]', attribute: 'href' }
    ],
    {
      // Wait for dynamic content
      waitForFunction: {
        fn: '() => document.querySelectorAll(".product-card").length > 0',
        timeout: 15000
      },
      // Remove unwanted elements
      addScriptTag: [
        {
          content: `
            // Remove ads and popups
            document.querySelectorAll('.ad, .popup, .modal').forEach(el => el.remove());
            
            // Wait for lazy-loaded content
            const observer = new IntersectionObserver((entries) => {
              entries.forEach(entry => {
                if (entry.isIntersecting) {
                  entry.target.classList.add('loaded');
                }
              });
            });
            
            document.querySelectorAll('[data-lazy]').forEach(el => observer.observe(el));
          `
        }
      ],
      // Block unnecessary resources
      rejectResourceTypes: ['image', 'font', 'media'],
      // Continue on errors
      bestAttempt: true
    },
    {
      // Launch options
      stealth: true,
      blockAds: true,
      blockConsentModals: true
    }
  )

  if (result.success && result.data) {
    console.log('Complex scraping results:', JSON.stringify(result.data, null, 2))
    
    // Process and save results
    const processedData = result.data.data.map(item => ({
      selector: item.selector,
      elementCount: item.results.length,
      sampleData: item.results.slice(0, 3) // First 3 results as sample
    }))
    
    fs.writeFileSync('complex-scraping-results.json', JSON.stringify(processedData, null, 2))
  } else {
    console.error('Complex scraping failed:', result.error?.message)
  }
}

/**
 * Run all advanced scraping examples
 */
export async function runAllAdvancedScrapingExamples() {
  console.log('Running all advanced scraping examples...\n')

  try {
    await scrapeEcommerceProduct()
    console.log('✓ E-commerce scraping completed\n')

    await structuredDataExtraction()
    console.log('✓ Structured data extraction completed\n')

    await bulkProductScraping()
    console.log('✓ Bulk scraping completed\n')

    await scrapeNewsArticles()
    console.log('✓ News scraping completed\n')

    await scrapeJobListings()
    console.log('✓ Job listings scraping completed\n')

    await scrapeSocialMedia()
    console.log('✓ Social media scraping completed\n')

    await scrapeWithPagination()
    console.log('✓ Pagination scraping completed\n')

    await customComplexScraping()
    console.log('✓ Complex scraping completed\n')

    console.log('All advanced scraping examples completed successfully!')
  } catch (error) {
    console.error('Advanced scraping example failed:', error)
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  runAllAdvancedScrapingExamples()
}