// Basic Usage Examples
// Simple examples demonstrating core functionality

import { BrowserlessClient } from '../client'
import { ScreenshotService, PDFService, ScrapingService } from '../services'
import fs from 'fs'

// Initialize the client
const client = new BrowserlessClient({
  apiToken: process.env.BROWSERLESS_API_TOKEN || 'your-token-here',
  region: 'sfo',
  timeout: 60000,
  retries: 3
})

/**
 * Basic Screenshot Example
 */
export async function basicScreenshot() {
  console.log('Taking basic screenshot...')
  
  const result = await client.screenshot({
    url: 'https://example.com',
    options: {
      type: 'png',
      fullPage: true
    }
  })

  if (result.success && result.data) {
    fs.writeFileSync('basic-screenshot.png', result.data.data)
    console.log('Screenshot saved as basic-screenshot.png')
  } else {
    console.error('Screenshot failed:', result.error?.message)
  }
}

/**
 * Basic PDF Example
 */
export async function basicPDF() {
  console.log('Generating basic PDF...')
  
  const result = await client.pdf({
    url: 'https://example.com',
    options: {
      format: 'A4',
      printBackground: true,
      margin: {
        top: '1in',
        right: '1in',
        bottom: '1in',
        left: '1in'
      }
    }
  })

  if (result.success && result.data) {
    fs.writeFileSync('basic-document.pdf', result.data.data)
    console.log('PDF saved as basic-document.pdf')
  } else {
    console.error('PDF generation failed:', result.error?.message)
  }
}

/**
 * Basic Scraping Example
 */
export async function basicScraping() {
  console.log('Scraping basic data...')
  
  const result = await client.scrape({
    url: 'https://example.com',
    elements: [
      { selector: 'h1' },
      { selector: 'p' },
      { selector: 'a', attribute: 'href' }
    ]
  })

  if (result.success && result.data) {
    console.log('Scraped data:', JSON.stringify(result.data, null, 2))
  } else {
    console.error('Scraping failed:', result.error?.message)
  }
}

/**
 * Basic Content Extraction Example
 */
export async function basicContent() {
  console.log('Extracting page content...')
  
  const result = await client.content({
    url: 'https://example.com'
  })

  if (result.success && result.data) {
    fs.writeFileSync('page-content.html', result.data.data)
    console.log('Content saved as page-content.html')
  } else {
    console.error('Content extraction failed:', result.error?.message)
  }
}

/**
 * Using Screenshot Service with Presets
 */
export async function screenshotWithPresets() {
  console.log('Taking screenshots with presets...')
  
  const screenshotService = new ScreenshotService({
    apiToken: process.env.BROWSERLESS_API_TOKEN || 'your-token-here'
  })

  // Desktop preset
  const desktopResult = await screenshotService.takeWithPreset(
    'https://example.com',
    'desktop'
  )

  if (desktopResult.success && desktopResult.data) {
    fs.writeFileSync('desktop-screenshot.png', desktopResult.data.data)
    console.log('Desktop screenshot saved')
  }

  // Mobile preset
  const mobileResult = await screenshotService.takeWithPreset(
    'https://example.com',
    'mobile'
  )

  if (mobileResult.success && mobileResult.data) {
    fs.writeFileSync('mobile-screenshot.png', mobileResult.data.data)
    console.log('Mobile screenshot saved')
  }
}

/**
 * Using PDF Service with Templates
 */
export async function pdfWithTemplates() {
  console.log('Generating PDFs with templates...')
  
  const pdfService = new PDFService({
    apiToken: process.env.BROWSERLESS_API_TOKEN || 'your-token-here'
  })

  // Business template
  const businessResult = await pdfService.generateWithTemplate(
    'https://example.com',
    'business'
  )

  if (businessResult.success && businessResult.data) {
    fs.writeFileSync('business-document.pdf', businessResult.data.data)
    console.log('Business PDF saved')
  }

  // Report preset
  const reportResult = await pdfService.generateWithPreset(
    'https://example.com',
    'report'
  )

  if (reportResult.success && reportResult.data) {
    fs.writeFileSync('report-document.pdf', reportResult.data.data)
    console.log('Report PDF saved')
  }
}

/**
 * Using Scraping Service with Templates
 */
export async function scrapingWithTemplates() {
  console.log('Scraping with templates...')
  
  const scrapingService = new ScrapingService({
    apiToken: process.env.BROWSERLESS_API_TOKEN || 'your-token-here'
  })

  // News article template
  const newsResult = await scrapingService.scrapeWithTemplate(
    'https://news.ycombinator.com',
    'news'
  )

  if (newsResult.success && newsResult.data) {
    console.log('News data:', JSON.stringify(newsResult.data, null, 2))
  }
}

/**
 * Health Check Example
 */
export async function healthCheck() {
  console.log('Performing health check...')
  
  const result = await client.healthCheck()

  if (result.success && result.data) {
    console.log('Service is healthy:', result.data)
  } else {
    console.error('Health check failed:', result.error?.message)
  }
}

/**
 * Error Handling Example
 */
export async function errorHandlingExample() {
  console.log('Demonstrating error handling...')
  
  // Try to screenshot an invalid URL
  const result = await client.screenshot({
    url: 'https://invalid-url-that-does-not-exist.com'
  })

  if (result.success) {
    console.log('Screenshot successful (unexpected)')
  } else {
    console.log('Expected error occurred:')
    console.log('- Status:', result.status)
    console.log('- Message:', result.error?.message)
    console.log('- Code:', result.error?.code)
  }
}

/**
 * Run all basic examples
 */
export async function runAllBasicExamples() {
  console.log('Running all basic examples...\n')

  try {
    await healthCheck()
    console.log('✓ Health check completed\n')

    await basicScreenshot()
    console.log('✓ Basic screenshot completed\n')

    await basicPDF()
    console.log('✓ Basic PDF completed\n')

    await basicScraping()
    console.log('✓ Basic scraping completed\n')

    await basicContent()
    console.log('✓ Basic content extraction completed\n')

    await screenshotWithPresets()
    console.log('✓ Screenshot presets completed\n')

    await pdfWithTemplates()
    console.log('✓ PDF templates completed\n')

    await scrapingWithTemplates()
    console.log('✓ Scraping templates completed\n')

    await errorHandlingExample()
    console.log('✓ Error handling example completed\n')

    console.log('All basic examples completed successfully!')
  } catch (error) {
    console.error('Example failed:', error)
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  runAllBasicExamples()
}