# Browserless Service Library

A comprehensive TypeScript library for interacting with [Browserless.io](https://browserless.io) REST APIs. This library provides a complete set of tools for browser automation, web scraping, PDF generation, screenshot capture, and performance monitoring.

## Features

- 🚀 **Complete API Coverage** - All Browserless.io REST API endpoints
- 🎯 **Specialized Services** - Purpose-built services for different use cases
- 📝 **TypeScript Support** - Full type safety and IntelliSense
- 🔄 **Retry Logic** - Built-in retry mechanisms for reliability
- 📊 **Performance Monitoring** - Lighthouse integration for performance audits
- 🤖 **Browser Automation** - Complex automation workflows
- 📄 **PDF Generation** - Advanced PDF creation with templates
- 📸 **Screenshot Capture** - Flexible screenshot options with presets
- 🕷️ **Web Scraping** - Structured data extraction with templates
- ⚡ **Bulk Operations** - Efficient batch processing
- 🛡️ **Error Handling** - Comprehensive error management
- 🌍 **Multi-Region Support** - Global endpoint selection

## Installation

```bash
npm install @your-org/browserless-service
# or
yarn add @your-org/browserless-service
```

## Quick Start

```typescript
import { BrowserlessClient } from '@/lib/browserless'

// Initialize the client
const client = new BrowserlessClient({
  apiToken: 'your-api-token-here',
  region: 'sfo', // 'sfo', 'lon', or 'ams'
  timeout: 60000,
  retries: 3
})

// Take a screenshot
const screenshot = await client.screenshot({
  url: 'https://example.com',
  options: {
    type: 'png',
    fullPage: true
  }
})

if (screenshot.success) {
  // Save the screenshot
  fs.writeFileSync('screenshot.png', screenshot.data.data)
}
```

## API Reference

### Core Client

The `BrowserlessClient` is the main entry point for all API interactions:

```typescript
import { BrowserlessClient } from '@/lib/browserless'

const client = new BrowserlessClient({
  apiToken: 'your-token',
  region: 'sfo', // Optional: 'sfo', 'lon', 'ams'
  timeout: 60000, // Optional: request timeout in ms
  retries: 3 // Optional: number of retries
})
```

### Available Methods

#### Screenshots
```typescript
// Basic screenshot
const result = await client.screenshot({
  url: 'https://example.com',
  options: { type: 'png', fullPage: true }
})

// Screenshot with custom viewport
const result = await client.screenshot({
  url: 'https://example.com',
  options: {
    type: 'png',
    clip: { x: 0, y: 0, width: 1200, height: 800 }
  }
})
```

#### PDF Generation
```typescript
// Basic PDF
const result = await client.pdf({
  url: 'https://example.com',
  options: {
    format: 'A4',
    printBackground: true
  }
})

// PDF with headers and footers
const result = await client.pdf({
  url: 'https://example.com',
  options: {
    format: 'A4',
    displayHeaderFooter: true,
    headerTemplate: '<div>Header</div>',
    footerTemplate: '<div>Page <span class="pageNumber"></span></div>'
  }
})
```

#### Web Scraping
```typescript
// Extract specific elements
const result = await client.scrape({
  url: 'https://example.com',
  elements: [
    { selector: 'h1' },
    { selector: '.price' },
    { selector: 'img', attribute: 'src' }
  ]
})

// Scrape with wait conditions
const result = await client.scrape({
  url: 'https://example.com',
  elements: [{ selector: '.dynamic-content' }],
  waitForSelector: {
    selector: '.dynamic-content',
    timeout: 10000
  }
})
```

#### Content Extraction
```typescript
// Get full page HTML
const result = await client.content({
  url: 'https://example.com'
})

// Content with custom scripts
const result = await client.content({
  url: 'https://example.com',
  addScriptTag: [
    { content: 'document.body.style.background = "white"' }
  ]
})
```

#### Browser Automation
```typescript
// Execute custom JavaScript
const result = await client.function({
  code: `
    async ({ url, searchTerm }) => {
      await page.goto(url);
      await page.type('#search', searchTerm);
      await page.click('button[type="submit"]');
      await page.waitForSelector('.results');
      
      return {
        title: await page.title(),
        resultCount: await page.$$eval('.result', els => els.length)
      };
    }
  `,
  context: {
    url: 'https://example.com',
    searchTerm: 'browserless'
  }
})
```

#### Performance Monitoring
```typescript
// Run Lighthouse audit
const result = await client.performance({
  url: 'https://example.com',
  config: {
    extends: 'lighthouse:default',
    settings: {
      onlyCategories: ['performance', 'accessibility']
    }
  }
})
```

## Specialized Services

### Screenshot Service

```typescript
import { ScreenshotService } from '@/lib/browserless/services'

const screenshotService = new ScreenshotService({
  apiToken: 'your-token'
})

// Use predefined presets
const result = await screenshotService.takeWithPreset(
  'https://example.com',
  'desktop' // 'mobile', 'fullPage', 'thumbnail', etc.
)

// Bulk screenshots
const results = await screenshotService.takeBulk({
  urls: ['https://site1.com', 'https://site2.com'],
  options: { type: 'png' },
  concurrency: 3
})

// Screenshot with element highlighting
const result = await screenshotService.takeWithHighlight(
  'https://example.com',
  '.important-element',
  '#ff0000' // highlight color
)
```

### PDF Service

```typescript
import { PDFService } from '@/lib/browserless/services'

const pdfService = new PDFService({
  apiToken: 'your-token'
})

// Use predefined presets
const result = await pdfService.generateWithPreset(
  'https://example.com',
  'report' // 'document', 'invoice', 'presentation', etc.
)

// Use templates
const result = await pdfService.generateWithTemplate(
  'https://example.com',
  'business' // 'minimal', 'branded'
)

// Bulk PDF generation
const results = await pdfService.generateBulk({
  urls: ['https://page1.com', 'https://page2.com'],
  preset: 'document',
  concurrency: 2
})
```

### Scraping Service

```typescript
import { ScrapingService } from '@/lib/browserless/services'

const scrapingService = new ScrapingService({
  apiToken: 'your-token'
})

// Use predefined templates
const result = await scrapingService.scrapeWithTemplate(
  'https://shop.example.com/product/123',
  'ecommerce' // 'news', 'blog', 'job', etc.
)

// Structured data extraction
const result = await scrapingService.scrapeStructuredData({
  url: 'https://example.com',
  rules: [
    {
      name: 'title',
      selector: 'h1',
      required: true,
      transform: (text) => text.trim().toUpperCase()
    },
    {
      name: 'price',
      selector: '.price',
      transform: (text) => parseFloat(text.replace(/[^0-9.]/g, ''))
    }
  ],
  cleanData: true
})

// Bulk scraping
const results = await scrapingService.scrapeBulk({
  urls: ['https://site1.com', 'https://site2.com'],
  elements: [{ selector: 'h1' }, { selector: '.content' }],
  concurrency: 3
})
```

### Automation Service

```typescript
import { AutomationService } from '@/lib/browserless/services'

const automationService = new AutomationService({
  apiToken: 'your-token'
})

// Use predefined tasks
const result = await automationService.executeTask('loginForm', {
  url: 'https://example.com/login',
  username: '<EMAIL>',
  password: 'password123'
})

// Fill forms
const result = await automationService.fillForm({
  url: 'https://example.com/contact',
  fields: {
    '#name': 'John Doe',
    '#email': '<EMAIL>',
    '#message': 'Hello world!'
  },
  submitSelector: 'button[type="submit"]',
  captureScreenshot: true
})

// Multi-step navigation
const result = await automationService.navigate({
  url: 'https://example.com',
  steps: [
    { action: 'click', selector: '.menu-button' },
    { action: 'wait', selector: '.dropdown-menu' },
    { action: 'click', selector: '.dropdown-item' },
    { action: 'type', selector: '#search', value: 'search term' }
  ],
  captureScreenshots: true
})
```

### Performance Service

```typescript
import { PerformanceService } from '@/lib/browserless/services'

const performanceService = new PerformanceService({
  apiToken: 'your-token'
})

// Use predefined presets
const result = await performanceService.auditWithPreset(
  'https://example.com',
  'mobile' // 'desktop', 'accessibility', 'seo', etc.
)

// Compare performance
const comparison = await performanceService.compare(
  'https://old-site.com',
  'https://new-site.com',
  'desktop'
)

// Monitor over time
const reports = await performanceService.monitor(
  'https://example.com',
  5, // number of checks
  60000, // interval in ms
  'fast' // preset
)

// Get insights
const insights = performanceService.getInsights(report)
console.log('Critical issues:', insights.criticalIssues)
console.log('Recommendations:', insights.recommendations)
```

## Configuration Options

### Launch Options (Query Parameters)

```typescript
const launchOptions = {
  headless: true, // Run in headless mode
  stealth: true, // Enable stealth mode
  blockAds: true, // Block advertisements
  blockConsentModals: true, // Block cookie consent modals
  proxy: 'residential', // Use residential proxy
  proxyCountry: 'us', // Proxy country code
  proxySticky: true, // Maintain same proxy IP
  timeout: 60000 // Session timeout in ms
}
```

### Request Options

```typescript
const requestOptions = {
  gotoOptions: {
    waitUntil: 'networkidle2', // Wait condition
    timeout: 30000 // Navigation timeout
  },
  waitForSelector: {
    selector: '.content',
    timeout: 10000
  },
  addScriptTag: [
    { url: 'https://code.jquery.com/jquery-3.7.1.min.js' },
    { content: 'console.log("Script executed")' }
  ],
  addStyleTag: [
    { content: 'body { background: white; }' }
  ],
  rejectResourceTypes: ['image', 'font'], // Block resource types
  bestAttempt: true // Continue on errors
}
```

## Error Handling

```typescript
try {
  const result = await client.screenshot({
    url: 'https://example.com'
  })
  
  if (result.success) {
    // Handle success
    console.log('Screenshot captured successfully')
    fs.writeFileSync('screenshot.png', result.data.data)
  } else {
    // Handle API error
    console.error('API Error:', result.error?.message)
    console.error('Status:', result.status)
  }
} catch (error) {
  // Handle network or other errors
  console.error('Request failed:', error.message)
}
```

## Regional Endpoints

The library supports multiple regional endpoints for optimal performance:

- **US West (SFO)**: `https://production-sfo.browserless.io`
- **Europe UK (LON)**: `https://production-lon.browserless.io`
- **Europe Amsterdam (AMS)**: `https://production-ams.browserless.io`

```typescript
const client = new BrowserlessClient({
  apiToken: 'your-token',
  region: 'lon' // Choose closest region
})
```

## Best Practices

### 1. Use Appropriate Timeouts
```typescript
// For simple operations
const client = new BrowserlessClient({
  apiToken: 'your-token',
  timeout: 30000
})

// For complex operations
const result = await client.pdf(options, {
  timeout: 120000 // 2 minutes
})
```

### 2. Implement Retry Logic
```typescript
const client = new BrowserlessClient({
  apiToken: 'your-token',
  retries: 3 // Built-in retry logic
})
```

### 3. Use Bulk Operations for Multiple URLs
```typescript
// Instead of individual requests
const results = await screenshotService.takeBulk({
  urls: urlList,
  concurrency: 3, // Limit concurrent requests
  delay: 1000 // Delay between batches
})
```

### 4. Optimize for Performance
```typescript
// Block unnecessary resources
const launchOptions = {
  blockAds: true,
  blockConsentModals: true
}

const requestOptions = {
  rejectResourceTypes: ['image', 'font', 'media']
}
```

### 5. Handle Errors Gracefully
```typescript
const result = await client.screenshot(options)

if (!result.success) {
  if (result.status === 401) {
    console.error('Invalid API token')
  } else if (result.status === 429) {
    console.error('Rate limit exceeded')
  } else {
    console.error('Request failed:', result.error?.message)
  }
}
```

## Examples

See the `/examples` directory for complete usage examples:

- [Basic Usage](./examples/basic-usage.ts)
- [Advanced Scraping](./examples/advanced-scraping.ts)
- [PDF Generation](./examples/pdf-generation.ts)
- [Performance Monitoring](./examples/performance-monitoring.ts)
- [Browser Automation](./examples/browser-automation.ts)

## API Token

Get your API token from [Browserless.io](https://account.browserless.io/signup/email?plan=free). Free plans are available for testing.

## Support

- [Browserless.io Documentation](https://docs.browserless.io/)
- [GitHub Issues](https://github.com/browserless/browserless/issues)
- [Community Discord](https://discord.gg/browserless)

## License

MIT License - see LICENSE file for details.