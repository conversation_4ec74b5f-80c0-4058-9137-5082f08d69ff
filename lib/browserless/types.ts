// Browserless Service Types
// Comprehensive TypeScript types for all Browserless.io REST API endpoints

// Base Configuration Types
export interface BrowserlessConfig {
  apiToken: string
  region?: 'sfo' | 'lon' | 'ams' | string
  baseUrl?: string
  timeout?: number
  retries?: number
}

export type BrowserlessRegion = 'sfo' | 'lon' | 'ams'

// Launch Parameters (Query Parameters)
export interface LaunchOptions {
  headless?: boolean
  stealth?: boolean
  blockAds?: boolean
  blockConsentModals?: boolean
  proxy?: string | 'residential'
  proxyCountry?: string
  proxySticky?: boolean
  timeout?: number
  launch?: string // JSON string for complex launch configurations
}

// Common Request Options
export interface BaseRequestOptions {
  url?: string
  html?: string
  gotoOptions?: GotoOptions
  bestAttempt?: boolean
  addScriptTag?: ScriptTag[]
  addStyleTag?: StyleTag[]
  rejectResourceTypes?: ResourceType[]
  rejectRequestPattern?: string[]
  waitForEvent?: WaitForEventOptions
  waitForFunction?: WaitForFunctionOptions
  waitForSelector?: WaitForSelectorOptions
  waitForTimeout?: number
}

// Navigation Options
export interface GotoOptions {
  timeout?: number
  waitUntil?: 'load' | 'domcontentloaded' | 'networkidle0' | 'networkidle2'
  referer?: string
}

// Script and Style Tags
export interface ScriptTag {
  url?: string
  content?: string
  type?: string
}

export interface StyleTag {
  url?: string
  content?: string
}

// Resource Types for Blocking
export type ResourceType = 
  | 'document'
  | 'stylesheet'
  | 'image'
  | 'media'
  | 'font'
  | 'script'
  | 'texttrack'
  | 'xhr'
  | 'fetch'
  | 'eventsource'
  | 'websocket'
  | 'manifest'
  | 'other'

// Wait Options
export interface WaitForEventOptions {
  event: string
  timeout?: number
}

export interface WaitForFunctionOptions {
  fn: string
  timeout?: number
  polling?: number | 'raf' | 'mutation'
  args?: any[]
}

export interface WaitForSelectorOptions {
  selector: string
  visible?: boolean
  hidden?: boolean
  timeout?: number
}

// Screenshot API Types
export interface ScreenshotOptions extends BaseRequestOptions {
  options?: {
    type?: 'png' | 'jpeg' | 'webp'
    quality?: number
    fullPage?: boolean
    clip?: {
      x: number
      y: number
      width: number
      height: number
    }
    omitBackground?: boolean
    encoding?: 'base64' | 'binary'
  }
}

export interface ScreenshotResponse {
  data: Buffer | string
  contentType: string
}

// PDF API Types
export interface PDFOptions extends BaseRequestOptions {
  options?: {
    scale?: number
    displayHeaderFooter?: boolean
    headerTemplate?: string
    footerTemplate?: string
    printBackground?: boolean
    landscape?: boolean
    pageRanges?: string
    format?: 'Letter' | 'Legal' | 'Tabloid' | 'Ledger' | 'A0' | 'A1' | 'A2' | 'A3' | 'A4' | 'A5' | 'A6'
    width?: string | number
    height?: string | number
    margin?: {
      top?: string | number
      right?: string | number
      bottom?: string | number
      left?: string | number
    }
    preferCSSPageSize?: boolean
    generateTaggedPDF?: boolean
    generateDocumentOutline?: boolean
  }
}

export interface PDFResponse {
  data: Buffer
  contentType: string
}

// Content API Types
export interface ContentOptions extends BaseRequestOptions {}

export interface ContentResponse {
  data: string
  contentType: string
}

// Scrape API Types
export interface ScrapeElement {
  selector: string
  attribute?: string
}

export interface ScrapeOptions extends BaseRequestOptions {
  elements: ScrapeElement[]
}

export interface ScrapeResult {
  selector: string
  results: Array<{
    text: string
    html: string
    attributes: Array<{
      name: string
      value: string
    }>
    width: number
    height: number
    left: number
    top: number
  }>
}

export interface ScrapeResponse {
  data: ScrapeResult[]
}

// Function API Types
export interface FunctionOptions {
  code: string
  context?: Record<string, any>
  detached?: boolean
}

export interface FunctionResponse {
  data: any
  logs: string[]
  errors: string[]
}

// Download API Types
export interface DownloadOptions extends BaseRequestOptions {
  elements?: Array<{
    selector: string
    timeout?: number
  }>
}

export interface DownloadResponse {
  data: Buffer
  filename: string
  contentType: string
}

// Export API Types
export interface ExportOptions extends BaseRequestOptions {
  includeAssets?: boolean
  format?: 'zip' | 'tar'
}

export interface ExportResponse {
  data: Buffer
  filename: string
  contentType: string
}

// Unblock API Types
export interface UnblockOptions extends BaseRequestOptions {
  returnScreenshot?: boolean
  returnCookies?: boolean
  returnHtml?: boolean
}

export interface UnblockResponse {
  html?: string
  screenshot?: Buffer | string
  cookies?: Array<{
    name: string
    value: string
    domain: string
    path: string
    expires?: number
    httpOnly?: boolean
    secure?: boolean
    sameSite?: 'Strict' | 'Lax' | 'None'
  }>
}

// Performance API Types
export interface PerformanceOptions extends BaseRequestOptions {
  config?: {
    extends?: 'lighthouse:default'
    settings?: {
      onlyCategories?: string[]
      skipAudits?: string[]
      throttlingMethod?: 'simulate' | 'devtools'
      throttling?: {
        rttMs?: number
        throughputKbps?: number
        cpuSlowdownMultiplier?: number
      }
    }
  }
}

export interface PerformanceResponse {
  data: {
    lhr: any // Lighthouse report object
    artifacts: any
    report: string
  }
}

// Error Types
export interface BrowserlessError {
  message: string
  code?: string
  status?: number
  details?: any
}

export class BrowserlessAPIError extends Error {
  public readonly status: number
  public readonly code?: string
  public readonly details?: any

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message)
    this.name = 'BrowserlessAPIError'
    this.status = status
    this.code = code
    this.details = details
  }
}

// Response wrapper type
export interface BrowserlessResponse<T = any> {
  success: boolean
  data?: T
  error?: BrowserlessError
  headers?: Record<string, string>
  status: number
}

// Service method options
export interface RequestOptions extends LaunchOptions {
  headers?: Record<string, string>
  signal?: AbortSignal
}