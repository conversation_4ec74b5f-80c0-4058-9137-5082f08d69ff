# Changelog

All notable changes to the Browserless Service Library will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-XX

### Added
- Initial release of the Browserless Service Library
- Complete TypeScript support for all Browserless.io REST API endpoints
- Core `BrowserlessClient` with full API coverage
- Specialized services for different use cases:
  - `ScreenshotService` - Advanced screenshot capture with presets
  - `PDFService` - PDF generation with templates and presets
  - `ScrapingService` - Web scraping with templates and structured data extraction
  - `AutomationService` - Browser automation and form filling
  - `PerformanceService` - Lighthouse performance monitoring
- Built-in retry logic and error handling
- Multi-region endpoint support (US, EU)
- Bulk operation support for all services
- Comprehensive documentation and examples
- Predefined templates and presets for common use cases

### Features
- **Screenshot Capture**
  - Multiple format support (PNG, JPEG, WebP)
  - Preset configurations (desktop, mobile, full-page, thumbnail)
  - Element highlighting and custom viewport sizes
  - Bulk screenshot processing

- **PDF Generation**
  - Multiple format presets (document, report, invoice, presentation)
  - Template system with headers/footers
  - Custom page breaks and watermarks
  - Bulk PDF generation

- **Web Scraping**
  - Template-based scraping for common sites (e-commerce, news, jobs)
  - Structured data extraction with transformations
  - Pagination support
  - Bulk scraping with concurrency control

- **Browser Automation**
  - Predefined automation tasks (login, forms, search)
  - Multi-step navigation workflows
  - Form filling and submission
  - Website monitoring and change detection

- **Performance Monitoring**
  - Lighthouse integration with presets
  - Performance comparison tools
  - Monitoring over time
  - Insights and recommendations

### Technical Features
- Full TypeScript type safety
- Comprehensive error handling
- Request retry mechanisms
- Regional endpoint selection
- Resource blocking and optimization
- Stealth mode support
- Proxy support (including residential proxies)
- Timeout and concurrency controls

### Documentation
- Complete API reference
- Usage examples for all features
- Best practices guide
- Error handling examples
- Performance optimization tips

### Examples
- Basic usage examples
- Advanced scraping scenarios
- PDF generation workflows
- Performance monitoring setups
- Browser automation scripts