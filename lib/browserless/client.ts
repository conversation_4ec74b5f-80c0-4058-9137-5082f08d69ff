// Browserless Client
// Main client class for interacting with Browserless.io REST APIs

import {
  BrowserlessConfig,
  BrowserlessResponse,
  BrowserlessAPIError,
  LaunchOptions,
  RequestOptions,
  ScreenshotOptions,
  ScreenshotResponse,
  PDFOptions,
  PDFResponse,
  ContentOptions,
  ContentResponse,
  ScrapeOptions,
  ScrapeResponse,
  FunctionOptions,
  FunctionResponse,
  DownloadOptions,
  DownloadResponse,
  ExportOptions,
  ExportResponse,
  UnblockOptions,
  UnblockResponse,
  PerformanceOptions,
  PerformanceResponse
} from './types'

import {
  buildBaseUrl,
  buildApiUrl,
  validateOptions,
  prepareRequestBody,
  getRequestHeaders,
  getContentType,
  isBinaryContent,
  parseErrorResponse,
  withRetry,
  createTimeoutPromise,
  validateApiToken,
  deepMerge
} from './utils'

/**
 * Main Browserless client for interacting with all REST API endpoints
 */
export class BrowserlessClient {
  private config: Required<BrowserlessConfig>
  private baseUrl: string

  constructor(config: BrowserlessConfig) {
    // Validate API token
    if (!config.apiToken) {
      throw new Error('API token is required')
    }

    if (!validateApiToken(config.apiToken)) {
      console.warn('API token format may be invalid. Expected UUID format.')
    }

    // Set default configuration
    this.config = {
      apiToken: config.apiToken,
      region: config.region || 'sfo',
      baseUrl: config.baseUrl || '',
      timeout: config.timeout || 60000,
      retries: config.retries || 3
    }

    this.baseUrl = buildBaseUrl(this.config)
  }

  /**
   * Update client configuration
   */
  public updateConfig(newConfig: Partial<BrowserlessConfig>): void {
    const mergedConfig = deepMerge(this.config, newConfig)
    
    // Ensure required properties are still present after merge
    this.config = {
      apiToken: mergedConfig.apiToken,
      region: mergedConfig.region || 'sfo',
      baseUrl: mergedConfig.baseUrl || '',
      timeout: mergedConfig.timeout || 60000,
      retries: mergedConfig.retries || 3
    }
    
    this.baseUrl = buildBaseUrl(this.config)
  }

  /**
   * Get current configuration
   */
  public getConfig(): BrowserlessConfig {
    return { ...this.config }
  }

  /**
   * Make a generic API request
   */
  private async makeRequest<T>(
    endpoint: string,
    options: any = {},
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<T>> {
    try {
      // Validate options for specific endpoints
      validateOptions(endpoint, options)

      // Build URL with query parameters
      const url = buildApiUrl(
        this.baseUrl,
        endpoint,
        this.config.apiToken,
        launchOptions,
        requestOptions
      )

      // Prepare request
      const requestHeaders = getRequestHeaders(requestOptions?.headers)
      const body = prepareRequestBody(options)

      // Create abort controller for timeout
      const abortController = new AbortController()
      const timeoutId = setTimeout(() => abortController.abort(), this.config.timeout)

      const requestPromise = fetch(url, {
        method: 'POST',
        headers: requestHeaders,
        body,
        signal: requestOptions?.signal || abortController.signal
      })

      // Race between request and timeout
      const response = await Promise.race([
        requestPromise,
        createTimeoutPromise(this.config.timeout)
      ])

      clearTimeout(timeoutId)

      // Handle response
      if (!response.ok) {
        const errorMessage = await parseErrorResponse(response)
        throw new BrowserlessAPIError(
          errorMessage,
          response.status,
          response.headers.get('x-error-code') || undefined,
          { url, endpoint }
        )
      }

      // Parse response based on content type
      const contentType = getContentType(response.headers)
      let data: T

      if (isBinaryContent(contentType)) {
        data = (await response.arrayBuffer()) as T
      } else if (contentType.includes('application/json')) {
        data = await response.json()
      } else {
        data = (await response.text()) as T
      }

      // Convert headers to plain object
      const headers: Record<string, string> = {}
      response.headers.forEach((value, key) => {
        headers[key] = value
      })

      return {
        success: true,
        data,
        status: response.status,
        headers
      }
    } catch (error) {
      if (error instanceof BrowserlessAPIError) {
        return {
          success: false,
          error: {
            message: error.message,
            code: error.code,
            status: error.status,
            details: error.details
          },
          status: error.status
        }
      }

      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error occurred'
        },
        status: 500
      }
    }
  }

  /**
   * Make a request with retry logic
   */
  private async makeRequestWithRetry<T>(
    endpoint: string,
    options: any = {},
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<T>> {
    return withRetry(
      () => this.makeRequest<T>(endpoint, options, launchOptions, requestOptions),
      this.config.retries
    )
  }

  /**
   * Take a screenshot of a webpage
   */
  public async screenshot(
    options: ScreenshotOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<ScreenshotResponse>> {
    const response = await this.makeRequestWithRetry<ArrayBuffer>(
      'screenshot',
      options,
      launchOptions,
      requestOptions
    )

    if (response.success && response.data) {
      return {
        ...response,
        data: {
          data: Buffer.from(response.data),
          contentType: response.headers?.['content-type'] || 'image/png'
        }
      }
    }

    return {
      success: false,
      error: response.error,
      status: response.status
    }
  }

  /**
   * Generate a PDF from a webpage
   */
  public async pdf(
    options: PDFOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<PDFResponse>> {
    const response = await this.makeRequestWithRetry<ArrayBuffer>(
      'pdf',
      options,
      launchOptions,
      requestOptions
    )

    if (response.success && response.data) {
      return {
        ...response,
        data: {
          data: Buffer.from(response.data),
          contentType: response.headers?.['content-type'] || 'application/pdf'
        }
      }
    }

    return {
      success: false,
      error: response.error,
      status: response.status
    }
  }

  /**
   * Get the HTML content of a webpage
   */
  public async content(
    options: ContentOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<ContentResponse>> {
    const response = await this.makeRequestWithRetry<string>(
      'content',
      options,
      launchOptions,
      requestOptions
    )

    if (response.success && response.data) {
      return {
        ...response,
        data: {
          data: response.data,
          contentType: response.headers?.['content-type'] || 'text/html'
        }
      }
    }

    return {
      success: false,
      error: response.error,
      status: response.status
    }
  }

  /**
   * Scrape structured data from a webpage
   */
  public async scrape(
    options: ScrapeOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<ScrapeResponse>> {
    return this.makeRequestWithRetry<ScrapeResponse>(
      'scrape',
      options,
      launchOptions,
      requestOptions
    )
  }

  /**
   * Execute custom JavaScript function in browser context
   */
  public async function(
    options: FunctionOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<FunctionResponse>> {
    return this.makeRequestWithRetry<FunctionResponse>(
      'function',
      options,
      launchOptions,
      requestOptions
    )
  }

  /**
   * Download files from a webpage
   */
  public async download(
    options: DownloadOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<DownloadResponse>> {
    const response = await this.makeRequestWithRetry<ArrayBuffer>(
      'download',
      options,
      launchOptions,
      requestOptions
    )

    if (response.success && response.data) {
      const contentDisposition = response.headers?.['content-disposition'] || ''
      const filename = this.extractFilenameFromHeader(contentDisposition) || 'download'

      return {
        ...response,
        data: {
          data: Buffer.from(response.data),
          filename,
          contentType: response.headers?.['content-type'] || 'application/octet-stream'
        }
      }
    }

    return {
      success: false,
      error: response.error,
      status: response.status
    }
  }

  /**
   * Export webpage as a self-contained archive
   */
  public async export(
    options: ExportOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<ExportResponse>> {
    const response = await this.makeRequestWithRetry<ArrayBuffer>(
      'export',
      options,
      launchOptions,
      requestOptions
    )

    if (response.success && response.data) {
      const contentDisposition = response.headers?.['content-disposition'] || ''
      const filename = this.extractFilenameFromHeader(contentDisposition) || 'export.zip'

      return {
        ...response,
        data: {
          data: Buffer.from(response.data),
          filename,
          contentType: response.headers?.['content-type'] || 'application/zip'
        }
      }
    }

    return {
      success: false,
      error: response.error,
      status: response.status
    }
  }

  /**
   * Unblock protected websites and get content
   */
  public async unblock(
    options: UnblockOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<UnblockResponse>> {
    return this.makeRequestWithRetry<UnblockResponse>(
      'unblock',
      options,
      launchOptions,
      requestOptions
    )
  }

  /**
   * Run performance analysis using Lighthouse
   */
  public async performance(
    options: PerformanceOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<PerformanceResponse>> {
    return this.makeRequestWithRetry<PerformanceResponse>(
      'performance',
      options,
      launchOptions,
      requestOptions
    )
  }

  /**
   * Test API connectivity and authentication
   */
  public async healthCheck(): Promise<BrowserlessResponse<{ status: string; timestamp: string }>> {
    try {
      const response = await this.content(
        { url: 'https://example.com' },
        { timeout: 10000 }
      )

      if (response.success) {
        return {
          success: true,
          data: {
            status: 'healthy',
            timestamp: new Date().toISOString()
          },
          status: 200
        }
      }

      return {
        success: false,
        error: {
          message: 'Health check failed',
          details: response.error
        },
        status: response.status
      }
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Health check failed'
        },
        status: 500
      }
    }
  }

  /**
   * Extract filename from Content-Disposition header
   */
  private extractFilenameFromHeader(contentDisposition: string): string | null {
    const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
    if (filenameMatch && filenameMatch[1]) {
      return filenameMatch[1].replace(/['"]/g, '')
    }
    return null
  }
}