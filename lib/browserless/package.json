{"name": "@coco-milk-store/browserless-service", "version": "1.0.0", "description": "Comprehensive TypeScript library for Browserless.io REST APIs", "main": "index.js", "types": "index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint . --ext .ts", "example:basic": "ts-node examples/basic-usage.ts", "example:scraping": "ts-node examples/advanced-scraping.ts"}, "keywords": ["browserless", "browser-automation", "web-scraping", "pdf-generation", "screenshot", "performance-monitoring", "lighthouse", "puppeteer", "headless-browser"], "author": "Coco Milk Store", "license": "MIT", "dependencies": {"node-fetch": "^3.3.2"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "peerDependencies": {"typescript": ">=4.5.0"}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/your-org/coco-milk-store"}, "bugs": {"url": "https://github.com/your-org/coco-milk-store/issues"}, "homepage": "https://github.com/your-org/coco-milk-store#readme", "engines": {"node": ">=16.0.0"}}