// Performance Service
// Specialized service for performance monitoring and analysis using Lighthouse

import { BrowserlessClient } from '../client'
import {
  BrowserlessConfig,
  BrowserlessResponse,
  PerformanceOptions,
  PerformanceResponse,
  LaunchOptions,
  RequestOptions
} from '../types'

export interface PerformancePreset {
  name: string
  description: string
  config: PerformanceOptions['config']
  launchOptions?: LaunchOptions
}

export interface PerformanceReport {
  url: string
  timestamp: string
  scores: {
    performance: number
    accessibility: number
    bestPractices: number
    seo: number
    pwa?: number
  }
  metrics: {
    firstContentfulPaint: number
    largestContentfulPaint: number
    firstInputDelay: number
    cumulativeLayoutShift: number
    speedIndex: number
    totalBlockingTime: number
  }
  opportunities: Array<{
    id: string
    title: string
    description: string
    score: number
    savings: number
  }>
  diagnostics: Array<{
    id: string
    title: string
    description: string
    score: number
  }>
}

export interface BulkPerformanceOptions {
  urls: string[]
  preset?: keyof typeof PerformanceService.PRESETS
  config?: PerformanceOptions['config']
  launchOptions?: LaunchOptions
  requestOptions?: RequestOptions
  concurrency?: number
  delay?: number
}

export interface BulkPerformanceResult {
  url: string
  success: boolean
  report?: PerformanceReport
  error?: string
}

export interface PerformanceComparison {
  baseline: PerformanceReport
  comparison: PerformanceReport
  differences: {
    scores: Record<string, number>
    metrics: Record<string, number>
  }
  improvements: string[]
  regressions: string[]
}

/**
 * Specialized service for performance monitoring and analysis
 */
export class PerformanceService {
  private client: BrowserlessClient

  // Predefined performance testing presets
  public static readonly PRESETS: Record<string, PerformancePreset> = {
    desktop: {
      name: 'Desktop Performance',
      description: 'Standard desktop performance audit',
      config: {
        extends: 'lighthouse:default',
        settings: {
          onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
          throttlingMethod: 'simulate',
          throttling: {
            rttMs: 40,
            throughputKbps: 10240,
            cpuSlowdownMultiplier: 1
          }
        }
      },
      launchOptions: {
        headless: true,
        blockAds: false
      }
    },
    
    mobile: {
      name: 'Mobile Performance',
      description: 'Mobile performance audit with throttling',
      config: {
        extends: 'lighthouse:default',
        settings: {
          onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
          throttlingMethod: 'simulate',
          throttling: {
            rttMs: 150,
            throughputKbps: 1638,
            cpuSlowdownMultiplier: 4
          }
        }
      },
      launchOptions: {
        headless: true,
        blockAds: false
      }
    },
    
    accessibility: {
      name: 'Accessibility Audit',
      description: 'Comprehensive accessibility testing',
      config: {
        extends: 'lighthouse:default',
        settings: {
          onlyCategories: ['accessibility'],
          skipAudits: ['color-contrast'] // Skip if needed for faster testing
        }
      },
      launchOptions: {
        headless: true
      }
    },
    
    seo: {
      name: 'SEO Audit',
      description: 'Search engine optimization audit',
      config: {
        extends: 'lighthouse:default',
        settings: {
          onlyCategories: ['seo']
        }
      },
      launchOptions: {
        headless: true,
        blockAds: true
      }
    },
    
    pwa: {
      name: 'PWA Audit',
      description: 'Progressive Web App audit',
      config: {
        extends: 'lighthouse:default',
        settings: {
          onlyCategories: ['pwa']
        }
      },
      launchOptions: {
        headless: true
      }
    },
    
    comprehensive: {
      name: 'Comprehensive Audit',
      description: 'Full Lighthouse audit with all categories',
      config: {
        extends: 'lighthouse:default',
        settings: {
          throttlingMethod: 'simulate'
        }
      },
      launchOptions: {
        headless: true,
        timeout: 120000 // Longer timeout for comprehensive audit
      }
    },
    
    fast: {
      name: 'Fast Audit',
      description: 'Quick performance check with essential metrics',
      config: {
        extends: 'lighthouse:default',
        settings: {
          onlyCategories: ['performance'],
          skipAudits: [
            'screenshot-thumbnails',
            'final-screenshot',
            'largest-contentful-paint-element',
            'layout-shift-elements'
          ]
        }
      },
      launchOptions: {
        headless: true,
        timeout: 30000
      }
    }
  }

  constructor(config: BrowserlessConfig) {
    this.client = new BrowserlessClient(config)
  }

  /**
   * Run performance audit using a preset
   */
  public async auditWithPreset(
    url: string,
    presetName: keyof typeof PerformanceService.PRESETS,
    customConfig?: Partial<PerformanceOptions>,
    customLaunchOptions?: Partial<LaunchOptions>
  ): Promise<BrowserlessResponse<PerformanceReport>> {
    const preset = PerformanceService.PRESETS[presetName]
    if (!preset) {
      throw new Error(`Unknown preset: ${presetName}`)
    }

    const options: PerformanceOptions = {
      url,
      config: preset.config,
      ...customConfig
    }

    const launchOptions: LaunchOptions = {
      ...preset.launchOptions,
      ...customLaunchOptions
    }

    const response = await this.client.performance(options, launchOptions)

    if (response.success && response.data) {
      const report = this.parsePerformanceReport(url, response.data)
      return {
        ...response,
        data: report
      }
    }

    return response as BrowserlessResponse<PerformanceReport>
  }

  /**
   * Run basic performance audit
   */
  public async audit(
    url: string,
    config?: PerformanceOptions['config'],
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<PerformanceReport>> {
    const options: PerformanceOptions = {
      url,
      config
    }

    const response = await this.client.performance(options, launchOptions)

    if (response.success && response.data) {
      const report = this.parsePerformanceReport(url, response.data)
      return {
        ...response,
        data: report
      }
    }

    return response as BrowserlessResponse<PerformanceReport>
  }

  /**
   * Run bulk performance audits
   */
  public async auditBulk(
    bulkOptions: BulkPerformanceOptions
  ): Promise<BulkPerformanceResult[]> {
    const {
      urls,
      preset,
      config,
      launchOptions = {},
      requestOptions = {},
      concurrency = 1, // Lower concurrency for performance tests
      delay = 5000 // Longer delay between tests
    } = bulkOptions

    const results: BulkPerformanceResult[] = []
    const chunks = this.chunkArray(urls, concurrency)

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (url) => {
        try {
          let response: BrowserlessResponse<PerformanceReport>

          if (preset) {
            response = await this.auditWithPreset(url, preset, { config }, launchOptions)
          } else {
            response = await this.audit(url, config, launchOptions)
          }

          if (response.success && response.data) {
            return {
              url,
              success: true,
              report: response.data
            }
          } else {
            return {
              url,
              success: false,
              error: response.error?.message || 'Unknown error'
            }
          }
        } catch (error) {
          return {
            url,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      })

      const chunkResults = await Promise.all(chunkPromises)
      results.push(...chunkResults)

      // Add delay between chunks
      if (delay > 0 && chunks.indexOf(chunk) < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    return results
  }

  /**
   * Compare performance between two URLs or reports
   */
  public async compare(
    baselineUrl: string,
    comparisonUrl: string,
    preset: keyof typeof PerformanceService.PRESETS = 'desktop',
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<PerformanceComparison>> {
    try {
      const [baselineResponse, comparisonResponse] = await Promise.all([
        this.auditWithPreset(baselineUrl, preset, {}, launchOptions),
        this.auditWithPreset(comparisonUrl, preset, {}, launchOptions)
      ])

      if (baselineResponse.success && comparisonResponse.success && 
          baselineResponse.data && comparisonResponse.data) {
        
        const comparison = this.createComparison(baselineResponse.data, comparisonResponse.data)
        
        return {
          success: true,
          data: comparison,
          status: 200
        }
      }

      return {
        success: false,
        error: {
          message: 'Failed to generate performance comparison',
          details: {
            baseline: baselineResponse.error,
            comparison: comparisonResponse.error
          }
        },
        status: 500
      }
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Comparison failed'
        },
        status: 500
      }
    }
  }

  /**
   * Monitor performance over time
   */
  public async monitor(
    url: string,
    checks: number,
    interval: number,
    preset: keyof typeof PerformanceService.PRESETS = 'fast',
    launchOptions?: LaunchOptions
  ): Promise<PerformanceReport[]> {
    const reports: PerformanceReport[] = []

    for (let i = 0; i < checks; i++) {
      try {
        const response = await this.auditWithPreset(url, preset, {}, launchOptions)
        
        if (response.success && response.data) {
          reports.push(response.data)
        }
      } catch (error) {
        console.warn(`Performance check ${i + 1} failed:`, error)
      }

      // Wait before next check (except for the last one)
      if (i < checks - 1) {
        await new Promise(resolve => setTimeout(resolve, interval))
      }
    }

    return reports
  }

  /**
   * Get performance insights and recommendations
   */
  public getInsights(report: PerformanceReport): {
    criticalIssues: string[]
    recommendations: string[]
    strengths: string[]
  } {
    const insights = {
      criticalIssues: [] as string[],
      recommendations: [] as string[],
      strengths: [] as string[]
    }

    // Analyze scores
    if (report.scores.performance < 50) {
      insights.criticalIssues.push('Poor performance score - immediate optimization needed')
    } else if (report.scores.performance > 90) {
      insights.strengths.push('Excellent performance score')
    }

    if (report.scores.accessibility < 80) {
      insights.criticalIssues.push('Accessibility issues detected - may affect user experience')
    }

    // Analyze metrics
    if (report.metrics.largestContentfulPaint > 2500) {
      insights.criticalIssues.push('Largest Contentful Paint is too slow (>2.5s)')
      insights.recommendations.push('Optimize images and reduce server response times')
    }

    if (report.metrics.cumulativeLayoutShift > 0.1) {
      insights.criticalIssues.push('High Cumulative Layout Shift detected')
      insights.recommendations.push('Add size attributes to images and reserve space for dynamic content')
    }

    if (report.metrics.firstInputDelay > 100) {
      insights.recommendations.push('Reduce JavaScript execution time to improve interactivity')
    }

    // Analyze opportunities
    const highImpactOpportunities = report.opportunities.filter(opp => opp.savings > 1000)
    if (highImpactOpportunities.length > 0) {
      insights.recommendations.push(
        `Focus on high-impact optimizations: ${highImpactOpportunities.map(o => o.title).join(', ')}`
      )
    }

    return insights
  }

  /**
   * Get available presets
   */
  public getPresets(): Record<string, PerformancePreset> {
    return PerformanceService.PRESETS
  }

  /**
   * Parse Lighthouse report into structured format
   */
  private parsePerformanceReport(url: string, data: PerformanceResponse): PerformanceReport {
    const lhr = data.data.lhr

    return {
      url,
      timestamp: new Date().toISOString(),
      scores: {
        performance: Math.round((lhr.categories.performance?.score || 0) * 100),
        accessibility: Math.round((lhr.categories.accessibility?.score || 0) * 100),
        bestPractices: Math.round((lhr.categories['best-practices']?.score || 0) * 100),
        seo: Math.round((lhr.categories.seo?.score || 0) * 100),
        pwa: lhr.categories.pwa ? Math.round(lhr.categories.pwa.score * 100) : undefined
      },
      metrics: {
        firstContentfulPaint: lhr.audits['first-contentful-paint']?.numericValue || 0,
        largestContentfulPaint: lhr.audits['largest-contentful-paint']?.numericValue || 0,
        firstInputDelay: lhr.audits['max-potential-fid']?.numericValue || 0,
        cumulativeLayoutShift: lhr.audits['cumulative-layout-shift']?.numericValue || 0,
        speedIndex: lhr.audits['speed-index']?.numericValue || 0,
        totalBlockingTime: lhr.audits['total-blocking-time']?.numericValue || 0
      },
      opportunities: Object.values(lhr.audits)
        .filter((audit: any) => audit.details?.type === 'opportunity')
        .map((audit: any) => ({
          id: audit.id,
          title: audit.title,
          description: audit.description,
          score: audit.score || 0,
          savings: audit.details?.overallSavingsMs || 0
        })),
      diagnostics: Object.values(lhr.audits)
        .filter((audit: any) => audit.details?.type === 'diagnostic')
        .map((audit: any) => ({
          id: audit.id,
          title: audit.title,
          description: audit.description,
          score: audit.score || 0
        }))
    }
  }

  /**
   * Create performance comparison
   */
  private createComparison(baseline: PerformanceReport, comparison: PerformanceReport): PerformanceComparison {
    const scoreDifferences: Record<string, number> = {}
    const metricDifferences: Record<string, number> = {}
    const improvements: string[] = []
    const regressions: string[] = []

    // Compare scores
    Object.keys(baseline.scores).forEach(key => {
      const baselineScore = baseline.scores[key as keyof typeof baseline.scores] || 0
      const comparisonScore = comparison.scores[key as keyof typeof comparison.scores] || 0
      const diff = comparisonScore - baselineScore
      
      scoreDifferences[key] = diff
      
      if (diff > 5) {
        improvements.push(`${key} score improved by ${diff} points`)
      } else if (diff < -5) {
        regressions.push(`${key} score decreased by ${Math.abs(diff)} points`)
      }
    })

    // Compare metrics
    Object.keys(baseline.metrics).forEach(key => {
      const baselineMetric = baseline.metrics[key as keyof typeof baseline.metrics]
      const comparisonMetric = comparison.metrics[key as keyof typeof comparison.metrics]
      const diff = comparisonMetric - baselineMetric
      
      metricDifferences[key] = diff
      
      // For metrics, lower is generally better
      if (diff < -100) {
        improvements.push(`${key} improved by ${Math.abs(diff)}ms`)
      } else if (diff > 100) {
        regressions.push(`${key} regressed by ${diff}ms`)
      }
    })

    return {
      baseline,
      comparison,
      differences: {
        scores: scoreDifferences,
        metrics: metricDifferences
      },
      improvements,
      regressions
    }
  }

  /**
   * Utility method to chunk array for batch processing
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }
}