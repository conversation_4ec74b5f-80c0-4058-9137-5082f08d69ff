// Screenshot Service
// Specialized service for taking screenshots with advanced features

import { BrowserlessClient } from '../client'
import {
  BrowserlessConfig,
  BrowserlessResponse,
  ScreenshotOptions,
  ScreenshotResponse,
  LaunchOptions,
  RequestOptions
} from '../types'
import { generateFilename } from '../utils'

export interface ScreenshotPreset {
  name: string
  description: string
  options: ScreenshotOptions
  launchOptions?: LaunchOptions
}

export interface BulkScreenshotOptions {
  urls: string[]
  options?: Partial<ScreenshotOptions>
  launchOptions?: LaunchOptions
  requestOptions?: RequestOptions
  concurrency?: number
  delay?: number
}

export interface BulkScreenshotResult {
  url: string
  success: boolean
  data?: Buffer
  error?: string
  filename?: string
}

/**
 * Specialized service for taking screenshots with advanced features
 */
export class ScreenshotService {
  private client: BrowserlessClient

  // Predefined screenshot presets
  public static readonly PRESETS: Record<string, ScreenshotPreset> = {
    desktop: {
      name: 'Desktop',
      description: 'Standard desktop screenshot (1920x1080)',
      options: {
        options: {
          type: 'png',
          fullPage: false
        }
      },
      launchOptions: {
        headless: true
      }
    },
    mobile: {
      name: 'Mobile',
      description: 'Mobile viewport screenshot (375x667)',
      options: {
        options: {
          type: 'png',
          fullPage: false,
          clip: {
            x: 0,
            y: 0,
            width: 375,
            height: 667
          }
        }
      },
      launchOptions: {
        headless: true
      }
    },
    fullPage: {
      name: 'Full Page',
      description: 'Complete page screenshot',
      options: {
        options: {
          type: 'png',
          fullPage: true
        }
      },
      launchOptions: {
        headless: true
      }
    },
    highQuality: {
      name: 'High Quality',
      description: 'High quality JPEG screenshot',
      options: {
        options: {
          type: 'jpeg',
          quality: 95,
          fullPage: true
        }
      },
      launchOptions: {
        headless: true
      }
    },
    thumbnail: {
      name: 'Thumbnail',
      description: 'Small thumbnail screenshot',
      options: {
        options: {
          type: 'jpeg',
          quality: 80,
          fullPage: false,
          clip: {
            x: 0,
            y: 0,
            width: 300,
            height: 200
          }
        }
      },
      launchOptions: {
        headless: true
      }
    },
    print: {
      name: 'Print Ready',
      description: 'High resolution screenshot for printing',
      options: {
        options: {
          type: 'png',
          fullPage: true,
          omitBackground: false
        }
      },
      launchOptions: {
        headless: true,
        blockAds: true
      }
    }
  }

  constructor(config: BrowserlessConfig) {
    this.client = new BrowserlessClient(config)
  }

  /**
   * Take a screenshot using a preset configuration
   */
  public async takeWithPreset(
    url: string,
    presetName: keyof typeof ScreenshotService.PRESETS,
    customOptions?: Partial<ScreenshotOptions>,
    customLaunchOptions?: Partial<LaunchOptions>
  ): Promise<BrowserlessResponse<ScreenshotResponse>> {
    const preset = ScreenshotService.PRESETS[presetName]
    if (!preset) {
      throw new Error(`Unknown preset: ${presetName}`)
    }

    const options: ScreenshotOptions = {
      url,
      ...preset.options,
      ...customOptions,
      options: {
        ...preset.options.options,
        ...customOptions?.options
      }
    }

    const launchOptions: LaunchOptions = {
      ...preset.launchOptions,
      ...customLaunchOptions
    }

    return this.client.screenshot(options, launchOptions)
  }

  /**
   * Take a basic screenshot
   */
  public async take(
    url: string,
    options?: Partial<ScreenshotOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<ScreenshotResponse>> {
    const screenshotOptions: ScreenshotOptions = {
      url,
      ...options
    }

    return this.client.screenshot(screenshotOptions, launchOptions)
  }

  /**
   * Take a screenshot of HTML content
   */
  public async takeFromHTML(
    html: string,
    options?: Partial<ScreenshotOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<ScreenshotResponse>> {
    const screenshotOptions: ScreenshotOptions = {
      html,
      ...options
    }

    return this.client.screenshot(screenshotOptions, launchOptions)
  }

  /**
   * Take multiple screenshots in bulk
   */
  public async takeBulk(
    bulkOptions: BulkScreenshotOptions
  ): Promise<BulkScreenshotResult[]> {
    const {
      urls,
      options = {},
      launchOptions = {},
      requestOptions = {},
      concurrency = 3,
      delay = 1000
    } = bulkOptions

    const results: BulkScreenshotResult[] = []
    const chunks = this.chunkArray(urls, concurrency)

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (url) => {
        try {
          const screenshotOptions: ScreenshotOptions = {
            url,
            ...options
          }

          const response = await this.client.screenshot(
            screenshotOptions,
            launchOptions,
            requestOptions
          )

          if (response.success && response.data) {
            const filename = generateFilename(url, 'image/png', 'png')
            return {
              url,
              success: true,
              data: response.data.data,
              filename
            }
          } else {
            return {
              url,
              success: false,
              error: response.error?.message || 'Unknown error'
            }
          }
        } catch (error) {
          return {
            url,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      })

      const chunkResults = await Promise.all(chunkPromises)
      results.push(...chunkResults)

      // Add delay between chunks to avoid rate limiting
      if (delay > 0 && chunks.indexOf(chunk) < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    return results
  }

  /**
   * Take a screenshot with element highlighting
   */
  public async takeWithHighlight(
    url: string,
    selector: string,
    highlightColor: string = '#ff0000',
    options?: Partial<ScreenshotOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<ScreenshotResponse>> {
    const highlightScript = `
      const element = document.querySelector('${selector}');
      if (element) {
        element.style.outline = '3px solid ${highlightColor}';
        element.style.outlineOffset = '2px';
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    `

    const screenshotOptions: ScreenshotOptions = {
      url,
      addScriptTag: [
        { content: highlightScript }
      ],
      waitForTimeout: 1000, // Wait for highlight to apply
      ...options
    }

    return this.client.screenshot(screenshotOptions, launchOptions)
  }

  /**
   * Take a screenshot with custom viewport size
   */
  public async takeWithViewport(
    url: string,
    width: number,
    height: number,
    options?: Partial<ScreenshotOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<ScreenshotResponse>> {
    const viewportScript = `
      window.resizeTo(${width}, ${height});
    `

    const screenshotOptions: ScreenshotOptions = {
      url,
      addScriptTag: [
        { content: viewportScript }
      ],
      waitForTimeout: 500,
      options: {
        clip: {
          x: 0,
          y: 0,
          width,
          height
        },
        ...options?.options
      },
      ...options
    }

    return this.client.screenshot(screenshotOptions, launchOptions)
  }

  /**
   * Take a screenshot after waiting for specific content
   */
  public async takeAfterContent(
    url: string,
    waitSelector: string,
    options?: Partial<ScreenshotOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<ScreenshotResponse>> {
    const screenshotOptions: ScreenshotOptions = {
      url,
      waitForSelector: {
        selector: waitSelector,
        timeout: 10000
      },
      ...options
    }

    return this.client.screenshot(screenshotOptions, launchOptions)
  }

  /**
   * Get available presets
   */
  public getPresets(): Record<string, ScreenshotPreset> {
    return ScreenshotService.PRESETS
  }

  /**
   * Utility method to chunk array for batch processing
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }
}