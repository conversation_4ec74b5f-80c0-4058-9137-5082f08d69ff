// PDF Service
// Specialized service for generating PDFs with advanced features

import { BrowserlessClient } from '../client'
import {
  BrowserlessConfig,
  BrowserlessResponse,
  PDFOptions,
  PDFResponse,
  LaunchOptions,
  RequestOptions
} from '../types'
import { generateFilename } from '../utils'

export interface PDFPreset {
  name: string
  description: string
  options: PDFOptions
  launchOptions?: LaunchOptions
}

export interface BulkPDFOptions {
  urls: string[]
  options?: Partial<PDFOptions>
  launchOptions?: LaunchOptions
  requestOptions?: RequestOptions
  concurrency?: number
  delay?: number
}

export interface BulkPDFResult {
  url: string
  success: boolean
  data?: Buffer
  error?: string
  filename?: string
}

export interface PDFTemplate {
  headerTemplate?: string
  footerTemplate?: string
  styles?: string
}

/**
 * Specialized service for generating PDFs with advanced features
 */
export class PDFService {
  private client: BrowserlessClient

  // Predefined PDF presets
  public static readonly PRESETS: Record<string, PDFPreset> = {
    document: {
      name: 'Document',
      description: 'Standard document format (A4, portrait)',
      options: {
        options: {
          format: 'A4',
          landscape: false,
          printBackground: true,
          margin: {
            top: '1in',
            right: '1in',
            bottom: '1in',
            left: '1in'
          }
        }
      },
      launchOptions: {
        headless: true,
        blockAds: true
      }
    },
    report: {
      name: 'Report',
      description: 'Professional report format with headers and footers',
      options: {
        options: {
          format: 'A4',
          landscape: false,
          printBackground: true,
          displayHeaderFooter: true,
          headerTemplate: `
            <div style="font-size: 10px; text-align: center; width: 100%; margin: 0 auto;">
              <span class="title"></span>
            </div>
          `,
          footerTemplate: `
            <div style="font-size: 10px; text-align: center; width: 100%; margin: 0 auto;">
              Page <span class="pageNumber"></span> of <span class="totalPages"></span>
            </div>
          `,
          margin: {
            top: '1.5in',
            right: '1in',
            bottom: '1.5in',
            left: '1in'
          }
        }
      },
      launchOptions: {
        headless: true,
        blockAds: true
      }
    },
    invoice: {
      name: 'Invoice',
      description: 'Invoice format (A4, optimized for business documents)',
      options: {
        options: {
          format: 'A4',
          landscape: false,
          printBackground: true,
          displayHeaderFooter: false,
          margin: {
            top: '0.5in',
            right: '0.5in',
            bottom: '0.5in',
            left: '0.5in'
          }
        }
      },
      launchOptions: {
        headless: true,
        blockAds: true,
        blockConsentModals: true
      }
    },
    presentation: {
      name: 'Presentation',
      description: 'Landscape format for presentations',
      options: {
        options: {
          format: 'A4',
          landscape: true,
          printBackground: true,
          margin: {
            top: '0.5in',
            right: '0.5in',
            bottom: '0.5in',
            left: '0.5in'
          }
        }
      },
      launchOptions: {
        headless: true,
        blockAds: true
      }
    },
    book: {
      name: 'Book',
      description: 'Book format with minimal margins',
      options: {
        options: {
          format: 'A4',
          landscape: false,
          printBackground: true,
          margin: {
            top: '0.75in',
            right: '0.75in',
            bottom: '0.75in',
            left: '1in'
          }
        }
      },
      launchOptions: {
        headless: true,
        blockAds: true
      }
    },
    legal: {
      name: 'Legal',
      description: 'Legal document format (Legal size)',
      options: {
        options: {
          format: 'Legal',
          landscape: false,
          printBackground: true,
          margin: {
            top: '1in',
            right: '1in',
            bottom: '1in',
            left: '1.5in'
          }
        }
      },
      launchOptions: {
        headless: true,
        blockAds: true
      }
    }
  }

  // Common PDF templates
  public static readonly TEMPLATES: Record<string, PDFTemplate> = {
    business: {
      headerTemplate: `
        <div style="font-size: 10px; padding: 10px; width: 100%; text-align: center; border-bottom: 1px solid #ccc;">
          <strong style="font-size: 12px;">Business Document</strong>
        </div>
      `,
      footerTemplate: `
        <div style="font-size: 9px; padding: 10px; width: 100%; text-align: center; border-top: 1px solid #ccc;">
          <span>Generated on <span class="date"></span> | Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
        </div>
      `,
      styles: `
        @media print {
          body { font-family: Arial, sans-serif; }
          h1, h2, h3 { color: #333; }
          .no-print { display: none; }
        }
      `
    },
    minimal: {
      footerTemplate: `
        <div style="font-size: 8px; text-align: center; width: 100%; margin: 0 auto;">
          <span class="pageNumber"></span>
        </div>
      `
    },
    branded: {
      headerTemplate: `
        <div style="font-size: 10px; padding: 15px; width: 100%; background: #f8f9fa; border-bottom: 2px solid #007bff;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <strong style="color: #007bff; font-size: 14px;">Company Name</strong>
            <span style="color: #6c757d;">Confidential</span>
          </div>
        </div>
      `,
      footerTemplate: `
        <div style="font-size: 9px; padding: 10px; width: 100%; background: #f8f9fa; border-top: 1px solid #dee2e6;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="color: #6c757d;">© 2024 Company Name. All rights reserved.</span>
            <span>Page <span class="pageNumber"></span> of <span class="totalPages"></span></span>
          </div>
        </div>
      `
    }
  }

  constructor(config: BrowserlessConfig) {
    this.client = new BrowserlessClient(config)
  }

  /**
   * Generate a PDF using a preset configuration
   */
  public async generateWithPreset(
    url: string,
    presetName: keyof typeof PDFService.PRESETS,
    customOptions?: Partial<PDFOptions>,
    customLaunchOptions?: Partial<LaunchOptions>
  ): Promise<BrowserlessResponse<PDFResponse>> {
    const preset = PDFService.PRESETS[presetName]
    if (!preset) {
      throw new Error(`Unknown preset: ${presetName}`)
    }

    const options: PDFOptions = {
      url,
      ...preset.options,
      ...customOptions,
      options: {
        ...preset.options.options,
        ...customOptions?.options
      }
    }

    const launchOptions: LaunchOptions = {
      ...preset.launchOptions,
      ...customLaunchOptions
    }

    return this.client.pdf(options, launchOptions)
  }

  /**
   * Generate a PDF with a template
   */
  public async generateWithTemplate(
    url: string,
    templateName: keyof typeof PDFService.TEMPLATES,
    options?: Partial<PDFOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<PDFResponse>> {
    const template = PDFService.TEMPLATES[templateName]
    if (!template) {
      throw new Error(`Unknown template: ${templateName}`)
    }

    const pdfOptions: PDFOptions = {
      url,
      ...options,
      addStyleTag: [
        ...(options?.addStyleTag || []),
        ...(template.styles ? [{ content: template.styles }] : [])
      ],
      options: {
        displayHeaderFooter: !!(template.headerTemplate || template.footerTemplate),
        headerTemplate: template.headerTemplate || '',
        footerTemplate: template.footerTemplate || '',
        ...options?.options
      }
    }

    return this.client.pdf(pdfOptions, launchOptions)
  }

  /**
   * Generate a basic PDF
   */
  public async generate(
    url: string,
    options?: Partial<PDFOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<PDFResponse>> {
    const pdfOptions: PDFOptions = {
      url,
      ...options
    }

    return this.client.pdf(pdfOptions, launchOptions)
  }

  /**
   * Generate a PDF from HTML content
   */
  public async generateFromHTML(
    html: string,
    options?: Partial<PDFOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<PDFResponse>> {
    const pdfOptions: PDFOptions = {
      html,
      ...options
    }

    return this.client.pdf(pdfOptions, launchOptions)
  }

  /**
   * Generate multiple PDFs in bulk
   */
  public async generateBulk(
    bulkOptions: BulkPDFOptions
  ): Promise<BulkPDFResult[]> {
    const {
      urls,
      options = {},
      launchOptions = {},
      requestOptions = {},
      concurrency = 2, // Lower concurrency for PDF generation
      delay = 2000
    } = bulkOptions

    const results: BulkPDFResult[] = []
    const chunks = this.chunkArray(urls, concurrency)

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (url) => {
        try {
          const pdfOptions: PDFOptions = {
            url,
            ...options
          }

          const response = await this.client.pdf(
            pdfOptions,
            launchOptions,
            requestOptions
          )

          if (response.success && response.data) {
            const filename = generateFilename(url, 'application/pdf', 'pdf')
            return {
              url,
              success: true,
              data: response.data.data,
              filename
            }
          } else {
            return {
              url,
              success: false,
              error: response.error?.message || 'Unknown error'
            }
          }
        } catch (error) {
          return {
            url,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      })

      const chunkResults = await Promise.all(chunkPromises)
      results.push(...chunkResults)

      // Add delay between chunks
      if (delay > 0 && chunks.indexOf(chunk) < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    return results
  }

  /**
   * Generate a PDF with custom page breaks
   */
  public async generateWithPageBreaks(
    url: string,
    pageBreakSelectors: string[],
    options?: Partial<PDFOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<PDFResponse>> {
    const pageBreakScript = `
      ${pageBreakSelectors.map(selector => `
        document.querySelectorAll('${selector}').forEach(element => {
          element.style.pageBreakBefore = 'always';
        });
      `).join('\n')}
    `

    const pdfOptions: PDFOptions = {
      url,
      addScriptTag: [
        { content: pageBreakScript }
      ],
      waitForTimeout: 1000,
      ...options
    }

    return this.client.pdf(pdfOptions, launchOptions)
  }

  /**
   * Generate a PDF with watermark
   */
  public async generateWithWatermark(
    url: string,
    watermarkText: string,
    options?: Partial<PDFOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<PDFResponse>> {
    const watermarkStyle = `
      body::before {
        content: "${watermarkText}";
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-45deg);
        font-size: 72px;
        color: rgba(0, 0, 0, 0.1);
        z-index: 9999;
        pointer-events: none;
        font-weight: bold;
        white-space: nowrap;
      }
    `

    const pdfOptions: PDFOptions = {
      url,
      addStyleTag: [
        { content: watermarkStyle }
      ],
      ...options
    }

    return this.client.pdf(pdfOptions, launchOptions)
  }

  /**
   * Get available presets
   */
  public getPresets(): Record<string, PDFPreset> {
    return PDFService.PRESETS
  }

  /**
   * Get available templates
   */
  public getTemplates(): Record<string, PDFTemplate> {
    return PDFService.TEMPLATES
  }

  /**
   * Utility method to chunk array for batch processing
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }
}