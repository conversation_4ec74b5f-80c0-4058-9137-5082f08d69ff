// Automation Service
// Specialized service for browser automation tasks using the function API

import { BrowserlessClient } from '../client'
import {
  BrowserlessConfig,
  BrowserlessResponse,
  FunctionOptions,
  FunctionResponse,
  LaunchOptions,
  RequestOptions
} from '../types'

export interface AutomationTask {
  name: string
  description: string
  code: string
  context?: Record<string, any>
  timeout?: number
}

export interface FormFillOptions {
  url: string
  fields: Record<string, string>
  submitSelector?: string
  waitAfterFill?: number
  captureScreenshot?: boolean
}

export interface NavigationOptions {
  url: string
  steps: NavigationStep[]
  captureScreenshots?: boolean
  waitBetweenSteps?: number
}

export interface NavigationStep {
  action: 'click' | 'type' | 'wait' | 'scroll' | 'hover' | 'select'
  selector?: string
  value?: string
  timeout?: number
  description?: string
}

export interface MonitoringOptions {
  url: string
  checkInterval: number
  maxChecks: number
  conditions: MonitoringCondition[]
}

export interface MonitoringCondition {
  type: 'element_exists' | 'element_text' | 'page_title' | 'url_contains'
  selector?: string
  expectedValue?: string
  description: string
}

export interface MonitoringResult {
  timestamp: string
  success: boolean
  conditions: Array<{
    condition: MonitoringCondition
    passed: boolean
    actualValue?: string
  }>
  screenshot?: Buffer
}

/**
 * Specialized service for browser automation tasks
 */
export class AutomationService {
  private client: BrowserlessClient

  // Predefined automation tasks
  public static readonly TASKS: Record<string, AutomationTask> = {
    loginForm: {
      name: 'Login Form',
      description: 'Fill and submit a login form',
      code: `
        async ({ url, username, password, usernameSelector = '#username', passwordSelector = '#password', submitSelector = 'button[type="submit"]' }) => {
          await page.goto(url);
          await page.waitForSelector(usernameSelector);
          
          await page.type(usernameSelector, username);
          await page.type(passwordSelector, password);
          
          await page.click(submitSelector);
          await page.waitForNavigation();
          
          return {
            success: true,
            currentUrl: page.url(),
            title: await page.title()
          };
        }
      `
    },
    
    contactForm: {
      name: 'Contact Form',
      description: 'Fill and submit a contact form',
      code: `
        async ({ url, name, email, message, nameSelector = '#name', emailSelector = '#email', messageSelector = '#message', submitSelector = 'button[type="submit"]' }) => {
          await page.goto(url);
          await page.waitForSelector(nameSelector);
          
          await page.type(nameSelector, name);
          await page.type(emailSelector, email);
          await page.type(messageSelector, message);
          
          await page.click(submitSelector);
          await page.waitForTimeout(2000);
          
          return {
            success: true,
            currentUrl: page.url(),
            title: await page.title()
          };
        }
      `
    },
    
    searchAndExtract: {
      name: 'Search and Extract',
      description: 'Perform a search and extract results',
      code: `
        async ({ url, searchTerm, searchSelector = '#search', submitSelector = 'button[type="submit"]', resultsSelector = '.search-result' }) => {
          await page.goto(url);
          await page.waitForSelector(searchSelector);
          
          await page.type(searchSelector, searchTerm);
          await page.click(submitSelector);
          await page.waitForSelector(resultsSelector);
          
          const results = await page.$$eval(resultsSelector, elements => 
            elements.map(el => ({
              title: el.querySelector('h3, .title')?.textContent?.trim(),
              description: el.querySelector('.description, p')?.textContent?.trim(),
              link: el.querySelector('a')?.href
            }))
          );
          
          return {
            success: true,
            searchTerm,
            resultCount: results.length,
            results
          };
        }
      `
    },
    
    priceMonitor: {
      name: 'Price Monitor',
      description: 'Monitor product price changes',
      code: `
        async ({ url, priceSelector = '.price' }) => {
          await page.goto(url);
          await page.waitForSelector(priceSelector);
          
          const priceText = await page.$eval(priceSelector, el => el.textContent?.trim());
          const price = priceText?.match(/[\\d,]+\\.?\\d*/)?.[0];
          
          return {
            success: true,
            timestamp: new Date().toISOString(),
            priceText,
            price: price ? parseFloat(price.replace(',', '')) : null,
            url
          };
        }
      `
    },
    
    socialMediaPost: {
      name: 'Social Media Post',
      description: 'Create a social media post',
      code: `
        async ({ url, content, postSelector = '[data-testid="tweetTextarea_0"]', submitSelector = '[data-testid="tweetButtonInline"]' }) => {
          await page.goto(url);
          await page.waitForSelector(postSelector);
          
          await page.type(postSelector, content);
          await page.waitForTimeout(1000);
          
          await page.click(submitSelector);
          await page.waitForTimeout(3000);
          
          return {
            success: true,
            content,
            timestamp: new Date().toISOString()
          };
        }
      `
    }
  }

  constructor(config: BrowserlessConfig) {
    this.client = new BrowserlessClient(config)
  }

  /**
   * Execute a predefined automation task
   */
  public async executeTask(
    taskName: keyof typeof AutomationService.TASKS,
    context: Record<string, any>,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<FunctionResponse>> {
    const task = AutomationService.TASKS[taskName]
    if (!task) {
      throw new Error(`Unknown task: ${taskName}`)
    }

    const functionOptions: FunctionOptions = {
      code: task.code,
      context: { ...task.context, ...context }
    }

    return this.client.function(functionOptions, launchOptions, requestOptions)
  }

  /**
   * Execute custom automation code
   */
  public async executeCustom(
    code: string,
    context?: Record<string, any>,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<FunctionResponse>> {
    const functionOptions: FunctionOptions = {
      code,
      context
    }

    return this.client.function(functionOptions, launchOptions, requestOptions)
  }

  /**
   * Fill and submit a form
   */
  public async fillForm(
    options: FormFillOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<FunctionResponse>> {
    const { url, fields, submitSelector, waitAfterFill = 1000, captureScreenshot = false } = options

    const code = `
      async ({ url, fields, submitSelector, waitAfterFill, captureScreenshot }) => {
        await page.goto(url);
        
        // Fill form fields
        for (const [selector, value] of Object.entries(fields)) {
          await page.waitForSelector(selector);
          await page.type(selector, value);
        }
        
        // Wait after filling
        await page.waitForTimeout(waitAfterFill);
        
        let screenshot = null;
        if (captureScreenshot) {
          screenshot = await page.screenshot({ encoding: 'base64' });
        }
        
        // Submit form if selector provided
        if (submitSelector) {
          await page.click(submitSelector);
          await page.waitForTimeout(2000);
        }
        
        return {
          success: true,
          currentUrl: page.url(),
          title: await page.title(),
          screenshot
        };
      }
    `

    const functionOptions: FunctionOptions = {
      code,
      context: { url, fields, submitSelector, waitAfterFill, captureScreenshot }
    }

    return this.client.function(functionOptions, launchOptions, requestOptions)
  }

  /**
   * Perform multi-step navigation
   */
  public async navigate(
    options: NavigationOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<FunctionResponse>> {
    const { url, steps, captureScreenshots = false, waitBetweenSteps = 1000 } = options

    const code = `
      async ({ url, steps, captureScreenshots, waitBetweenSteps }) => {
        await page.goto(url);
        const results = [];
        
        for (let i = 0; i < steps.length; i++) {
          const step = steps[i];
          const stepResult = { step: i + 1, action: step.action, description: step.description };
          
          try {
            switch (step.action) {
              case 'click':
                await page.waitForSelector(step.selector);
                await page.click(step.selector);
                break;
                
              case 'type':
                await page.waitForSelector(step.selector);
                await page.type(step.selector, step.value);
                break;
                
              case 'wait':
                if (step.selector) {
                  await page.waitForSelector(step.selector, { timeout: step.timeout || 10000 });
                } else {
                  await page.waitForTimeout(step.timeout || 1000);
                }
                break;
                
              case 'scroll':
                if (step.selector) {
                  await page.$eval(step.selector, el => el.scrollIntoView());
                } else {
                  await page.evaluate(() => window.scrollBy(0, window.innerHeight));
                }
                break;
                
              case 'hover':
                await page.waitForSelector(step.selector);
                await page.hover(step.selector);
                break;
                
              case 'select':
                await page.waitForSelector(step.selector);
                await page.select(step.selector, step.value);
                break;
            }
            
            stepResult.success = true;
            
            if (captureScreenshots) {
              stepResult.screenshot = await page.screenshot({ encoding: 'base64' });
            }
            
          } catch (error) {
            stepResult.success = false;
            stepResult.error = error.message;
          }
          
          results.push(stepResult);
          
          if (waitBetweenSteps > 0 && i < steps.length - 1) {
            await page.waitForTimeout(waitBetweenSteps);
          }
        }
        
        return {
          success: true,
          currentUrl: page.url(),
          title: await page.title(),
          steps: results
        };
      }
    `

    const functionOptions: FunctionOptions = {
      code,
      context: { url, steps, captureScreenshots, waitBetweenSteps }
    }

    return this.client.function(functionOptions, launchOptions, requestOptions)
  }

  /**
   * Monitor a webpage for changes
   */
  public async monitor(
    options: MonitoringOptions,
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<MonitoringResult[]> {
    const { url, checkInterval, maxChecks, conditions } = options
    const results: MonitoringResult[] = []

    for (let check = 0; check < maxChecks; check++) {
      const code = `
        async ({ url, conditions }) => {
          await page.goto(url);
          const conditionResults = [];
          
          for (const condition of conditions) {
            const result = { condition, passed: false };
            
            try {
              switch (condition.type) {
                case 'element_exists':
                  const element = await page.$(condition.selector);
                  result.passed = !!element;
                  break;
                  
                case 'element_text':
                  const textElement = await page.$(condition.selector);
                  if (textElement) {
                    const text = await textElement.textContent();
                    result.actualValue = text?.trim();
                    result.passed = text?.includes(condition.expectedValue) || false;
                  }
                  break;
                  
                case 'page_title':
                  const title = await page.title();
                  result.actualValue = title;
                  result.passed = title.includes(condition.expectedValue) || false;
                  break;
                  
                case 'url_contains':
                  const currentUrl = page.url();
                  result.actualValue = currentUrl;
                  result.passed = currentUrl.includes(condition.expectedValue) || false;
                  break;
              }
            } catch (error) {
              result.error = error.message;
            }
            
            conditionResults.push(result);
          }
          
          return {
            timestamp: new Date().toISOString(),
            conditions: conditionResults,
            screenshot: await page.screenshot({ encoding: 'base64' })
          };
        }
      `

      const functionOptions: FunctionOptions = {
        code,
        context: { url, conditions }
      }

      try {
        const response = await this.client.function(functionOptions, launchOptions, requestOptions)
        
        if (response.success && response.data) {
          const result: MonitoringResult = {
            timestamp: response.data.data.timestamp,
            success: true,
            conditions: response.data.data.conditions,
            screenshot: response.data.data.screenshot ? 
              Buffer.from(response.data.data.screenshot, 'base64') : undefined
          }
          results.push(result)
        }
      } catch (error) {
        results.push({
          timestamp: new Date().toISOString(),
          success: false,
          conditions: conditions.map(condition => ({
            condition,
            passed: false
          }))
        })
      }

      // Wait before next check (except for the last one)
      if (check < maxChecks - 1) {
        await new Promise(resolve => setTimeout(resolve, checkInterval))
      }
    }

    return results
  }

  /**
   * Extract data with complex interactions
   */
  public async extractWithInteraction(
    url: string,
    interactions: NavigationStep[],
    extractionSelectors: string[],
    launchOptions?: LaunchOptions,
    requestOptions?: RequestOptions
  ): Promise<BrowserlessResponse<FunctionResponse>> {
    const code = `
      async ({ url, interactions, extractionSelectors }) => {
        await page.goto(url);
        
        // Perform interactions
        for (const interaction of interactions) {
          switch (interaction.action) {
            case 'click':
              await page.waitForSelector(interaction.selector);
              await page.click(interaction.selector);
              break;
            case 'type':
              await page.waitForSelector(interaction.selector);
              await page.type(interaction.selector, interaction.value);
              break;
            case 'wait':
              if (interaction.selector) {
                await page.waitForSelector(interaction.selector);
              } else {
                await page.waitForTimeout(interaction.timeout || 1000);
              }
              break;
          }
          await page.waitForTimeout(500); // Small delay between interactions
        }
        
        // Extract data
        const extractedData = {};
        for (const selector of extractionSelectors) {
          try {
            const elements = await page.$$(selector);
            extractedData[selector] = await Promise.all(
              elements.map(async el => ({
                text: await el.textContent(),
                html: await el.innerHTML(),
                attributes: await el.evaluate(node => {
                  const attrs = {};
                  for (const attr of node.attributes) {
                    attrs[attr.name] = attr.value;
                  }
                  return attrs;
                })
              }))
            );
          } catch (error) {
            extractedData[selector] = { error: error.message };
          }
        }
        
        return {
          success: true,
          currentUrl: page.url(),
          extractedData
        };
      }
    `

    const functionOptions: FunctionOptions = {
      code,
      context: { url, interactions, extractionSelectors }
    }

    return this.client.function(functionOptions, launchOptions, requestOptions)
  }

  /**
   * Get available automation tasks
   */
  public getTasks(): Record<string, AutomationTask> {
    return AutomationService.TASKS
  }
}