// Scraping Service
// Specialized service for web scraping with advanced data extraction features

import { BrowserlessClient } from '../client'
import {
  BrowserlessConfig,
  BrowserlessResponse,
  ScrapeOptions,
  ScrapeResponse,
  ScrapeElement,
  ContentOptions,
  ContentResponse,
  LaunchOptions,
  RequestOptions
} from '../types'

export interface ScrapingTemplate {
  name: string
  description: string
  elements: ScrapeElement[]
  waitConditions?: {
    selector?: string
    timeout?: number
    function?: string
  }
  preprocessing?: {
    removeElements?: string[]
    addStyles?: string[]
  }
}

export interface BulkScrapeOptions {
  urls: string[]
  elements: ScrapeElement[]
  options?: Partial<ScrapeOptions>
  launchOptions?: LaunchOptions
  requestOptions?: RequestOptions
  concurrency?: number
  delay?: number
}

export interface BulkScrapeResult {
  url: string
  success: boolean
  data?: any
  error?: string
}

export interface DataExtractionRule {
  name: string
  selector: string
  attribute?: string
  transform?: (value: string) => any
  required?: boolean
}

export interface StructuredDataOptions {
  rules: DataExtractionRule[]
  url: string
  waitForContent?: boolean
  cleanData?: boolean
}

/**
 * Specialized service for web scraping with advanced data extraction features
 */
export class ScrapingService {
  private client: BrowserlessClient

  // Predefined scraping templates for common use cases
  public static readonly TEMPLATES: Record<string, ScrapingTemplate> = {
    ecommerce: {
      name: 'E-commerce Product',
      description: 'Extract product information from e-commerce sites',
      elements: [
        { selector: 'h1, .product-title, [data-testid="product-title"]' },
        { selector: '.price, .product-price, [data-testid="price"]' },
        { selector: '.description, .product-description, [data-testid="description"]' },
        { selector: '.rating, .product-rating, [data-testid="rating"]' },
        { selector: '.availability, .stock-status, [data-testid="availability"]' },
        { selector: 'img.product-image, .product-gallery img', attribute: 'src' }
      ],
      waitConditions: {
        selector: '.price, .product-price',
        timeout: 10000
      }
    },
    news: {
      name: 'News Article',
      description: 'Extract article content from news websites',
      elements: [
        { selector: 'h1, .headline, .article-title' },
        { selector: '.author, .byline, [data-testid="author"]' },
        { selector: '.publish-date, .article-date, time' },
        { selector: '.article-content, .story-body, .post-content' },
        { selector: '.tags, .categories, .article-tags' }
      ],
      preprocessing: {
        removeElements: ['.ad', '.advertisement', '.social-share', '.newsletter-signup']
      }
    },
    blog: {
      name: 'Blog Post',
      description: 'Extract blog post content and metadata',
      elements: [
        { selector: 'h1, .post-title, .entry-title' },
        { selector: '.author, .post-author' },
        { selector: '.post-date, .published-date' },
        { selector: '.post-content, .entry-content, .article-content' },
        { selector: '.tags, .post-tags' },
        { selector: '.comments-count' }
      ]
    },
    social: {
      name: 'Social Media Post',
      description: 'Extract social media post content',
      elements: [
        { selector: '.post-text, .tweet-text, .post-content' },
        { selector: '.author, .username, .post-author' },
        { selector: '.timestamp, .post-date' },
        { selector: '.likes, .reactions' },
        { selector: '.shares, .retweets' },
        { selector: '.comments-count' }
      ]
    },
    directory: {
      name: 'Business Directory',
      description: 'Extract business information from directory listings',
      elements: [
        { selector: '.business-name, .company-name, h1' },
        { selector: '.address, .location' },
        { selector: '.phone, .telephone' },
        { selector: '.website, .url', attribute: 'href' },
        { selector: '.rating, .stars' },
        { selector: '.hours, .opening-hours' },
        { selector: '.description, .about' }
      ]
    },
    job: {
      name: 'Job Listing',
      description: 'Extract job posting information',
      elements: [
        { selector: '.job-title, .position-title, h1' },
        { selector: '.company, .employer' },
        { selector: '.location, .job-location' },
        { selector: '.salary, .compensation' },
        { selector: '.job-type, .employment-type' },
        { selector: '.description, .job-description' },
        { selector: '.requirements, .qualifications' },
        { selector: '.posted-date, .job-date' }
      ]
    }
  }

  constructor(config: BrowserlessConfig) {
    this.client = new BrowserlessClient(config)
  }

  /**
   * Scrape data using a predefined template
   */
  public async scrapeWithTemplate(
    url: string,
    templateName: keyof typeof ScrapingService.TEMPLATES,
    customOptions?: Partial<ScrapeOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<ScrapeResponse>> {
    const template = ScrapingService.TEMPLATES[templateName]
    if (!template) {
      throw new Error(`Unknown template: ${templateName}`)
    }

    let options: ScrapeOptions = {
      url,
      elements: template.elements,
      ...customOptions
    }

    // Apply preprocessing if defined
    if (template.preprocessing) {
      if (template.preprocessing.removeElements) {
        const removeScript = template.preprocessing.removeElements
          .map(selector => `
            document.querySelectorAll('${selector}').forEach(el => el.remove());
          `).join('\n')
        
        options.addScriptTag = [
          ...(options.addScriptTag || []),
          { content: removeScript }
        ]
      }

      if (template.preprocessing.addStyles) {
        options.addStyleTag = [
          ...(options.addStyleTag || []),
          ...template.preprocessing.addStyles.map(content => ({ content }))
        ]
      }
    }

    // Apply wait conditions
    if (template.waitConditions) {
      if (template.waitConditions.selector) {
        options.waitForSelector = {
          selector: template.waitConditions.selector,
          timeout: template.waitConditions.timeout || 10000
        }
      }
      if (template.waitConditions.function) {
        options.waitForFunction = {
          fn: template.waitConditions.function,
          timeout: template.waitConditions.timeout || 10000
        }
      }
    }

    return this.client.scrape(options, launchOptions)
  }

  /**
   * Basic scraping with custom elements
   */
  public async scrape(
    url: string,
    elements: ScrapeElement[],
    options?: Partial<ScrapeOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<ScrapeResponse>> {
    const scrapeOptions: ScrapeOptions = {
      url,
      elements,
      ...options
    }

    return this.client.scrape(scrapeOptions, launchOptions)
  }

  /**
   * Scrape structured data with transformation rules
   */
  public async scrapeStructuredData(
    options: StructuredDataOptions,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<Record<string, any>>> {
    const { rules, url, waitForContent, cleanData } = options

    // Convert rules to scrape elements
    const elements: ScrapeElement[] = rules.map(rule => ({
      selector: rule.selector,
      attribute: rule.attribute
    }))

    const scrapeOptions: ScrapeOptions = {
      url,
      elements,
      ...(waitForContent && {
        waitForSelector: {
          selector: rules[0]?.selector || 'body',
          timeout: 10000
        }
      })
    }

    const response = await this.client.scrape(scrapeOptions, launchOptions)

    if (response.success && response.data) {
      // Transform the scraped data according to rules
      const structuredData: Record<string, any> = {}

      rules.forEach((rule, index) => {
        const scrapeResult = response.data!.data[index]
        if (scrapeResult && scrapeResult.results.length > 0) {
          let value = rule.attribute 
            ? scrapeResult.results[0].attributes.find(attr => attr.name === rule.attribute)?.value
            : scrapeResult.results[0].text

          if (value) {
            // Clean data if requested
            if (cleanData) {
              value = this.cleanText(value)
            }

            // Apply transformation if provided
            if (rule.transform) {
              try {
                value = rule.transform(value)
              } catch (error) {
                console.warn(`Transformation failed for rule ${rule.name}:`, error)
              }
            }

            structuredData[rule.name] = value
          } else if (rule.required) {
            structuredData[rule.name] = null
          }
        } else if (rule.required) {
          structuredData[rule.name] = null
        }
      })

      return {
        ...response,
        data: structuredData
      }
    }

    return response as BrowserlessResponse<Record<string, any>>
  }

  /**
   * Bulk scraping for multiple URLs
   */
  public async scrapeBulk(
    bulkOptions: BulkScrapeOptions
  ): Promise<BulkScrapeResult[]> {
    const {
      urls,
      elements,
      options = {},
      launchOptions = {},
      requestOptions = {},
      concurrency = 3,
      delay = 1000
    } = bulkOptions

    const results: BulkScrapeResult[] = []
    const chunks = this.chunkArray(urls, concurrency)

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (url) => {
        try {
          const scrapeOptions: ScrapeOptions = {
            url,
            elements,
            ...options
          }

          const response = await this.client.scrape(
            scrapeOptions,
            launchOptions,
            requestOptions
          )

          if (response.success && response.data) {
            return {
              url,
              success: true,
              data: response.data
            }
          } else {
            return {
              url,
              success: false,
              error: response.error?.message || 'Unknown error'
            }
          }
        } catch (error) {
          return {
            url,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      })

      const chunkResults = await Promise.all(chunkPromises)
      results.push(...chunkResults)

      // Add delay between chunks
      if (delay > 0 && chunks.indexOf(chunk) < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }

    return results
  }

  /**
   * Get full page content (HTML)
   */
  public async getPageContent(
    url: string,
    options?: Partial<ContentOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<ContentResponse>> {
    const contentOptions: ContentOptions = {
      url,
      ...options
    }

    return this.client.content(contentOptions, launchOptions)
  }

  /**
   * Scrape with pagination support
   */
  public async scrapeWithPagination(
    url: string,
    elements: ScrapeElement[],
    paginationSelector: string,
    maxPages: number = 10,
    options?: Partial<ScrapeOptions>,
    launchOptions?: LaunchOptions
  ): Promise<BrowserlessResponse<ScrapeResponse[]>> {
    const allResults: ScrapeResponse[] = []
    let currentUrl = url
    let pageCount = 0

    while (pageCount < maxPages) {
      try {
        const scrapeOptions: ScrapeOptions = {
          url: currentUrl,
          elements,
          ...options
        }

        const response = await this.client.scrape(scrapeOptions, launchOptions)

        if (response.success && response.data) {
          allResults.push(response.data)
          pageCount++

          // Try to find next page link
          const nextPageResponse = await this.client.scrape({
            url: currentUrl,
            elements: [{ selector: paginationSelector, attribute: 'href' }]
          }, launchOptions)

          if (nextPageResponse.success && 
              nextPageResponse.data?.data[0]?.results.length > 0) {
            const nextPageUrl = nextPageResponse.data.data[0].results[0].attributes
              .find(attr => attr.name === 'href')?.value

            if (nextPageUrl && nextPageUrl !== currentUrl) {
              currentUrl = new URL(nextPageUrl, currentUrl).href
            } else {
              break // No more pages
            }
          } else {
            break // No pagination link found
          }
        } else {
          break // Scraping failed
        }
      } catch (error) {
        break // Error occurred
      }
    }

    return {
      success: true,
      data: allResults,
      status: 200
    }
  }

  /**
   * Get available templates
   */
  public getTemplates(): Record<string, ScrapingTemplate> {
    return ScrapingService.TEMPLATES
  }

  /**
   * Clean extracted text
   */
  private cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/\n\s*\n/g, '\n') // Remove empty lines
      .trim()
  }

  /**
   * Utility method to chunk array for batch processing
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }
}