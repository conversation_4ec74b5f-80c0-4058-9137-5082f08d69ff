// Browserless Service Utilities
// Helper functions and utilities for the Browserless service

import { BrowserlessConfig, BrowserlessRegion, LaunchOptions, RequestOptions } from './types'

// Regional endpoints mapping
export const BROWSERLESS_ENDPOINTS: Record<BrowserlessRegion, string> = {
  sfo: 'https://production-sfo.browserless.io',
  lon: 'https://production-lon.browserless.io',
  ams: 'https://production-ams.browserless.io'
}

/**
 * Build the base URL for Browserless API requests
 */
export function buildBaseUrl(config: BrowserlessConfig): string {
  if (config.baseUrl) {
    return config.baseUrl
  }

  const region = (config.region || 'sfo') as BrowserlessRegion
  return BROWSERLESS_ENDPOINTS[region] || BROWSERLESS_ENDPOINTS.sfo
}

/**
 * Build query parameters for API requests
 */
export function buildQueryParams(
  apiToken: string,
  launchOptions?: LaunchOptions,
  requestOptions?: RequestOptions
): URLSearchParams {
  const params = new URLSearchParams()
  
  // Add API token
  params.append('token', apiToken)
  
  // Add launch options as query parameters
  const options = { ...launchOptions, ...requestOptions }
  
  Object.entries(options).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (key === 'launch' && typeof value === 'object') {
        // Convert launch object to JSON string
        params.append(key, JSON.stringify(value))
      } else {
        params.append(key, String(value))
      }
    }
  })
  
  return params
}

/**
 * Build the complete URL for API requests
 */
export function buildApiUrl(
  baseUrl: string,
  endpoint: string,
  apiToken: string,
  launchOptions?: LaunchOptions,
  requestOptions?: RequestOptions
): string {
  const url = new URL(`${baseUrl}/${endpoint}`)
  const params = buildQueryParams(apiToken, launchOptions, requestOptions)
  
  params.forEach((value, key) => {
    url.searchParams.append(key, value)
  })
  
  return url.toString()
}

/**
 * Validate required options for different API endpoints
 */
export function validateOptions(endpoint: string, options: any): void {
  switch (endpoint) {
    case 'screenshot':
    case 'pdf':
    case 'content':
      if (!options.url && !options.html) {
        throw new Error(`${endpoint} API requires either 'url' or 'html' parameter`)
      }
      if (options.url && options.html) {
        throw new Error(`${endpoint} API cannot have both 'url' and 'html' parameters`)
      }
      break
    
    case 'scrape':
      if (!options.url && !options.html) {
        throw new Error('Scrape API requires either \'url\' or \'html\' parameter')
      }
      if (!options.elements || !Array.isArray(options.elements) || options.elements.length === 0) {
        throw new Error('Scrape API requires \'elements\' array with at least one element')
      }
      break
    
    case 'function':
      if (!options.code) {
        throw new Error('Function API requires \'code\' parameter')
      }
      break
  }
}

/**
 * Sanitize and prepare request body
 */
export function prepareRequestBody(options: any): string {
  // Remove undefined values and clean up the object
  const cleanOptions = JSON.parse(JSON.stringify(options))
  return JSON.stringify(cleanOptions)
}

/**
 * Get appropriate headers for API requests
 */
export function getRequestHeaders(customHeaders?: Record<string, string>): Record<string, string> {
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'Cache-Control': 'no-cache',
    'User-Agent': 'Browserless-Client/1.0.0'
  }
  
  return { ...defaultHeaders, ...customHeaders }
}

/**
 * Determine content type from response headers
 */
export function getContentType(headers: Headers): string {
  return headers.get('content-type') || 'application/octet-stream'
}

/**
 * Check if response is binary content
 */
export function isBinaryContent(contentType: string): boolean {
  const binaryTypes = [
    'application/pdf',
    'image/',
    'application/zip',
    'application/octet-stream'
  ]
  
  return binaryTypes.some(type => contentType.includes(type))
}

/**
 * Parse error response from Browserless API
 */
export async function parseErrorResponse(response: Response): Promise<string> {
  try {
    const contentType = response.headers.get('content-type') || ''
    
    if (contentType.includes('application/json')) {
      const errorData = await response.json()
      return errorData.message || errorData.error || 'Unknown API error'
    } else {
      const errorText = await response.text()
      return errorText || `HTTP ${response.status}: ${response.statusText}`
    }
  } catch {
    return `HTTP ${response.status}: ${response.statusText}`
  }
}

/**
 * Retry logic for failed requests
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      
      // Don't retry on client errors (4xx)
      if (error instanceof Error && 'status' in error) {
        const status = (error as any).status
        if (status >= 400 && status < 500) {
          throw error
        }
      }
      
      if (attempt === maxRetries) {
        break
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt))
    }
  }
  
  throw lastError!
}

/**
 * Create a timeout promise for request cancellation
 */
export function createTimeoutPromise(timeoutMs: number): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Request timeout after ${timeoutMs}ms`))
    }, timeoutMs)
  })
}

/**
 * Validate API token format
 */
export function validateApiToken(token: string): boolean {
  // Browserless tokens are typically UUIDs
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
  return uuidRegex.test(token) || token.length > 10 // Allow for different token formats
}

/**
 * Format file size for logging
 */
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`
}

/**
 * Generate filename from URL or content type
 */
export function generateFilename(url?: string, contentType?: string, extension?: string): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  
  if (extension) {
    return `browserless-${timestamp}.${extension}`
  }
  
  if (contentType) {
    const ext = getExtensionFromContentType(contentType)
    return `browserless-${timestamp}.${ext}`
  }
  
  if (url) {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname
      const filename = pathname.split('/').pop() || 'page'
      return `${filename}-${timestamp}`
    } catch {
      // Invalid URL, use default
    }
  }
  
  return `browserless-${timestamp}`
}

/**
 * Get file extension from content type
 */
export function getExtensionFromContentType(contentType: string): string {
  const typeMap: Record<string, string> = {
    'application/pdf': 'pdf',
    'image/png': 'png',
    'image/jpeg': 'jpg',
    'image/webp': 'webp',
    'text/html': 'html',
    'application/json': 'json',
    'application/zip': 'zip',
    'text/plain': 'txt'
  }
  
  for (const [type, ext] of Object.entries(typeMap)) {
    if (contentType.includes(type)) {
      return ext
    }
  }
  
  return 'bin'
}

/**
 * Deep merge objects (for configuration merging)
 */
export function deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  const result = { ...target }
  
  for (const key in source) {
    if (source[key] !== undefined) {
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key] || ({} as T[Extract<keyof T, string>]), source[key] as any)
      } else {
        result[key] = source[key] as any
      }
    }
  }
  
  return result
}