'use client'

import { Element } from '@craftjs/core'

// Page template definitions following page builder patterns
export const pageTemplates = {
  // Landing Page Template
  landing: {
    id: 'landing',
    name: 'Landing Page',
    description: 'High-converting landing page with hero, features, and CTA',
    category: 'marketing',
    preview: '/templates/landing-preview.jpg',
    structure: {
      type: 'CraftContainer',
      props: {
        padding: 0,
        margin: 0,
        background: '#ffffff',
        minHeight: '100vh'
      },
      nodes: [
        // Header Section
        {
          id: 'header',
          type: 'CraftSection',
          props: {
            padding: 20,
            background: '#ffffff',
            className: 'border-b'
          },
          nodes: [
            {
              type: 'CraftContainer',
              props: { padding: 16 },
              nodes: [
                {
                  type: 'CraftHeading',
                  props: {
                    text: 'Your Brand',
                    level: 1,
                    fontSize: 24,
                    fontWeight: 'bold'
                  }
                }
              ]
            }
          ]
        },
        // Hero Section
        {
          id: 'hero',
          type: 'CraftSection',
          props: {
            padding: 80,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            minHeight: 500
          },
          nodes: [
            {
              type: 'CraftContainer',
              props: { padding: 40 },
              nodes: [
                {
                  type: 'CraftHeading',
                  props: {
                    text: 'Transform Your Business Today',
                    level: 1,
                    fontSize: 48,
                    fontWeight: 'bold',
                    color: '#ffffff',
                    textAlign: 'center'
                  }
                },
                {
                  type: 'CraftText',
                  props: {
                    text: 'Discover the power of our innovative solution that helps businesses grow faster and more efficiently.',
                    fontSize: 18,
                    color: '#f0f0f0',
                    textAlign: 'center'
                  }
                },
                {
                  type: 'CraftButton',
                  props: {
                    text: 'Get Started Now',
                    variant: 'default',
                    size: 'lg'
                  }
                }
              ]
            }
          ]
        },
        // Features Section
        {
          id: 'features',
          type: 'CraftSection',
          props: {
            padding: 80,
            background: '#f8f9fa'
          },
          nodes: [
            {
              type: 'CraftContainer',
              props: { padding: 40 },
              nodes: [
                {
                  type: 'CraftHeading',
                  props: {
                    text: 'Why Choose Us',
                    level: 2,
                    fontSize: 36,
                    fontWeight: 'bold',
                    textAlign: 'center'
                  }
                },
                // Feature cards would go here
              ]
            }
          ]
        }
      ]
    }
  },

  // Business Website Template
  business: {
    id: 'business',
    name: 'Business Website',
    description: 'Professional business website with services and contact',
    category: 'business',
    preview: '/templates/business-preview.jpg',
    structure: {
      type: 'CraftContainer',
      props: {
        padding: 0,
        margin: 0,
        background: '#ffffff'
      },
      nodes: [
        // Navigation
        {
          id: 'navigation',
          type: 'CraftSection',
          props: {
            padding: 16,
            background: '#ffffff',
            className: 'border-b sticky top-0 z-50'
          }
        },
        // About Section
        {
          id: 'about',
          type: 'CraftSection',
          props: {
            padding: 60,
            background: '#ffffff'
          }
        },
        // Services Section
        {
          id: 'services',
          type: 'CraftSection',
          props: {
            padding: 60,
            background: '#f8f9fa'
          }
        },
        // Contact Section
        {
          id: 'contact',
          type: 'CraftSection',
          props: {
            padding: 60,
            background: '#ffffff'
          }
        }
      ]
    }
  },

  // Portfolio Template
  portfolio: {
    id: 'portfolio',
    name: 'Portfolio',
    description: 'Creative portfolio to showcase your work',
    category: 'creative',
    preview: '/templates/portfolio-preview.jpg',
    structure: {
      type: 'CraftContainer',
      props: {
        padding: 0,
        margin: 0,
        background: '#000000'
      },
      nodes: [
        // Hero Section
        {
          id: 'hero',
          type: 'CraftSection',
          props: {
            padding: 100,
            background: '#000000',
            minHeight: '100vh'
          }
        },
        // Work Section
        {
          id: 'work',
          type: 'CraftSection',
          props: {
            padding: 80,
            background: '#ffffff'
          }
        },
        // About Section
        {
          id: 'about',
          type: 'CraftSection',
          props: {
            padding: 80,
            background: '#f8f9fa'
          }
        }
      ]
    }
  },

  // E-commerce Template
  ecommerce: {
    id: 'ecommerce',
    name: 'E-commerce Store',
    description: 'Online store with product showcase and shopping features',
    category: 'ecommerce',
    preview: '/templates/ecommerce-preview.jpg',
    structure: {
      type: 'CraftContainer',
      props: {
        padding: 0,
        margin: 0,
        background: '#ffffff'
      },
      nodes: [
        // Header with Cart
        {
          id: 'header',
          type: 'CraftSection',
          props: {
            padding: 16,
            background: '#ffffff',
            className: 'border-b'
          }
        },
        // Product Hero
        {
          id: 'product-hero',
          type: 'CraftSection',
          props: {
            padding: 40,
            background: '#f8f9fa'
          }
        },
        // Featured Products
        {
          id: 'featured-products',
          type: 'CraftSection',
          props: {
            padding: 60,
            background: '#ffffff'
          }
        }
      ]
    }
  },

  // Blog Template
  blog: {
    id: 'blog',
    name: 'Blog',
    description: 'Content-focused blog layout with articles and sidebar',
    category: 'content',
    preview: '/templates/blog-preview.jpg',
    structure: {
      type: 'CraftContainer',
      props: {
        padding: 0,
        margin: 0,
        background: '#ffffff'
      },
      nodes: [
        // Header
        {
          id: 'header',
          type: 'CraftSection',
          props: {
            padding: 20,
            background: '#ffffff',
            className: 'border-b'
          }
        },
        // Main Content Area
        {
          id: 'main-content',
          type: 'CraftSection',
          props: {
            padding: 40,
            background: '#ffffff'
          }
        },
        // Sidebar
        {
          id: 'sidebar',
          type: 'CraftSection',
          props: {
            padding: 40,
            background: '#f8f9fa'
          }
        }
      ]
    }
  }
}

// Template categories for organization
export const templateCategories = {
  marketing: {
    name: 'Marketing',
    description: 'Landing pages and promotional sites',
    icon: 'TrendingUp'
  },
  business: {
    name: 'Business',
    description: 'Professional business websites',
    icon: 'Building'
  },
  creative: {
    name: 'Creative',
    description: 'Portfolios and creative showcases',
    icon: 'Palette'
  },
  ecommerce: {
    name: 'E-commerce',
    description: 'Online stores and product catalogs',
    icon: 'ShoppingCart'
  },
  content: {
    name: 'Content',
    description: 'Blogs and content-focused sites',
    icon: 'FileText'
  }
}

// Helper functions for template management
export const templateHelpers = {
  /**
   * Get templates by category
   */
  getByCategory: (category: string) => {
    return Object.values(pageTemplates).filter(template => template.category === category)
  },

  /**
   * Get template by ID
   */
  getById: (id: string) => {
    return pageTemplates[id as keyof typeof pageTemplates]
  },

  /**
   * Get all template categories
   */
  getCategories: () => {
    return Object.entries(templateCategories).map(([key, value]) => ({
      id: key,
      ...value
    }))
  },

  /**
   * Convert template structure to Craft.js nodes
   */
  templateToNodes: (template: any) => {
    // This would convert the template structure to actual Craft.js node tree
    // Implementation depends on how you want to handle the conversion
    return template.structure
  },

  /**
   * Apply template to editor
   */
  applyTemplate: (templateId: string, editorActions: any) => {
    const template = templateHelpers.getById(templateId)
    if (!template) return false

    try {
      // Clear existing content
      editorActions.clearEvents()
      
      // Apply template structure
      const nodes = templateHelpers.templateToNodes(template)
      editorActions.deserialize(JSON.stringify({
        ROOT: {
          type: { resolvedName: 'CraftContainer' },
          isCanvas: true,
          props: {},
          displayName: 'Container',
          custom: {},
          hidden: false,
          nodes: [nodes],
          linkedNodes: {}
        },
        ...nodes
      }))

      return true
    } catch (error) {
      console.error('Failed to apply template:', error)
      return false
    }
  }
}
