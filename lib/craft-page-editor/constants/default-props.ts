'use client'

import { CRAFT_COMPONENT_TYPES } from './component-types'

// Default props for each component type
export const CRAFT_DEFAULT_PROPS = {
  [CRAFT_COMPONENT_TYPES.CONTAINER]: {
    padding: 16,
    margin: 0,
    background: 'transparent',
    borderRadius: 0,
    border: 'none',
    minHeight: 50,
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.SECTION]: {
    padding: 24,
    margin: 0,
    background: 'transparent',
    borderRadius: 0,
    border: 'none',
    minHeight: 100,
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.GRID]: {
    columns: 2,
    gap: 16,
    padding: 16,
    margin: 0,
    background: 'transparent',
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.COLUMN]: {
    span: 1,
    padding: 8,
    margin: 0,
    background: 'transparent',
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.CARD]: {
    padding: 24,
    margin: 0,
    background: '#ffffff',
    borderRadius: 8,
    border: '1px solid #e2e8f0',
    shadow: 'sm',
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.TEXT]: {
    text: 'Edit this text',
    fontSize: 16,
    fontWeight: 'normal',
    color: '#000000',
    textAlign: 'left',
    lineHeight: 1.5,
    margin: 0,
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.HEADING]: {
    text: 'Heading',
    level: 2,
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'left',
    margin: 0,
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.BUTTON]: {
    text: 'Button',
    variant: 'default',
    size: 'default',
    disabled: false,
    fullWidth: false,
    onClick: () => {},
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.IMAGE]: {
    src: '/placeholder.svg',
    alt: 'Image',
    width: 200,
    height: 150,
    objectFit: 'cover',
    borderRadius: 0,
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.INPUT]: {
    placeholder: 'Enter text...',
    type: 'text',
    value: '',
    disabled: false,
    required: false,
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.ALERT]: {
    title: 'Alert',
    description: 'This is an alert message',
    variant: 'default',
    dismissible: false,
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.BADGE]: {
    text: 'Badge',
    variant: 'default',
    size: 'default',
    className: ''
  },

  [CRAFT_COMPONENT_TYPES.DIVIDER]: {
    orientation: 'horizontal',
    thickness: 1,
    color: '#e2e8f0',
    margin: 16,
    className: ''
  }
} as const

// Common style properties that can be applied to most components
export const CRAFT_COMMON_STYLE_PROPS = {
  margin: 0,
  padding: 0,
  background: 'transparent',
  borderRadius: 0,
  border: 'none',
  opacity: 1,
  transform: 'none',
  transition: 'none'
} as const

// Responsive breakpoints for component sizing
export const CRAFT_BREAKPOINTS = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
} as const

// Animation presets
export const CRAFT_ANIMATIONS = {
  none: 'none',
  fadeIn: 'fadeIn 0.3s ease-in-out',
  slideIn: 'slideIn 0.3s ease-in-out',
  scaleIn: 'scaleIn 0.3s ease-in-out',
  bounceIn: 'bounceIn 0.5s ease-in-out'
} as const

// Shadow presets
export const CRAFT_SHADOWS = {
  none: 'none',
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)'
} as const

// Border radius presets
export const CRAFT_BORDER_RADIUS = {
  none: '0',
  sm: '0.125rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  full: '9999px'
} as const

export type CraftDefaultProps = typeof CRAFT_DEFAULT_PROPS
export type CraftComponentDefaultProps<T extends keyof CraftDefaultProps> = CraftDefaultProps[T]
