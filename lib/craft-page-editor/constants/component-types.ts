'use client'

// Component type constants for Craft.js
export const CRAFT_COMPONENT_TYPES = {
  // Layout Components
  CONTAINER: 'CraftContainer',
  SECTION: 'CraftSection',
  GRID: 'CraftGrid',
  COLUMN: 'CraftColumn',
  CARD: 'CraftCard',
  
  // Content Components
  TEXT: 'CraftText',
  HEADING: 'CraftHeading',
  BUTTON: 'CraftButton',
  IMAGE: 'CraftImage',
  DIVIDER: 'CraftDivider',
  
  // Form Components
  INPUT: 'CraftInput',
  TEXTAREA: 'CraftTextarea',
  SELECT: 'CraftSelect',
  CHECKBOX: 'CraftCheckbox',
  RADIO: 'CraftRadio',
  
  // Feedback Components
  ALERT: 'CraftAlert',
  BADGE: 'CraftBadge',
  TOAST: 'CraftToast',
  
  // Navigation Components
  LINK: 'CraftLink',
  BREADCRUMB: 'CraftBreadcrumb',
  TABS: 'CraftTabs',
  
  // Media Components
  VIDEO: 'CraftVideo',
  AUDIO: 'CraftAudio',
  GALLERY: 'CraftGallery',
  
  // Data Components
  TABLE: 'CraftTable',
  LIST: 'CraftList',
  CHART: 'CraftChart'
} as const

// Component categories for toolbox organization
export const CRAFT_COMPONENT_CATEGORIES = {
  LAYOUT: 'layout',
  CONTENT: 'content',
  FORM: 'form',
  MEDIA: 'media',
  NAVIGATION: 'navigation',
  FEEDBACK: 'feedback',
  DATA: 'data',
  CUSTOM: 'custom'
} as const

// Canvas component types (droppable regions)
export const CRAFT_CANVAS_TYPES = [
  CRAFT_COMPONENT_TYPES.CONTAINER,
  CRAFT_COMPONENT_TYPES.SECTION,
  CRAFT_COMPONENT_TYPES.GRID,
  CRAFT_COMPONENT_TYPES.COLUMN,
  CRAFT_COMPONENT_TYPES.CARD
] as const

// Draggable component types
export const CRAFT_DRAGGABLE_TYPES = [
  CRAFT_COMPONENT_TYPES.TEXT,
  CRAFT_COMPONENT_TYPES.HEADING,
  CRAFT_COMPONENT_TYPES.BUTTON,
  CRAFT_COMPONENT_TYPES.IMAGE,
  CRAFT_COMPONENT_TYPES.INPUT,
  CRAFT_COMPONENT_TYPES.ALERT,
  CRAFT_COMPONENT_TYPES.BADGE,
  CRAFT_COMPONENT_TYPES.DIVIDER
] as const

// Component display names
export const CRAFT_COMPONENT_DISPLAY_NAMES = {
  [CRAFT_COMPONENT_TYPES.CONTAINER]: 'Container',
  [CRAFT_COMPONENT_TYPES.SECTION]: 'Section',
  [CRAFT_COMPONENT_TYPES.GRID]: 'Grid',
  [CRAFT_COMPONENT_TYPES.COLUMN]: 'Column',
  [CRAFT_COMPONENT_TYPES.CARD]: 'Card',
  [CRAFT_COMPONENT_TYPES.TEXT]: 'Text',
  [CRAFT_COMPONENT_TYPES.HEADING]: 'Heading',
  [CRAFT_COMPONENT_TYPES.BUTTON]: 'Button',
  [CRAFT_COMPONENT_TYPES.IMAGE]: 'Image',
  [CRAFT_COMPONENT_TYPES.INPUT]: 'Input',
  [CRAFT_COMPONENT_TYPES.ALERT]: 'Alert',
  [CRAFT_COMPONENT_TYPES.BADGE]: 'Badge',
  [CRAFT_COMPONENT_TYPES.DIVIDER]: 'Divider'
} as const

// Component icons (using Lucide React icons)
export const CRAFT_COMPONENT_ICONS = {
  [CRAFT_COMPONENT_TYPES.CONTAINER]: 'Box',
  [CRAFT_COMPONENT_TYPES.SECTION]: 'Layout',
  [CRAFT_COMPONENT_TYPES.GRID]: 'Grid3X3',
  [CRAFT_COMPONENT_TYPES.COLUMN]: 'Columns',
  [CRAFT_COMPONENT_TYPES.CARD]: 'RectangleHorizontal',
  [CRAFT_COMPONENT_TYPES.TEXT]: 'Type',
  [CRAFT_COMPONENT_TYPES.HEADING]: 'Heading',
  [CRAFT_COMPONENT_TYPES.BUTTON]: 'MousePointer',
  [CRAFT_COMPONENT_TYPES.IMAGE]: 'Image',
  [CRAFT_COMPONENT_TYPES.INPUT]: 'Input',
  [CRAFT_COMPONENT_TYPES.ALERT]: 'AlertTriangle',
  [CRAFT_COMPONENT_TYPES.BADGE]: 'Tag',
  [CRAFT_COMPONENT_TYPES.DIVIDER]: 'Minus'
} as const

export type CraftComponentType = typeof CRAFT_COMPONENT_TYPES[keyof typeof CRAFT_COMPONENT_TYPES]
export type CraftComponentCategory = typeof CRAFT_COMPONENT_CATEGORIES[keyof typeof CRAFT_COMPONENT_CATEGORIES]
