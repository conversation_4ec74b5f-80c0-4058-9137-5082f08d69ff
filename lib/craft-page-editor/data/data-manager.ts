'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// Data source types
export type DataSourceType = 
  | 'api' 
  | 'graphql' 
  | 'database' 
  | 'cms' 
  | 'static' 
  | 'realtime'
  | 'webhook'

export interface DataSource {
  id: string
  name: string
  type: DataSourceType
  config: {
    url?: string
    headers?: Record<string, string>
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
    body?: any
    query?: string // For GraphQL
    table?: string // For database
    collection?: string // For CMS
    apiKey?: string
    authentication?: {
      type: 'bearer' | 'basic' | 'apikey' | 'oauth'
      token?: string
      username?: string
      password?: string
      key?: string
    }
    polling?: {
      enabled: boolean
      interval: number // in milliseconds
    }
    cache?: {
      enabled: boolean
      ttl: number // in seconds
    }
    transform?: string // JavaScript function to transform data
    validation?: {
      schema?: any
      required?: string[]
    }
  }
  isActive: boolean
  lastFetch?: Date
  lastError?: string
  createdAt: Date
  updatedAt: Date
}

export interface DataBinding {
  id: string
  componentId: string
  componentProp: string
  dataSourceId: string
  dataPath: string // JSONPath or dot notation
  transform?: string // JavaScript function to transform value
  fallback?: any
  conditions?: Array<{
    field: string
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'exists'
    value: any
  }>
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

export interface DataCache {
  [sourceId: string]: {
    data: any
    timestamp: Date
    ttl: number
  }
}

interface DataState {
  // Data sources
  dataSources: DataSource[]
  
  // Data bindings
  dataBindings: DataBinding[]
  
  // Cached data
  cache: DataCache
  
  // Loading states
  loadingStates: Record<string, boolean>
  
  // Error states
  errors: Record<string, string>
  
  // Actions
  addDataSource: (source: Omit<DataSource, 'id' | 'createdAt' | 'updatedAt'>) => string
  updateDataSource: (id: string, updates: Partial<DataSource>) => void
  deleteDataSource: (id: string) => void
  testDataSource: (id: string) => Promise<boolean>
  
  addDataBinding: (binding: Omit<DataBinding, 'id' | 'createdAt' | 'updatedAt'>) => string
  updateDataBinding: (id: string, updates: Partial<DataBinding>) => void
  deleteDataBinding: (id: string) => void
  
  fetchData: (sourceId: string, force?: boolean) => Promise<any>
  refreshAllData: () => Promise<void>
  clearCache: (sourceId?: string) => void
  
  getComponentData: (componentId: string) => Record<string, any>
  getDataForBinding: (bindingId: string) => any
  
  // Real-time subscriptions
  subscriptions: Map<string, any>
  subscribe: (sourceId: string) => void
  unsubscribe: (sourceId: string) => void
}

export const useDataManager = create<DataState>()(
  persist(
    (set, get) => ({
      dataSources: [],
      dataBindings: [],
      cache: {},
      loadingStates: {},
      errors: {},
      subscriptions: new Map(),

      addDataSource: (sourceData) => {
        const source: DataSource = {
          ...sourceData,
          id: `ds_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          createdAt: new Date(),
          updatedAt: new Date()
        }
        set((state) => ({
          dataSources: [...state.dataSources, source]
        }))
        return source.id
      },

      updateDataSource: (id, updates) => {
        set((state) => ({
          dataSources: state.dataSources.map(source =>
            source.id === id 
              ? { ...source, ...updates, updatedAt: new Date() }
              : source
          )
        }))
      },

      deleteDataSource: (id) => {
        // Clean up related bindings and cache
        set((state) => ({
          dataSources: state.dataSources.filter(source => source.id !== id),
          dataBindings: state.dataBindings.filter(binding => binding.dataSourceId !== id),
          cache: Object.fromEntries(
            Object.entries(state.cache).filter(([sourceId]) => sourceId !== id)
          )
        }))
        
        // Unsubscribe if it's a real-time source
        get().unsubscribe(id)
      },

      testDataSource: async (id) => {
        const source = get().dataSources.find(s => s.id === id)
        if (!source) return false

        try {
          set((state) => ({
            loadingStates: { ...state.loadingStates, [id]: true },
            errors: { ...state.errors, [id]: '' }
          }))

          const data = await dataFetcher.fetchFromSource(source)
          
          set((state) => ({
            loadingStates: { ...state.loadingStates, [id]: false }
          }))
          
          return !!data
        } catch (error) {
          set((state) => ({
            loadingStates: { ...state.loadingStates, [id]: false },
            errors: { 
              ...state.errors, 
              [id]: error instanceof Error ? error.message : 'Unknown error'
            }
          }))
          return false
        }
      },

      addDataBinding: (bindingData) => {
        const binding: DataBinding = {
          ...bindingData,
          id: `db_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          createdAt: new Date(),
          updatedAt: new Date()
        }
        set((state) => ({
          dataBindings: [...state.dataBindings, binding]
        }))
        return binding.id
      },

      updateDataBinding: (id, updates) => {
        set((state) => ({
          dataBindings: state.dataBindings.map(binding =>
            binding.id === id 
              ? { ...binding, ...updates, updatedAt: new Date() }
              : binding
          )
        }))
      },

      deleteDataBinding: (id) => {
        set((state) => ({
          dataBindings: state.dataBindings.filter(binding => binding.id !== id)
        }))
      },

      fetchData: async (sourceId, force = false) => {
        const state = get()
        const source = state.dataSources.find(s => s.id === sourceId)
        if (!source || !source.isActive) return null

        // Check cache first
        const cached = state.cache[sourceId]
        if (!force && cached && source.config.cache?.enabled) {
          const isExpired = Date.now() - cached.timestamp.getTime() > cached.ttl * 1000
          if (!isExpired) {
            return cached.data
          }
        }

        try {
          set((state) => ({
            loadingStates: { ...state.loadingStates, [sourceId]: true },
            errors: { ...state.errors, [sourceId]: '' }
          }))

          const data = await dataFetcher.fetchFromSource(source)
          
          // Cache the data
          if (source.config.cache?.enabled) {
            set((state) => ({
              cache: {
                ...state.cache,
                [sourceId]: {
                  data,
                  timestamp: new Date(),
                  ttl: source.config.cache?.ttl || 300
                }
              }
            }))
          }

          // Update last fetch time
          get().updateDataSource(sourceId, { lastFetch: new Date() })

          set((state) => ({
            loadingStates: { ...state.loadingStates, [sourceId]: false }
          }))

          return data
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          
          set((state) => ({
            loadingStates: { ...state.loadingStates, [sourceId]: false },
            errors: { ...state.errors, [sourceId]: errorMessage }
          }))
          
          get().updateDataSource(sourceId, { lastError: errorMessage })
          
          throw error
        }
      },

      refreshAllData: async () => {
        const state = get()
        const activeSources = state.dataSources.filter(s => s.isActive)
        
        await Promise.allSettled(
          activeSources.map(source => get().fetchData(source.id, true))
        )
      },

      clearCache: (sourceId) => {
        if (sourceId) {
          set((state) => ({
            cache: Object.fromEntries(
              Object.entries(state.cache).filter(([id]) => id !== sourceId)
            )
          }))
        } else {
          set({ cache: {} })
        }
      },

      getComponentData: (componentId) => {
        const state = get()
        const bindings = state.dataBindings.filter(
          b => b.componentId === componentId && b.isActive
        )
        
        const componentData: Record<string, any> = {}
        
        bindings.forEach(binding => {
          const data = get().getDataForBinding(binding.id)
          if (data !== undefined) {
            componentData[binding.componentProp] = data
          }
        })
        
        return componentData
      },

      getDataForBinding: (bindingId) => {
        const state = get()
        const binding = state.dataBindings.find(b => b.id === bindingId)
        if (!binding || !binding.isActive) return undefined

        const cached = state.cache[binding.dataSourceId]
        if (!cached) return binding.fallback

        try {
          // Extract data using path
          let value = dataUtils.getValueByPath(cached.data, binding.dataPath)
          
          // Apply conditions
          if (binding.conditions && binding.conditions.length > 0) {
            const conditionsMet = binding.conditions.every(condition => 
              dataUtils.evaluateCondition(cached.data, condition)
            )
            if (!conditionsMet) {
              return binding.fallback
            }
          }
          
          // Apply transform
          if (binding.transform) {
            value = dataUtils.applyTransform(value, binding.transform)
          }
          
          return value !== undefined ? value : binding.fallback
        } catch (error) {
          console.error('Error getting data for binding:', error)
          return binding.fallback
        }
      },

      subscribe: (sourceId) => {
        const source = get().dataSources.find(s => s.id === sourceId)
        if (!source || source.type !== 'realtime') return

        // Implementation would depend on the real-time technology (WebSocket, SSE, etc.)
        // This is a placeholder for the subscription logic
        console.log('Subscribing to real-time source:', sourceId)
      },

      unsubscribe: (sourceId) => {
        const subscription = get().subscriptions.get(sourceId)
        if (subscription) {
          // Close the subscription
          if (typeof subscription.close === 'function') {
            subscription.close()
          }
          get().subscriptions.delete(sourceId)
        }
      }
    }),
    {
      name: 'craft-data-manager',
      version: 1,
      partialize: (state) => ({
        dataSources: state.dataSources,
        dataBindings: state.dataBindings
        // Don't persist cache, loading states, or errors
      })
    }
  )
)

// Data fetcher utility
export const dataFetcher = {
  async fetchFromSource(source: DataSource): Promise<any> {
    switch (source.type) {
      case 'api':
        return this.fetchFromAPI(source)
      case 'graphql':
        return this.fetchFromGraphQL(source)
      case 'static':
        return this.fetchStaticData(source)
      default:
        throw new Error(`Unsupported data source type: ${source.type}`)
    }
  },

  async fetchFromAPI(source: DataSource): Promise<any> {
    const { url, method = 'GET', headers = {}, body, authentication } = source.config
    
    if (!url) throw new Error('API URL is required')

    const requestHeaders = { ...headers }
    
    // Add authentication
    if (authentication) {
      switch (authentication.type) {
        case 'bearer':
          requestHeaders.Authorization = `Bearer ${authentication.token}`
          break
        case 'basic':
          const credentials = btoa(`${authentication.username}:${authentication.password}`)
          requestHeaders.Authorization = `Basic ${credentials}`
          break
        case 'apikey':
          requestHeaders[authentication.key || 'X-API-Key'] = authentication.token
          break
      }
    }

    const response = await fetch(url, {
      method,
      headers: requestHeaders,
      body: body ? JSON.stringify(body) : undefined
    })

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    
    // Apply transform if provided
    if (source.config.transform) {
      return dataUtils.applyTransform(data, source.config.transform)
    }
    
    return data
  },

  async fetchFromGraphQL(source: DataSource): Promise<any> {
    const { url, query, headers = {}, authentication } = source.config
    
    if (!url || !query) throw new Error('GraphQL URL and query are required')

    const requestHeaders = {
      'Content-Type': 'application/json',
      ...headers
    }
    
    if (authentication?.type === 'bearer') {
      requestHeaders.Authorization = `Bearer ${authentication.token}`
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: requestHeaders,
      body: JSON.stringify({ query })
    })

    if (!response.ok) {
      throw new Error(`GraphQL request failed: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.errors) {
      throw new Error(`GraphQL errors: ${result.errors.map((e: any) => e.message).join(', ')}`)
    }
    
    return result.data
  },

  async fetchStaticData(source: DataSource): Promise<any> {
    // For static data, return the data directly from config
    return source.config.body || {}
  }
}

// Data utilities
export const dataUtils = {
  /**
   * Get value from object using dot notation path
   */
  getValueByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  },

  /**
   * Set value in object using dot notation path
   */
  setValueByPath(obj: any, path: string, value: any): void {
    const keys = path.split('.')
    const lastKey = keys.pop()
    if (!lastKey) return

    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {}
      }
      return current[key]
    }, obj)

    target[lastKey] = value
  },

  /**
   * Evaluate condition against data
   */
  evaluateCondition(data: any, condition: DataBinding['conditions'][0]): boolean {
    const value = this.getValueByPath(data, condition.field)
    
    switch (condition.operator) {
      case 'equals':
        return value === condition.value
      case 'not_equals':
        return value !== condition.value
      case 'contains':
        return String(value).includes(String(condition.value))
      case 'greater_than':
        return Number(value) > Number(condition.value)
      case 'less_than':
        return Number(value) < Number(condition.value)
      case 'exists':
        return value !== undefined && value !== null
      default:
        return false
    }
  },

  /**
   * Apply JavaScript transform function to data
   */
  applyTransform(data: any, transformCode: string): any {
    try {
      // Create a safe function context
      const transformFunction = new Function('data', `return (${transformCode})(data)`)
      return transformFunction(data)
    } catch (error) {
      console.error('Transform function error:', error)
      return data
    }
  },

  /**
   * Validate data against schema
   */
  validateData(data: any, schema: any): { valid: boolean; errors: string[] } {
    // Basic validation - in production, use a proper schema validator like Joi or Zod
    const errors: string[] = []
    
    if (schema.required) {
      schema.required.forEach((field: string) => {
        if (this.getValueByPath(data, field) === undefined) {
          errors.push(`Required field '${field}' is missing`)
        }
      })
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}
