'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Sparkles, 
  Wand2, 
  Lightbulb, 
  Zap, 
  Brain,
  MessageSquare,
  Layout,
  Palette,
  Target,
  TrendingUp,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useAIAssistant } from '../hooks/use-ai-assistant'
import { useEditor } from '@craftjs/core'

interface AIToolbarProps {
  className?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
  compact?: boolean
}

export function AIToolbar({ className, position = 'top', compact = false }: AIToolbarProps) {
  const [intent, setIntent] = useState('')
  const [activeTab, setActiveTab] = useState<'generate' | 'optimize' | 'content' | 'layout'>('generate')
  const { 
    isGenerating, 
    lastSuggestion, 
    error,
    generateAndApply,
    optimizeComponent,
    generateContent,
    suggestLayoutImprovements,
    clearError
  } = useAIAssistant()
  
  const { selected } = useEditor((state) => {
    const [currentNodeId] = state.events.selected
    return {
      selected: currentNodeId ? {
        id: currentNodeId,
        node: state.nodes[currentNodeId]
      } : null
    }
  })

  const handleGenerate = async () => {
    if (!intent.trim()) return
    
    await generateAndApply(intent, selected?.id)
    setIntent('')
  }

  const handleOptimize = async () => {
    if (!selected?.id) return
    await optimizeComponent(selected.id)
  }

  const handleGenerateContent = async () => {
    if (!selected?.id) return
    
    const componentType = selected.node?.data.type?.replace('Craft', '').toLowerCase()
    await generateContent(selected.id, componentType, {
      tone: 'professional',
      length: 'medium'
    })
  }

  const handleLayoutSuggestions = async () => {
    await suggestLayoutImprovements(['conversion', 'accessibility', 'mobile-friendly'])
  }

  const tabs = [
    { id: 'generate', label: 'Generate', icon: Sparkles },
    { id: 'optimize', label: 'Optimize', icon: Zap },
    { id: 'content', label: 'Content', icon: MessageSquare },
    { id: 'layout', label: 'Layout', icon: Layout }
  ] as const

  const renderTabContent = () => {
    switch (activeTab) {
      case 'generate':
        return (
          <div className="space-y-4">
            <div className="flex space-x-2">
              <Input
                placeholder="Describe what you want to create..."
                value={intent}
                onChange={(e) => setIntent(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleGenerate()}
                className="flex-1"
              />
              <Button 
                onClick={handleGenerate}
                disabled={isGenerating || !intent.trim()}
                size="sm"
              >
                {isGenerating ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Wand2 className="w-4 h-4" />
                )}
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-2">
              {[
                'Add a hero section',
                'Create a contact form',
                'Add testimonials',
                'Insert call-to-action button',
                'Add feature cards'
              ].map((suggestion) => (
                <Button
                  key={suggestion}
                  variant="outline"
                  size="sm"
                  onClick={() => setIntent(suggestion)}
                  className="text-xs"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        )

      case 'optimize':
        return (
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              {selected ? (
                <>
                  <p>Selected: <Badge variant="outline">{selected.node?.data.type}</Badge></p>
                  <Button 
                    onClick={handleOptimize}
                    disabled={isGenerating}
                    className="w-full mt-2"
                    size="sm"
                  >
                    {isGenerating ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <Zap className="w-4 h-4 mr-2" />
                    )}
                    Optimize Component
                  </Button>
                </>
              ) : (
                <p>Select a component to optimize it with AI</p>
              )}
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Quick Optimizations:</h4>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm" disabled={!selected}>
                  <Target className="w-3 h-3 mr-1" />
                  Accessibility
                </Button>
                <Button variant="outline" size="sm" disabled={!selected}>
                  <TrendingUp className="w-3 h-3 mr-1" />
                  Performance
                </Button>
                <Button variant="outline" size="sm" disabled={!selected}>
                  <Palette className="w-3 h-3 mr-1" />
                  Design
                </Button>
                <Button variant="outline" size="sm" disabled={!selected}>
                  <Brain className="w-3 h-3 mr-1" />
                  UX
                </Button>
              </div>
            </div>
          </div>
        )

      case 'content':
        return (
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              {selected ? (
                <>
                  <p>Generate content for: <Badge variant="outline">{selected.node?.data.type}</Badge></p>
                  <Button 
                    onClick={handleGenerateContent}
                    disabled={isGenerating}
                    className="w-full mt-2"
                    size="sm"
                  >
                    {isGenerating ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    ) : (
                      <MessageSquare className="w-4 h-4 mr-2" />
                    )}
                    Generate Content
                  </Button>
                </>
              ) : (
                <p>Select a component to generate content</p>
              )}
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Content Types:</h4>
              <div className="flex flex-wrap gap-1">
                {['Professional', 'Casual', 'Friendly', 'Technical'].map((tone) => (
                  <Badge key={tone} variant="secondary" className="text-xs cursor-pointer">
                    {tone}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )

      case 'layout':
        return (
          <div className="space-y-4">
            <Button 
              onClick={handleLayoutSuggestions}
              disabled={isGenerating}
              className="w-full"
              size="sm"
            >
              {isGenerating ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : (
                <Lightbulb className="w-4 h-4 mr-2" />
              )}
              Analyze & Suggest
            </Button>
            
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Focus Areas:</h4>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm">
                  <Target className="w-3 h-3 mr-1" />
                  Conversion
                </Button>
                <Button variant="outline" size="sm">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  Performance
                </Button>
                <Button variant="outline" size="sm">
                  <Palette className="w-3 h-3 mr-1" />
                  Visual Appeal
                </Button>
                <Button variant="outline" size="sm">
                  <Brain className="w-3 h-3 mr-1" />
                  User Flow
                </Button>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  if (compact) {
    return (
      <div className={cn('flex items-center space-x-2 p-2 bg-white border rounded-lg shadow-sm', className)}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setActiveTab('generate')}
          className={cn(activeTab === 'generate' && 'bg-blue-50 text-blue-600')}
        >
          <Sparkles className="w-4 h-4" />
        </Button>
        <Separator orientation="vertical" className="h-6" />
        <Input
          placeholder="AI prompt..."
          value={intent}
          onChange={(e) => setIntent(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleGenerate()}
          className="w-40"
          size="sm"
        />
        <Button 
          onClick={handleGenerate}
          disabled={isGenerating || !intent.trim()}
          size="sm"
        >
          {isGenerating ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Wand2 className="w-4 h-4" />
          )}
        </Button>
      </div>
    )
  }

  return (
    <Card className={cn('w-80', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center space-x-2">
          <Brain className="w-4 h-4" />
          <span>AI Assistant</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setActiveTab(tab.id)}
                className="flex-1 text-xs"
              >
                <Icon className="w-3 h-3 mr-1" />
                {tab.label}
              </Button>
            )
          })}
        </div>

        {/* Error Display */}
        {error && (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2 h-auto p-0 text-red-600"
            >
              ×
            </Button>
          </div>
        )}

        {/* Tab Content */}
        <ScrollArea className="h-64">
          {renderTabContent()}
        </ScrollArea>

        {/* Last Suggestion */}
        {lastSuggestion && (
          <div className="p-2 bg-blue-50 border border-blue-200 rounded text-sm">
            <p className="font-medium text-blue-800">Last Suggestion:</p>
            <p className="text-blue-600 text-xs mt-1">
              {lastSuggestion.reasoning || JSON.stringify(lastSuggestion).substring(0, 100)}...
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
