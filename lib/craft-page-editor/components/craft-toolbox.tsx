'use client'

import React from 'react'
import { useEditor, Element } from '@craftjs/core'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Type,
  MousePointer,
  Box,
  RectangleHorizontal,
  Layout,
  Image,
  AlertTriangle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CraftContainer } from '../user-components/craft-container'
import { CraftText } from '../user-components/craft-text'
import { CraftButton } from '../user-components/craft-button'
import { CraftCard } from '../user-components/craft-card'
import { CraftToolboxItem } from '../types'

// Toolbox items configuration
const toolboxItems: CraftToolboxItem[] = [
  // Layout Components
  {
    id: 'container',
    name: 'Container',
    icon: <Box className="w-4 h-4" />,
    component: CraftContainer,
    defaultProps: { padding: 20, minHeight: 100 },
    category: 'layout',
    description: 'A flexible container for other components'
  },
  {
    id: 'card',
    name: 'Card',
    icon: <RectangleHorizontal className="w-4 h-4" />,
    component: CraftCard,
    defaultProps: {},
    category: 'layout',
    description: 'A card with header, content, and footer sections'
  },

  // Content Components
  {
    id: 'text',
    name: 'Text',
    icon: <Type className="w-4 h-4" />,
    component: CraftText,
    defaultProps: { text: 'Your text here' },
    category: 'content',
    description: 'Editable text content'
  },
  {
    id: 'button',
    name: 'Button',
    icon: <MousePointer className="w-4 h-4" />,
    component: CraftButton,
    defaultProps: { text: 'Click me' },
    category: 'content',
    description: 'Interactive button component'
  }
]

// Group items by category
const groupedItems = toolboxItems.reduce((acc, item) => {
  const category = item.category || 'custom'
  if (!acc[category]) {
    acc[category] = []
  }
  acc[category].push(item)
  return acc
}, {} as Record<string, CraftToolboxItem[]>)

// Category display names and icons
const categoryConfig = {
  layout: { name: 'Layout', icon: <Layout className="w-4 h-4" /> },
  content: { name: 'Content', icon: <Type className="w-4 h-4" /> },
  form: { name: 'Forms', icon: <Box className="w-4 h-4" /> },
  media: { name: 'Media', icon: <Image className="w-4 h-4" /> },
  feedback: { name: 'Feedback', icon: <AlertTriangle className="w-4 h-4" /> },
  custom: { name: 'Custom', icon: <Box className="w-4 h-4" /> }
}

interface CraftToolboxProps {
  className?: string
  compact?: boolean
}

export function CraftToolbox({ className, compact = false }: CraftToolboxProps) {
  const { connectors } = useEditor()

  // Create a draggable component
  const createDraggableComponent = (item: CraftToolboxItem) => {
    if (item.id === 'container') {
      // For container, create as Canvas
      return <Element is={CraftContainer} {...item.defaultProps} canvas />
    }
    
    // For other components, create normally
    const Component = item.component
    return <Component {...item.defaultProps} />
  }

  // Render toolbox item
  const renderToolboxItem = (item: CraftToolboxItem) => (
    <Button
      key={item.id}
      ref={(ref) => {
        if (ref) {
          connectors.create(ref, createDraggableComponent(item))
        }
      }}
      variant="ghost"
      className={cn(
        'w-full justify-start h-auto p-3 hover:bg-gray-100 transition-colors',
        compact && 'p-2'
      )}
    >
      <div className="flex items-center space-x-3 w-full">
        <div className="flex-shrink-0 text-gray-600">
          {item.icon}
        </div>
        <div className="flex-1 text-left">
          <div className="font-medium text-sm">{item.name}</div>
          {!compact && (
            <div className="text-xs text-gray-500 mt-1">
              {item.description}
            </div>
          )}
        </div>
      </div>
    </Button>
  )

  // Render category section
  const renderCategory = (category: string, items: CraftToolboxItem[]) => {
    const config = categoryConfig[category as keyof typeof categoryConfig]
    if (!config) return null

    return (
      <div key={category} className="space-y-2">
        <div className="flex items-center space-x-2 px-3 py-2">
          <div className="text-gray-600">
            {config.icon}
          </div>
          <h3 className="font-medium text-sm text-gray-900">
            {config.name}
          </h3>
          <Badge variant="secondary" className="text-xs">
            {items.length}
          </Badge>
        </div>
        
        <div className="space-y-1">
          {items.map(renderToolboxItem)}
        </div>
      </div>
    )
  }

  return (
    <Card className={cn('h-full', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center space-x-2">
          <Box className="w-4 h-4" />
          <span>Components</span>
        </CardTitle>
        <p className="text-sm text-gray-600">
          Drag components to the canvas
        </p>
      </CardHeader>
      
      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-200px)]">
          <div className="space-y-4 p-4">
            {Object.entries(groupedItems).map(([category, items]) =>
              renderCategory(category, items)
            )}
            
            {Object.keys(groupedItems).length > 1 && (
              <>
                <Separator className="my-4" />
                <div className="px-3 py-2">
                  <p className="text-xs text-gray-500">
                    Total: {toolboxItems.length} components
                  </p>
                </div>
              </>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

// Compact version for smaller spaces
export function CraftToolboxCompact({ className }: { className?: string }) {
  return <CraftToolbox className={className} compact={true} />
}
