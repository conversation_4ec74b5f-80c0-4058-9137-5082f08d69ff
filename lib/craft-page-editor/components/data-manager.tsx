'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Database,
  Plus,
  Trash2,
  RefreshCw,
  Play,
  Pause,
  Link,
  Unlink,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useDataManager, DataSource, DataBinding } from '../data/data-manager'
import { toast } from 'sonner'

interface DataManagerProps {
  className?: string
  onDataBind?: (bindingId: string) => void
}

export function DataManager({ className, onDataBind }: DataManagerProps) {
  const {
    dataSources,
    dataBindings,
    loadingStates,
    errors,
    addDataSource,
    updateDataSource,
    deleteDataSource,
    testDataSource,
    addDataBinding,
    updateDataBinding,
    deleteDataBinding,
    fetchData,
    refreshAllData,
    clearCache
  } = useDataManager()

  const [activeTab, setActiveTab] = useState<'sources' | 'bindings' | 'cache'>('sources')
  const [selectedSource, setSelectedSource] = useState<DataSource | null>(null)
  const [selectedBinding, setSelectedBinding] = useState<DataBinding | null>(null)

  const handleCreateDataSource = () => {
    try {
      const newSourceId = addDataSource({
        name: 'New Data Source',
        type: 'api',
        config: {
          url: '',
          method: 'GET',
          headers: {},
          cache: {
            enabled: true,
            ttl: 300
          }
        },
        isActive: true
      })

      const newSource = dataSources.find(s => s.id === newSourceId)
      if (newSource) {
        setSelectedSource(newSource)
      }

      toast.success('New data source created')
    } catch (error) {
      toast.error('Failed to create data source')
      console.error('Error creating data source:', error)
    }
  }

  const handleTestDataSource = async (sourceId: string) => {
    try {
      const success = await testDataSource(sourceId)
      if (success) {
        toast.success('Data source test successful')
      } else {
        toast.error('Data source test failed')
      }
    } catch (error) {
      toast.error('Data source test failed')
    }
  }

  const handleRefreshData = async (sourceId?: string) => {
    try {
      if (sourceId) {
        await fetchData(sourceId, true)
        toast.success('Data refreshed')
      } else {
        await refreshAllData()
        toast.success('All data refreshed')
      }
    } catch (error) {
      toast.error('Failed to refresh data')
    }
  }

  const handleCreateDataBinding = () => {
    if (dataSources.length === 0) {
      toast.error('Please create a data source first')
      return
    }

    try {
      const newBindingId = addDataBinding({
        componentId: '',
        componentProp: '',
        dataSourceId: dataSources[0]?.id || '',
        dataPath: '',
        isActive: true
      })

      const newBinding = dataBindings.find(b => b.id === newBindingId)
      if (newBinding) {
        setSelectedBinding(newBinding)
      }

      toast.success('New data binding created')
    } catch (error) {
      toast.error('Failed to create data binding')
      console.error('Error creating data binding:', error)
    }
  }

  const getSourceStatusIcon = (source: DataSource) => {
    if (loadingStates[source.id]) {
      return <Clock className="w-4 h-4 text-yellow-500 animate-spin" />
    }
    if (errors[source.id]) {
      return <AlertCircle className="w-4 h-4 text-red-500" />
    }
    if (source.isActive) {
      return <CheckCircle className="w-4 h-4 text-green-500" />
    }
    return <Pause className="w-4 h-4 text-gray-400" />
  }

  const renderDataSourceCard = (source: DataSource) => {
    return (
      <Card 
        key={source.id}
        className={cn(
          'cursor-pointer transition-all hover:shadow-md',
          selectedSource?.id === source.id && 'ring-2 ring-blue-500'
        )}
        onClick={() => setSelectedSource(source)}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getSourceStatusIcon(source)}
              <CardTitle className="text-sm">{source.name}</CardTitle>
            </div>
            <Badge variant="secondary" className="text-xs">
              {source.type}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="text-xs text-gray-500 mb-2">
            {source.config.url || 'No URL configured'}
          </div>
          
          {errors[source.id] && (
            <div className="text-xs text-red-500 mb-2">
              {errors[source.id]}
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <div className="text-xs text-gray-500">
              {source.lastFetch ? `Last: ${source.lastFetch.toLocaleTimeString()}` : 'Never fetched'}
            </div>
            
            <div className="flex space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  handleTestDataSource(source.id)
                }}
                disabled={loadingStates[source.id]}
              >
                <Play className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  handleRefreshData(source.id)
                }}
                disabled={loadingStates[source.id]}
              >
                <RefreshCw className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  deleteDataSource(source.id)
                }}
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const renderDataBindingCard = (binding: DataBinding) => {
    const source = dataSources.find(s => s.id === binding.dataSourceId)
    
    return (
      <Card 
        key={binding.id}
        className={cn(
          'cursor-pointer transition-all hover:shadow-md',
          selectedBinding?.id === binding.id && 'ring-2 ring-blue-500'
        )}
        onClick={() => setSelectedBinding(binding)}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {binding.isActive ? (
                <Link className="w-4 h-4 text-green-500" />
              ) : (
                <Unlink className="w-4 h-4 text-gray-400" />
              )}
              <CardTitle className="text-sm">
                {binding.componentId || 'No Component'}
              </CardTitle>
            </div>
            <Badge variant="secondary" className="text-xs">
              {binding.componentProp}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="text-xs text-gray-500 mb-2">
            Source: {source?.name || 'Unknown'}
          </div>
          <div className="text-xs text-gray-500 mb-2">
            Path: {binding.dataPath || 'No path'}
          </div>
          
          <div className="flex items-center justify-between">
            <div className="text-xs text-gray-500">
              {binding.conditions?.length ? `${binding.conditions.length} conditions` : 'No conditions'}
            </div>
            
            <div className="flex space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onDataBind?.(binding.id)
                }}
              >
                <Zap className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  deleteDataBinding(binding.id)
                }}
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const renderSourceEditor = () => {
    if (!selectedSource) {
      return (
        <div className="flex items-center justify-center h-64 text-center">
          <div>
            <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="font-medium text-gray-900 mb-2">No Source Selected</h3>
            <p className="text-sm text-gray-500">
              Select a data source to edit its configuration
            </p>
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Edit Data Source</h3>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleTestDataSource(selectedSource.id)}
              disabled={loadingStates[selectedSource.id]}
            >
              <Play className="w-3 h-3 mr-1" />
              Test
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleRefreshData(selectedSource.id)}
              disabled={loadingStates[selectedSource.id]}
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Refresh
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="source-name">Source Name</Label>
            <Input
              id="source-name"
              value={selectedSource.name}
              onChange={(e) => updateDataSource(selectedSource.id, { name: e.target.value })}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="source-type">Source Type</Label>
            <Select
              value={selectedSource.type}
              onValueChange={(value) => updateDataSource(selectedSource.id, {
                type: value as DataSource['type']
              })}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="api">REST API</SelectItem>
                <SelectItem value="graphql">GraphQL</SelectItem>
                <SelectItem value="static">Static Data</SelectItem>
                <SelectItem value="realtime">Real-time</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {selectedSource.type === 'api' && (
            <>
              <div>
                <Label htmlFor="source-url">API URL</Label>
                <Input
                  id="source-url"
                  value={selectedSource.config.url || ''}
                  onChange={(e) => updateDataSource(selectedSource.id, {
                    config: { ...selectedSource.config, url: e.target.value }
                  })}
                  placeholder="https://api.example.com/data"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="source-method">HTTP Method</Label>
                <Select
                  value={selectedSource.config.method || 'GET'}
                  onValueChange={(value) => updateDataSource(selectedSource.id, {
                    config: { ...selectedSource.config, method: value as any }
                  })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GET">GET</SelectItem>
                    <SelectItem value="POST">POST</SelectItem>
                    <SelectItem value="PUT">PUT</SelectItem>
                    <SelectItem value="DELETE">DELETE</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Headers Configuration */}
              <div>
                <Label>Headers</Label>
                <Textarea
                  value={JSON.stringify(selectedSource.config.headers || {}, null, 2)}
                  onChange={(e) => {
                    try {
                      const headers = JSON.parse(e.target.value)
                      updateDataSource(selectedSource.id, {
                        config: { ...selectedSource.config, headers }
                      })
                    } catch (error) {
                      // Invalid JSON, ignore
                    }
                  }}
                  placeholder='{"Authorization": "Bearer token", "Content-Type": "application/json"}'
                  className="mt-1 font-mono text-xs"
                  rows={3}
                />
              </div>

              {/* Authentication */}
              <div>
                <Label>Authentication Type</Label>
                <Select
                  value={selectedSource.config.authentication?.type || 'none'}
                  onValueChange={(value) => {
                    if (value === 'none') {
                      updateDataSource(selectedSource.id, {
                        config: { ...selectedSource.config, authentication: undefined }
                      })
                    } else {
                      updateDataSource(selectedSource.id, {
                        config: {
                          ...selectedSource.config,
                          authentication: { type: value as any }
                        }
                      })
                    }
                  }}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="bearer">Bearer Token</SelectItem>
                    <SelectItem value="basic">Basic Auth</SelectItem>
                    <SelectItem value="apikey">API Key</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {selectedSource.config.authentication?.type === 'bearer' && (
                <div>
                  <Label htmlFor="auth-token">Bearer Token</Label>
                  <Input
                    id="auth-token"
                    type="password"
                    value={selectedSource.config.authentication.token || ''}
                    onChange={(e) => updateDataSource(selectedSource.id, {
                      config: {
                        ...selectedSource.config,
                        authentication: {
                          type: 'bearer',
                          token: e.target.value
                        }
                      }
                    })}
                    placeholder="Enter bearer token"
                    className="mt-1"
                  />
                </div>
              )}

              {selectedSource.config.authentication?.type === 'basic' && (
                <>
                  <div>
                    <Label htmlFor="auth-username">Username</Label>
                    <Input
                      id="auth-username"
                      value={selectedSource.config.authentication.username || ''}
                      onChange={(e) => updateDataSource(selectedSource.id, {
                        config: {
                          ...selectedSource.config,
                          authentication: {
                            type: 'basic',
                            username: e.target.value,
                            password: selectedSource.config.authentication?.password || ''
                          }
                        }
                      })}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="auth-password">Password</Label>
                    <Input
                      id="auth-password"
                      type="password"
                      value={selectedSource.config.authentication.password || ''}
                      onChange={(e) => updateDataSource(selectedSource.id, {
                        config: {
                          ...selectedSource.config,
                          authentication: {
                            type: 'basic',
                            username: selectedSource.config.authentication?.username || '',
                            password: e.target.value
                          }
                        }
                      })}
                      className="mt-1"
                    />
                  </div>
                </>
              )}

              {selectedSource.config.authentication?.type === 'apikey' && (
                <>
                  <div>
                    <Label htmlFor="auth-key-name">API Key Header</Label>
                    <Input
                      id="auth-key-name"
                      value={selectedSource.config.authentication.key || 'X-API-Key'}
                      onChange={(e) => updateDataSource(selectedSource.id, {
                        config: {
                          ...selectedSource.config,
                          authentication: {
                            type: 'apikey',
                            key: e.target.value,
                            token: selectedSource.config.authentication?.token || ''
                          }
                        }
                      })}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="auth-key-value">API Key Value</Label>
                    <Input
                      id="auth-key-value"
                      type="password"
                      value={selectedSource.config.authentication.token || ''}
                      onChange={(e) => updateDataSource(selectedSource.id, {
                        config: {
                          ...selectedSource.config,
                          authentication: {
                            type: 'apikey',
                            key: selectedSource.config.authentication?.key || 'X-API-Key',
                            token: e.target.value
                          }
                        }
                      })}
                      className="mt-1"
                    />
                  </div>
                </>
              )}

              {/* Request Body for POST/PUT */}
              {(selectedSource.config.method === 'POST' || selectedSource.config.method === 'PUT') && (
                <div>
                  <Label>Request Body (JSON)</Label>
                  <Textarea
                    value={JSON.stringify(selectedSource.config.body || {}, null, 2)}
                    onChange={(e) => {
                      try {
                        const body = JSON.parse(e.target.value)
                        updateDataSource(selectedSource.id, {
                          config: { ...selectedSource.config, body }
                        })
                      } catch (error) {
                        // Invalid JSON, ignore
                      }
                    }}
                    placeholder='{"key": "value"}'
                    className="mt-1 font-mono text-xs"
                    rows={4}
                  />
                </div>
              )}
            </>
          )}

          {selectedSource.type === 'graphql' && (
            <>
              <div>
                <Label htmlFor="graphql-url">GraphQL Endpoint</Label>
                <Input
                  id="graphql-url"
                  value={selectedSource.config.url || ''}
                  onChange={(e) => updateDataSource(selectedSource.id, {
                    config: { ...selectedSource.config, url: e.target.value }
                  })}
                  placeholder="https://api.example.com/graphql"
                  className="mt-1"
                />
              </div>

              <div>
                <Label>GraphQL Query</Label>
                <Textarea
                  value={selectedSource.config.query || ''}
                  onChange={(e) => updateDataSource(selectedSource.id, {
                    config: { ...selectedSource.config, query: e.target.value }
                  })}
                  placeholder="query { users { id name email } }"
                  className="mt-1 font-mono text-xs"
                  rows={6}
                />
              </div>
            </>
          )}

          {selectedSource.type === 'static' && (
            <div>
              <Label>Static Data (JSON)</Label>
              <Textarea
                value={JSON.stringify(selectedSource.config.body || {}, null, 2)}
                onChange={(e) => {
                  try {
                    const body = JSON.parse(e.target.value)
                    updateDataSource(selectedSource.id, {
                      config: { ...selectedSource.config, body }
                    })
                  } catch (error) {
                    // Invalid JSON, ignore
                  }
                }}
                placeholder='{"data": [{"id": 1, "name": "Example"}]}'
                className="mt-1 font-mono text-xs"
                rows={8}
              />
            </div>
          )}

          {/* Cache Configuration */}
          <Separator />
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Checkbox
                id="cache-enabled"
                checked={selectedSource.config.cache?.enabled || false}
                onCheckedChange={(checked) => updateDataSource(selectedSource.id, {
                  config: {
                    ...selectedSource.config,
                    cache: {
                      enabled: !!checked,
                      ttl: selectedSource.config.cache?.ttl || 300
                    }
                  }
                })}
              />
              <Label htmlFor="cache-enabled">Enable Caching</Label>
            </div>

            {selectedSource.config.cache?.enabled && (
              <div>
                <Label htmlFor="cache-ttl">Cache TTL (seconds)</Label>
                <Input
                  id="cache-ttl"
                  type="number"
                  value={selectedSource.config.cache.ttl || 300}
                  onChange={(e) => updateDataSource(selectedSource.id, {
                    config: {
                      ...selectedSource.config,
                      cache: {
                        enabled: selectedSource.config.cache?.enabled || false,
                        ttl: parseInt(e.target.value) || 300
                      }
                    }
                  })}
                  className="mt-1"
                />
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="source-active"
              checked={selectedSource.isActive}
              onCheckedChange={(checked) => updateDataSource(selectedSource.id, { isActive: !!checked })}
            />
            <Label htmlFor="source-active">Active</Label>
          </div>
        </div>
      </div>
    )
  }

  const renderBindingEditor = () => {
    if (!selectedBinding) {
      return (
        <div className="flex items-center justify-center h-64 text-center">
          <div>
            <Link className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="font-medium text-gray-900 mb-2">No Binding Selected</h3>
            <p className="text-sm text-gray-500">
              Select a data binding to edit its configuration
            </p>
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Edit Data Binding</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDataBind?.(selectedBinding.id)}
          >
            <Zap className="w-3 h-3 mr-1" />
            Apply
          </Button>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="binding-component">Component ID</Label>
            <Input
              id="binding-component"
              value={selectedBinding.componentId}
              onChange={(e) => updateDataBinding(selectedBinding.id, { componentId: e.target.value })}
              placeholder="component-id"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="binding-prop">Component Property</Label>
            <Input
              id="binding-prop"
              value={selectedBinding.componentProp}
              onChange={(e) => updateDataBinding(selectedBinding.id, { componentProp: e.target.value })}
              placeholder="text, src, href, etc."
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="binding-source">Data Source</Label>
            <Select
              value={selectedBinding.dataSourceId}
              onValueChange={(value) => updateDataBinding(selectedBinding.id, { dataSourceId: value })}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {dataSources.map((source) => (
                  <SelectItem key={source.id} value={source.id}>
                    {source.name} ({source.type})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="binding-path">Data Path</Label>
            <Input
              id="binding-path"
              value={selectedBinding.dataPath}
              onChange={(e) => updateDataBinding(selectedBinding.id, { dataPath: e.target.value })}
              placeholder="data.users[0].name"
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">
              Use dot notation to access nested properties (e.g., data.users[0].name)
            </p>
          </div>

          <div>
            <Label>Transform Function (Optional)</Label>
            <Textarea
              value={selectedBinding.transform || ''}
              onChange={(e) => updateDataBinding(selectedBinding.id, { transform: e.target.value })}
              placeholder="(data) => data.toUpperCase()"
              className="mt-1 font-mono text-xs"
              rows={3}
            />
            <p className="text-xs text-gray-500 mt-1">
              JavaScript function to transform the data before applying
            </p>
          </div>

          <div>
            <Label>Fallback Value</Label>
            <Input
              value={selectedBinding.fallback || ''}
              onChange={(e) => updateDataBinding(selectedBinding.id, { fallback: e.target.value })}
              placeholder="Default value if data is unavailable"
              className="mt-1"
            />
          </div>

          <Separator />

          <div>
            <Label className="text-sm font-medium">Conditions (Optional)</Label>
            <p className="text-xs text-gray-500 mb-2">
              Only apply binding when conditions are met
            </p>

            {selectedBinding.conditions?.map((condition, index) => (
              <div key={index} className="flex space-x-2 mb-2">
                <Input
                  placeholder="field"
                  value={condition.field}
                  onChange={(e) => {
                    const newConditions = [...(selectedBinding.conditions || [])]
                    newConditions[index] = { ...condition, field: e.target.value }
                    updateDataBinding(selectedBinding.id, { conditions: newConditions })
                  }}
                  className="flex-1"
                />
                <Select
                  value={condition.operator}
                  onValueChange={(value) => {
                    const newConditions = [...(selectedBinding.conditions || [])]
                    newConditions[index] = { ...condition, operator: value as any }
                    updateDataBinding(selectedBinding.id, { conditions: newConditions })
                  }}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="equals">Equals</SelectItem>
                    <SelectItem value="not_equals">Not Equals</SelectItem>
                    <SelectItem value="contains">Contains</SelectItem>
                    <SelectItem value="greater_than">Greater Than</SelectItem>
                    <SelectItem value="less_than">Less Than</SelectItem>
                    <SelectItem value="exists">Exists</SelectItem>
                  </SelectContent>
                </Select>
                <Input
                  placeholder="value"
                  value={condition.value}
                  onChange={(e) => {
                    const newConditions = [...(selectedBinding.conditions || [])]
                    newConditions[index] = { ...condition, value: e.target.value }
                    updateDataBinding(selectedBinding.id, { conditions: newConditions })
                  }}
                  className="flex-1"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const newConditions = selectedBinding.conditions?.filter((_, i) => i !== index) || []
                    updateDataBinding(selectedBinding.id, { conditions: newConditions })
                  }}
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              </div>
            ))}

            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const newConditions = [
                  ...(selectedBinding.conditions || []),
                  { field: '', operator: 'equals' as const, value: '' }
                ]
                updateDataBinding(selectedBinding.id, { conditions: newConditions })
              }}
              className="w-full"
            >
              <Plus className="w-3 h-3 mr-1" />
              Add Condition
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="binding-active"
              checked={selectedBinding.isActive}
              onCheckedChange={(checked) => updateDataBinding(selectedBinding.id, { isActive: !!checked })}
            />
            <Label htmlFor="binding-active">Active</Label>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Database className="w-5 h-5" />
              <span>Data Manager</span>
            </CardTitle>
            <CardDescription>
              Manage data sources, bindings, and dynamic content
            </CardDescription>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={() => refreshAllData()}>
              <RefreshCw className="w-3 h-3 mr-1" />
              Refresh All
            </Button>
            <Button variant="outline" size="sm" onClick={() => clearCache()}>
              <Trash2 className="w-3 h-3 mr-1" />
              Clear Cache
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="sources">Data Sources</TabsTrigger>
            <TabsTrigger value="bindings">Data Bindings</TabsTrigger>
            <TabsTrigger value="cache">Cache</TabsTrigger>
          </TabsList>
          
          <TabsContent value="sources" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Data Sources ({dataSources.length})</h3>
              <Button onClick={handleCreateDataSource}>
                <Plus className="w-3 h-3 mr-1" />
                New Source
              </Button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <ScrollArea className="h-96">
                  <div className="space-y-2 pr-4">
                    {dataSources.map(renderDataSourceCard)}
                  </div>
                </ScrollArea>
              </div>
              
              <div>
                <ScrollArea className="h-96">
                  {renderSourceEditor()}
                </ScrollArea>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="bindings" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Data Bindings ({dataBindings.length})</h3>
              <Button onClick={handleCreateDataBinding}>
                <Plus className="w-3 h-3 mr-1" />
                New Binding
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <ScrollArea className="h-96">
                  <div className="space-y-2 pr-4">
                    {dataBindings.map(renderDataBindingCard)}
                  </div>
                </ScrollArea>
              </div>

              <div>
                <ScrollArea className="h-96">
                  {renderBindingEditor()}
                </ScrollArea>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="cache" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Cache Management</h3>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={() => clearCache()}>
                  <Trash2 className="w-3 h-3 mr-1" />
                  Clear All Cache
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dataSources
                .filter(source => source.config.cache?.enabled)
                .map((source) => {
                  const cacheData = useDataManager.getState().cache[source.id]
                  return (
                    <Card key={source.id}>
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-sm">{source.name}</CardTitle>
                          <Badge variant="secondary" className="text-xs">
                            {source.type}
                          </Badge>
                        </div>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <div className="space-y-2">
                          <div className="flex justify-between text-xs">
                            <span className="text-gray-500">Status:</span>
                            <span className={cacheData ? 'text-green-600' : 'text-gray-400'}>
                              {cacheData ? 'Cached' : 'No Cache'}
                            </span>
                          </div>

                          {cacheData && (
                            <>
                              <div className="flex justify-between text-xs">
                                <span className="text-gray-500">Cached:</span>
                                <span>{cacheData.timestamp.toLocaleTimeString()}</span>
                              </div>

                              <div className="flex justify-between text-xs">
                                <span className="text-gray-500">TTL:</span>
                                <span>{cacheData.ttl}s</span>
                              </div>

                              <div className="flex justify-between text-xs">
                                <span className="text-gray-500">Size:</span>
                                <span>{JSON.stringify(cacheData.data).length} chars</span>
                              </div>

                              <div className="flex justify-between text-xs">
                                <span className="text-gray-500">Expires:</span>
                                <span className={
                                  Date.now() - cacheData.timestamp.getTime() > cacheData.ttl * 1000
                                    ? 'text-red-500'
                                    : 'text-green-600'
                                }>
                                  {Date.now() - cacheData.timestamp.getTime() > cacheData.ttl * 1000
                                    ? 'Expired'
                                    : `${Math.round((cacheData.ttl * 1000 - (Date.now() - cacheData.timestamp.getTime())) / 1000)}s`
                                  }
                                </span>
                              </div>
                            </>
                          )}

                          <div className="flex space-x-1 pt-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRefreshData(source.id)}
                              disabled={loadingStates[source.id]}
                              className="flex-1"
                            >
                              <RefreshCw className="w-3 h-3 mr-1" />
                              Refresh
                            </Button>
                            {cacheData && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => clearCache(source.id)}
                                className="flex-1"
                              >
                                <Trash2 className="w-3 h-3 mr-1" />
                                Clear
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
            </div>

            {dataSources.filter(source => source.config.cache?.enabled).length === 0 && (
              <div className="text-center py-8">
                <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="font-medium text-gray-900 mb-2">No Cached Data Sources</h3>
                <p className="text-sm text-gray-500">
                  Enable caching on your data sources to see cache information here
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
