'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  TrendingUp, 
  Building, 
  Palette, 
  ShoppingCart, 
  FileText,
  Eye,
  Download,
  Sparkles
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { pageTemplates, templateCategories, templateHelpers } from '../templates/page-templates'
import { useEditor } from '@craftjs/core'
import { toast } from 'sonner'

interface TemplateSelectorProps {
  className?: string
  onTemplateSelect?: (templateId: string) => void
  showPreview?: boolean
}

const categoryIcons = {
  marketing: TrendingUp,
  business: Building,
  creative: Palette,
  ecommerce: ShoppingCart,
  content: FileText
}

export function TemplateSelector({ 
  className, 
  onTemplateSelect,
  showPreview = true 
}: TemplateSelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState('marketing')
  const [previewTemplate, setPreviewTemplate] = useState<string | null>(null)
  const { actions } = useEditor()

  const handleTemplateSelect = (templateId: string) => {
    try {
      const success = templateHelpers.applyTemplate(templateId, actions)
      if (success) {
        toast.success('Template applied successfully!')
        onTemplateSelect?.(templateId)
      } else {
        toast.error('Failed to apply template')
      }
    } catch (error) {
      console.error('Template application error:', error)
      toast.error('Error applying template')
    }
  }

  const renderTemplateCard = (template: any) => {
    const IconComponent = categoryIcons[template.category as keyof typeof categoryIcons]
    
    return (
      <Card key={template.id} className="group hover:shadow-lg transition-shadow cursor-pointer">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {IconComponent && <IconComponent className="w-4 h-4 text-gray-600" />}
              <CardTitle className="text-sm">{template.name}</CardTitle>
            </div>
            <Badge variant="secondary" className="text-xs">
              {templateCategories[template.category as keyof typeof templateCategories]?.name}
            </Badge>
          </div>
          <CardDescription className="text-xs">
            {template.description}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="pt-0">
          {/* Template Preview */}
          <div className="aspect-video bg-gray-100 rounded-md mb-3 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
              <div className="text-center">
                <Sparkles className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-xs text-gray-500">Template Preview</p>
              </div>
            </div>
            
            {/* Preview overlay on hover */}
            <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
              {showPreview && (
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => setPreviewTemplate(template.id)}
                    >
                      <Eye className="w-3 h-3 mr-1" />
                      Preview
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl">
                    <DialogHeader>
                      <DialogTitle>{template.name}</DialogTitle>
                      <DialogDescription>{template.description}</DialogDescription>
                    </DialogHeader>
                    <div className="aspect-video bg-gray-100 rounded-lg">
                      {/* Template preview would be rendered here */}
                      <div className="w-full h-full flex items-center justify-center">
                        <p className="text-gray-500">Template Preview Coming Soon</p>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
              
              <Button
                variant="default"
                size="sm"
                onClick={() => handleTemplateSelect(template.id)}
              >
                <Download className="w-3 h-3 mr-1" />
                Use Template
              </Button>
            </div>
          </div>
          
          {/* Template Actions */}
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleTemplateSelect(template.id)}
              className="flex-1"
            >
              Use Template
            </Button>
            {showPreview && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setPreviewTemplate(template.id)}
              >
                <Eye className="w-3 h-3" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  const categories = templateHelpers.getCategories()
  const templates = Object.values(pageTemplates)

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Sparkles className="w-5 h-5" />
          <span>Page Templates</span>
        </CardTitle>
        <CardDescription>
          Choose from professionally designed templates to get started quickly
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          <TabsList className="grid w-full grid-cols-5">
            {categories.map((category) => {
              const IconComponent = categoryIcons[category.id as keyof typeof categoryIcons]
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className="flex items-center space-x-1"
                >
                  {IconComponent && <IconComponent className="w-3 h-3" />}
                  <span className="hidden sm:inline">{category.name}</span>
                </TabsTrigger>
              )
            })}
          </TabsList>
          
          {categories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="mt-4">
              <div className="mb-4">
                <h3 className="font-medium text-sm">{category.name}</h3>
                <p className="text-xs text-gray-600">{category.description}</p>
              </div>
              
              <ScrollArea className="h-96">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pr-4">
                  {templateHelpers.getByCategory(category.id).map(renderTemplateCard)}
                </div>
              </ScrollArea>
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  )
}

// Compact version for smaller spaces
export function TemplateQuickSelect({ 
  className,
  onTemplateSelect 
}: {
  className?: string
  onTemplateSelect?: (templateId: string) => void
}) {
  const { actions } = useEditor()
  
  const popularTemplates = [
    pageTemplates.landing,
    pageTemplates.business,
    pageTemplates.portfolio
  ]

  const handleSelect = (templateId: string) => {
    try {
      const success = templateHelpers.applyTemplate(templateId, actions)
      if (success) {
        toast.success('Template applied!')
        onTemplateSelect?.(templateId)
      }
    } catch (error) {
      toast.error('Failed to apply template')
    }
  }

  return (
    <div className={cn('space-y-2', className)}>
      <h4 className="text-sm font-medium">Quick Start</h4>
      <div className="grid grid-cols-1 gap-2">
        {popularTemplates.map((template) => (
          <Button
            key={template.id}
            variant="outline"
            size="sm"
            onClick={() => handleSelect(template.id)}
            className="justify-start"
          >
            <Sparkles className="w-3 h-3 mr-2" />
            {template.name}
          </Button>
        ))}
      </div>
    </div>
  )
}
