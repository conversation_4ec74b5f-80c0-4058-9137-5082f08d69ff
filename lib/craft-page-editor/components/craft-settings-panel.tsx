'use client'

import React from 'react'
import { useEditor } from '@craftjs/core'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Settings, Info } from 'lucide-react'
import { cn } from '@/lib/utils'

interface CraftSettingsPanelProps {
  className?: string
}

export function CraftSettingsPanel({ className }: CraftSettingsPanelProps) {
  const { selected } = useEditor((state, query) => {
    const [currentNodeId] = state.events.selected
    let selected = null

    if (currentNodeId) {
      const node = state.nodes[currentNodeId]
      selected = {
        id: currentNodeId,
        name: node.data.displayName || node.data.type,
        type: node.data.type,
        settings: node.related && node.related.settings,
        isDeletable: query.node(currentNodeId).isDeletable(),
        isRoot: currentNodeId === 'ROOT',
        props: node.data.props,
        custom: node.data.custom
      }
    }

    return {
      selected
    }
  })

  // Render when no component is selected
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-64 text-center p-6">
      <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
        <Settings className="w-6 h-6 text-gray-400" />
      </div>
      <h3 className="font-medium text-gray-900 mb-2">
        No Component Selected
      </h3>
      <p className="text-sm text-gray-500 max-w-xs">
        Click on a component in the canvas to view and edit its properties
      </p>
    </div>
  )

  // Render root node info
  const renderRootInfo = () => (
    <div className="p-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          This is the root canvas. You can't edit its properties, but you can add components to it.
        </AlertDescription>
      </Alert>
    </div>
  )

  // Render component info header
  const renderComponentHeader = () => {
    if (!selected) return null

    return (
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>Properties</span>
          </CardTitle>
          <Badge variant="outline" className="text-xs">
            {String(selected.type)}
          </Badge>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-900">
            {String(selected.name)}
          </span>
          {selected.isDeletable && (
            <Badge variant="secondary" className="text-xs">
              Editable
            </Badge>
          )}
        </div>
      </CardHeader>
    )
  }

  // Render settings content
  const renderSettingsContent = () => {
    if (!selected) return renderEmptyState()
    
    if (selected.isRoot) return renderRootInfo()

    if (!selected.settings) {
      return (
        <div className="p-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              This component doesn't have configurable properties.
            </AlertDescription>
          </Alert>
        </div>
      )
    }

    // Render the component's settings panel
    const SettingsComponent = selected.settings
    return (
      <div className="p-4">
        <SettingsComponent />
      </div>
    )
  }

  return (
    <Card className={cn('h-full', className)}>
      {selected && !selected.isRoot && renderComponentHeader()}
      
      <CardContent className="p-0">
        <ScrollArea className="h-[calc(100vh-200px)]">
          {renderSettingsContent()}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

// Alternative compact settings panel
export function CraftSettingsPanelCompact({ className }: { className?: string }) {
  const { selected } = useEditor((state) => {
    const [currentNodeId] = state.events.selected
    let selected = null

    if (currentNodeId) {
      const node = state.nodes[currentNodeId]
      selected = {
        id: currentNodeId,
        name: node.data.displayName || node.data.type,
        settings: node.related && node.related.settings,
        isRoot: currentNodeId === 'ROOT'
      }
    }

    return { selected }
  })

  if (!selected || selected.isRoot || !selected.settings) {
    return (
      <div className={cn('p-4 text-center', className)}>
        <p className="text-sm text-gray-500">
          {!selected ? 'Select a component' : 'No settings available'}
        </p>
      </div>
    )
  }

  const SettingsComponent = selected.settings
  return (
    <div className={cn('p-4', className)}>
      <SettingsComponent />
    </div>
  )
}
