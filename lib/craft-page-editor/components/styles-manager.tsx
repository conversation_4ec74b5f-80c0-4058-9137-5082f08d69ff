'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Palette, 
  Plus, 
  Edit, 
  Trash2, 
  Copy, 
  Download, 
  Upload,
  Eye,
  EyeOff,
  Paintbrush,
  Type,
  Layout,
  Spacing,
  Zap
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useStylesManager, StyleDefinition, StyleTheme } from '../styles/styles-manager'
import { CustomFieldRenderer } from '@/lib/core/builders/components/properties-panel/custom-fields'
import { toast } from 'sonner'

interface StylesManagerProps {
  className?: string
  onStyleApply?: (styleId: string) => void
}

export function StylesManager({ className, onStyleApply }: StylesManagerProps) {
  const {
    currentTheme,
    customThemes,
    styles,
    setTheme,
    createCustomTheme,
    addStyle,
    updateStyle,
    deleteStyle,
    duplicateStyle,
    generateCSS,
    exportStyles,
    importStyles
  } = useStylesManager()

  const [activeTab, setActiveTab] = useState<'styles' | 'themes' | 'global'>('styles')
  const [selectedStyle, setSelectedStyle] = useState<StyleDefinition | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Filter styles based on search term
  const filteredStyles = styles.filter(style =>
    style.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    style.category.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Group styles by category
  const groupedStyles = filteredStyles.reduce((acc, style) => {
    if (!acc[style.category]) {
      acc[style.category] = []
    }
    acc[style.category].push(style)
    return acc
  }, {} as Record<string, StyleDefinition[]>)

  const handleCreateStyle = () => {
    const newStyleId = addStyle({
      name: 'New Style',
      category: 'layout',
      properties: {
        padding: '16px',
        margin: '8px'
      }
    })
    
    const newStyle = styles.find(s => s.id === newStyleId)
    if (newStyle) {
      setSelectedStyle(newStyle)
      setIsCreating(true)
    }
    
    toast.success('New style created')
  }

  const handleUpdateStyle = (styleId: string, updates: Partial<StyleDefinition>) => {
    updateStyle(styleId, updates)
    toast.success('Style updated')
  }

  const handleDeleteStyle = (styleId: string) => {
    deleteStyle(styleId)
    if (selectedStyle?.id === styleId) {
      setSelectedStyle(null)
    }
    toast.success('Style deleted')
  }

  const handleDuplicateStyle = (styleId: string) => {
    const newStyleId = duplicateStyle(styleId)
    const newStyle = styles.find(s => s.id === newStyleId)
    if (newStyle) {
      setSelectedStyle(newStyle)
    }
    toast.success('Style duplicated')
  }

  const handleExportStyles = () => {
    const exported = exportStyles()
    const blob = new Blob([exported], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `styles-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Styles exported')
  }

  const handleImportStyles = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        importStyles(content)
        toast.success('Styles imported')
      } catch (error) {
        toast.error('Failed to import styles')
      }
    }
    reader.readAsText(file)
  }

  const categoryIcons = {
    layout: Layout,
    typography: Type,
    colors: Palette,
    spacing: Spacing,
    effects: Zap
  }

  const renderStyleCard = (style: StyleDefinition) => {
    const IconComponent = categoryIcons[style.category as keyof typeof categoryIcons] || Layout

    return (
      <Card 
        key={style.id}
        className={cn(
          'cursor-pointer transition-all hover:shadow-md',
          selectedStyle?.id === style.id && 'ring-2 ring-blue-500'
        )}
        onClick={() => setSelectedStyle(style)}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <IconComponent className="w-4 h-4 text-gray-600" />
              <CardTitle className="text-sm">{style.name}</CardTitle>
            </div>
            <Badge variant="secondary" className="text-xs">
              {style.category}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="flex items-center justify-between">
            <div className="text-xs text-gray-500">
              {Object.keys(style.properties).length} properties
            </div>
            
            <div className="flex space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onStyleApply?.(style.id)
                }}
              >
                <Eye className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  handleDuplicateStyle(style.id)
                }}
              >
                <Copy className="w-3 h-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  handleDeleteStyle(style.id)
                }}
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const renderStyleEditor = () => {
    if (!selectedStyle) {
      return (
        <div className="flex items-center justify-center h-64 text-center">
          <div>
            <Paintbrush className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="font-medium text-gray-900 mb-2">No Style Selected</h3>
            <p className="text-sm text-gray-500">
              Select a style to edit its properties
            </p>
          </div>
        </div>
      )
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Edit Style</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onStyleApply?.(selectedStyle.id)}
          >
            <Eye className="w-3 h-3 mr-1" />
            Apply
          </Button>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="style-name">Style Name</Label>
            <Input
              id="style-name"
              value={selectedStyle.name}
              onChange={(e) => handleUpdateStyle(selectedStyle.id, { name: e.target.value })}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="style-category">Category</Label>
            <select
              id="style-category"
              value={selectedStyle.category}
              onChange={(e) => handleUpdateStyle(selectedStyle.id, { 
                category: e.target.value as StyleDefinition['category']
              })}
              className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="layout">Layout</option>
              <option value="typography">Typography</option>
              <option value="colors">Colors</option>
              <option value="spacing">Spacing</option>
              <option value="effects">Effects</option>
            </select>
          </div>

          <Separator />

          <div>
            <Label>CSS Properties</Label>
            <div className="mt-2 space-y-2">
              {Object.entries(selectedStyle.properties).map(([prop, value], index) => (
                <div key={index} className="flex space-x-2">
                  <Input
                    placeholder="Property"
                    value={prop}
                    onChange={(e) => {
                      const newProperties = { ...selectedStyle.properties }
                      delete newProperties[prop]
                      newProperties[e.target.value] = value
                      handleUpdateStyle(selectedStyle.id, { properties: newProperties })
                    }}
                    className="flex-1"
                  />
                  <Input
                    placeholder="Value"
                    value={String(value)}
                    onChange={(e) => {
                      const newProperties = { ...selectedStyle.properties }
                      newProperties[prop] = e.target.value
                      handleUpdateStyle(selectedStyle.id, { properties: newProperties })
                    }}
                    className="flex-1"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newProperties = { ...selectedStyle.properties }
                      delete newProperties[prop]
                      handleUpdateStyle(selectedStyle.id, { properties: newProperties })
                    }}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              ))}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const newProperties = { ...selectedStyle.properties }
                  newProperties['new-property'] = 'value'
                  handleUpdateStyle(selectedStyle.id, { properties: newProperties })
                }}
                className="w-full"
              >
                <Plus className="w-3 h-3 mr-1" />
                Add Property
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Palette className="w-5 h-5" />
              <span>Styles Manager</span>
            </CardTitle>
            <CardDescription>
              Manage styles, themes, and global CSS for your components
            </CardDescription>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" onClick={handleExportStyles}>
              <Download className="w-3 h-3 mr-1" />
              Export
            </Button>
            <label>
              <Button variant="outline" size="sm" asChild>
                <span>
                  <Upload className="w-3 h-3 mr-1" />
                  Import
                </span>
              </Button>
              <input
                type="file"
                accept=".json"
                onChange={handleImportStyles}
                className="hidden"
              />
            </label>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="styles">Styles</TabsTrigger>
            <TabsTrigger value="themes">Themes</TabsTrigger>
            <TabsTrigger value="global">Global CSS</TabsTrigger>
          </TabsList>
          
          <TabsContent value="styles" className="space-y-4">
            <div className="flex items-center justify-between">
              <Input
                placeholder="Search styles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
              <Button onClick={handleCreateStyle}>
                <Plus className="w-3 h-3 mr-1" />
                New Style
              </Button>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium mb-3">Available Styles</h3>
                <ScrollArea className="h-96">
                  <div className="space-y-2 pr-4">
                    {Object.entries(groupedStyles).map(([category, categoryStyles]) => (
                      <div key={category}>
                        <h4 className="text-sm font-medium text-gray-700 mb-2 capitalize">
                          {category}
                        </h4>
                        <div className="space-y-2 mb-4">
                          {categoryStyles.map(renderStyleCard)}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
              
              <div>
                <h3 className="font-medium mb-3">Style Editor</h3>
                <ScrollArea className="h-96">
                  {renderStyleEditor()}
                </ScrollArea>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="themes" className="space-y-4">
            <div className="text-center py-8">
              <Palette className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">Theme Management</h3>
              <p className="text-sm text-gray-500">
                Theme management coming soon
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="global" className="space-y-4">
            <div className="text-center py-8">
              <Type className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">Global CSS</h3>
              <p className="text-sm text-gray-500">
                Global CSS editor coming soon
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
