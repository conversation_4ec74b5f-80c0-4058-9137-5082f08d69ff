'use client'

import React, { useState } from 'react'
import { Editor, Frame, Element } from '@craftjs/core'
import { cn } from '@/lib/utils'
import { CraftToolbox } from './craft-toolbox'
import { CraftSettingsPanel } from './craft-settings-panel'
import { CraftTopbar } from './craft-topbar'
import { AIToolbar } from './ai-toolbar'
import { TemplateSelector } from './template-selector'
import { CraftContainer } from '../user-components/craft-container'
import { CraftText } from '../user-components/craft-text'
import { CraftButton } from '../user-components/craft-button'
import { CraftCard, CraftCardHeader, CraftCardContent, CraftCardFooter } from '../user-components/craft-card'
import { CraftEditorConfig, CraftEventHandlers } from '../types'

// Default resolver with all available components
const defaultResolver = {
  CraftContainer,
  CraftText,
  CraftButton,
  CraftCard,
  CraftCardHeader,
  CraftCardContent,
  CraftCardFooter
}

interface CraftPageEditorProps {
  config?: Partial<CraftEditorConfig>
  eventHandlers?: CraftEventHandlers
  initialContent?: React.ReactNode
  className?: string
  showToolbox?: boolean
  showSettings?: boolean
  showTopbar?: boolean
  showAI?: boolean
  showTemplates?: boolean
  toolboxPosition?: 'left' | 'right'
  settingsPosition?: 'left' | 'right'
  aiPosition?: 'left' | 'right' | 'top' | 'bottom'
  mode?: 'builder' | 'preview' | 'template-select'
}

export function CraftPageEditor({
  config = {},
  eventHandlers = {},
  initialContent,
  className,
  showToolbox = true,
  showSettings = true,
  showTopbar = true,
  showAI = true,
  showTemplates = false,
  toolboxPosition = 'left',
  settingsPosition = 'right',
  aiPosition = 'top',
  mode = 'builder'
}: CraftPageEditorProps) {
  const [leftPanelOpen, setLeftPanelOpen] = useState(true)
  const [rightPanelOpen, setRightPanelOpen] = useState(true)
  const [currentMode, setCurrentMode] = useState(mode)

  // Merge default config with provided config
  const editorConfig: CraftEditorConfig = {
    enabled: true,
    indicator: true,
    resolver: defaultResolver,
    ...config,
    resolver: { ...defaultResolver, ...config.resolver }
  }

  // Default initial content if none provided
  const defaultInitialContent = (
    <Element is={CraftContainer} padding={40} background="#f8f9fa" minHeight={400} canvas>
      <CraftText 
        text="Welcome to Craft.js Page Editor" 
        fontSize={32} 
        fontWeight="bold" 
        textAlign="center"
      />
      <CraftText 
        text="Start building your page by dragging components from the toolbox" 
        fontSize={16} 
        textAlign="center" 
        color="#666666"
      />
      <div style={{ display: 'flex', justifyContent: 'center', marginTop: '20px' }}>
        <CraftButton text="Get Started" variant="default" size="lg" />
      </div>
    </Element>
  )

  const renderContent = () => {
    if (currentMode === 'template-select') {
      return (
        <TemplateSelector
          onTemplateSelect={(templateId) => {
            setCurrentMode('builder')
            // Template is already applied by the selector
          }}
        />
      )
    }

    return initialContent || defaultInitialContent
  }

  const renderLeftPanel = () => {
    if (!leftPanelOpen) return null

    if (toolboxPosition === 'left' && showToolbox) {
      return <CraftToolbox />
    }

    if (settingsPosition === 'left' && showSettings) {
      return <CraftSettingsPanel />
    }

    if (aiPosition === 'left' && showAI) {
      return <AIToolbar />
    }

    return null
  }

  const renderRightPanel = () => {
    if (!rightPanelOpen) return null

    if (settingsPosition === 'right' && showSettings) {
      return <CraftSettingsPanel />
    }

    if (toolboxPosition === 'right' && showToolbox) {
      return <CraftToolbox />
    }

    if (aiPosition === 'right' && showAI) {
      return <AIToolbar />
    }

    return null
  }

  return (
    <div className={cn('craft-page-editor h-full flex flex-col', className)}>
      <Editor 
        resolver={editorConfig.resolver}
        enabled={editorConfig.enabled}
        indicator={editorConfig.indicator}
        onRender={editorConfig.onRender}
        onNodesChange={editorConfig.onNodesChange}
        normalizeNodes={editorConfig.normalizeNodes}
      >
        {/* Top Toolbar */}
        {showTopbar && (
          <CraftTopbar 
            onToggleLeftPanel={() => setLeftPanelOpen(!leftPanelOpen)}
            onToggleRightPanel={() => setRightPanelOpen(!rightPanelOpen)}
            leftPanelOpen={leftPanelOpen}
            rightPanelOpen={rightPanelOpen}
            eventHandlers={eventHandlers}
          />
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel */}
          {(showToolbox && toolboxPosition === 'left') || (showSettings && settingsPosition === 'left') ? (
            <div className={cn(
              'w-80 flex-shrink-0 border-r border-gray-200 bg-white transition-all duration-200',
              !leftPanelOpen && 'w-0 overflow-hidden'
            )}>
              {renderLeftPanel()}
            </div>
          ) : null}

          {/* Center Canvas Area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="flex-1 overflow-auto bg-gray-50 p-4">
              <div className="max-w-full mx-auto">
                <Frame>
                  {renderContent()}
                </Frame>
              </div>
            </div>
          </div>

          {/* Right Panel */}
          {(showSettings && settingsPosition === 'right') || (showToolbox && toolboxPosition === 'right') ? (
            <div className={cn(
              'w-80 flex-shrink-0 border-l border-gray-200 bg-white transition-all duration-200',
              !rightPanelOpen && 'w-0 overflow-hidden'
            )}>
              {renderRightPanel()}
            </div>
          ) : null}
        </div>

        {/* Status Bar */}
        <div className="h-8 bg-gray-100 border-t border-gray-200 flex items-center justify-between px-4 text-xs text-gray-600">
          <div className="flex items-center space-x-4">
            <span>Craft.js Page Editor</span>
            <span>•</span>
            <span>Ready to build</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <span>v1.0.0</span>
            <span>•</span>
            <span>{new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </Editor>
    </div>
  )
}

// Export with default props for easier usage
export const SimpleCraftPageEditor: React.FC<{
  initialContent?: React.ReactNode
  className?: string
}> = ({ initialContent, className }) => (
  <CraftPageEditor
    initialContent={initialContent}
    className={className}
    showToolbox={true}
    showSettings={true}
    showTopbar={true}
  />
)
