'use client'

import React from 'react'
import { useEditor } from '@craftjs/core'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  PanelLeft, 
  PanelRight, 
  Eye, 
  EyeOff, 
  Undo, 
  Redo, 
  Download, 
  Upload,
  Save,
  Settings,
  Code,
  Smartphone,
  Tablet,
  Monitor
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { CraftEventHandlers } from '../types'
import { toast } from 'sonner'

interface CraftTopbarProps {
  onToggleLeftPanel?: () => void
  onToggleRightPanel?: () => void
  leftPanelOpen?: boolean
  rightPanelOpen?: boolean
  eventHandlers?: CraftEventHandlers
  className?: string
  showDevicePreview?: boolean
  showUndoRedo?: boolean
  showSaveLoad?: boolean
}

export function CraftTopbar({
  onToggleLeftPanel,
  onToggleRightPanel,
  leftPanelOpen = true,
  rightPanelOpen = true,
  eventHandlers = {},
  className,
  showDevicePreview = true,
  showUndoRedo = true,
  showSaveLoad = true
}: CraftTopbarProps) {
  const { 
    actions, 
    query, 
    enabled, 
    canUndo, 
    canRedo 
  } = useEditor((state, query) => ({
    enabled: state.options.enabled,
    canUndo: query.history.canUndo(),
    canRedo: query.history.canRedo()
  }))

  const [devicePreview, setDevicePreview] = React.useState<'desktop' | 'tablet' | 'mobile'>('desktop')

  // Handle editor enable/disable
  const handleToggleEditor = (enabled: boolean) => {
    actions.setOptions(options => {
      options.enabled = enabled
    })
  }

  // Handle undo/redo
  const handleUndo = () => {
    if (canUndo) {
      actions.history.undo()
      toast.success('Undone')
    }
  }

  const handleRedo = () => {
    if (canRedo) {
      actions.history.redo()
      toast.success('Redone')
    }
  }

  // Handle save/load
  const handleSave = () => {
    try {
      const json = query.serialize()
      // Here you would typically save to your backend
      console.log('Serialized JSON:', json)
      localStorage.setItem('craft-page-editor-state', json)
      toast.success('Page saved successfully')
      eventHandlers.onUpdate?.('ROOT', {})
    } catch (error) {
      console.error('Save error:', error)
      toast.error('Failed to save page')
    }
  }

  const handleLoad = () => {
    try {
      const saved = localStorage.getItem('craft-page-editor-state')
      if (saved) {
        actions.deserialize(saved)
        toast.success('Page loaded successfully')
      } else {
        toast.error('No saved page found')
      }
    } catch (error) {
      console.error('Load error:', error)
      toast.error('Failed to load page')
    }
  }

  const handleExport = () => {
    try {
      const json = query.serialize()
      const blob = new Blob([json], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `craft-page-${Date.now()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      toast.success('Page exported successfully')
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export page')
    }
  }

  // Device preview icons
  const deviceIcons = {
    desktop: <Monitor className="w-4 h-4" />,
    tablet: <Tablet className="w-4 h-4" />,
    mobile: <Smartphone className="w-4 h-4" />
  }

  return (
    <div className={cn(
      'h-14 bg-white border-b border-gray-200 flex items-center justify-between px-4',
      className
    )}>
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        {/* Panel Toggles */}
        <div className="flex items-center space-x-2">
          {onToggleLeftPanel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleLeftPanel}
              className={cn(leftPanelOpen && 'bg-gray-100')}
            >
              <PanelLeft className="w-4 h-4" />
            </Button>
          )}
          
          {onToggleRightPanel && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleRightPanel}
              className={cn(rightPanelOpen && 'bg-gray-100')}
            >
              <PanelRight className="w-4 h-4" />
            </Button>
          )}
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Editor Toggle */}
        <div className="flex items-center space-x-2">
          <Switch
            id="editor-enabled"
            checked={enabled}
            onCheckedChange={handleToggleEditor}
          />
          <Label htmlFor="editor-enabled" className="text-sm">
            Edit Mode
          </Label>
          <Badge variant={enabled ? 'default' : 'secondary'} className="text-xs">
            {enabled ? 'ON' : 'OFF'}
          </Badge>
        </div>

        {/* Undo/Redo */}
        {showUndoRedo && (
          <>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleUndo}
                disabled={!canUndo}
              >
                <Undo className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRedo}
                disabled={!canRedo}
              >
                <Redo className="w-4 h-4" />
              </Button>
            </div>
          </>
        )}
      </div>

      {/* Center Section - Device Preview */}
      {showDevicePreview && (
        <div className="flex items-center space-x-1 bg-gray-100 rounded-md p-1">
          {(['desktop', 'tablet', 'mobile'] as const).map((device) => (
            <Button
              key={device}
              variant={devicePreview === device ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setDevicePreview(device)}
              className="h-8 px-3"
            >
              {deviceIcons[device]}
              <span className="ml-1 capitalize text-xs">
                {device}
              </span>
            </Button>
          ))}
        </div>
      )}

      {/* Right Section */}
      <div className="flex items-center space-x-2">
        {/* Save/Load Actions */}
        {showSaveLoad && (
          <>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSave}
            >
              <Save className="w-4 h-4 mr-1" />
              Save
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLoad}
            >
              <Upload className="w-4 h-4 mr-1" />
              Load
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExport}
            >
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>

            <Separator orientation="vertical" className="h-6" />
          </>
        )}

        {/* Preview Toggle */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleToggleEditor(!enabled)}
        >
          {enabled ? (
            <>
              <Eye className="w-4 h-4 mr-1" />
              Preview
            </>
          ) : (
            <>
              <EyeOff className="w-4 h-4 mr-1" />
              Edit
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
