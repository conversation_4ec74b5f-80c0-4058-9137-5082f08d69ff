# Craft.js Page Editor Library

A comprehensive, production-ready page editor built with Craft.js and shadcn/ui components. This library provides a complete visual page building experience with drag-and-drop functionality, real-time editing, and seamless integration with existing design systems.

## 🌟 Features

### Core Functionality
- **Drag & Drop Interface** - Intuitive component placement and rearrangement
- **Real-time Editing** - Live preview with instant updates
- **Visual Properties Panel** - Dynamic component configuration using existing custom fields
- **Undo/Redo Support** - Full history management
- **Save/Load/Export** - Persistent state management

### Component System
- **Shadcn/ui Integration** - All components built on shadcn/ui foundation
- **HOC Architecture** - Higher Order Components for enhanced functionality
- **Canvas Regions** - Droppable areas with validation rules
- **Text Editing** - In-place content editing capabilities
- **Responsive Design** - Mobile-first component design

### Developer Experience
- **TypeScript Support** - Full type safety and IntelliSense
- **Modular Architecture** - Easy to extend and customize
- **Documentation Compliance** - Follows Craft.js patterns exactly
- **Custom Fields Integration** - Uses existing properties panel system

## 📦 Installation

The library is already installed as part of your project. Craft.js core is included:

```bash
pnpm add @craftjs/core
```

## 🚀 Quick Start

### Basic Usage

```tsx
import { CraftPageEditor } from '@/lib/craft-page-editor'

export default function MyPageEditor() {
  return (
    <CraftPageEditor
      showToolbox={true}
      showSettings={true}
      showTopbar={true}
    />
  )
}
```

### Advanced Configuration

```tsx
import { CraftPageEditor, CraftContainer, CraftText } from '@/lib/craft-page-editor'

export default function AdvancedEditor() {
  const initialContent = (
    <CraftContainer padding={40} background="#f8f9fa">
      <CraftText text="Welcome to my page!" fontSize={24} />
    </CraftContainer>
  )

  return (
    <CraftPageEditor
      initialContent={initialContent}
      config={{
        enabled: true,
        indicator: true,
        resolver: {
          // Add custom components here
        }
      }}
      eventHandlers={{
        onSelect: (nodeId) => console.log('Selected:', nodeId),
        onUpdate: (nodeId, props) => console.log('Updated:', nodeId),
        onCreate: (nodeId) => console.log('Created:', nodeId),
        onDelete: (nodeId) => console.log('Deleted:', nodeId)
      }}
    />
  )
}
```

## 🧩 Available Components

### Layout Components
- **CraftContainer** - Flexible container with Canvas functionality
- **CraftCard** - Card with header, content, and footer sections
- **CraftSection** - Section-level container
- **CraftGrid** - Grid layout system
- **CraftColumn** - Grid column component

### Content Components
- **CraftText** - Editable text with typography controls
- **CraftHeading** - Semantic headings (H1-H6)
- **CraftButton** - Interactive button with variants
- **CraftImage** - Image component with sizing controls

### Form Components (Coming Soon)
- **CraftInput** - Text input field
- **CraftTextarea** - Multi-line text input
- **CraftSelect** - Dropdown selection

## 🎨 Creating Custom Components

### Basic Component

```tsx
import { withCraftNode, createCraftMinimalSettings } from '@/lib/craft-page-editor/hocs'

const MyComponent = ({ text, color }) => (
  <div style={{ color }}>{text}</div>
)

const MyComponentSettings = createCraftMinimalSettings([
  {
    name: 'text',
    label: 'Text Content',
    type: 'text',
    placeholder: 'Enter text...'
  },
  {
    name: 'color',
    label: 'Text Color',
    type: 'color'
  }
])

export const CraftMyComponent = withCraftNode(MyComponent, {
  displayName: 'My Component',
  defaultProps: { text: 'Hello', color: '#000000' }
})

CraftMyComponent.craft = {
  ...CraftMyComponent.craft,
  related: {
    settings: MyComponentSettings
  }
}
```

### Canvas Component

```tsx
import { withCraftCanvas } from '@/lib/craft-page-editor/hocs'

const MyContainer = ({ children, ...props }) => (
  <div {...props}>{children}</div>
)

export const CraftMyContainer = withCraftCanvas(MyContainer, {
  displayName: 'My Container',
  acceptedTypes: ['CraftText', 'CraftButton'], // Optional restrictions
  maxChildren: 5 // Optional limit
})
```

## 🔧 HOCs (Higher Order Components)

### withCraftNode
Wraps components to make them draggable and configurable.

```tsx
withCraftNode(Component, {
  displayName: 'Component Name',
  defaultProps: { /* default props */ },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})
```

### withCraftCanvas
Creates droppable regions that accept other components.

```tsx
withCraftCanvas(Component, {
  displayName: 'Canvas Name',
  acceptedTypes: ['ComponentType1', 'ComponentType2'],
  maxChildren: 10
})
```

### withCraftSettings
Generates settings panels using the existing custom fields system.

```tsx
withCraftSettings({
  fields: [
    {
      name: 'property',
      label: 'Property Label',
      type: 'text' // Uses existing field types
    }
  ],
  sections: [
    {
      title: 'Content',
      fields: ['property'],
      collapsible: false
    }
  ]
})
```

## 🎯 Integration with Existing Systems

### Custom Fields Integration
The library seamlessly integrates with your existing custom fields system:

```tsx
import { CustomFieldRenderer } from '@/lib/core/builders/components/properties-panel/custom-fields'

// All existing field types are supported:
// - text, textarea, number, boolean
// - select, multi-select, color, range
// - image, file, date, time
// - spacing, border, shadow, gradient
// - And many more...
```

### State Management
Works with existing state management patterns:

```tsx
import { useCraftEditor } from '@/lib/craft-page-editor/hooks'

function MyComponent() {
  const { actions, query, utils } = useCraftEditor({
    onSelect: (nodeId) => {
      // Handle selection
    },
    onUpdate: (nodeId, props) => {
      // Handle updates
    }
  })

  return (
    <button onClick={() => utils.exportPage()}>
      Export Page
    </button>
  )
}
```

## 📚 API Reference

### CraftPageEditor Props
```tsx
interface CraftPageEditorProps {
  config?: Partial<CraftEditorConfig>
  eventHandlers?: CraftEventHandlers
  initialContent?: React.ReactNode
  className?: string
  showToolbox?: boolean
  showSettings?: boolean
  showTopbar?: boolean
  toolboxPosition?: 'left' | 'right'
  settingsPosition?: 'left' | 'right'
}
```

### Event Handlers
```tsx
interface CraftEventHandlers {
  onSelect?: (nodeId: string | null) => void
  onHover?: (nodeId: string | null) => void
  onDrag?: (nodeId: string | null) => void
  onDrop?: (draggedId: string, targetId: string) => void
  onCreate?: (nodeId: string) => void
  onDelete?: (nodeId: string) => void
  onUpdate?: (nodeId: string, props: Record<string, any>) => void
}
```

## 🎮 Demo

Visit `/craft-page-editor-demo` to see the library in action with:
- Live drag & drop functionality
- Real-time property editing
- Component toolbox
- Save/load capabilities
- Responsive preview modes

## 🔄 Following Craft.js Patterns

This library strictly follows Craft.js documentation patterns:

1. **Nodes Concept** - Every component is managed by a Node
2. **User Components** - Components use `useNode` hook with connectors
3. **Editor Integration** - Uses `useEditor` for state management
4. **Canvas/Frame** - Proper droppable regions and draggable elements
5. **Related Components** - Settings panels as related components

## 🤝 Contributing

1. Follow existing code patterns
2. Add comprehensive TypeScript types
3. Include settings panels for new components
4. Update documentation
5. Test with the demo page

## 📄 License

This library is part of the main project and follows the same license terms.
