'use client'

import { useState, useCallback } from 'react'
import { useEditor } from '@craftjs/core'
import { craftAI, aiHelpers } from '../ai/ai-assistant'
import { toast } from 'sonner'

export interface AIAssistantState {
  isGenerating: boolean
  lastSuggestion: any
  error: string | null
  history: Array<{
    timestamp: Date
    action: string
    input: string
    output: any
  }>
}

export function useAIAssistant() {
  const { actions, query } = useEditor()
  const [state, setState] = useState<AIAssistantState>({
    isGenerating: false,
    lastSuggestion: null,
    error: null,
    history: []
  })

  const updateState = useCallback((updates: Partial<AIAssistantState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  const addToHistory = useCallback((action: string, input: string, output: any) => {
    setState(prev => ({
      ...prev,
      history: [
        ...prev.history.slice(-9), // Keep last 10 entries
        {
          timestamp: new Date(),
          action,
          input,
          output
        }
      ]
    }))
  }, [])

  /**
   * Generate a component based on user intent
   */
  const generateComponent = useCallback(async (
    intent: string,
    parentNodeId?: string
  ) => {
    updateState({ isGenerating: true, error: null })
    
    try {
      const currentNodes = query.getSerializedNodes()
      const existingComponents = Object.values(currentNodes).map((node: any) => node.type)
      
      const suggestion = await craftAI.generateComponent(intent, {
        existingComponents,
        pageType: 'landing',
        targetAudience: 'general'
      })

      if (suggestion) {
        updateState({ lastSuggestion: suggestion })
        addToHistory('generateComponent', intent, suggestion)
        toast.success('AI component suggestion generated!')
        return suggestion
      } else {
        throw new Error('Failed to generate component suggestion')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      updateState({ error: errorMessage })
      toast.error(`AI generation failed: ${errorMessage}`)
      return null
    } finally {
      updateState({ isGenerating: false })
    }
  }, [query, updateState, addToHistory])

  /**
   * Apply AI suggestion to the editor
   */
  const applySuggestion = useCallback((suggestion: any, parentNodeId?: string) => {
    try {
      // Map AI suggestion to Craft.js component
      const componentMap: Record<string, string> = {
        text: 'CraftText',
        button: 'CraftButton',
        container: 'CraftContainer',
        card: 'CraftCard',
        heading: 'CraftHeading',
        image: 'CraftImage'
      }

      const componentType = componentMap[suggestion.componentType]
      if (!componentType) {
        throw new Error(`Unknown component type: ${suggestion.componentType}`)
      }

      // Create the component node
      const nodeTree = {
        type: componentType,
        props: suggestion.props,
        nodes: suggestion.children || []
      }

      // Add to editor
      if (parentNodeId) {
        actions.add(nodeTree, parentNodeId)
      } else {
        actions.add(nodeTree, 'ROOT')
      }

      toast.success('AI suggestion applied successfully!')
      addToHistory('applySuggestion', JSON.stringify(suggestion), nodeTree)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      updateState({ error: errorMessage })
      toast.error(`Failed to apply suggestion: ${errorMessage}`)
    }
  }, [actions, updateState, addToHistory])

  /**
   * Generate and apply component in one step
   */
  const generateAndApply = useCallback(async (
    intent: string,
    parentNodeId?: string
  ) => {
    const suggestion = await generateComponent(intent, parentNodeId)
    if (suggestion) {
      applySuggestion(suggestion, parentNodeId)
    }
    return suggestion
  }, [generateComponent, applySuggestion])

  /**
   * Optimize existing component with AI
   */
  const optimizeComponent = useCallback(async (nodeId: string) => {
    updateState({ isGenerating: true, error: null })
    
    try {
      const node = query.node(nodeId).get()
      const optimizedProps = await craftAI.optimizeComponentProps(
        node.data.type,
        node.data.props,
        {
          deviceType: 'responsive',
          performanceGoals: ['fast-loading', 'accessible'],
          accessibilityLevel: 'enhanced'
        }
      )

      if (optimizedProps && optimizedProps.props) {
        actions.setProp(nodeId, (props: any) => {
          Object.assign(props, optimizedProps.props)
        })
        
        toast.success('Component optimized with AI!')
        addToHistory('optimizeComponent', nodeId, optimizedProps)
        return optimizedProps
      } else {
        throw new Error('Failed to generate optimization suggestions')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      updateState({ error: errorMessage })
      toast.error(`Optimization failed: ${errorMessage}`)
      return null
    } finally {
      updateState({ isGenerating: false })
    }
  }, [query, actions, updateState, addToHistory])

  /**
   * Generate content for a component
   */
  const generateContent = useCallback(async (
    nodeId: string,
    componentType: string,
    context?: any
  ) => {
    updateState({ isGenerating: true, error: null })
    
    try {
      const content = await craftAI.generateContent(componentType, {
        topic: context?.topic || 'general',
        tone: context?.tone || 'professional',
        length: context?.length || 'medium',
        audience: context?.audience || 'general'
      })

      if (content) {
        // Apply content based on component type
        actions.setProp(nodeId, (props: any) => {
          if (componentType === 'text' || componentType === 'heading') {
            props.text = content
          } else if (componentType === 'button') {
            props.text = content
          } else if (componentType === 'image') {
            props.alt = content
          }
        })

        toast.success('AI content generated!')
        addToHistory('generateContent', `${componentType}:${nodeId}`, content)
        return content
      } else {
        throw new Error('Failed to generate content')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      updateState({ error: errorMessage })
      toast.error(`Content generation failed: ${errorMessage}`)
      return null
    } finally {
      updateState({ isGenerating: false })
    }
  }, [actions, updateState, addToHistory])

  /**
   * Suggest layout improvements
   */
  const suggestLayoutImprovements = useCallback(async (goals: string[] = []) => {
    updateState({ isGenerating: true, error: null })
    
    try {
      const currentLayout = query.getSerializedNodes()
      const suggestions = await craftAI.suggestLayoutImprovements(currentLayout, goals)

      if (suggestions) {
        updateState({ lastSuggestion: suggestions })
        toast.success('Layout improvement suggestions generated!')
        addToHistory('suggestLayoutImprovements', goals.join(', '), suggestions)
        return suggestions
      } else {
        throw new Error('Failed to generate layout suggestions')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      updateState({ error: errorMessage })
      toast.error(`Layout analysis failed: ${errorMessage}`)
      return null
    } finally {
      updateState({ isGenerating: false })
    }
  }, [query, updateState, addToHistory])

  /**
   * Quick AI actions using helpers
   */
  const quickActions = {
    quickGenerate: useCallback(async (intent: string) => {
      const suggestion = await aiHelpers.quickGenerate(intent)
      if (suggestion) {
        updateState({ lastSuggestion: suggestion })
        return suggestion
      }
      return null
    }, [updateState]),

    smartContent: useCallback(async (nodeId: string, componentType: string) => {
      const node = query.node(nodeId).get()
      const existingContent = node.data.props.text || ''
      
      const content = await aiHelpers.smartContent(componentType, existingContent)
      if (content) {
        actions.setProp(nodeId, (props: any) => {
          props.text = content
        })
        toast.success('Smart content applied!')
      }
      return content
    }, [query, actions])
  }

  return {
    // State
    ...state,
    
    // Actions
    generateComponent,
    applySuggestion,
    generateAndApply,
    optimizeComponent,
    generateContent,
    suggestLayoutImprovements,
    
    // Quick actions
    ...quickActions,
    
    // Utilities
    clearHistory: useCallback(() => {
      updateState({ history: [] })
    }, [updateState]),
    
    clearError: useCallback(() => {
      updateState({ error: null })
    }, [updateState])
  }
}
