'use client'

import { useEditor } from '@craftjs/core'
import { useCallback, useMemo } from 'react'
import { CraftEditorState, CraftEventHandlers } from '../types'
import { craftUtils } from '../utils/craft-utils'
import { toast } from 'sonner'

/**
 * Enhanced hook for Craft.js editor with additional utilities
 */
export function useCraftEditor(eventHandlers: CraftEventHandlers = {}) {
  const {
    actions,
    query,
    connectors,
    ...editorState
  } = useEditor((state, query) => ({
    enabled: state.options.enabled,
    nodes: state.nodes,
    events: state.events,
    canUndo: query.history.canUndo(),
    canRedo: query.history.canRedo(),
    selectedNodeIds: Array.from(state.events.selected),
    hoveredNodeId: state.events.hovered,
    draggedNodeId: state.events.dragged
  }))

  // Enhanced actions with event handlers
  const enhancedActions = useMemo(() => ({
    ...actions,

    // Enhanced setProp with validation
    setProp: useCallback((nodeId: string, cb: (props: any) => void) => {
      try {
        actions.setProp(nodeId, cb)
        eventHandlers.onUpdate?.(nodeId, {})
      } catch (error) {
        console.error('setProp error:', error)
        toast.error('Failed to update component properties')
      }
    }, [actions, eventHandlers]),

    // Enhanced delete with confirmation
    delete: useCallback((nodeId: string) => {
      try {
        const node = editorState.nodes[nodeId]
        if (!node) return

        const isConfirmed = window.confirm(
          `Are you sure you want to delete "${craftUtils.getDisplayName(node)}"?`
        )
        
        if (isConfirmed) {
          actions.delete(nodeId)
          eventHandlers.onDelete?.(nodeId)
          toast.success('Component deleted')
        }
      } catch (error) {
        console.error('Delete error:', error)
        toast.error('Failed to delete component')
      }
    }, [actions, editorState.nodes, eventHandlers]),

    // Enhanced add with validation
    add: useCallback((nodeTree: any, parentId?: string) => {
      try {
        actions.add(nodeTree, parentId)
        eventHandlers.onCreate?.(nodeTree.id || craftUtils.generateId())
        toast.success('Component added')
      } catch (error) {
        console.error('Add error:', error)
        toast.error('Failed to add component')
      }
    }, [actions, eventHandlers]),

    // Enhanced move with validation
    move: useCallback((targetId: string, newParentId: string, index?: number) => {
      try {
        actions.move(targetId, newParentId, index)
        eventHandlers.onDrop?.(targetId, newParentId)
      } catch (error) {
        console.error('Move error:', error)
        toast.error('Failed to move component')
      }
    }, [actions, eventHandlers]),

    // Undo with feedback
    undo: useCallback(() => {
      if (editorState.canUndo) {
        actions.history.undo()
        toast.success('Undone')
      }
    }, [actions, editorState.canUndo]),

    // Redo with feedback
    redo: useCallback(() => {
      if (editorState.canRedo) {
        actions.history.redo()
        toast.success('Redone')
      }
    }, [actions, editorState.canRedo]),

    // Clear selection
    clearSelection: useCallback(() => {
      actions.clearEvents()
      eventHandlers.onSelect?.(null)
    }, [actions, eventHandlers]),

    // Select node
    selectNode: useCallback((nodeId: string | null) => {
      if (nodeId) {
        actions.selectNode(nodeId)
        eventHandlers.onSelect?.(nodeId)
      } else {
        actions.clearEvents()
        eventHandlers.onSelect?.(null)
      }
    }, [actions, eventHandlers])
  }), [actions, editorState, eventHandlers])

  // Enhanced query methods
  const enhancedQuery = useMemo(() => ({
    ...query,

    // Get component tree
    getComponentTree: useCallback(() => {
      try {
        return query.getSerializedNodes()
      } catch (error) {
        console.error('getComponentTree error:', error)
        return {}
      }
    }, [query]),

    // Get selected node info
    getSelectedNode: useCallback(() => {
      const selectedIds = editorState.selectedNodeIds
      if (selectedIds.length === 0) return null
      
      const nodeId = selectedIds[0]
      const node = editorState.nodes[nodeId]
      
      if (!node) return null
      
      return {
        id: nodeId,
        node,
        displayName: craftUtils.getDisplayName(node),
        isCanvas: craftUtils.isCanvas(node),
        hasChildren: craftUtils.hasChildren(node),
        depth: craftUtils.getNodeDepth(nodeId, editorState.nodes),
        isDeletable: query.node(nodeId).isDeletable()
      }
    }, [editorState.selectedNodeIds, editorState.nodes, query]),

    // Get node ancestors
    getAncestors: useCallback((nodeId: string) => {
      const ancestors: string[] = []
      let currentId = nodeId
      
      while (currentId && currentId !== 'ROOT') {
        const node = editorState.nodes[currentId]
        if (!node || !node.data.parent) break
        ancestors.push(node.data.parent)
        currentId = node.data.parent
      }
      
      return ancestors
    }, [editorState.nodes]),

    // Get node descendants
    getDescendants: useCallback((nodeId: string) => {
      const descendants: string[] = []
      const node = editorState.nodes[nodeId]
      
      if (!node || !node.data.nodes) return descendants
      
      const traverse = (nodes: string[]) => {
        nodes.forEach(childId => {
          descendants.push(childId)
          const childNode = editorState.nodes[childId]
          if (childNode && childNode.data.nodes) {
            traverse(childNode.data.nodes)
          }
        })
      }
      
      traverse(node.data.nodes)
      return descendants
    }, [editorState.nodes]),

    // Check if node can accept drop
    canAcceptDrop: useCallback((targetId: string, draggedType: string) => {
      const targetNode = editorState.nodes[targetId]
      if (!targetNode) return false
      
      return craftUtils.acceptsType(targetNode, draggedType)
    }, [editorState.nodes]),

    // Get component statistics
    getStats: useCallback(() => {
      const nodes = Object.values(editorState.nodes)
      const componentTypes: Record<string, number> = {}
      
      nodes.forEach(node => {
        const type = node.data.type
        componentTypes[type] = (componentTypes[type] || 0) + 1
      })
      
      return {
        totalComponents: nodes.length - 1, // Exclude ROOT
        componentTypes,
        maxDepth: Math.max(...nodes.map(node => 
          craftUtils.getNodeDepth(node.id, editorState.nodes)
        ))
      }
    }, [editorState.nodes])
  }), [query, editorState])

  // Utility functions
  const utils = useMemo(() => ({
    // Export page data
    exportPage: useCallback(() => {
      try {
        const serialized = query.serialize()
        const blob = new Blob([serialized], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `craft-page-${Date.now()}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        toast.success('Page exported successfully')
      } catch (error) {
        console.error('Export error:', error)
        toast.error('Failed to export page')
      }
    }, [query]),

    // Import page data
    importPage: useCallback((jsonData: string) => {
      try {
        actions.deserialize(jsonData)
        toast.success('Page imported successfully')
      } catch (error) {
        console.error('Import error:', error)
        toast.error('Failed to import page')
      }
    }, [actions]),

    // Save to localStorage
    saveToLocal: useCallback((key: string = 'craft-page-editor') => {
      try {
        const serialized = query.serialize()
        localStorage.setItem(key, serialized)
        toast.success('Page saved locally')
      } catch (error) {
        console.error('Save to local error:', error)
        toast.error('Failed to save page locally')
      }
    }, [query]),

    // Load from localStorage
    loadFromLocal: useCallback((key: string = 'craft-page-editor') => {
      try {
        const saved = localStorage.getItem(key)
        if (saved) {
          actions.deserialize(saved)
          toast.success('Page loaded from local storage')
        } else {
          toast.error('No saved page found')
        }
      } catch (error) {
        console.error('Load from local error:', error)
        toast.error('Failed to load page from local storage')
      }
    }, [actions])
  }), [query, actions])

  return {
    // Original editor state
    ...editorState,
    
    // Enhanced actions
    actions: enhancedActions,
    
    // Enhanced query
    query: enhancedQuery,
    
    // Original connectors
    connectors,
    
    // Utility functions
    utils
  }
}
