'use client'

import { ReactNode, ComponentType } from 'react'
import { Node, NodeId } from '@craftjs/core'

// Import types from custom fields - using any for now to avoid circular dependencies
type FieldConfig = any
type FieldValue = any

// Base Craft.js Component Props
export interface CraftComponentProps {
  children?: ReactNode
  className?: string
  style?: React.CSSProperties
}

// Node-specific props for components that use useNode
export interface CraftNodeProps extends CraftComponentProps {
  nodeId?: NodeId
  isSelected?: boolean
  isHovered?: boolean
  isDragging?: boolean
  isCanvas?: boolean
}

// Canvas-specific props for droppable regions
export interface CraftCanvasProps extends CraftNodeProps {
  canvas: true
  droppableId?: string
  acceptedTypes?: string[]
  maxChildren?: number
}

// Settings panel props
export interface CraftSettingsProps {
  nodeId: NodeId
  fields: FieldConfig[]
  values: Record<string, FieldValue>
  onChange: (field: string, value: FieldValue) => void
}

// Editor state interface
export interface CraftEditorState {
  nodes: Record<NodeId, Node>
  events: {
    selected: Set<NodeId>
    hovered: NodeId | null
    dragged: NodeId | null
  }
  options: {
    enabled: boolean
    indicator: boolean
  }
}

// User Component definition
export type CraftUserComponent = ComponentType<any> & {
  craft: {
    props?: Record<string, any>
    rules?: {
      canDrag?: (node: Node) => boolean
      canDrop?: (node: Node, target: Node) => boolean
      canMoveIn?: (nodes: Node[]) => boolean
      canMoveOut?: (nodes: Node[]) => boolean
    }
    related?: {
      settings?: ComponentType<any>
      toolbar?: ComponentType<any>
    }
    displayName?: string
    custom?: Record<string, any>
  }
}

// Toolbox item for drag-and-drop creation
export interface CraftToolboxItem {
  id: string
  name: string
  icon?: ReactNode
  component: CraftUserComponent
  defaultProps?: Record<string, any>
  category?: string
  description?: string
}

// Component categories for toolbox organization
export type CraftComponentCategory = 
  | 'layout'
  | 'content'
  | 'form'
  | 'media'
  | 'navigation'
  | 'feedback'
  | 'data'
  | 'custom'

// Validation rules for components
export interface CraftValidationRule {
  field: string
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: any
  message: string
  validator?: (value: any) => boolean
}

// Serialization options
export interface CraftSerializationOptions {
  includeMetadata?: boolean
  compressOutput?: boolean
  validateNodes?: boolean
}

// Editor configuration
export interface CraftEditorConfig {
  enabled?: boolean
  indicator?: boolean
  resolver: Record<string, CraftUserComponent>
  onRender?: (info: { render: ReactNode }) => ReactNode
  onNodesChange?: (query: any) => void
  normalizeNodes?: (state: any) => void
}

// HOC configuration types
export interface WithCraftNodeConfig {
  displayName?: string
  defaultProps?: Record<string, any>
  rules?: CraftUserComponent['craft']['rules']
}

export interface WithCraftCanvasConfig extends WithCraftNodeConfig {
  acceptedTypes?: string[]
  maxChildren?: number
  droppableId?: string
}

export interface WithCraftSettingsConfig {
  fields: FieldConfig[]
  sections?: Array<{
    title: string
    fields: string[]
    collapsible?: boolean
  }>
}

// Event handlers
export interface CraftEventHandlers {
  onSelect?: (nodeId: NodeId | null) => void
  onHover?: (nodeId: NodeId | null) => void
  onDrag?: (nodeId: NodeId | null) => void
  onDrop?: (draggedId: NodeId, targetId: NodeId) => void
  onCreate?: (nodeId: NodeId) => void
  onDelete?: (nodeId: NodeId) => void
  onUpdate?: (nodeId: NodeId, props: Record<string, any>) => void
}

// Utility types
export type CraftNodeType = string
export type CraftNodeData = Record<string, any>
export type CraftNodeEvents = Record<string, boolean>

// Component registry for dynamic loading
export interface CraftComponentRegistry {
  [key: string]: CraftUserComponent
}

// Theme integration
export interface CraftThemeConfig {
  colors?: Record<string, string>
  spacing?: Record<string, string>
  typography?: Record<string, string>
  borderRadius?: Record<string, string>
  shadows?: Record<string, string>
}
