'use client'

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// CSS-in-JS style definitions
export interface StyleDefinition {
  id: string
  name: string
  category: 'layout' | 'typography' | 'colors' | 'spacing' | 'effects' | 'responsive'
  properties: Record<string, any>
  cssClass?: string
  mediaQueries?: Record<string, Record<string, any>>
  pseudoClasses?: Record<string, Record<string, any>>
  variables?: Record<string, string>
  isGlobal?: boolean
  isCustom?: boolean
  createdAt: Date
  updatedAt: Date
}

export interface StyleTheme {
  id: string
  name: string
  description: string
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    foreground: string
    muted: string
    border: string
    destructive: string
    warning: string
    success: string
  }
  typography: {
    fontFamily: {
      sans: string[]
      serif: string[]
      mono: string[]
    }
    fontSize: Record<string, string>
    fontWeight: Record<string, number>
    lineHeight: Record<string, number>
    letterSpacing: Record<string, string>
  }
  spacing: Record<string, string>
  borderRadius: Record<string, string>
  shadows: Record<string, string>
  breakpoints: Record<string, string>
  animations: Record<string, string>
}

interface StylesState {
  // Current theme
  currentTheme: StyleTheme
  customThemes: StyleTheme[]
  
  // Style definitions
  styles: StyleDefinition[]
  globalStyles: string
  
  // Component styles
  componentStyles: Record<string, string[]> // componentId -> styleIds
  
  // CSS generation
  generatedCSS: string
  
  // Actions
  setTheme: (theme: StyleTheme) => void
  createCustomTheme: (theme: Omit<StyleTheme, 'id'>) => void
  updateTheme: (id: string, updates: Partial<StyleTheme>) => void
  deleteTheme: (id: string) => void
  
  addStyle: (style: Omit<StyleDefinition, 'id' | 'createdAt' | 'updatedAt'>) => string
  updateStyle: (id: string, updates: Partial<StyleDefinition>) => void
  deleteStyle: (id: string) => void
  duplicateStyle: (id: string) => string
  
  applyStyleToComponent: (componentId: string, styleId: string) => void
  removeStyleFromComponent: (componentId: string, styleId: string) => void
  
  generateCSS: () => void
  exportStyles: () => string
  importStyles: (styles: string) => void
  
  // Responsive utilities
  getResponsiveStyles: (styleId: string) => Record<string, Record<string, any>>
  generateResponsiveCSS: (styles: Record<string, any>, breakpoints: string[]) => string
}

// Default theme based on shadcn/ui
const defaultTheme: StyleTheme = {
  id: 'default',
  name: 'Default Theme',
  description: 'Default shadcn/ui theme',
  colors: {
    primary: 'hsl(222.2 84% 4.9%)',
    secondary: 'hsl(210 40% 96%)',
    accent: 'hsl(210 40% 94%)',
    background: 'hsl(0 0% 100%)',
    foreground: 'hsl(222.2 84% 4.9%)',
    muted: 'hsl(210 40% 96%)',
    border: 'hsl(214.3 31.8% 91.4%)',
    destructive: 'hsl(0 84.2% 60.2%)',
    warning: 'hsl(38 92% 50%)',
    success: 'hsl(142 76% 36%)'
  },
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      serif: ['Georgia', 'serif'],
      mono: ['Fira Code', 'monospace']
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem'
    },
    fontWeight: {
      normal: 400,
      medium: 500,
      semibold: 600,
      bold: 700
    },
    lineHeight: {
      tight: 1.25,
      normal: 1.5,
      relaxed: 1.75
    },
    letterSpacing: {
      tight: '-0.025em',
      normal: '0em',
      wide: '0.025em'
    }
  },
  spacing: {
    '0': '0px',
    '1': '0.25rem',
    '2': '0.5rem',
    '3': '0.75rem',
    '4': '1rem',
    '5': '1.25rem',
    '6': '1.5rem',
    '8': '2rem',
    '10': '2.5rem',
    '12': '3rem',
    '16': '4rem',
    '20': '5rem',
    '24': '6rem'
  },
  borderRadius: {
    none: '0px',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    full: '9999px'
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1)'
  },
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
  },
  animations: {
    'fade-in': 'fadeIn 0.3s ease-in-out',
    'slide-up': 'slideUp 0.3s ease-out',
    'scale-in': 'scaleIn 0.2s ease-out'
  }
}

export const useStylesManager = create<StylesState>()(
  persist(
    (set, get) => ({
      currentTheme: defaultTheme,
      customThemes: [],
      styles: [],
      globalStyles: '',
      componentStyles: {},
      generatedCSS: '',

      setTheme: (theme) => {
        set({ currentTheme: theme })
        get().generateCSS()
      },

      createCustomTheme: (themeData) => {
        const theme: StyleTheme = {
          ...themeData,
          id: `theme_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
        }
        set((state) => ({
          customThemes: [...state.customThemes, theme]
        }))
        return theme.id
      },

      updateTheme: (id, updates) => {
        set((state) => ({
          customThemes: state.customThemes.map(theme =>
            theme.id === id ? { ...theme, ...updates } : theme
          ),
          currentTheme: state.currentTheme.id === id 
            ? { ...state.currentTheme, ...updates }
            : state.currentTheme
        }))
        get().generateCSS()
      },

      deleteTheme: (id) => {
        set((state) => ({
          customThemes: state.customThemes.filter(theme => theme.id !== id)
        }))
      },

      addStyle: (styleData) => {
        const style: StyleDefinition = {
          ...styleData,
          id: `style_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          createdAt: new Date(),
          updatedAt: new Date()
        }
        set((state) => ({
          styles: [...state.styles, style]
        }))
        get().generateCSS()
        return style.id
      },

      updateStyle: (id, updates) => {
        set((state) => ({
          styles: state.styles.map(style =>
            style.id === id 
              ? { ...style, ...updates, updatedAt: new Date() }
              : style
          )
        }))
        get().generateCSS()
      },

      deleteStyle: (id) => {
        set((state) => ({
          styles: state.styles.filter(style => style.id !== id),
          componentStyles: Object.fromEntries(
            Object.entries(state.componentStyles).map(([componentId, styleIds]) => [
              componentId,
              styleIds.filter(styleId => styleId !== id)
            ])
          )
        }))
        get().generateCSS()
      },

      duplicateStyle: (id) => {
        const state = get()
        const originalStyle = state.styles.find(s => s.id === id)
        if (!originalStyle) return ''

        const duplicatedStyle: StyleDefinition = {
          ...originalStyle,
          id: `style_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
          name: `${originalStyle.name} (Copy)`,
          createdAt: new Date(),
          updatedAt: new Date()
        }

        set((state) => ({
          styles: [...state.styles, duplicatedStyle]
        }))
        
        return duplicatedStyle.id
      },

      applyStyleToComponent: (componentId, styleId) => {
        set((state) => ({
          componentStyles: {
            ...state.componentStyles,
            [componentId]: [
              ...(state.componentStyles[componentId] || []),
              styleId
            ].filter((id, index, arr) => arr.indexOf(id) === index) // Remove duplicates
          }
        }))
      },

      removeStyleFromComponent: (componentId, styleId) => {
        set((state) => ({
          componentStyles: {
            ...state.componentStyles,
            [componentId]: (state.componentStyles[componentId] || [])
              .filter(id => id !== styleId)
          }
        }))
      },

      generateCSS: () => {
        const state = get()
        const { currentTheme, styles } = state
        
        let css = ''
        
        // Generate CSS custom properties for theme
        css += ':root {\n'
        Object.entries(currentTheme.colors).forEach(([key, value]) => {
          css += `  --color-${key}: ${value};\n`
        })
        Object.entries(currentTheme.spacing).forEach(([key, value]) => {
          css += `  --spacing-${key}: ${value};\n`
        })
        Object.entries(currentTheme.borderRadius).forEach(([key, value]) => {
          css += `  --radius-${key}: ${value};\n`
        })
        css += '}\n\n'
        
        // Generate styles
        styles.forEach(style => {
          if (style.cssClass) {
            css += `.${style.cssClass} {\n`
            Object.entries(style.properties).forEach(([prop, value]) => {
              css += `  ${prop}: ${value};\n`
            })
            css += '}\n\n'
          }
          
          // Media queries
          if (style.mediaQueries) {
            Object.entries(style.mediaQueries).forEach(([breakpoint, props]) => {
              css += `@media (min-width: ${currentTheme.breakpoints[breakpoint]}) {\n`
              css += `  .${style.cssClass} {\n`
              Object.entries(props).forEach(([prop, value]) => {
                css += `    ${prop}: ${value};\n`
              })
              css += '  }\n'
              css += '}\n\n'
            })
          }
          
          // Pseudo classes
          if (style.pseudoClasses) {
            Object.entries(style.pseudoClasses).forEach(([pseudo, props]) => {
              css += `.${style.cssClass}:${pseudo} {\n`
              Object.entries(props).forEach(([prop, value]) => {
                css += `  ${prop}: ${value};\n`
              })
              css += '}\n\n'
            })
          }
        })
        
        set({ generatedCSS: css })
        
        // Inject CSS into document
        if (typeof document !== 'undefined') {
          let styleElement = document.getElementById('craft-dynamic-styles')
          if (!styleElement) {
            styleElement = document.createElement('style')
            styleElement.id = 'craft-dynamic-styles'
            document.head.appendChild(styleElement)
          }
          styleElement.textContent = css
        }
      },

      exportStyles: () => {
        const state = get()
        return JSON.stringify({
          theme: state.currentTheme,
          customThemes: state.customThemes,
          styles: state.styles,
          componentStyles: state.componentStyles
        }, null, 2)
      },

      importStyles: (stylesJson) => {
        try {
          const imported = JSON.parse(stylesJson)
          set({
            currentTheme: imported.theme || defaultTheme,
            customThemes: imported.customThemes || [],
            styles: imported.styles || [],
            componentStyles: imported.componentStyles || {}
          })
          get().generateCSS()
        } catch (error) {
          console.error('Failed to import styles:', error)
        }
      },

      getResponsiveStyles: (styleId) => {
        const style = get().styles.find(s => s.id === styleId)
        return style?.mediaQueries || {}
      },

      generateResponsiveCSS: (styles, breakpoints) => {
        let css = ''
        breakpoints.forEach(breakpoint => {
          if (styles[breakpoint]) {
            css += `@media (min-width: ${get().currentTheme.breakpoints[breakpoint]}) {\n`
            Object.entries(styles[breakpoint]).forEach(([prop, value]) => {
              css += `  ${prop}: ${value};\n`
            })
            css += '}\n'
          }
        })
        return css
      }
    }),
    {
      name: 'craft-styles-manager',
      version: 1
    }
  )
)

// Utility functions
export const stylesUtils = {
  /**
   * Convert CSS properties object to CSS string
   */
  propertiesToCSS: (properties: Record<string, any>): string => {
    return Object.entries(properties)
      .map(([prop, value]) => `${prop}: ${value}`)
      .join('; ')
  },

  /**
   * Generate CSS class name from style definition
   */
  generateClassName: (styleName: string): string => {
    return `craft-${styleName.toLowerCase().replace(/\s+/g, '-')}`
  },

  /**
   * Merge multiple style objects
   */
  mergeStyles: (...styles: Record<string, any>[]): Record<string, any> => {
    return styles.reduce((merged, style) => ({ ...merged, ...style }), {})
  },

  /**
   * Convert theme colors to CSS custom properties
   */
  themeToCSS: (theme: StyleTheme): string => {
    let css = ':root {\n'
    Object.entries(theme.colors).forEach(([key, value]) => {
      css += `  --color-${key}: ${value};\n`
    })
    css += '}\n'
    return css
  }
}
