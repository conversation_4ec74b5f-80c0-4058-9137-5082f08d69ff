'use client'

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { withCraftNode, createCraftMinimalSettings } from '../hocs'

// Badge variant types
type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline'

// Base Badge component props
interface CraftBadgeProps {
  text: string
  variant: BadgeVariant
  size: 'sm' | 'default' | 'lg'
  className?: string
  style?: React.CSSProperties
}

// Base Badge component (unwrapped)
const BaseBadge: React.FC<CraftBadgeProps> = ({
  text,
  variant,
  size,
  className,
  style,
  ...props
}) => {
  return (
    <Badge
      variant={variant}
      className={cn(
        'craft-badge',
        {
          'text-xs px-2 py-0.5': size === 'sm',
          'text-sm px-2.5 py-0.5': size === 'default',
          'text-base px-3 py-1': size === 'lg'
        },
        className
      )}
      style={style}
      {...props}
    >
      {text}
    </Badge>
  )
}

// Settings component for Badge
const CraftBadgeSettings = createCraftMinimalSettings([
  {
    id: 'text',
    name: 'text',
    label: 'Badge Text',
    type: 'text',
    placeholder: 'Enter badge text...',
    description: 'The text displayed in the badge'
  },
  {
    id: 'variant',
    name: 'variant',
    label: 'Badge Variant',
    type: 'select',
    options: [
      { label: 'Default', value: 'default' },
      { label: 'Secondary', value: 'secondary' },
      { label: 'Destructive', value: 'destructive' },
      { label: 'Outline', value: 'outline' }
    ],
    description: 'The visual style of the badge'
  },
  {
    id: 'size',
    name: 'size',
    label: 'Badge Size',
    type: 'select',
    options: [
      { label: 'Small', value: 'sm' },
      { label: 'Default', value: 'default' },
      { label: 'Large', value: 'lg' }
    ],
    description: 'The size of the badge'
  }
])

// Wrapped Badge component with Craft.js functionality
export const CraftBadge = withCraftNode(BaseBadge, {
  displayName: 'Badge',
  defaultProps: {
    text: 'Badge',
    variant: 'default' as BadgeVariant,
    size: 'default' as const
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
;(CraftBadge as any).craft = {
  ...CraftBadge.craft,
  related: {
    settings: CraftBadgeSettings
  }
}

// Export the settings component separately
export { CraftBadgeSettings }
