'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { withCraftTextNode, createCraftMinimalSettings } from '../hocs'
import { CRAFT_COMPONENT_TYPES, CRAFT_DEFAULT_PROPS } from '../constants'

// Heading level type
type HeadingLevel = 1 | 2 | 3 | 4 | 5 | 6

// Base Heading component props
interface CraftHeadingProps {
  text: string
  level: HeadingLevel
  fontSize: number
  fontWeight: 'normal' | 'medium' | 'semibold' | 'bold'
  color: string
  textAlign: 'left' | 'center' | 'right' | 'justify'
  className?: string
  style?: React.CSSProperties
}

// Base Heading component (unwrapped)
const BaseHeading: React.FC<CraftHeadingProps> = ({
  text,
  level,
  fontSize,
  fontWeight,
  color,
  textAlign,
  className,
  style,
  ...props
}) => {
  const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements

  const headingStyles: React.CSSProperties = {
    fontSize: `${fontSize}px`,
    fontWeight,
    color,
    textAlign,
    margin: 0,
    ...style
  }

  return (
    <HeadingTag
      className={cn('craft-heading', className)}
      style={headingStyles}
      {...props}
    >
      {text}
    </HeadingTag>
  )
}

// Settings component for Heading
const CraftHeadingSettings = createCraftMinimalSettings([
  {
    id: 'text',
    name: 'text',
    label: 'Heading Text',
    type: 'text',
    placeholder: 'Enter heading text...',
    description: 'The heading text content'
  },
  {
    id: 'level',
    name: 'level',
    label: 'Heading Level',
    type: 'select',
    options: [
      { label: 'H1', value: 1 },
      { label: 'H2', value: 2 },
      { label: 'H3', value: 3 },
      { label: 'H4', value: 4 },
      { label: 'H5', value: 5 },
      { label: 'H6', value: 6 }
    ],
    description: 'Semantic heading level'
  },
  {
    id: 'fontSize',
    name: 'fontSize',
    label: 'Font Size',
    type: 'range',
    min: 12,
    max: 72,
    step: 1,
    description: 'Font size in pixels'
  },
  {
    id: 'fontWeight',
    name: 'fontWeight',
    label: 'Font Weight',
    type: 'select',
    options: [
      { label: 'Normal', value: 'normal' },
      { label: 'Medium', value: 'medium' },
      { label: 'Semi Bold', value: 'semibold' },
      { label: 'Bold', value: 'bold' }
    ],
    description: 'Font weight'
  },
  {
    id: 'color',
    name: 'color',
    label: 'Text Color',
    type: 'color',
    description: 'Heading text color'
  },
  {
    id: 'textAlign',
    name: 'textAlign',
    label: 'Text Alignment',
    type: 'select',
    options: [
      { label: 'Left', value: 'left' },
      { label: 'Center', value: 'center' },
      { label: 'Right', value: 'right' },
      { label: 'Justify', value: 'justify' }
    ],
    description: 'Text alignment'
  }
])

// Wrapped Heading component with Craft.js functionality
export const CraftHeading = withCraftTextNode(BaseHeading, {
  displayName: 'Heading',
  defaultProps: CRAFT_DEFAULT_PROPS[CRAFT_COMPONENT_TYPES.HEADING],
  textProp: 'text',
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
CraftHeading.craft = {
  ...CraftHeading.craft,
  related: {
    settings: CraftHeadingSettings
  }
}

// Export the settings component separately
export { CraftHeadingSettings }
