'use client'

import React from 'react'
import { Element } from '@craftjs/core'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { withCraftCanvas, withCraftRestrictedCanvas, createCraftCommonSettings } from '../hocs'
import { CraftText } from './craft-text'
import { CraftButton } from './craft-button'
import { CRAFT_COMPONENT_TYPES, CRAFT_DEFAULT_PROPS } from '../constants'

// Base Card component props
interface CraftCardProps {
  padding: number
  margin: number
  background: string
  borderRadius: number
  border: string
  shadow: string
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}

// Card Header Canvas - only accepts text components
const CraftCardHeader = withCraftRestrictedCanvas(
  ({ children, ...props }: { children?: React.ReactNode }) => (
    <CardHeader {...props}>
      {children}
    </CardHeader>
  ),
  {
    displayName: 'Card Header',
    acceptedTypes: [CRAFT_COMPONENT_TYPES.TEXT, CRAFT_COMPONENT_TYPES.HEADING],
    maxChildren: 2,
    defaultProps: {}
  }
)

// Card Content Canvas - accepts any components
const CraftCardContent = withCraftCanvas(
  ({ children, ...props }: { children?: React.ReactNode }) => (
    <CardContent {...props}>
      {children}
    </CardContent>
  ),
  {
    displayName: 'Card Content',
    defaultProps: {}
  }
)

// Card Footer Canvas - only accepts buttons and text
const CraftCardFooter = withCraftRestrictedCanvas(
  ({ children, ...props }: { children?: React.ReactNode }) => (
    <div className="card-footer p-6 pt-0" {...props}>
      {children}
    </div>
  ),
  {
    displayName: 'Card Footer',
    acceptedTypes: [CRAFT_COMPONENT_TYPES.BUTTON, CRAFT_COMPONENT_TYPES.TEXT],
    maxChildren: 3,
    defaultProps: {}
  }
)

// Base Card component (unwrapped)
const BaseCard: React.FC<CraftCardProps> = ({
  padding,
  margin,
  background,
  borderRadius,
  border,
  shadow,
  className,
  style,
  children,
  ...props
}) => {
  const cardStyles: React.CSSProperties = {
    margin: `${margin}px`,
    background,
    borderRadius: `${borderRadius}px`,
    border,
    boxShadow: shadow,
    ...style
  }

  return (
    <Card
      className={cn('craft-card', className)}
      style={cardStyles}
      {...props}
    >
      {/* Default structure with droppable regions */}
      <Element is={CraftCardHeader} id="header" canvas>
        <CraftText text="Card Title" fontSize={20} fontWeight="semibold" />
        <CraftText text="Card description goes here" fontSize={14} color="#666666" />
      </Element>
      
      <Element is={CraftCardContent} id="content" canvas>
        <CraftText text="This is the card content area. You can add any components here." />
      </Element>
      
      <Element is={CraftCardFooter} id="footer" canvas>
        <CraftButton text="Learn More" variant="default" size="sm" />
      </Element>
      
      {children}
    </Card>
  )
}

// Settings component for Card
const CraftCardSettings = createCraftCommonSettings([
  {
    id: 'padding',
    name: 'padding',
    label: 'Padding',
    type: 'range',
    min: 0,
    max: 100,
    step: 4,
    description: 'Internal spacing in pixels'
  },
  {
    id: 'margin',
    name: 'margin',
    label: 'Margin',
    type: 'range',
    min: 0,
    max: 100,
    step: 4,
    description: 'External spacing in pixels'
  },
  {
    id: 'background',
    name: 'background',
    label: 'Background Color',
    type: 'color',
    description: 'Card background color'
  },
  {
    id: 'borderRadius',
    name: 'borderRadius',
    label: 'Border Radius',
    type: 'range',
    min: 0,
    max: 50,
    step: 1,
    description: 'Corner radius in pixels'
  },
  {
    id: 'border',
    name: 'border',
    label: 'Border',
    type: 'text',
    description: 'Card border style'
  },
  {
    id: 'shadow',
    name: 'shadow',
    label: 'Shadow',
    type: 'text',
    description: 'Card shadow effect'
  }
], [
  {
    title: 'Layout',
    fields: ['padding', 'margin'],
    collapsible: false
  },
  {
    title: 'Appearance',
    fields: ['background', 'borderRadius', 'border', 'shadow'],
    collapsible: true
  },
  {
    title: 'Advanced',
    fields: ['className'],
    collapsible: true
  }
])

// Wrapped Card component with Craft.js functionality
export const CraftCard = withCraftCanvas(BaseCard, {
  displayName: 'Card',
  defaultProps: CRAFT_DEFAULT_PROPS[CRAFT_COMPONENT_TYPES.CARD],
  rules: {
    canDrag: (node) => {
      // Card can be dragged if it's a child of another Canvas
      return node.data.parent && node.data.parent !== 'ROOT'
    },
    canDrop: () => false, // Card has predefined structure, no direct drops
    canMoveIn: () => false, // Components go into specific regions
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
CraftCard.craft = {
  ...CraftCard.craft,
  related: {
    settings: CraftCardSettings
  }
}

// Export all card-related components
export { 
  CraftCardSettings,
  CraftCardHeader,
  CraftCardContent,
  CraftCardFooter
}
