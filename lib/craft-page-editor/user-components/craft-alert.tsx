'use client'

import React from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertTriangle, CheckCircle, Info, XCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { with<PERSON>raftNode, createCraftMinimalSettings } from '../hocs'
import { CRAFT_COMPONENT_TYPES, CRAFT_DEFAULT_PROPS } from '../constants'

// Alert variant types
type AlertVariant = 'default' | 'destructive' | 'success' | 'warning' | 'info'

// Base Alert component props
interface CraftAlertProps {
  title: string
  description: string
  variant: AlertVariant
  showIcon: boolean
  dismissible: boolean
  className?: string
  style?: React.CSSProperties
  onDismiss?: () => void
}

// Icon mapping for variants
const variantIcons = {
  default: Info,
  destructive: XCircle,
  success: CheckCircle,
  warning: AlertTriangle,
  info: Info
}

// Base Alert component (unwrapped)
const BaseAlert: React.FC<CraftAlertProps> = ({
  title,
  description,
  variant,
  showIcon,
  dismissible,
  className,
  style,
  onDismiss,
  ...props
}) => {
  const [isVisible, setIsVisible] = React.useState(true)
  const IconComponent = variantIcons[variant]

  const handleDismiss = () => {
    setIsVisible(false)
    onDismiss?.()
  }

  if (!isVisible) return null

  return (
    <Alert 
      variant={variant === 'success' || variant === 'warning' || variant === 'info' ? 'default' : variant}
      className={cn(
        'craft-alert',
        {
          'border-green-200 bg-green-50 text-green-800': variant === 'success',
          'border-yellow-200 bg-yellow-50 text-yellow-800': variant === 'warning',
          'border-blue-200 bg-blue-50 text-blue-800': variant === 'info',
        },
        className
      )}
      style={style}
      {...props}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-2 flex-1">
          {showIcon && IconComponent && (
            <IconComponent className="h-4 w-4 mt-0.5 flex-shrink-0" />
          )}
          <div className="flex-1">
            {title && <AlertTitle className="mb-1">{title}</AlertTitle>}
            {description && <AlertDescription>{description}</AlertDescription>}
          </div>
        </div>
        
        {dismissible && (
          <button
            onClick={handleDismiss}
            className="ml-2 flex-shrink-0 text-current opacity-50 hover:opacity-100 transition-opacity"
            aria-label="Dismiss alert"
          >
            <XCircle className="h-4 w-4" />
          </button>
        )}
      </div>
    </Alert>
  )
}

// Settings component for Alert
const CraftAlertSettings = createCraftMinimalSettings([
  {
    id: 'title',
    name: 'title',
    label: 'Alert Title',
    type: 'text',
    placeholder: 'Enter alert title...',
    description: 'The title of the alert'
  },
  {
    id: 'description',
    name: 'description',
    label: 'Alert Description',
    type: 'textarea',
    placeholder: 'Enter alert description...',
    description: 'The main content of the alert'
  },
  {
    id: 'variant',
    name: 'variant',
    label: 'Alert Variant',
    type: 'select',
    options: [
      { label: 'Default', value: 'default' },
      { label: 'Destructive', value: 'destructive' },
      { label: 'Success', value: 'success' },
      { label: 'Warning', value: 'warning' },
      { label: 'Info', value: 'info' }
    ],
    description: 'The visual style of the alert'
  },
  {
    id: 'showIcon',
    name: 'showIcon',
    label: 'Show Icon',
    type: 'boolean',
    description: 'Whether to display an icon based on the variant'
  },
  {
    id: 'dismissible',
    name: 'dismissible',
    label: 'Dismissible',
    type: 'boolean',
    description: 'Whether the alert can be dismissed by the user'
  }
])

// Wrapped Alert component with Craft.js functionality
export const CraftAlert = withCraftNode(BaseAlert, {
  displayName: 'Alert',
  defaultProps: {
    title: 'Alert Title',
    description: 'This is an alert message',
    variant: 'default' as AlertVariant,
    showIcon: true,
    dismissible: false
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
;(CraftAlert as any).craft = {
  ...CraftAlert.craft,
  related: {
    settings: CraftAlertSettings
  }
}

// Export the settings component separately
export { CraftAlertSettings }
