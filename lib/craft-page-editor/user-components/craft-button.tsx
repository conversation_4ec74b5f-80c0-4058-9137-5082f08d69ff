'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { withCraftNode, createCraftMinimalSettings } from '../hocs'
import { CRAFT_COMPONENT_TYPES, CRAFT_DEFAULT_PROPS } from '../constants'

// Button variant and size types from shadcn/ui
type ButtonVariant = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
type ButtonSize = 'default' | 'sm' | 'lg' | 'icon'

// Base Button component props
interface CraftButtonProps {
  text: string
  variant: ButtonVariant
  size: ButtonSize
  disabled: boolean
  fullWidth: boolean
  onClick?: () => void
  className?: string
  style?: React.CSSProperties
}

// Base Button component (unwrapped)
const BaseButton: React.FC<CraftButtonProps> = ({
  text,
  variant,
  size,
  disabled,
  fullWidth,
  onClick,
  className,
  style,
  ...props
}) => {
  return (
    <Button
      variant={variant}
      size={size}
      disabled={disabled}
      onClick={onClick}
      className={cn(
        'craft-button',
        {
          'w-full': fullWidth
        },
        className
      )}
      style={style}
      {...props}
    >
      {text}
    </Button>
  )
}

// Settings component for Button
const CraftButtonSettings = createCraftMinimalSettings([
  {
    id: 'text',
    name: 'text',
    label: 'Button Text',
    type: 'text',
    placeholder: 'Enter button text...',
    description: 'The text displayed on the button'
  },
  {
    id: 'variant',
    name: 'variant',
    label: 'Button Variant',
    type: 'select',
    options: [
      { label: 'Default', value: 'default' },
      { label: 'Destructive', value: 'destructive' },
      { label: 'Outline', value: 'outline' },
      { label: 'Secondary', value: 'secondary' },
      { label: 'Ghost', value: 'ghost' },
      { label: 'Link', value: 'link' }
    ],
    description: 'Button style variant'
  },
  {
    id: 'size',
    name: 'size',
    label: 'Button Size',
    type: 'select',
    options: [
      { label: 'Default', value: 'default' },
      { label: 'Small', value: 'sm' },
      { label: 'Large', value: 'lg' },
      { label: 'Icon', value: 'icon' }
    ],
    description: 'Button size'
  },
  {
    id: 'disabled',
    name: 'disabled',
    label: 'Disabled',
    type: 'boolean',
    description: 'Whether the button is disabled'
  },
  {
    id: 'fullWidth',
    name: 'fullWidth',
    label: 'Full Width',
    type: 'boolean',
    description: 'Whether the button takes full width of its container'
  }
])

// Wrapped Button component with Craft.js functionality
export const CraftButton = withCraftNode(BaseButton, {
  displayName: 'Button',
  defaultProps: CRAFT_DEFAULT_PROPS[CRAFT_COMPONENT_TYPES.BUTTON],
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
CraftButton.craft = {
  ...CraftButton.craft,
  related: {
    settings: CraftButtonSettings
  }
}

// Export the settings component separately
export { CraftButtonSettings }
