'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { withCraftCanvas, createCraftCommonSettings } from '../hocs'
import { CRAFT_COMPONENT_TYPES, CRAFT_DEFAULT_PROPS } from '../constants'

// Base Container component props
interface CraftContainerProps {
  padding: number
  margin: number
  background: string
  borderRadius: number
  border: string
  minHeight: number
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}

// Base Container component (unwrapped)
const BaseContainer: React.FC<CraftContainerProps> = ({
  padding,
  margin,
  background,
  borderRadius,
  border,
  minHeight,
  className,
  style,
  children,
  ...props
}) => {
  const containerStyles: React.CSSProperties = {
    padding: `${padding}px`,
    margin: `${margin}px`,
    background,
    borderRadius: `${borderRadius}px`,
    border,
    minHeight: `${minHeight}px`,
    ...style
  }

  return (
    <div
      className={cn('craft-container', className)}
      style={containerStyles}
      {...props}
    >
      {children}
    </div>
  )
}

// Settings component for Container
const CraftContainerSettings = createCraftCommonSettings([
  {
    id: 'padding',
    name: 'padding',
    label: 'Padding',
    type: 'range',
    min: 0,
    max: 100,
    step: 4,
    description: 'Internal spacing in pixels'
  },
  {
    id: 'margin',
    name: 'margin',
    label: 'Margin',
    type: 'range',
    min: 0,
    max: 100,
    step: 4,
    description: 'External spacing in pixels'
  },
  {
    id: 'background',
    name: 'background',
    label: 'Background Color',
    type: 'color',
    description: 'Container background color'
  },
  {
    id: 'borderRadius',
    name: 'borderRadius',
    label: 'Border Radius',
    type: 'range',
    min: 0,
    max: 50,
    step: 1,
    description: 'Corner radius in pixels'
  },
  {
    id: 'border',
    name: 'border',
    label: 'Border',
    type: 'text',
    description: 'Container border style'
  },
  {
    id: 'minHeight',
    name: 'minHeight',
    label: 'Minimum Height',
    type: 'range',
    min: 20,
    max: 500,
    step: 10,
    description: 'Minimum height in pixels'
  }
], [
  {
    title: 'Layout',
    fields: ['padding', 'margin', 'minHeight'],
    collapsible: false
  },
  {
    title: 'Appearance',
    fields: ['background', 'borderRadius', 'border'],
    collapsible: true
  },
  {
    title: 'Advanced',
    fields: ['className'],
    collapsible: true
  }
])

// Wrapped Container component with Craft.js Canvas functionality
export const CraftContainer = withCraftCanvas(BaseContainer, {
  displayName: 'Container',
  defaultProps: CRAFT_DEFAULT_PROPS[CRAFT_COMPONENT_TYPES.CONTAINER],
  rules: {
    canDrag: (node) => {
      // Container can be dragged if it's a child of another Canvas
      return node.data.parent && node.data.parent !== 'ROOT'
    },
    canDrop: () => true, // Container is always droppable
    canMoveIn: () => true, // Accepts any component
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
CraftContainer.craft = {
  ...CraftContainer.craft,
  related: {
    settings: CraftContainerSettings
  }
}

// Export the settings component separately
export { CraftContainerSettings }
