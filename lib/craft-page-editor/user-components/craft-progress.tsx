'use client'

import React from 'react'
import { Progress } from '@/components/ui/progress'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { withCraftNode, createCraftMinimalSettings } from '../hocs'

// Base Progress component props
interface CraftProgressProps {
  label: string
  value: number
  max: number
  showLabel: boolean
  showValue: boolean
  showPercentage: boolean
  size: 'sm' | 'default' | 'lg'
  color: string
  backgroundColor: string
  animated: boolean
  className?: string
  style?: React.CSSProperties
}

// Base Progress component (unwrapped)
const BaseProgress: React.FC<CraftProgressProps> = ({
  label,
  value,
  max,
  showLabel,
  showValue,
  showPercentage,
  size,
  color,
  backgroundColor,
  animated,
  className,
  style,
  ...props
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  const progressId = React.useId()

  const progressStyles: React.CSSProperties = {
    backgroundColor,
    ...style
  }

  const indicatorStyles: React.CSSProperties = {
    backgroundColor: color
  }

  return (
    <div className={cn('craft-progress-wrapper', className)} style={style}>
      {(showLabel && label) || showValue || showPercentage ? (
        <div className="flex justify-between items-center mb-2">
          {showLabel && label && (
            <Label htmlFor={progressId} className="text-sm font-medium">
              {label}
            </Label>
          )}
          
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            {showValue && (
              <span>{value}/{max}</span>
            )}
            {showPercentage && (
              <span>({Math.round(percentage)}%)</span>
            )}
          </div>
        </div>
      ) : null}
      
      <Progress
        id={progressId}
        value={percentage}
        className={cn(
          'craft-progress',
          {
            'h-1': size === 'sm',
            'h-2': size === 'default',
            'h-3': size === 'lg'
          },
          animated && 'transition-all duration-300 ease-in-out',
          className
        )}
        style={progressStyles}
        {...props}
      />
    </div>
  )
}

// Settings component for Progress
const CraftProgressSettings = createCraftMinimalSettings([
  {
    id: 'label',
    name: 'label',
    label: 'Progress Label',
    type: 'text',
    placeholder: 'Enter label...',
    description: 'The label for the progress bar'
  },
  {
    id: 'value',
    name: 'value',
    label: 'Current Value',
    type: 'range',
    min: 0,
    max: 100,
    step: 1,
    description: 'The current progress value'
  },
  {
    id: 'max',
    name: 'max',
    label: 'Maximum Value',
    type: 'number',
    min: 1,
    description: 'The maximum value for the progress bar'
  },
  {
    id: 'showLabel',
    name: 'showLabel',
    label: 'Show Label',
    type: 'boolean',
    description: 'Whether to display the label'
  },
  {
    id: 'showValue',
    name: 'showValue',
    label: 'Show Value',
    type: 'boolean',
    description: 'Whether to display the current/max values'
  },
  {
    id: 'showPercentage',
    name: 'showPercentage',
    label: 'Show Percentage',
    type: 'boolean',
    description: 'Whether to display the percentage'
  },
  {
    id: 'size',
    name: 'size',
    label: 'Size',
    type: 'select',
    options: [
      { label: 'Small', value: 'sm' },
      { label: 'Default', value: 'default' },
      { label: 'Large', value: 'lg' }
    ],
    description: 'The size of the progress bar'
  },
  {
    id: 'color',
    name: 'color',
    label: 'Progress Color',
    type: 'color',
    description: 'The color of the progress indicator'
  },
  {
    id: 'backgroundColor',
    name: 'backgroundColor',
    label: 'Background Color',
    type: 'color',
    description: 'The background color of the progress bar'
  },
  {
    id: 'animated',
    name: 'animated',
    label: 'Animated',
    type: 'boolean',
    description: 'Whether to animate progress changes'
  }
])

// Wrapped Progress component with Craft.js functionality
export const CraftProgress = withCraftNode(BaseProgress, {
  displayName: 'Progress',
  defaultProps: {
    label: 'Progress',
    value: 50,
    max: 100,
    showLabel: true,
    showValue: false,
    showPercentage: true,
    size: 'default' as const,
    color: '#3b82f6',
    backgroundColor: '#e5e7eb',
    animated: true
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
;(CraftProgress as any).craft = {
  ...CraftProgress.craft,
  related: {
    settings: CraftProgressSettings
  }
}

// Export the settings component separately
export { CraftProgressSettings }
