'use client'

import React from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { withCraftNode, createCraftMinimalSettings } from '../hocs'

// Input types
type InputType = 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search'

// Base Input component props
interface CraftInputProps {
  label: string
  placeholder: string
  type: InputType
  value: string
  required: boolean
  disabled: boolean
  showLabel: boolean
  helperText: string
  errorText: string
  className?: string
  style?: React.CSSProperties
  onChange?: (value: string) => void
}

// Base Input component (unwrapped)
const BaseInput: React.FC<CraftInputProps> = ({
  label,
  placeholder,
  type,
  value,
  required,
  disabled,
  showLabel,
  helperText,
  errorText,
  className,
  style,
  onChange,
  ...props
}) => {
  const [inputValue, setInputValue] = React.useState(value || '')
  const [hasError, setHasError] = React.useState(false)
  const inputId = React.useId()

  React.useEffect(() => {
    setInputValue(value || '')
  }, [value])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    onChange?.(newValue)
    
    // Basic validation
    if (required && !newValue.trim()) {
      setHasError(true)
    } else if (type === 'email' && newValue && !isValidEmail(newValue)) {
      setHasError(true)
    } else {
      setHasError(false)
    }
  }

  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  return (
    <div className={cn('craft-input-wrapper', className)} style={style}>
      {showLabel && label && (
        <Label 
          htmlFor={inputId}
          className={cn(
            'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
            required && 'after:content-["*"] after:ml-0.5 after:text-red-500'
          )}
        >
          {label}
        </Label>
      )}
      
      <Input
        id={inputId}
        type={type}
        placeholder={placeholder}
        value={inputValue}
        onChange={handleChange}
        required={required}
        disabled={disabled}
        className={cn(
          'craft-input',
          hasError && 'border-red-500 focus-visible:ring-red-500',
          showLabel && label && 'mt-2'
        )}
        {...props}
      />
      
      {(helperText || errorText || hasError) && (
        <div className="mt-1 text-sm">
          {hasError || errorText ? (
            <span className="text-red-500">
              {errorText || (required && !inputValue.trim() ? 'This field is required' : 'Invalid input')}
            </span>
          ) : helperText ? (
            <span className="text-gray-500">{helperText}</span>
          ) : null}
        </div>
      )}
    </div>
  )
}

// Settings component for Input
const CraftInputSettings = createCraftMinimalSettings([
  {
    id: 'label',
    name: 'label',
    label: 'Input Label',
    type: 'text',
    placeholder: 'Enter label...',
    description: 'The label for the input field'
  },
  {
    id: 'placeholder',
    name: 'placeholder',
    label: 'Placeholder Text',
    type: 'text',
    placeholder: 'Enter placeholder...',
    description: 'Placeholder text shown when input is empty'
  },
  {
    id: 'type',
    name: 'type',
    label: 'Input Type',
    type: 'select',
    options: [
      { label: 'Text', value: 'text' },
      { label: 'Email', value: 'email' },
      { label: 'Password', value: 'password' },
      { label: 'Number', value: 'number' },
      { label: 'Phone', value: 'tel' },
      { label: 'URL', value: 'url' },
      { label: 'Search', value: 'search' }
    ],
    description: 'The type of input field'
  },
  {
    id: 'required',
    name: 'required',
    label: 'Required',
    type: 'boolean',
    description: 'Whether the input is required'
  },
  {
    id: 'disabled',
    name: 'disabled',
    label: 'Disabled',
    type: 'boolean',
    description: 'Whether the input is disabled'
  },
  {
    id: 'showLabel',
    name: 'showLabel',
    label: 'Show Label',
    type: 'boolean',
    description: 'Whether to display the label'
  },
  {
    id: 'helperText',
    name: 'helperText',
    label: 'Helper Text',
    type: 'text',
    placeholder: 'Enter helper text...',
    description: 'Additional help text below the input'
  },
  {
    id: 'errorText',
    name: 'errorText',
    label: 'Error Text',
    type: 'text',
    placeholder: 'Enter error text...',
    description: 'Error message to display'
  }
])

// Wrapped Input component with Craft.js functionality
export const CraftInput = withCraftNode(BaseInput, {
  displayName: 'Input',
  defaultProps: {
    label: 'Input Label',
    placeholder: 'Enter text...',
    type: 'text' as InputType,
    value: '',
    required: false,
    disabled: false,
    showLabel: true,
    helperText: '',
    errorText: ''
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
;(CraftInput as any).craft = {
  ...CraftInput.craft,
  related: {
    settings: CraftInputSettings
  }
}

// Export the settings component separately
export { CraftInputSettings }
