'use client'

import React from 'react'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { withCraftNode, createCraftMinimalSettings } from '../hocs'

// Base Checkbox component props
interface CraftCheckboxProps {
  label: string
  checked: boolean
  disabled: boolean
  required: boolean
  helperText: string
  errorText: string
  className?: string
  style?: React.CSSProperties
  onChange?: (checked: boolean) => void
}

// Base Checkbox component (unwrapped)
const BaseCheckbox: React.FC<CraftCheckboxProps> = ({
  label,
  checked,
  disabled,
  required,
  helperText,
  errorText,
  className,
  style,
  onChange,
  ...props
}) => {
  const [isChecked, setIsChecked] = React.useState(checked || false)
  const [hasError, setHasError] = React.useState(false)
  const checkboxId = React.useId()

  React.useEffect(() => {
    setIsChecked(checked || false)
  }, [checked])

  const handleCheckedChange = (newChecked: boolean) => {
    setIsChecked(newChecked)
    onChange?.(newChecked)
    
    // Basic validation
    if (required && !newChecked) {
      setHasError(true)
    } else {
      setHasError(false)
    }
  }

  return (
    <div className={cn('craft-checkbox-wrapper', className)} style={style}>
      <div className="flex items-start space-x-2">
        <Checkbox
          id={checkboxId}
          checked={isChecked}
          onCheckedChange={handleCheckedChange}
          disabled={disabled}
          required={required}
          className={cn(
            'craft-checkbox',
            hasError && 'border-red-500 data-[state=checked]:bg-red-500'
          )}
          {...props}
        />
        
        {label && (
          <div className="grid gap-1.5 leading-none">
            <Label 
              htmlFor={checkboxId}
              className={cn(
                'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
                required && 'after:content-["*"] after:ml-0.5 after:text-red-500'
              )}
            >
              {label}
            </Label>
            
            {(helperText || errorText || hasError) && (
              <div className="text-sm">
                {hasError || errorText ? (
                  <span className="text-red-500">
                    {errorText || (required && !isChecked ? 'This field is required' : 'Invalid selection')}
                  </span>
                ) : helperText ? (
                  <span className="text-gray-500">{helperText}</span>
                ) : null}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Settings component for Checkbox
const CraftCheckboxSettings = createCraftMinimalSettings([
  {
    id: 'label',
    name: 'label',
    label: 'Checkbox Label',
    type: 'text',
    placeholder: 'Enter label...',
    description: 'The label for the checkbox'
  },
  {
    id: 'checked',
    name: 'checked',
    label: 'Checked',
    type: 'boolean',
    description: 'Whether the checkbox is checked by default'
  },
  {
    id: 'disabled',
    name: 'disabled',
    label: 'Disabled',
    type: 'boolean',
    description: 'Whether the checkbox is disabled'
  },
  {
    id: 'required',
    name: 'required',
    label: 'Required',
    type: 'boolean',
    description: 'Whether the checkbox is required'
  },
  {
    id: 'helperText',
    name: 'helperText',
    label: 'Helper Text',
    type: 'text',
    placeholder: 'Enter helper text...',
    description: 'Additional help text below the checkbox'
  },
  {
    id: 'errorText',
    name: 'errorText',
    label: 'Error Text',
    type: 'text',
    placeholder: 'Enter error text...',
    description: 'Error message to display'
  }
])

// Wrapped Checkbox component with Craft.js functionality
export const CraftCheckbox = withCraftNode(BaseCheckbox, {
  displayName: 'Checkbox',
  defaultProps: {
    label: 'Checkbox Label',
    checked: false,
    disabled: false,
    required: false,
    helperText: '',
    errorText: ''
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
;(CraftCheckbox as any).craft = {
  ...CraftCheckbox.craft,
  related: {
    settings: CraftCheckboxSettings
  }
}

// Export the settings component separately
export { CraftCheckboxSettings }
