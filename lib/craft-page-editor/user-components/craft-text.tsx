'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { withCraftTextNode, createCraftMinimalSettings } from '../hocs'
import { CRAFT_COMPONENT_TYPES, CRAFT_DEFAULT_PROPS } from '../constant'

// Base Text component props
interface CraftTextProps {
  text: string
  fontSize: number
  fontWeight: 'normal' | 'medium' | 'semibold' | 'bold'
  color: string
  textAlign: 'left' | 'center' | 'right' | 'justify'
  lineHeight: number
  className?: string
  style?: React.CSSProperties
}

// Base Text component (unwrapped)
const BaseText: React.FC<CraftTextProps> = ({
  text,
  fontSize,
  fontWeight,
  color,
  textAlign,
  lineHeight,
  className,
  style,
  ...props
}) => {
  const textStyles: React.CSSProperties = {
    fontSize: `${fontSize}px`,
    fontWeight,
    color,
    textAlign,
    lineHeight,
    ...style
  }

  return (
    <p
      className={cn('craft-text', className)}
      style={textStyles}
      {...props}
    >
      {text}
    </p>
  )
}

// Settings component for Text
const CraftTextSettings = createCraftMinimalSettings([
  {
    id: 'text',
    name: 'text',
    label: 'Text Content',
    type: 'textarea',
    placeholder: 'Enter your text...',
    description: 'The text content to display'
  },
  {
    id: 'fontSize',
    name: 'fontSize',
    label: 'Font Size',
    type: 'range',
    min: 8,
    max: 72,
    step: 1,
    description: 'Font size in pixels'
  },
  {
    id: 'fontWeight',
    name: 'fontWeight',
    label: 'Font Weight',
    type: 'select',
    options: [
      { label: 'Normal', value: 'normal' },
      { label: 'Medium', value: 'medium' },
      { label: 'Semi Bold', value: 'semibold' },
      { label: 'Bold', value: 'bold' }
    ],
    description: 'Font weight'
  },
  {
    id: 'color',
    name: 'color',
    label: 'Text Color',
    type: 'color',
    description: 'Text color'
  },
  {
    id: 'textAlign',
    name: 'textAlign',
    label: 'Text Alignment',
    type: 'select',
    options: [
      { label: 'Left', value: 'left' },
      { label: 'Center', value: 'center' },
      { label: 'Right', value: 'right' },
      { label: 'Justify', value: 'justify' }
    ],
    description: 'Text alignment'
  },
  {
    id: 'lineHeight',
    name: 'lineHeight',
    label: 'Line Height',
    type: 'range',
    min: 0.8,
    max: 3,
    step: 0.1,
    description: 'Line height multiplier'
  }
])

// Wrapped Text component with Craft.js functionality
export const CraftText = withCraftTextNode(BaseText, {
  displayName: 'Text',
  defaultProps: CRAFT_DEFAULT_PROPS[CRAFT_COMPONENT_TYPES.TEXT],
  textProp: 'text',
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
CraftText.craft = {
  ...CraftText.craft,
  related: {
    settings: CraftTextSettings
  }
}

// Export the settings component separately for use in other contexts
export { CraftTextSettings }
