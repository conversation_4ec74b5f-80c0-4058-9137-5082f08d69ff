'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { withCraftNode, createCraftMinimalSettings } from '../hocs'
import { CRAFT_COMPONENT_TYPES, CRAFT_DEFAULT_PROPS } from '../constants'

// Base Image component props
interface CraftImageProps {
  src: string
  alt: string
  width: number
  height: number
  objectFit: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down'
  borderRadius: number
  className?: string
  style?: React.CSSProperties
}

// Base Image component (unwrapped)
const BaseImage: React.FC<CraftImageProps> = ({
  src,
  alt,
  width,
  height,
  objectFit,
  borderRadius,
  className,
  style,
  ...props
}) => {
  const imageStyles: React.CSSProperties = {
    width: `${width}px`,
    height: `${height}px`,
    objectFit,
    borderRadius: `${borderRadius}px`,
    ...style
  }

  return (
    <img
      src={src}
      alt={alt}
      className={cn('craft-image', className)}
      style={imageStyles}
      {...props}
    />
  )
}

// Settings component for Image
const CraftImageSettings = createCraftMinimalSettings([
  {
    id: 'src',
    name: 'src',
    label: 'Image URL',
    type: 'text',
    placeholder: 'Enter image URL...',
    description: 'The image source URL'
  },
  {
    id: 'alt',
    name: 'alt',
    label: 'Alt Text',
    type: 'text',
    placeholder: 'Enter alt text...',
    description: 'Alternative text for accessibility'
  },
  {
    id: 'width',
    name: 'width',
    label: 'Width',
    type: 'range',
    min: 50,
    max: 800,
    step: 10,
    description: 'Image width in pixels'
  },
  {
    id: 'height',
    name: 'height',
    label: 'Height',
    type: 'range',
    min: 50,
    max: 600,
    step: 10,
    description: 'Image height in pixels'
  },
  {
    id: 'objectFit',
    name: 'objectFit',
    label: 'Object Fit',
    type: 'select',
    options: [
      { label: 'Cover', value: 'cover' },
      { label: 'Contain', value: 'contain' },
      { label: 'Fill', value: 'fill' },
      { label: 'None', value: 'none' },
      { label: 'Scale Down', value: 'scale-down' }
    ],
    description: 'How the image should fit within its container'
  },
  {
    id: 'borderRadius',
    name: 'borderRadius',
    label: 'Border Radius',
    type: 'range',
    min: 0,
    max: 50,
    step: 1,
    description: 'Corner radius in pixels'
  }
])

// Wrapped Image component with Craft.js functionality
export const CraftImage = withCraftNode(BaseImage, {
  displayName: 'Image',
  defaultProps: CRAFT_DEFAULT_PROPS[CRAFT_COMPONENT_TYPES.IMAGE],
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
CraftImage.craft = {
  ...CraftImage.craft,
  related: {
    settings: CraftImageSettings
  }
}

// Export the settings component separately
export { CraftImageSettings }
