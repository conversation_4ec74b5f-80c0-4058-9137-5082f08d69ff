'use client'

import React from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { withCraftNode, createCraftMinimalSettings } from '../hocs'

// Option type for select
interface SelectOption {
  label: string
  value: string
  disabled?: boolean
}

// Base Select component props
interface CraftSelectProps {
  label: string
  placeholder: string
  value: string
  options: SelectOption[]
  required: boolean
  disabled: boolean
  showLabel: boolean
  helperText: string
  errorText: string
  className?: string
  style?: React.CSSProperties
  onChange?: (value: string) => void
}

// Base Select component (unwrapped)
const BaseSelect: React.FC<CraftSelectProps> = ({
  label,
  placeholder,
  value,
  options,
  required,
  disabled,
  showLabel,
  helperText,
  errorText,
  className,
  style,
  onChange,
  ...props
}) => {
  const [selectValue, setSelectValue] = React.useState(value || '')
  const [hasError, setHasError] = React.useState(false)
  const selectId = React.useId()

  React.useEffect(() => {
    setSelectValue(value || '')
  }, [value])

  const handleValueChange = (newValue: string) => {
    setSelectValue(newValue)
    onChange?.(newValue)
    
    // Basic validation
    if (required && !newValue) {
      setHasError(true)
    } else {
      setHasError(false)
    }
  }

  return (
    <div className={cn('craft-select-wrapper', className)} style={style}>
      {showLabel && label && (
        <Label 
          htmlFor={selectId}
          className={cn(
            'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
            required && 'after:content-["*"] after:ml-0.5 after:text-red-500'
          )}
        >
          {label}
        </Label>
      )}
      
      <Select
        value={selectValue}
        onValueChange={handleValueChange}
        disabled={disabled}
        required={required}
      >
        <SelectTrigger 
          id={selectId}
          className={cn(
            'craft-select',
            hasError && 'border-red-500 focus:ring-red-500',
            showLabel && label && 'mt-2'
          )}
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option, index) => (
            <SelectItem 
              key={index} 
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {(helperText || errorText || hasError) && (
        <div className="mt-1 text-sm">
          {hasError || errorText ? (
            <span className="text-red-500">
              {errorText || (required && !selectValue ? 'Please select an option' : 'Invalid selection')}
            </span>
          ) : helperText ? (
            <span className="text-gray-500">{helperText}</span>
          ) : null}
        </div>
      )}
    </div>
  )
}

// Settings component for Select
const CraftSelectSettings = createCraftMinimalSettings([
  {
    id: 'label',
    name: 'label',
    label: 'Select Label',
    type: 'text',
    placeholder: 'Enter label...',
    description: 'The label for the select field'
  },
  {
    id: 'placeholder',
    name: 'placeholder',
    label: 'Placeholder Text',
    type: 'text',
    placeholder: 'Enter placeholder...',
    description: 'Placeholder text shown when no option is selected'
  },
  {
    id: 'options',
    name: 'options',
    label: 'Options',
    type: 'key-value',
    description: 'The options available in the select dropdown',
    defaultValue: [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' }
    ]
  },
  {
    id: 'required',
    name: 'required',
    label: 'Required',
    type: 'boolean',
    description: 'Whether the select is required'
  },
  {
    id: 'disabled',
    name: 'disabled',
    label: 'Disabled',
    type: 'boolean',
    description: 'Whether the select is disabled'
  },
  {
    id: 'showLabel',
    name: 'showLabel',
    label: 'Show Label',
    type: 'boolean',
    description: 'Whether to display the label'
  },
  {
    id: 'helperText',
    name: 'helperText',
    label: 'Helper Text',
    type: 'text',
    placeholder: 'Enter helper text...',
    description: 'Additional help text below the select'
  },
  {
    id: 'errorText',
    name: 'errorText',
    label: 'Error Text',
    type: 'text',
    placeholder: 'Enter error text...',
    description: 'Error message to display'
  }
])

// Wrapped Select component with Craft.js functionality
export const CraftSelect = withCraftNode(BaseSelect, {
  displayName: 'Select',
  defaultProps: {
    label: 'Select Label',
    placeholder: 'Choose an option...',
    value: '',
    options: [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' }
    ],
    required: false,
    disabled: false,
    showLabel: true,
    helperText: '',
    errorText: ''
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
;(CraftSelect as any).craft = {
  ...CraftSelect.craft,
  related: {
    settings: CraftSelectSettings
  }
}

// Export the settings component separately
export { CraftSelectSettings }
