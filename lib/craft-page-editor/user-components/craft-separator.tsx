'use client'

import React from 'react'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { withCraftNode, createCraftMinimalSettings } from '../hocs'

// Base Separator component props
interface CraftSeparatorProps {
  orientation: 'horizontal' | 'vertical'
  thickness: number
  color: string
  style: 'solid' | 'dashed' | 'dotted'
  spacing: number
  className?: string
  style?: React.CSSProperties
}

// Base Separator component (unwrapped)
const BaseSeparator: React.FC<CraftSeparatorProps> = ({
  orientation,
  thickness,
  color,
  style: lineStyle,
  spacing,
  className,
  style,
  ...props
}) => {
  const separatorStyles: React.CSSProperties = {
    backgroundColor: orientation === 'horizontal' ? color : undefined,
    borderColor: orientation === 'vertical' ? color : undefined,
    height: orientation === 'horizontal' ? `${thickness}px` : undefined,
    width: orientation === 'vertical' ? `${thickness}px` : undefined,
    borderStyle: lineStyle,
    margin: orientation === 'horizontal' 
      ? `${spacing}px 0` 
      : `0 ${spacing}px`,
    ...style
  }

  return (
    <Separator
      orientation={orientation}
      className={cn(
        'craft-separator',
        orientation === 'horizontal' && 'w-full',
        orientation === 'vertical' && 'h-full',
        className
      )}
      style={separatorStyles}
      {...props}
    />
  )
}

// Settings component for Separator
const CraftSeparatorSettings = createCraftMinimalSettings([
  {
    id: 'orientation',
    name: 'orientation',
    label: 'Orientation',
    type: 'select',
    options: [
      { label: 'Horizontal', value: 'horizontal' },
      { label: 'Vertical', value: 'vertical' }
    ],
    description: 'The orientation of the separator'
  },
  {
    id: 'thickness',
    name: 'thickness',
    label: 'Thickness',
    type: 'range',
    min: 1,
    max: 10,
    step: 1,
    description: 'The thickness of the separator in pixels'
  },
  {
    id: 'color',
    name: 'color',
    label: 'Color',
    type: 'color',
    description: 'The color of the separator'
  },
  {
    id: 'style',
    name: 'style',
    label: 'Line Style',
    type: 'select',
    options: [
      { label: 'Solid', value: 'solid' },
      { label: 'Dashed', value: 'dashed' },
      { label: 'Dotted', value: 'dotted' }
    ],
    description: 'The style of the separator line'
  },
  {
    id: 'spacing',
    name: 'spacing',
    label: 'Spacing',
    type: 'range',
    min: 0,
    max: 50,
    step: 2,
    description: 'The spacing around the separator in pixels'
  }
])

// Wrapped Separator component with Craft.js functionality
export const CraftSeparator = withCraftNode(BaseSeparator, {
  displayName: 'Separator',
  defaultProps: {
    orientation: 'horizontal' as const,
    thickness: 1,
    color: '#e5e7eb',
    style: 'solid' as const,
    spacing: 16
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
;(CraftSeparator as any).craft = {
  ...CraftSeparator.craft,
  related: {
    settings: CraftSeparatorSettings
  }
}

// Export the settings component separately
export { CraftSeparatorSettings }
