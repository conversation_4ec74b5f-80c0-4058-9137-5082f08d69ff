'use client'

import React from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'
import { withCraftNode, createCraftMinimalSettings } from '../hocs'

// Base Textarea component props
interface CraftTextareaProps {
  label: string
  placeholder: string
  value: string
  rows: number
  maxLength?: number
  required: boolean
  disabled: boolean
  showLabel: boolean
  showCharCount: boolean
  helperText: string
  errorText: string
  className?: string
  style?: React.CSSProperties
  onChange?: (value: string) => void
}

// Base Textarea component (unwrapped)
const BaseTextarea: React.FC<CraftTextareaProps> = ({
  label,
  placeholder,
  value,
  rows,
  maxLength,
  required,
  disabled,
  showLabel,
  showCharCount,
  helperText,
  errorText,
  className,
  style,
  onChange,
  ...props
}) => {
  const [textValue, setTextValue] = React.useState(value || '')
  const [hasError, setHasError] = React.useState(false)
  const textareaId = React.useId()

  React.useEffect(() => {
    setTextValue(value || '')
  }, [value])

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    
    // Check max length
    if (maxLength && newValue.length > maxLength) {
      return
    }
    
    setTextValue(newValue)
    onChange?.(newValue)
    
    // Basic validation
    if (required && !newValue.trim()) {
      setHasError(true)
    } else {
      setHasError(false)
    }
  }

  const charCount = textValue.length
  const isNearLimit = maxLength && charCount > maxLength * 0.8

  return (
    <div className={cn('craft-textarea-wrapper', className)} style={style}>
      {showLabel && label && (
        <Label 
          htmlFor={textareaId}
          className={cn(
            'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
            required && 'after:content-["*"] after:ml-0.5 after:text-red-500'
          )}
        >
          {label}
        </Label>
      )}
      
      <Textarea
        id={textareaId}
        placeholder={placeholder}
        value={textValue}
        onChange={handleChange}
        rows={rows}
        maxLength={maxLength}
        required={required}
        disabled={disabled}
        className={cn(
          'craft-textarea',
          hasError && 'border-red-500 focus-visible:ring-red-500',
          showLabel && label && 'mt-2'
        )}
        {...props}
      />
      
      <div className="flex justify-between items-start mt-1">
        <div className="flex-1">
          {(helperText || errorText || hasError) && (
            <div className="text-sm">
              {hasError || errorText ? (
                <span className="text-red-500">
                  {errorText || (required && !textValue.trim() ? 'This field is required' : 'Invalid input')}
                </span>
              ) : helperText ? (
                <span className="text-gray-500">{helperText}</span>
              ) : null}
            </div>
          )}
        </div>
        
        {showCharCount && maxLength && (
          <div className={cn(
            'text-xs ml-2 flex-shrink-0',
            isNearLimit ? 'text-orange-500' : 'text-gray-400',
            charCount === maxLength && 'text-red-500'
          )}>
            {charCount}/{maxLength}
          </div>
        )}
      </div>
    </div>
  )
}

// Settings component for Textarea
const CraftTextareaSettings = createCraftMinimalSettings([
  {
    id: 'label',
    name: 'label',
    label: 'Textarea Label',
    type: 'text',
    placeholder: 'Enter label...',
    description: 'The label for the textarea field'
  },
  {
    id: 'placeholder',
    name: 'placeholder',
    label: 'Placeholder Text',
    type: 'text',
    placeholder: 'Enter placeholder...',
    description: 'Placeholder text shown when textarea is empty'
  },
  {
    id: 'rows',
    name: 'rows',
    label: 'Rows',
    type: 'range',
    min: 2,
    max: 20,
    step: 1,
    description: 'Number of visible text lines'
  },
  {
    id: 'maxLength',
    name: 'maxLength',
    label: 'Max Length',
    type: 'number',
    min: 0,
    description: 'Maximum number of characters allowed'
  },
  {
    id: 'required',
    name: 'required',
    label: 'Required',
    type: 'boolean',
    description: 'Whether the textarea is required'
  },
  {
    id: 'disabled',
    name: 'disabled',
    label: 'Disabled',
    type: 'boolean',
    description: 'Whether the textarea is disabled'
  },
  {
    id: 'showLabel',
    name: 'showLabel',
    label: 'Show Label',
    type: 'boolean',
    description: 'Whether to display the label'
  },
  {
    id: 'showCharCount',
    name: 'showCharCount',
    label: 'Show Character Count',
    type: 'boolean',
    description: 'Whether to display character count'
  },
  {
    id: 'helperText',
    name: 'helperText',
    label: 'Helper Text',
    type: 'text',
    placeholder: 'Enter helper text...',
    description: 'Additional help text below the textarea'
  },
  {
    id: 'errorText',
    name: 'errorText',
    label: 'Error Text',
    type: 'text',
    placeholder: 'Enter error text...',
    description: 'Error message to display'
  }
])

// Wrapped Textarea component with Craft.js functionality
export const CraftTextarea = withCraftNode(BaseTextarea, {
  displayName: 'Textarea',
  defaultProps: {
    label: 'Textarea Label',
    placeholder: 'Enter your message...',
    value: '',
    rows: 4,
    maxLength: undefined,
    required: false,
    disabled: false,
    showLabel: true,
    showCharCount: false,
    helperText: '',
    errorText: ''
  },
  rules: {
    canDrag: () => true,
    canDrop: () => false,
    canMoveIn: () => false,
    canMoveOut: () => true
  }
})

// Add settings to craft configuration
;(CraftTextarea as any).craft = {
  ...CraftTextarea.craft,
  related: {
    settings: CraftTextareaSettings
  }
}

// Export the settings component separately
export { CraftTextareaSettings }
