'use client'

import { Node, NodeId } from '@craftjs/core'

/**
 * Utility functions for Craft.js Page Editor
 */
export const craftUtils = {
  /**
   * Generate a unique ID for components
   */
  generateId: (): string => {
    return `craft_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  },

  /**
   * Deep clone an object
   */
  deepClone: <T>(obj: T): T => {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T
    if (obj instanceof Array) return obj.map(item => craftUtils.deepClone(item)) as unknown as T
    if (typeof obj === 'object') {
      const clonedObj = {} as T
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = craftUtils.deepClone(obj[key])
        }
      }
      return clonedObj
    }
    return obj
  },

  /**
   * Merge objects deeply
   */
  deepMerge: <T extends Record<string, any>>(target: T, source: Partial<T>): T => {
    const result = { ...target }
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const sourceValue = source[key]
        const targetValue = result[key]
        
        if (
          sourceValue && 
          typeof sourceValue === 'object' && 
          !Array.isArray(sourceValue) &&
          targetValue &&
          typeof targetValue === 'object' &&
          !Array.isArray(targetValue)
        ) {
          result[key] = craftUtils.deepMerge(targetValue, sourceValue)
        } else {
          result[key] = sourceValue as T[Extract<keyof T, string>]
        }
      }
    }
    
    return result
  },

  /**
   * Get nested property value from object
   */
  getNestedValue: (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined
    }, obj)
  },

  /**
   * Set nested property value in object
   */
  setNestedValue: (obj: any, path: string, value: any): void => {
    const keys = path.split('.')
    const lastKey = keys.pop()
    
    if (!lastKey) return
    
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {}
      }
      return current[key]
    }, obj)
    
    target[lastKey] = value
  },

  /**
   * Validate component props against schema
   */
  validateProps: (props: Record<string, any>, schema: Record<string, any>): boolean => {
    try {
      for (const key in schema) {
        const schemaValue = schema[key]
        const propValue = props[key]
        
        if (schemaValue.required && (propValue === undefined || propValue === null)) {
          return false
        }
        
        if (propValue !== undefined && schemaValue.type) {
          const expectedType = schemaValue.type
          const actualType = typeof propValue
          
          if (expectedType !== actualType) {
            return false
          }
        }
      }
      return true
    } catch (error) {
      console.error('Props validation error:', error)
      return false
    }
  },

  /**
   * Sanitize HTML content
   */
  sanitizeHtml: (html: string): string => {
    // Basic HTML sanitization - in production, use a proper library like DOMPurify
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
  },

  /**
   * Format file size
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  /**
   * Debounce function
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func(...args), wait)
    }
  },

  /**
   * Throttle function
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean
    
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  /**
   * Check if component is a Canvas
   */
  isCanvas: (node: Node): boolean => {
    return node.data.custom?.isCanvas === true
  },

  /**
   * Check if component accepts certain types
   */
  acceptsType: (node: Node, type: string): boolean => {
    const acceptedTypes = node.data.custom?.acceptedTypes
    if (!acceptedTypes || !Array.isArray(acceptedTypes)) return true
    return acceptedTypes.includes(type)
  },

  /**
   * Get component display name
   */
  getDisplayName: (node: Node): string => {
    return String(node.data.displayName || node.data.type || 'Component')
  },

  /**
   * Check if node has children
   */
  hasChildren: (node: Node): boolean => {
    return node.data.nodes && node.data.nodes.length > 0
  },

  /**
   * Get node depth in tree
   */
  getNodeDepth: (nodeId: NodeId, nodes: Record<NodeId, Node>): number => {
    let depth = 0
    let currentId = nodeId
    
    while (currentId && currentId !== 'ROOT') {
      const node = nodes[currentId]
      if (!node || !node.data.parent) break
      currentId = node.data.parent
      depth++
    }
    
    return depth
  },

  /**
   * Convert CSS value to pixels
   */
  toPx: (value: string | number): number => {
    if (typeof value === 'number') return value
    if (typeof value === 'string') {
      const num = parseFloat(value)
      if (value.includes('rem')) return num * 16 // Assuming 1rem = 16px
      if (value.includes('em')) return num * 16 // Simplified
      return num
    }
    return 0
  },

  /**
   * Generate CSS from style object
   */
  generateCSS: (styles: Record<string, any>): string => {
    return Object.entries(styles)
      .map(([key, value]) => {
        const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase()
        return `${cssKey}: ${value};`
      })
      .join(' ')
  },

  /**
   * Parse CSS string to object
   */
  parseCSS: (cssString: string): Record<string, string> => {
    const styles: Record<string, string> = {}
    
    cssString.split(';').forEach(rule => {
      const [property, value] = rule.split(':').map(s => s.trim())
      if (property && value) {
        const camelKey = property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
        styles[camelKey] = value
      }
    })
    
    return styles
  }
}

export default craftUtils
