'use client'

// Core Craft.js exports
export { Editor, Frame, Element, useNode, useEditor } from '@craftjs/core'

// Main Editor Components
export { CraftPageEditor, SimpleCraftPageEditor } from './components/craft-page-editor'
export { CraftToolbox, CraftToolboxCompact } from './components/craft-toolbox'
export { CraftSettingsPanel, CraftSettingsPanelCompact } from './components/craft-settings-panel'
export { CraftTopbar } from './components/craft-topbar'
export { AIToolbar } from './components/ai-toolbar'
export { TemplateSelector } from './components/template-selector'
export { StylesManager } from './components/styles-manager'
export { DataManager } from './components/data-manager'

// User Components (Shadcn/ui wrapped for Craft.js)
export { CraftButton } from './user-components/craft-button'
export { CraftText } from './user-components/craft-text'
export { CraftContainer } from './user-components/craft-container'
export { <PERSON>Card, CraftCardHeader, <PERSON><PERSON><PERSON><PERSON>ontent, CraftCardFooter } from './user-components/craft-card'
export { CraftImage } from './user-components/craft-image'
export { CraftHeading } from './user-components/craft-heading'
export { CraftAlert } from './user-components/craft-alert'
export { CraftBadge } from './user-components/craft-badge'
export { CraftInput } from './user-components/craft-input'
export { CraftTextarea } from './user-components/craft-textarea'
export { CraftSelect } from './user-components/craft-select'
export { CraftCheckbox } from './user-components/craft-checkbox'
export { CraftSeparator } from './user-components/craft-separator'
export { CraftProgress } from './user-components/craft-progress'

// HOCs (Higher Order Components)
export { withCraftNode, withCraftTextNode, withCraftStaticNode } from './hocs'
export { withCraftCanvas, withCraftRestrictedCanvas } from './hocs'
export { withCraftSettings, createCraftCommonSettings, createCraftMinimalSettings } from './hocs'

// Hooks
export { useCraftEditor } from './hooks/use-craft-editor'
export { useAIAssistant } from './hooks/use-ai-assistant'

// AI & Data Management
export { craftAI, aiHelpers } from './ai/ai-assistant'
export { useDataManager, dataFetcher, dataUtils } from './data/data-manager'
export { useStylesManager, stylesUtils } from './styles/styles-manager'

// Templates
export { pageTemplates, templateCategories, templateHelpers } from './templates/page-templates'

// Types (exported from individual modules)
// export type { ... } from './types' - Types available from individual modules

// Utils
export { craftUtils } from './utils/craft-utils'

// Constants
export { CRAFT_COMPONENT_TYPES, CRAFT_COMPONENT_CATEGORIES } from './constants'
export { CRAFT_DEFAULT_PROPS } from './constants'
