'use client'

import { generateObject, generateText } from 'ai'
import { openai } from '@ai-sdk/openai'
import { z } from 'zod'

// AI-powered component generation schema
const ComponentGenerationSchema = z.object({
  componentType: z.enum(['text', 'button', 'container', 'card', 'heading', 'image']),
  props: z.record(z.any()),
  children: z.array(z.any()).optional(),
  reasoning: z.string()
})

const LayoutSuggestionSchema = z.object({
  layout: z.enum(['single-column', 'two-column', 'three-column', 'hero-section', 'grid']),
  components: z.array(ComponentGenerationSchema),
  description: z.string()
})

const PageStructureSchema = z.object({
  sections: z.array(z.object({
    type: z.enum(['header', 'hero', 'content', 'features', 'testimonials', 'footer']),
    layout: LayoutSuggestionSchema,
    priority: z.number()
  })),
  theme: z.object({
    primaryColor: z.string(),
    secondaryColor: z.string(),
    fontFamily: z.string(),
    spacing: z.enum(['compact', 'normal', 'spacious'])
  }),
  metadata: z.object({
    title: z.string(),
    description: z.string(),
    keywords: z.array(z.string())
  })
})

export class CraftAIAssistant {
  private model = openai('gpt-4-turbo')

  /**
   * Generate component suggestions based on user intent
   */
  async generateComponent(
    intent: string,
    context: {
      existingComponents?: string[]
      pageType?: string
      targetAudience?: string
    } = {}
  ) {
    try {
      const { object } = await generateObject({
        model: this.model,
        schema: ComponentGenerationSchema,
        prompt: `
          Generate a component for a page builder based on this user intent: "${intent}"
          
          Context:
          - Existing components: ${context.existingComponents?.join(', ') || 'none'}
          - Page type: ${context.pageType || 'general'}
          - Target audience: ${context.targetAudience || 'general'}
          
          Available component types: text, button, container, card, heading, image
          
          Provide appropriate props that would make this component effective for the user's intent.
          Consider accessibility, user experience, and modern design principles.
        `
      })

      return object
    } catch (error) {
      console.error('AI component generation failed:', error)
      return null
    }
  }

  /**
   * Suggest layout improvements for existing page structure
   */
  async suggestLayoutImprovements(
    currentLayout: any,
    goals: string[] = []
  ) {
    try {
      const { object } = await generateObject({
        model: this.model,
        schema: LayoutSuggestionSchema,
        prompt: `
          Analyze this current page layout and suggest improvements:
          ${JSON.stringify(currentLayout, null, 2)}
          
          Goals: ${goals.join(', ')}
          
          Suggest a better layout structure that:
          1. Improves user experience
          2. Follows modern design principles
          3. Enhances conversion potential
          4. Maintains accessibility standards
          5. Is mobile-responsive
        `
      })

      return object
    } catch (error) {
      console.error('AI layout suggestion failed:', error)
      return null
    }
  }

  /**
   * Generate complete page structure from description
   */
  async generatePageStructure(
    description: string,
    requirements: {
      industry?: string
      purpose?: string
      features?: string[]
      constraints?: string[]
    } = {}
  ) {
    try {
      const { object } = await generateObject({
        model: this.model,
        schema: PageStructureSchema,
        prompt: `
          Create a complete page structure for: "${description}"
          
          Requirements:
          - Industry: ${requirements.industry || 'general'}
          - Purpose: ${requirements.purpose || 'informational'}
          - Required features: ${requirements.features?.join(', ') || 'none specified'}
          - Constraints: ${requirements.constraints?.join(', ') || 'none'}
          
          Generate a modern, professional page structure with:
          1. Logical section hierarchy
          2. Appropriate component choices
          3. Cohesive theme and styling
          4. SEO-friendly metadata
          5. Conversion-optimized layout
          
          Prioritize sections by importance (1 = highest priority).
        `
      })

      return object
    } catch (error) {
      console.error('AI page structure generation failed:', error)
      return null
    }
  }

  /**
   * Optimize component properties for better performance and UX
   */
  async optimizeComponentProps(
    componentType: string,
    currentProps: Record<string, any>,
    context: {
      deviceType?: 'mobile' | 'tablet' | 'desktop'
      performanceGoals?: string[]
      accessibilityLevel?: 'basic' | 'enhanced' | 'full'
    } = {}
  ) {
    try {
      const { text } = await generateText({
        model: this.model,
        prompt: `
          Optimize these component properties for better performance and user experience:
          
          Component Type: ${componentType}
          Current Props: ${JSON.stringify(currentProps, null, 2)}
          
          Context:
          - Device Type: ${context.deviceType || 'responsive'}
          - Performance Goals: ${context.performanceGoals?.join(', ') || 'general optimization'}
          - Accessibility Level: ${context.accessibilityLevel || 'enhanced'}
          
          Provide optimized props as JSON with explanations for each change.
          Focus on:
          1. Performance optimization
          2. Accessibility improvements
          3. Mobile responsiveness
          4. User experience enhancements
          5. Modern design standards
        `
      })

      try {
        return JSON.parse(text)
      } catch {
        return { suggestions: text }
      }
    } catch (error) {
      console.error('AI props optimization failed:', error)
      return null
    }
  }

  /**
   * Generate content suggestions for components
   */
  async generateContent(
    componentType: string,
    context: {
      topic?: string
      tone?: 'professional' | 'casual' | 'friendly' | 'authoritative'
      length?: 'short' | 'medium' | 'long'
      audience?: string
    } = {}
  ) {
    try {
      const { text } = await generateText({
        model: this.model,
        prompt: `
          Generate appropriate content for a ${componentType} component.
          
          Context:
          - Topic: ${context.topic || 'general'}
          - Tone: ${context.tone || 'professional'}
          - Length: ${context.length || 'medium'}
          - Target Audience: ${context.audience || 'general'}
          
          Provide content that is:
          1. Engaging and relevant
          2. Appropriate for the component type
          3. Optimized for the target audience
          4. Clear and actionable
          5. SEO-friendly when applicable
          
          For buttons: provide compelling call-to-action text
          For headings: provide clear, descriptive titles
          For text: provide informative, well-structured content
          For images: provide descriptive alt text and captions
        `
      })

      return text.trim()
    } catch (error) {
      console.error('AI content generation failed:', error)
      return null
    }
  }

  /**
   * Analyze page performance and suggest improvements
   */
  async analyzePagePerformance(
    pageStructure: any,
    metrics: {
      loadTime?: number
      interactionTime?: number
      accessibilityScore?: number
      seoScore?: number
    } = {}
  ) {
    try {
      const { text } = await generateText({
        model: this.model,
        prompt: `
          Analyze this page structure and performance metrics, then suggest improvements:
          
          Page Structure: ${JSON.stringify(pageStructure, null, 2)}
          
          Performance Metrics:
          - Load Time: ${metrics.loadTime || 'not measured'}ms
          - Interaction Time: ${metrics.interactionTime || 'not measured'}ms
          - Accessibility Score: ${metrics.accessibilityScore || 'not measured'}/100
          - SEO Score: ${metrics.seoScore || 'not measured'}/100
          
          Provide specific, actionable recommendations for:
          1. Performance optimization
          2. Accessibility improvements
          3. SEO enhancements
          4. User experience improvements
          5. Conversion optimization
          
          Prioritize recommendations by impact and implementation difficulty.
        `
      })

      return text
    } catch (error) {
      console.error('AI performance analysis failed:', error)
      return null
    }
  }
}

// Singleton instance
export const craftAI = new CraftAIAssistant()

// Helper functions for common AI operations
export const aiHelpers = {
  /**
   * Quick component generation with smart defaults
   */
  async quickGenerate(intent: string) {
    return await craftAI.generateComponent(intent, {
      pageType: 'landing',
      targetAudience: 'general'
    })
  },

  /**
   * Smart content generation based on component context
   */
  async smartContent(componentType: string, existingContent?: string) {
    const context = this.inferContextFromContent(existingContent)
    return await craftAI.generateContent(componentType, context)
  },

  /**
   * Infer context from existing content
   */
  inferContextFromContent(content?: string) {
    if (!content) return {}
    
    // Simple heuristics to infer context
    const wordCount = content.split(' ').length
    const hasExclamation = content.includes('!')
    const hasQuestion = content.includes('?')
    
    return {
      length: wordCount < 10 ? 'short' : wordCount < 50 ? 'medium' : 'long',
      tone: hasExclamation ? 'friendly' : hasQuestion ? 'casual' : 'professional'
    }
  }
}
