'use client'

import React, { forwardRef, ComponentType } from 'react'
import { useNode } from '@craftjs/core'
import { cn } from '@/lib/utils'
import { WithCraftCanvasConfig, CraftCanvasProps } from '../types'

/**
 * Higher Order Component that wraps components to make them droppable Canvas regions
 * Following Craft.js Canvas Node pattern exactly
 */
export function withCraftCanvas<P extends Record<string, any>>(
  WrappedComponent: ComponentType<P>,
  config: WithCraftCanvasConfig = {}
) {
  const CraftCanvasComponent = forwardRef<any, P & CraftCanvasProps>((props, ref) => {
    const {
      connectors: { connect, drag },
      isSelected,
      isHovered,
      isDragging,
      nodeId
    } = useNode((state) => ({
      isSelected: state.events.selected,
      isHovered: state.events.hovered,
      isDragging: state.events.dragged,
      nodeId: state.id
    }))

    // Merge props with default props from config
    const mergedProps = {
      ...config.defaultProps,
      ...props
    }

    // Add craft-specific classes for Canvas styling
    const craftClasses = cn(
      'craft-canvas',
      'craft-droppable',
      {
        'craft-canvas--selected': isSelected,
        'craft-canvas--hovered': isHovered,
        'craft-canvas--dragging': isDragging,
        'craft-canvas--empty': !props.children,
        'craft-canvas--has-children': !!props.children,
      },
      props.className
    )

    // Create the ref callback that combines connect and drag for Canvas
    const canvasRef = (element: HTMLElement | null) => {
      if (element) {
        // For Canvas nodes, connect defines the droppable area
        // drag makes the canvas itself draggable if it's a child of another canvas
        connect(drag(element))
        if (ref) {
          if (typeof ref === 'function') {
            ref(element)
          } else {
            ref.current = element
          }
        }
      }
    }

    // Add empty state placeholder when no children
    const renderChildren = () => {
      if (!props.children) {
        return (
          <div className="craft-canvas-placeholder">
            <div className="craft-canvas-placeholder-content">
              <div className="craft-canvas-placeholder-icon">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M12 5v14M5 12h14" />
                </svg>
              </div>
              <p className="craft-canvas-placeholder-text">
                Drop components here
              </p>
            </div>
          </div>
        )
      }
      return props.children
    }

    return (
      <WrappedComponent
        {...mergedProps}
        ref={canvasRef}
        className={craftClasses}
        data-craft-canvas-id={nodeId}
        data-craft-droppable={true}
        data-craft-selected={isSelected}
        data-craft-hovered={isHovered}
        data-craft-dragging={isDragging}
        data-craft-accepted-types={config.acceptedTypes?.join(',')}
        data-craft-max-children={config.maxChildren}
      >
        {renderChildren()}
      </WrappedComponent>
    )
  })

  // Set display name for debugging
  CraftCanvasComponent.displayName = config.displayName || `withCraftCanvas(${WrappedComponent.displayName || WrappedComponent.name})`

  // Add craft configuration for Canvas
  ;(CraftCanvasComponent as any).craft = {
    props: config.defaultProps || {},
    rules: {
      canDrag: (node: any) => {
        // Canvas can be dragged if it's a child of another Canvas
        return node.data.parent && node.data.parent !== 'ROOT'
      },
      canDrop: () => true, // Canvas is always droppable
      canMoveIn: (incomingNodes: any[]) => {
        // Check if incoming nodes are accepted types
        if (config.acceptedTypes && config.acceptedTypes.length > 0) {
          return incomingNodes.every((node: any) =>
            config.acceptedTypes!.includes(node.data.type)
          )
        }

        // Check max children limit
        if (config.maxChildren) {
          // This would need access to current children count
          // Implementation depends on Craft.js internal state
          return true // Simplified for now
        }

        return true
      },
      canMoveOut: () => true,
      ...config.rules
    },
    displayName: config.displayName || WrappedComponent.displayName || WrappedComponent.name,
    custom: {
      isCanvas: true,
      acceptedTypes: config.acceptedTypes || [],
      maxChildren: config.maxChildren,
      droppableId: config.droppableId
    }
  }

  return CraftCanvasComponent as any
}

/**
 * HOC for creating restricted Canvas regions
 * Only accepts specific component types
 */
export function withCraftRestrictedCanvas<P extends Record<string, any>>(
  WrappedComponent: ComponentType<P>,
  config: WithCraftCanvasConfig & {
    acceptedTypes: string[]
    rejectMessage?: string
  }
) {
  const { acceptedTypes, rejectMessage = 'This component is not allowed here' } = config

  const CraftRestrictedCanvasComponent = forwardRef<HTMLElement, P & CraftCanvasProps>((props, ref) => {
    const {
      connectors: { connect, drag },
      isSelected,
      isHovered,
      isDragging
    } = useNode((state) => ({
      isSelected: state.events.selected,
      isHovered: state.events.hovered,
      isDragging: state.events.dragged
    }))

    const mergedProps = {
      ...config.defaultProps,
      ...props
    }

    const craftClasses = cn(
      'craft-canvas',
      'craft-restricted-canvas',
      {
        'craft-canvas--selected': isSelected,
        'craft-canvas--hovered': isHovered,
        'craft-canvas--dragging': isDragging,
        'craft-canvas--empty': !props.children,
      },
      props.className
    )

    const canvasRef = (element: HTMLElement | null) => {
      if (element) {
        connect(drag(element))
        if (ref) {
          if (typeof ref === 'function') {
            ref(element)
          } else {
            ref.current = element
          }
        }
      }
    }

    const renderChildren = () => {
      if (!props.children) {
        return (
          <div className="craft-canvas-placeholder craft-canvas-placeholder--restricted">
            <div className="craft-canvas-placeholder-content">
              <div className="craft-canvas-placeholder-icon">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M12 5v14M5 12h14" />
                </svg>
              </div>
              <p className="craft-canvas-placeholder-text">
                Drop {acceptedTypes.join(', ')} here
              </p>
            </div>
          </div>
        )
      }
      return props.children
    }

    return (
      <WrappedComponent
        {...mergedProps}
        ref={canvasRef}
        className={craftClasses}
        data-craft-restricted-canvas={true}
        data-craft-accepted-types={acceptedTypes.join(',')}
      >
        {renderChildren()}
      </WrappedComponent>
    )
  })

  CraftRestrictedCanvasComponent.displayName = `withCraftRestrictedCanvas(${WrappedComponent.displayName || WrappedComponent.name})`

  ;(CraftRestrictedCanvasComponent as any).craft = {
    props: config.defaultProps || {},
    rules: {
      canDrag: (node: any) => node.data.parent && node.data.parent !== 'ROOT',
      canDrop: () => true,
      canMoveIn: (incomingNodes: any[]) => {
        return incomingNodes.every((node: any) => acceptedTypes.includes(node.data.type))
      },
      canMoveOut: () => true,
      ...config.rules
    },
    displayName: config.displayName || WrappedComponent.displayName || WrappedComponent.name,
    custom: {
      isCanvas: true,
      isRestricted: true,
      acceptedTypes,
      rejectMessage,
      maxChildren: config.maxChildren
    }
  }

  return CraftRestrictedCanvasComponent as any
}
