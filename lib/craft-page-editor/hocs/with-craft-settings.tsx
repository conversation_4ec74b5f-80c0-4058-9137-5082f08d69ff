'use client'

import React, { ComponentType } from 'react'
import { useNode } from '@craftjs/core'
import { CustomFieldRenderer } from '@/lib/core/builders/components/properties-panel/custom-fields'
import { FieldConfig, FieldValue } from '@/lib/core/builders/components/properties-panel/custom-fields/types'
import { WithCraftSettingsConfig } from '../types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui/button'
import { Trash2 } from 'lucide-react'

/**
 * Higher Order Component that creates settings panels for Craft.js components
 * Integrates with existing custom fields system
 */
export function withCraftSettings<P extends Record<string, any>>(
  config: WithCraftSettingsConfig
) {
  const CraftSettingsComponent: ComponentType = () => {
    const {
      actions: { setProp },
      id: nodeId,
      props,
      isDeletable
    } = useNode((node) => ({
      props: node.data.props,
      isDeletable: true // Simplified for now
    }))

    // Mock delete function for now
    const deleteNode = () => {
      console.log('Delete node:', nodeId)
    }

    // Handle field value changes
    const handleFieldChange = (fieldName: string, value: FieldValue) => {
      setProp((props: any) => {
        // Handle nested property paths (e.g., 'style.color')
        const keys = fieldName.split('.')
        let current = props
        
        for (let i = 0; i < keys.length - 1; i++) {
          if (!current[keys[i]]) {
            current[keys[i]] = {}
          }
          current = current[keys[i]]
        }
        
        current[keys[keys.length - 1]] = value
      })
    }

    // Get current field value from props
    const getFieldValue = (fieldName: string): FieldValue => {
      const keys = fieldName.split('.')
      let current = props
      
      for (const key of keys) {
        if (current && typeof current === 'object' && key in current) {
          current = current[key]
        } else {
          return undefined
        }
      }
      
      return current
    }

    // Handle component deletion
    const handleDelete = () => {
      if (isDeletable) {
        deleteNode()
      }
    }

    // Render fields by section
    const renderSection = (section: any, sectionIndex: number) => {
      const sectionFields = config.fields.filter(field => 
        section.fields.includes(field.name)
      )

      if (sectionFields.length === 0) return null

      return (
        <div key={sectionIndex} className="craft-settings-section">
          <div className="craft-settings-section-header">
            <h4 className="text-sm font-medium text-gray-900">
              {section.title}
            </h4>
          </div>
          
          <div className="craft-settings-section-content space-y-4">
            {sectionFields.map((field, fieldIndex) => (
              <div key={fieldIndex} className="craft-settings-field">
                <CustomFieldRenderer
                  config={field}
                  value={getFieldValue(field.name)}
                  onChange={(value) => handleFieldChange(field.name, value)}
                />
              </div>
            ))}
          </div>
          
          {sectionIndex < (config.sections?.length || 1) - 1 && (
            <Separator className="my-4" />
          )}
        </div>
      )
    }

    // Render all fields if no sections defined
    const renderAllFields = () => {
      return (
        <div className="craft-settings-fields space-y-4">
          {config.fields.map((field, index) => (
            <div key={index} className="craft-settings-field">
              <CustomFieldRenderer
                config={field}
                value={getFieldValue(field.name)}
                onChange={(value) => handleFieldChange(field.name, value)}
              />
            </div>
          ))}
        </div>
      )
    }

    return (
      <Card className="craft-settings-panel">
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Component Settings</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Render sections or all fields */}
          {config.sections && config.sections.length > 0 
            ? config.sections.map(renderSection)
            : renderAllFields()
          }
          
          {/* Delete button */}
          {isDeletable && (
            <>
              <Separator className="my-4" />
              <div className="craft-settings-actions">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleDelete}
                  className="w-full"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete Component
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    )
  }

  CraftSettingsComponent.displayName = 'CraftSettingsComponent'

  return CraftSettingsComponent
}

/**
 * Creates a settings component with predefined common fields
 */
export function createCraftCommonSettings(
  additionalFields: FieldConfig[] = [],
  sections?: WithCraftSettingsConfig['sections']
): ComponentType {
  // Common fields that most components need
  const commonFields: FieldConfig[] = [
    {
      id: 'className',
      name: 'className',
      label: 'CSS Classes',
      type: 'text',
      placeholder: 'Enter CSS classes...',
      description: 'Additional CSS classes to apply'
    },
    {
      id: 'style.margin',
      name: 'style.margin',
      label: 'Margin',
      type: 'spacing',
      description: 'Component margin'
    },
    {
      id: 'style.padding',
      name: 'style.padding',
      label: 'Padding',
      type: 'spacing',
      description: 'Component padding'
    },
    {
      id: 'style.background',
      name: 'style.background',
      label: 'Background',
      type: 'color',
      description: 'Background color'
    },
    {
      id: 'style.borderRadius',
      name: 'style.borderRadius',
      label: 'Border Radius',
      type: 'range',
      min: 0,
      max: 50,
      step: 1,
      description: 'Border radius in pixels'
    },
    {
      id: 'style.opacity',
      name: 'style.opacity',
      label: 'Opacity',
      type: 'range',
      min: 0,
      max: 1,
      step: 0.1,
      description: 'Component opacity'
    }
  ]

  const allFields = [...commonFields, ...additionalFields]

  const defaultSections = sections || [
    {
      title: 'Content',
      fields: additionalFields.map(f => f.name),
      collapsible: false
    },
    {
      title: 'Appearance',
      fields: ['style.background', 'style.borderRadius', 'style.opacity'],
      collapsible: true
    },
    {
      title: 'Spacing',
      fields: ['style.margin', 'style.padding'],
      collapsible: true
    },
    {
      title: 'Advanced',
      fields: ['className'],
      collapsible: true
    }
  ]

  return withCraftSettings({
    fields: allFields,
    sections: defaultSections
  })
}

/**
 * Creates a minimal settings component with only essential fields
 */
export function createCraftMinimalSettings(
  fields: FieldConfig[]
): ComponentType {
  const fieldsWithIds = fields.map(field => ({
    ...field,
    id: field.id || field.name,
  }))

  return withCraftSettings({
    fields: [
      ...fieldsWithIds,
      {
        id: 'className',
        name: 'className',
        label: 'CSS Classes',
        type: 'text',
        placeholder: 'Enter CSS classes...'
      }
    ]
  })
}
