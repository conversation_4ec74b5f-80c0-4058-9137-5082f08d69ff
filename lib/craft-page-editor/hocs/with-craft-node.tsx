'use client'

import React, { forwardRef, ComponentType } from 'react'
import { useNode, Node } from '@craftjs/core'
import { cn } from '@/lib/utils'
import { WithCraftNodeConfig, CraftNodeProps, CraftUserComponent } from '../types'

/**
 * Higher Order Component that wraps any component to make it work with Craft.js
 * Provides drag/drop functionality and node management
 */
export function withCraftNode<P extends Record<string, any>>(
  WrappedComponent: ComponentType<P>,
  config: WithCraftNodeConfig = {}
) {
  const CraftNodeComponent = forwardRef<any, P & CraftNodeProps>((props, ref) => {
    const {
      connectors: { connect, drag },
      isSelected,
      isHovered,
      isDragging,
      nodeId
    } = useNode((state) => ({
      isSelected: state.events.selected,
      isHovered: state.events.hovered,
      isDragging: state.events.dragged,
      nodeId: state.id
    }))

    // Merge props with default props from config
    const mergedProps = {
      ...config.defaultProps,
      ...props
    }

    // Add craft-specific classes for styling
    const craftClasses = cn(
      'craft-node',
      {
        'craft-node--selected': isSelected,
        'craft-node--hovered': isHovered,
        'craft-node--dragging': isDragging,
      },
      props.className
    )

    // Create the ref callback that combines connect and drag
    const nodeRef = (element: HTMLElement | null) => {
      if (element) {
        connect(drag(element))
        if (ref) {
          if (typeof ref === 'function') {
            ref(element)
          } else {
            ref.current = element
          }
        }
      }
    }

    return (
      <WrappedComponent
        {...mergedProps}
        ref={nodeRef}
        className={craftClasses}
        data-craft-node-id={nodeId}
        data-craft-selected={isSelected}
        data-craft-hovered={isHovered}
        data-craft-dragging={isDragging}
      />
    )
  })

  // Set display name for debugging
  CraftNodeComponent.displayName = config.displayName || `withCraftNode(${WrappedComponent.displayName || WrappedComponent.name})`

  // Add craft configuration
  ;(CraftNodeComponent as any).craft = {
    props: config.defaultProps || {},
    rules: config.rules || {
      canDrag: () => true,
      canDrop: () => true,
      canMoveIn: () => true,
      canMoveOut: () => true
    },
    displayName: config.displayName || WrappedComponent.displayName || WrappedComponent.name
  }

  return CraftNodeComponent as any
}

/**
 * HOC specifically for text-editable components
 * Adds content editing capabilities
 */
export function withCraftTextNode<P extends Record<string, any>>(
  WrappedComponent: ComponentType<P>,
  config: WithCraftNodeConfig & {
    textProp?: string
    editableSelector?: string
  } = {}
) {
  const { textProp = 'text', editableSelector = '[contenteditable]' } = config

  const CraftTextNodeComponent = forwardRef<HTMLElement, P & CraftNodeProps>((props, ref) => {
    const {
      connectors: { connect, drag },
      actions: { setProp },
      isSelected,
      isHovered,
      isDragging
    } = useNode((state) => ({
      isSelected: state.events.selected,
      isHovered: state.events.hovered,
      isDragging: state.events.dragged
    }))

    const [isEditing, setIsEditing] = React.useState(false)

    // Enable editing when component is selected and clicked
    React.useEffect(() => {
      if (!isSelected) {
        setIsEditing(false)
      }
    }, [isSelected])

    const handleClick = (e: React.MouseEvent) => {
      if (isSelected && !isEditing) {
        setIsEditing(true)
      }
      props.onClick?.(e)
    }

    const handleTextChange = (newText: string) => {
      setProp((props: any) => {
        props[textProp] = newText
      })
    }

    const mergedProps = {
      ...config.defaultProps,
      ...props,
      onClick: handleClick,
      contentEditable: isEditing,
      suppressContentEditableWarning: true,
      onBlur: () => setIsEditing(false),
      onInput: (e: React.FormEvent<HTMLElement>) => {
        const target = e.target as HTMLElement
        handleTextChange(target.innerText || target.textContent || '')
      }
    }

    const craftClasses = cn(
      'craft-text-node',
      {
        'craft-text-node--editing': isEditing,
        'craft-node--selected': isSelected,
        'craft-node--hovered': isHovered,
        'craft-node--dragging': isDragging,
      },
      props.className
    )

    const nodeRef = (element: HTMLElement | null) => {
      if (element) {
        connect(drag(element))
        if (ref) {
          if (typeof ref === 'function') {
            ref(element)
          } else {
            ref.current = element
          }
        }
      }
    }

    return (
      <WrappedComponent
        {...mergedProps}
        ref={nodeRef}
        className={craftClasses}
      />
    )
  })

  CraftTextNodeComponent.displayName = `withCraftTextNode(${WrappedComponent.displayName || WrappedComponent.name})`

  ;(CraftTextNodeComponent as any).craft = {
    props: config.defaultProps || {},
    rules: config.rules || {
      canDrag: () => true,
      canDrop: () => false,
      canMoveIn: () => false,
      canMoveOut: () => true
    },
    displayName: config.displayName || WrappedComponent.displayName || WrappedComponent.name
  }

  return CraftTextNodeComponent as any
}

/**
 * HOC for components that should not be draggable but can contain other components
 * Useful for layout components that should stay in place
 */
export function withCraftStaticNode<P extends Record<string, any>>(
  WrappedComponent: ComponentType<P>,
  config: WithCraftNodeConfig = {}
) {
  const CraftStaticNodeComponent = forwardRef<HTMLElement, P & CraftNodeProps>((props, ref) => {
    const {
      connectors: { connect },
      isSelected,
      isHovered
    } = useNode((state) => ({
      isSelected: state.events.selected,
      isHovered: state.events.hovered
    }))

    const mergedProps = {
      ...config.defaultProps,
      ...props
    }

    const craftClasses = cn(
      'craft-static-node',
      {
        'craft-node--selected': isSelected,
        'craft-node--hovered': isHovered,
      },
      props.className
    )

    const nodeRef = (element: HTMLElement | null) => {
      if (element) {
        connect(element)
        if (ref) {
          if (typeof ref === 'function') {
            ref(element)
          } else {
            ref.current = element
          }
        }
      }
    }

    return (
      <WrappedComponent
        {...mergedProps}
        ref={nodeRef}
        className={craftClasses}
      />
    )
  })

  CraftStaticNodeComponent.displayName = `withCraftStaticNode(${WrappedComponent.displayName || WrappedComponent.name})`

  ;(CraftStaticNodeComponent as any).craft = {
    props: config.defaultProps || {},
    rules: {
      canDrag: () => false,
      canDrop: () => false,
      canMoveIn: () => false,
      canMoveOut: () => false,
      ...config.rules
    },
    displayName: config.displayName || WrappedComponent.displayName || WrappedComponent.name
  }

  return CraftStaticNodeComponent as any
}
