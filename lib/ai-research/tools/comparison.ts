// Comparison Tools
// Tools for comparing different sources, claims, and information

import { tool } from 'ai'
import { z } from 'zod'
import { ComparisonResult, ComparisonDimension, SearchResult } from '../types'
import { webSearchTool } from './web-search'
import { contentAnalysisTool } from './content-analysis'

/**
 * Content Comparison Tool
 * Compare multiple sources or pieces of content
 */
export const contentComparisonTool = tool({
  description: 'Compare multiple sources or pieces of content across various dimensions',
  parameters: z.object({
    subjects: z.array(z.string()).describe('Subjects to compare'),
    dimensions: z.array(z.string()).optional().describe('Specific dimensions to compare'),
    sources: z.array(z.string().url()).optional().describe('Specific sources to analyze'),
    searchForSources: z.boolean().default(true).describe('Search for sources if not provided'),
    maxSourcesPerSubject: z.number().default(5).describe('Maximum sources per subject'),
    includeAnalysis: z.boolean().default(true).describe('Include detailed content analysis')
  }),
  execute: async ({ 
    subjects, 
    dimensions, 
    sources, 
    searchForSources,
    maxSourcesPerSubject,
    includeAnalysis 
  }) => {
    if (subjects.length < 2) {
      throw new Error('At least 2 subjects are required for comparison')
    }

    try {
      // Gather sources for each subject
      const subjectSources: Record<string, SearchResult[]> = {}
      
      if (searchForSources) {
        for (const subject of subjects) {
          const searchResults = await webSearchTool.execute({
            query: subject,
            maxResults: maxSourcesPerSubject,
            providers: ['google'],
            includeContent: includeAnalysis
          })
          
          subjectSources[subject] = searchResults.results || []
        }
      } else if (sources) {
        // Distribute provided sources among subjects
        subjects.forEach((subject, index) => {
          subjectSources[subject] = sources
            .slice(index * maxSourcesPerSubject, (index + 1) * maxSourcesPerSubject)
            .map(url => ({
              title: url,
              url,
              snippet: '',
              source: 'provided'
            }))
        })
      }

      // Analyze content if requested
      const subjectAnalyses: Record<string, any[]> = {}
      if (includeAnalysis) {
        for (const [subject, sources] of Object.entries(subjectSources)) {
          subjectAnalyses[subject] = []
          
          for (const source of sources.slice(0, 3)) { // Limit analysis
            try {
              const analysis = await contentAnalysisTool.execute({
                url: source.url,
                analysisDepth: 'basic',
                includeTopics: true,
                includeSentiment: true,
                includeCredibility: true
              })
              
              subjectAnalyses[subject].push(analysis.analysis)
            } catch (error) {
              console.warn(`Analysis failed for ${source.url}:`, error)
            }
          }
        }
      }

      // Perform comparison across dimensions
      const comparisonDimensions = await generateComparisonDimensions(
        subjects,
        subjectSources,
        subjectAnalyses,
        dimensions
      )

      // Generate overall comparison summary
      const summary = generateComparisonSummary(subjects, comparisonDimensions)

      // Determine winner if applicable
      const winner = determineWinner(comparisonDimensions)

      // Calculate confidence
      const confidence = calculateComparisonConfidence(comparisonDimensions, subjectSources)

      return {
        subjects,
        dimensions: comparisonDimensions,
        summary,
        winner,
        confidence,
        metadata: {
          totalSources: Object.values(subjectSources).flat().length,
          analysisIncluded: includeAnalysis,
          timestamp: new Date().toISOString()
        }
      } as ComparisonResult
    } catch (error) {
      throw new Error(`Comparison failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Side-by-Side Comparison Tool
 * Generate detailed side-by-side comparisons
 */
export const sideBySideComparisonTool = tool({
  description: 'Generate detailed side-by-side comparisons with structured output',
  parameters: z.object({
    item1: z.object({
      name: z.string(),
      url: z.string().url().optional(),
      description: z.string().optional()
    }).describe('First item to compare'),
    item2: z.object({
      name: z.string(),
      url: z.string().url().optional(),
      description: z.string().optional()
    }).describe('Second item to compare'),
    comparisonCriteria: z.array(z.string()).describe('Criteria for comparison'),
    includeScoring: z.boolean().default(true).describe('Include numerical scoring'),
    weightCriteria: z.boolean().default(false).describe('Apply weights to criteria')
  }),
  execute: async ({ 
    item1, 
    item2, 
    comparisonCriteria,
    includeScoring,
    weightCriteria 
  }) => {
    try {
      // Gather information about each item
      const item1Info = await gatherItemInformation(item1)
      const item2Info = await gatherItemInformation(item2)

      // Compare across each criterion
      const criteriaComparisons = []
      
      for (const criterion of comparisonCriteria) {
        const comparison = await compareByCriterion(
          item1Info,
          item2Info,
          criterion,
          includeScoring
        )
        
        criteriaComparisons.push(comparison)
      }

      // Calculate overall scores if scoring is enabled
      let overallScores = null
      if (includeScoring) {
        overallScores = calculateOverallScores(criteriaComparisons, weightCriteria)
      }

      // Generate recommendations
      const recommendations = generateRecommendations(criteriaComparisons, overallScores)

      return {
        items: [item1, item2],
        criteriaComparisons,
        overallScores,
        recommendations,
        winner: overallScores ? 
          (overallScores.item1 > overallScores.item2 ? item1.name : item2.name) : 
          null,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`Side-by-side comparison failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Competitive Analysis Tool
 * Analyze competitors across multiple dimensions
 */
export const competitiveAnalysisTool = tool({
  description: 'Perform competitive analysis across multiple competitors and dimensions',
  parameters: z.object({
    competitors: z.array(z.string()).describe('List of competitors to analyze'),
    analysisDimensions: z.array(z.enum(['pricing', 'features', 'market_share', 'customer_satisfaction', 'innovation', 'reputation'])).describe('Dimensions for analysis'),
    includeMarketData: z.boolean().default(true).describe('Include market data and trends'),
    timeframe: z.enum(['current', 'past_year', 'past_quarter']).default('current').describe('Analysis timeframe'),
    outputFormat: z.enum(['detailed', 'summary', 'matrix']).default('detailed').describe('Output format')
  }),
  execute: async ({ 
    competitors, 
    analysisDimensions, 
    includeMarketData,
    timeframe,
    outputFormat 
  }) => {
    try {
      const competitorData: Record<string, any> = {}

      // Gather data for each competitor
      for (const competitor of competitors) {
        competitorData[competitor] = await gatherCompetitorData(
          competitor,
          analysisDimensions,
          timeframe
        )
      }

      // Analyze each dimension
      const dimensionAnalysis: Record<string, any> = {}
      
      for (const dimension of analysisDimensions) {
        dimensionAnalysis[dimension] = await analyzeDimension(
          competitors,
          competitorData,
          dimension
        )
      }

      // Include market data if requested
      let marketData = null
      if (includeMarketData) {
        marketData = await gatherMarketData(competitors, timeframe)
      }

      // Format output based on requested format
      const formattedOutput = formatCompetitiveAnalysis(
        competitors,
        competitorData,
        dimensionAnalysis,
        marketData,
        outputFormat
      )

      return {
        competitors,
        dimensions: analysisDimensions,
        analysis: formattedOutput,
        marketData,
        insights: generateCompetitiveInsights(dimensionAnalysis),
        recommendations: generateCompetitiveRecommendations(dimensionAnalysis),
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`Competitive analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

// Helper functions

async function generateComparisonDimensions(
  subjects: string[],
  subjectSources: Record<string, SearchResult[]>,
  subjectAnalyses: Record<string, any[]>,
  requestedDimensions?: string[]
): Promise<ComparisonDimension[]> {
  const dimensions: ComparisonDimension[] = []

  // Default dimensions if none specified
  const defaultDimensions = ['credibility', 'sentiment', 'coverage', 'recency']
  const dimensionsToAnalyze = requestedDimensions || defaultDimensions

  for (const dimensionName of dimensionsToAnalyze) {
    const values: Record<string, any> = {}
    let winner: string | undefined
    let reasoning = ''
    const evidence: SearchResult[] = []

    switch (dimensionName) {
      case 'credibility':
        subjects.forEach(subject => {
          const analyses = subjectAnalyses[subject] || []
          const avgCredibility = analyses.length > 0 
            ? analyses.reduce((sum, a) => sum + (a.credibility || 0), 0) / analyses.length
            : 0
          values[subject] = avgCredibility
        })
        
        winner = Object.entries(values).sort(([,a], [,b]) => b - a)[0]?.[0]
        reasoning = `${winner} has the highest average credibility score`
        break

      case 'sentiment':
        subjects.forEach(subject => {
          const analyses = subjectAnalyses[subject] || []
          const sentiments = analyses.map(a => a.sentiment).filter(Boolean)
          const positiveCount = sentiments.filter(s => s === 'positive').length
          values[subject] = sentiments.length > 0 ? positiveCount / sentiments.length : 0
        })
        
        winner = Object.entries(values).sort(([,a], [,b]) => b - a)[0]?.[0]
        reasoning = `${winner} has the most positive sentiment coverage`
        break

      case 'coverage':
        subjects.forEach(subject => {
          values[subject] = subjectSources[subject]?.length || 0
        })
        
        winner = Object.entries(values).sort(([,a], [,b]) => b - a)[0]?.[0]
        reasoning = `${winner} has the most comprehensive coverage`
        break

      case 'recency':
        subjects.forEach(subject => {
          const sources = subjectSources[subject] || []
          const recentSources = sources.filter(s => {
            if (!s.timestamp) return false
            const sourceDate = new Date(s.timestamp)
            const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            return sourceDate > monthAgo
          })
          values[subject] = sources.length > 0 ? recentSources.length / sources.length : 0
        })
        
        winner = Object.entries(values).sort(([,a], [,b]) => b - a)[0]?.[0]
        reasoning = `${winner} has the most recent coverage`
        break

      default:
        // Custom dimension - basic comparison
        subjects.forEach(subject => {
          values[subject] = Math.random() // Placeholder
        })
        reasoning = `Custom dimension: ${dimensionName}`
    }

    dimensions.push({
      name: dimensionName,
      values,
      winner,
      reasoning,
      evidence
    })
  }

  return dimensions
}

function generateComparisonSummary(subjects: string[], dimensions: ComparisonDimension[]): string {
  const winCounts: Record<string, number> = {}
  subjects.forEach(subject => winCounts[subject] = 0)

  dimensions.forEach(dimension => {
    if (dimension.winner) {
      winCounts[dimension.winner]++
    }
  })

  const overallWinner = Object.entries(winCounts).sort(([,a], [,b]) => b - a)[0]?.[0]
  
  return `Comparison of ${subjects.join(', ')} across ${dimensions.length} dimensions. ${overallWinner} leads in ${winCounts[overallWinner]} out of ${dimensions.length} dimensions.`
}

function determineWinner(dimensions: ComparisonDimension[]): string | undefined {
  const winCounts: Record<string, number> = {}

  dimensions.forEach(dimension => {
    if (dimension.winner) {
      winCounts[dimension.winner] = (winCounts[dimension.winner] || 0) + 1
    }
  })

  if (Object.keys(winCounts).length === 0) return undefined

  const sortedWinners = Object.entries(winCounts).sort(([,a], [,b]) => b - a)
  
  // Only return winner if there's a clear lead
  if (sortedWinners.length > 1 && sortedWinners[0][1] === sortedWinners[1][1]) {
    return undefined // Tie
  }

  return sortedWinners[0][0]
}

function calculateComparisonConfidence(
  dimensions: ComparisonDimension[],
  subjectSources: Record<string, SearchResult[]>
): number {
  let confidence = 0.5 // Base confidence

  // Boost for number of dimensions
  confidence += Math.min(0.2, dimensions.length * 0.05)

  // Boost for source quantity
  const totalSources = Object.values(subjectSources).flat().length
  confidence += Math.min(0.2, totalSources * 0.01)

  // Boost for clear winners
  const clearWinners = dimensions.filter(d => d.winner).length
  confidence += Math.min(0.1, (clearWinners / dimensions.length) * 0.1)

  return Math.min(1, confidence)
}

async function gatherItemInformation(item: any): Promise<any> {
  const info: any = {
    name: item.name,
    description: item.description || '',
    url: item.url,
    searchResults: []
  }

  if (item.url) {
    try {
      const analysis = await contentAnalysisTool.execute({
        url: item.url,
        analysisDepth: 'basic',
        includeTopics: true,
        includeSentiment: true
      })
      
      info.analysis = analysis.analysis
    } catch (error) {
      console.warn(`Failed to analyze ${item.url}:`, error)
    }
  }

  // Search for additional information
  try {
    const searchResults = await webSearchTool.execute({
      query: item.name,
      maxResults: 3,
      providers: ['google']
    })
    
    info.searchResults = searchResults.results || []
  } catch (error) {
    console.warn(`Search failed for ${item.name}:`, error)
  }

  return info
}

async function compareByCriterion(
  item1Info: any,
  item2Info: any,
  criterion: string,
  includeScoring: boolean
): Promise<any> {
  const comparison: any = {
    criterion,
    item1: {},
    item2: {},
    winner: null,
    reasoning: ''
  }

  // Extract relevant information for the criterion
  const item1Value = extractCriterionValue(item1Info, criterion)
  const item2Value = extractCriterionValue(item2Info, criterion)

  comparison.item1.value = item1Value
  comparison.item2.value = item2Value

  if (includeScoring) {
    const scores = scoreByCriterion(item1Value, item2Value, criterion)
    comparison.item1.score = scores.item1
    comparison.item2.score = scores.item2
    comparison.winner = scores.item1 > scores.item2 ? item1Info.name : item2Info.name
  }

  comparison.reasoning = generateCriterionReasoning(item1Info.name, item2Info.name, criterion, comparison)

  return comparison
}

function extractCriterionValue(itemInfo: any, criterion: string): any {
  // Extract value based on criterion type
  switch (criterion.toLowerCase()) {
    case 'credibility':
      return itemInfo.analysis?.credibility || 0
    case 'sentiment':
      return itemInfo.analysis?.sentiment || 'neutral'
    case 'topics':
      return itemInfo.analysis?.topics?.length || 0
    case 'coverage':
      return itemInfo.searchResults?.length || 0
    default:
      return 'N/A'
  }
}

function scoreByCriterion(value1: any, value2: any, criterion: string): { item1: number; item2: number } {
  // Simple scoring logic - would be more sophisticated in production
  if (typeof value1 === 'number' && typeof value2 === 'number') {
    const max = Math.max(value1, value2)
    return {
      item1: max > 0 ? (value1 / max) * 10 : 5,
      item2: max > 0 ? (value2 / max) * 10 : 5
    }
  }

  // Default scoring for non-numeric values
  return { item1: 5, item2: 5 }
}

function generateCriterionReasoning(name1: string, name2: string, criterion: string, comparison: any): string {
  if (comparison.winner) {
    return `${comparison.winner} performs better on ${criterion} with a score of ${comparison.winner === name1 ? comparison.item1.score : comparison.item2.score}`
  }
  
  return `Both ${name1} and ${name2} perform similarly on ${criterion}`
}

function calculateOverallScores(criteriaComparisons: any[], weightCriteria: boolean): any {
  const item1Scores = criteriaComparisons.map(c => c.item1.score || 0)
  const item2Scores = criteriaComparisons.map(c => c.item2.score || 0)

  // Simple average for now - would implement weighting if requested
  return {
    item1: item1Scores.reduce((sum, score) => sum + score, 0) / item1Scores.length,
    item2: item2Scores.reduce((sum, score) => sum + score, 0) / item2Scores.length
  }
}

function generateRecommendations(criteriaComparisons: any[], overallScores: any): string[] {
  const recommendations = []

  if (overallScores) {
    const winner = overallScores.item1 > overallScores.item2 ? 'item1' : 'item2'
    recommendations.push(`Overall recommendation: Choose ${winner} based on comprehensive analysis`)
  }

  // Add specific recommendations based on criteria
  criteriaComparisons.forEach(comparison => {
    if (comparison.winner) {
      recommendations.push(`For ${comparison.criterion}: ${comparison.winner} is recommended`)
    }
  })

  return recommendations
}

async function gatherCompetitorData(competitor: string, dimensions: string[], timeframe: string): Promise<any> {
  // Placeholder implementation - would gather real competitor data
  return {
    name: competitor,
    data: dimensions.reduce((acc, dim) => {
      acc[dim] = Math.random() * 10 // Placeholder data
      return acc
    }, {} as Record<string, number>)
  }
}

async function analyzeDimension(competitors: string[], competitorData: Record<string, any>, dimension: string): Promise<any> {
  const values = competitors.map(comp => ({
    competitor: comp,
    value: competitorData[comp].data[dimension]
  }))

  const sorted = values.sort((a, b) => b.value - a.value)
  
  return {
    dimension,
    leader: sorted[0],
    rankings: sorted,
    average: values.reduce((sum, v) => sum + v.value, 0) / values.length
  }
}

async function gatherMarketData(competitors: string[], timeframe: string): Promise<any> {
  // Placeholder implementation
  return {
    marketSize: '$1B',
    growthRate: '15%',
    trends: ['Digital transformation', 'AI adoption'],
    timeframe
  }
}

function formatCompetitiveAnalysis(
  competitors: string[],
  competitorData: Record<string, any>,
  dimensionAnalysis: Record<string, any>,
  marketData: any,
  format: string
): any {
  switch (format) {
    case 'matrix':
      return createCompetitiveMatrix(competitors, dimensionAnalysis)
    case 'summary':
      return createCompetitiveSummary(dimensionAnalysis)
    default:
      return {
        competitors,
        competitorData,
        dimensionAnalysis,
        marketData
      }
  }
}

function createCompetitiveMatrix(competitors: string[], dimensionAnalysis: Record<string, any>): any {
  const matrix: Record<string, Record<string, number>> = {}
  
  competitors.forEach(competitor => {
    matrix[competitor] = {}
    Object.entries(dimensionAnalysis).forEach(([dimension, analysis]) => {
      const competitorRanking = (analysis as any).rankings.find((r: any) => r.competitor === competitor)
      matrix[competitor][dimension] = competitorRanking?.value || 0
    })
  })

  return matrix
}

function createCompetitiveSummary(dimensionAnalysis: Record<string, any>): any {
  return Object.entries(dimensionAnalysis).map(([dimension, analysis]) => ({
    dimension,
    leader: (analysis as any).leader,
    insights: `${(analysis as any).leader.competitor} leads in ${dimension} with a score of ${(analysis as any).leader.value}`
  }))
}

function generateCompetitiveInsights(dimensionAnalysis: Record<string, any>): string[] {
  const insights = []
  
  Object.entries(dimensionAnalysis).forEach(([dimension, analysis]) => {
    const leader = (analysis as any).leader
    insights.push(`${leader.competitor} dominates in ${dimension} with ${leader.value.toFixed(1)}/10`)
  })

  return insights
}

function generateCompetitiveRecommendations(dimensionAnalysis: Record<string, any>): string[] {
  const recommendations = []
  
  // Find overall leader
  const leaderCounts: Record<string, number> = {}
  Object.values(dimensionAnalysis).forEach((analysis: any) => {
    const leader = analysis.leader.competitor
    leaderCounts[leader] = (leaderCounts[leader] || 0) + 1
  })

  const overallLeader = Object.entries(leaderCounts).sort(([,a], [,b]) => b - a)[0]?.[0]
  
  if (overallLeader) {
    recommendations.push(`Consider ${overallLeader} as the market leader across multiple dimensions`)
  }

  return recommendations
}