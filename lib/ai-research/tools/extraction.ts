// Data Extraction Tools
// Tools for extracting structured data from unstructured content

import { tool } from 'ai'
import { z } from 'zod'
import { generateObject } from 'ai'
import { openai } from '@ai-sdk/openai'
import { extractTextFromHTML, extractDates, extractNumbers, extractUrls, extractEmails } from '../utils/text-processing'

/**
 * Structured Data Extraction Tool
 * Extract structured data from unstructured text content
 */
export const structuredDataExtractionTool = tool({
  description: 'Extract structured data from unstructured text content',
  parameters: z.object({
    content: z.string().describe('Content to extract data from'),
    extractionTypes: z.array(z.enum(['entities', 'dates', 'numbers', 'urls', 'emails', 'addresses', 'phone_numbers', 'organizations', 'people', 'locations', 'events'])).describe('Types of data to extract'),
    outputFormat: z.enum(['json', 'structured', 'categorized']).default('structured').describe('Output format'),
    confidenceThreshold: z.number().default(0.7).describe('Minimum confidence for extracted data'),
    includeContext: z.boolean().default(true).describe('Include context around extracted data')
  }),
  execute: async ({ 
    content, 
    extractionTypes, 
    outputFormat,
    confidenceThreshold,
    includeContext 
  }) => {
    try {
      const extractedData: any = {}

      // Extract different types of data
      for (const type of extractionTypes) {
        switch (type) {
          case 'entities':
            extractedData.entities = await extractEntities(content, includeContext)
            break
          case 'dates':
            extractedData.dates = extractDates(content)
            break
          case 'numbers':
            extractedData.numbers = extractNumbers(content)
            break
          case 'urls':
            extractedData.urls = extractUrls(content)
            break
          case 'emails':
            extractedData.emails = extractEmails(content)
            break
          case 'addresses':
            extractedData.addresses = await extractAddresses(content, includeContext)
            break
          case 'phone_numbers':
            extractedData.phoneNumbers = extractPhoneNumbers(content)
            break
          case 'organizations':
            extractedData.organizations = await extractOrganizations(content, includeContext)
            break
          case 'people':
            extractedData.people = await extractPeople(content, includeContext)
            break
          case 'locations':
            extractedData.locations = await extractLocations(content, includeContext)
            break
          case 'events':
            extractedData.events = await extractEvents(content, includeContext)
            break
        }
      }

      // Filter by confidence if applicable
      if (confidenceThreshold > 0) {
        Object.keys(extractedData).forEach(key => {
          if (Array.isArray(extractedData[key])) {
            extractedData[key] = extractedData[key].filter((item: any) => 
              !item.confidence || item.confidence >= confidenceThreshold
            )
          }
        })
      }

      // Format output
      const formattedOutput = formatExtractionOutput(extractedData, outputFormat)

      return {
        extractedData: formattedOutput,
        metadata: {
          extractionTypes,
          totalItems: Object.values(extractedData).flat().length,
          confidenceThreshold,
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      throw new Error(`Data extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Table Extraction Tool
 * Extract and structure tabular data from content
 */
export const tableExtractionTool = tool({
  description: 'Extract and structure tabular data from HTML or text content',
  parameters: z.object({
    content: z.string().describe('Content containing tables'),
    contentType: z.enum(['html', 'text', 'markdown']).describe('Type of content'),
    includeHeaders: z.boolean().default(true).describe('Include table headers'),
    normalizeData: z.boolean().default(true).describe('Normalize extracted data'),
    maxTables: z.number().default(10).describe('Maximum number of tables to extract')
  }),
  execute: async ({ 
    content, 
    contentType, 
    includeHeaders,
    normalizeData,
    maxTables 
  }) => {
    try {
      let tables: any[] = []

      switch (contentType) {
        case 'html':
          tables = extractHTMLTables(content, includeHeaders, maxTables)
          break
        case 'text':
          tables = extractTextTables(content, includeHeaders, maxTables)
          break
        case 'markdown':
          tables = extractMarkdownTables(content, includeHeaders, maxTables)
          break
      }

      // Normalize data if requested
      if (normalizeData) {
        tables = tables.map(table => normalizeTableData(table))
      }

      // Add metadata to each table
      tables = tables.map((table, index) => ({
        ...table,
        id: `table_${index + 1}`,
        rowCount: table.rows?.length || 0,
        columnCount: table.headers?.length || table.rows?.[0]?.length || 0
      }))

      return {
        tables,
        metadata: {
          totalTables: tables.length,
          contentType,
          normalizeData,
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      throw new Error(`Table extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Form Data Extraction Tool
 * Extract form fields and structure from content
 */
export const formDataExtractionTool = tool({
  description: 'Extract form fields and structure from HTML or text content',
  parameters: z.object({
    content: z.string().describe('Content containing forms'),
    extractFieldTypes: z.boolean().default(true).describe('Extract field types and validation'),
    includeLabels: z.boolean().default(true).describe('Include field labels'),
    extractValidation: z.boolean().default(false).describe('Extract validation rules'),
    groupByForm: z.boolean().default(true).describe('Group fields by form')
  }),
  execute: async ({ 
    content, 
    extractFieldTypes, 
    includeLabels,
    extractValidation,
    groupByForm 
  }) => {
    try {
      const forms = extractForms(content, {
        extractFieldTypes,
        includeLabels,
        extractValidation,
        groupByForm
      })

      return {
        forms,
        metadata: {
          totalForms: forms.length,
          totalFields: forms.reduce((sum, form) => sum + form.fields.length, 0),
          extractFieldTypes,
          includeLabels,
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      throw new Error(`Form extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Citation Extraction Tool
 * Extract citations and references from academic content
 */
export const citationExtractionTool = tool({
  description: 'Extract citations and references from academic or research content',
  parameters: z.object({
    content: z.string().describe('Content containing citations'),
    citationStyles: z.array(z.enum(['apa', 'mla', 'chicago', 'ieee', 'harvard'])).default(['apa']).describe('Citation styles to recognize'),
    extractInText: z.boolean().default(true).describe('Extract in-text citations'),
    extractBibliography: z.boolean().default(true).describe('Extract bibliography/references'),
    validateCitations: z.boolean().default(false).describe('Validate citation format')
  }),
  execute: async ({ 
    content, 
    citationStyles, 
    extractInText,
    extractBibliography,
    validateCitations 
  }) => {
    try {
      const citations: any = {
        inText: [],
        bibliography: [],
        metadata: {}
      }

      if (extractInText) {
        citations.inText = await extractInTextCitations(content, citationStyles)
      }

      if (extractBibliography) {
        citations.bibliography = await extractBibliographyCitations(content, citationStyles)
      }

      if (validateCitations) {
        citations.validation = await validateCitationFormats(citations, citationStyles)
      }

      citations.metadata = {
        totalInTextCitations: citations.inText.length,
        totalBibliographyCitations: citations.bibliography.length,
        citationStyles,
        timestamp: new Date().toISOString()
      }

      return citations
    } catch (error) {
      throw new Error(`Citation extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

// Helper functions

async function extractEntities(content: string, includeContext: boolean): Promise<any[]> {
  const result = await generateObject({
    model: openai('gpt-4-turbo'),
    prompt: `Extract named entities from the following text. Include person names, organizations, locations, and other significant entities:\n\n${content.substring(0, 3000)}`,
    schema: z.object({
      entities: z.array(z.object({
        text: z.string(),
        type: z.enum(['person', 'organization', 'location', 'event', 'product', 'concept']),
        confidence: z.number(),
        context: z.string().optional()
      }))
    })
  })

  return result.object.entities.map(entity => ({
    ...entity,
    context: includeContext ? extractContext(content, entity.text) : undefined
  }))
}

async function extractAddresses(content: string, includeContext: boolean): Promise<any[]> {
  // Address patterns
  const addressPatterns = [
    /\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Court|Ct|Place|Pl)\s*,?\s*[A-Za-z\s]+,?\s*[A-Z]{2}\s*\d{5}(?:-\d{4})?/g,
    /\d+\s+[A-Za-z\s]+,\s*[A-Za-z\s]+,\s*[A-Z]{2}\s*\d{5}/g
  ]

  const addresses = []
  
  for (const pattern of addressPatterns) {
    const matches = content.match(pattern) || []
    matches.forEach(match => {
      addresses.push({
        text: match.trim(),
        type: 'address',
        confidence: 0.8,
        context: includeContext ? extractContext(content, match) : undefined
      })
    })
  }

  return addresses
}

function extractPhoneNumbers(content: string): any[] {
  const phonePatterns = [
    /\b\d{3}-\d{3}-\d{4}\b/g,
    /\b\(\d{3}\)\s*\d{3}-\d{4}\b/g,
    /\b\d{3}\.\d{3}\.\d{4}\b/g,
    /\b\+\d{1,3}\s*\d{3,4}\s*\d{3,4}\s*\d{3,4}\b/g
  ]

  const phoneNumbers = []
  
  for (const pattern of phonePatterns) {
    const matches = content.match(pattern) || []
    matches.forEach(match => {
      phoneNumbers.push({
        text: match.trim(),
        type: 'phone_number',
        confidence: 0.9
      })
    })
  }

  return [...new Set(phoneNumbers.map(p => p.text))].map(text => ({ text, type: 'phone_number', confidence: 0.9 }))
}

async function extractOrganizations(content: string, includeContext: boolean): Promise<any[]> {
  const result = await generateObject({
    model: openai('gpt-4-turbo'),
    prompt: `Extract organization names from the following text:\n\n${content.substring(0, 3000)}`,
    schema: z.object({
      organizations: z.array(z.object({
        name: z.string(),
        type: z.string().optional(),
        confidence: z.number()
      }))
    })
  })

  return result.object.organizations.map(org => ({
    text: org.name,
    type: 'organization',
    subtype: org.type,
    confidence: org.confidence,
    context: includeContext ? extractContext(content, org.name) : undefined
  }))
}

async function extractPeople(content: string, includeContext: boolean): Promise<any[]> {
  const result = await generateObject({
    model: openai('gpt-4-turbo'),
    prompt: `Extract person names from the following text:\n\n${content.substring(0, 3000)}`,
    schema: z.object({
      people: z.array(z.object({
        name: z.string(),
        title: z.string().optional(),
        confidence: z.number()
      }))
    })
  })

  return result.object.people.map(person => ({
    text: person.name,
    type: 'person',
    title: person.title,
    confidence: person.confidence,
    context: includeContext ? extractContext(content, person.name) : undefined
  }))
}

async function extractLocations(content: string, includeContext: boolean): Promise<any[]> {
  const result = await generateObject({
    model: openai('gpt-4-turbo'),
    prompt: `Extract location names (cities, countries, landmarks) from the following text:\n\n${content.substring(0, 3000)}`,
    schema: z.object({
      locations: z.array(z.object({
        name: z.string(),
        type: z.enum(['city', 'country', 'state', 'landmark', 'region']),
        confidence: z.number()
      }))
    })
  })

  return result.object.locations.map(location => ({
    text: location.name,
    type: 'location',
    subtype: location.type,
    confidence: location.confidence,
    context: includeContext ? extractContext(content, location.name) : undefined
  }))
}

async function extractEvents(content: string, includeContext: boolean): Promise<any[]> {
  const result = await generateObject({
    model: openai('gpt-4-turbo'),
    prompt: `Extract event names and descriptions from the following text:\n\n${content.substring(0, 3000)}`,
    schema: z.object({
      events: z.array(z.object({
        name: z.string(),
        date: z.string().optional(),
        description: z.string().optional(),
        confidence: z.number()
      }))
    })
  })

  return result.object.events.map(event => ({
    text: event.name,
    type: 'event',
    date: event.date,
    description: event.description,
    confidence: event.confidence,
    context: includeContext ? extractContext(content, event.name) : undefined
  }))
}

function extractContext(content: string, target: string, contextLength: number = 200): string {
  const index = content.toLowerCase().indexOf(target.toLowerCase())
  if (index === -1) return ''

  const start = Math.max(0, index - contextLength / 2)
  const end = Math.min(content.length, index + target.length + contextLength / 2)

  return content.substring(start, end).trim()
}

function formatExtractionOutput(data: any, format: string): any {
  switch (format) {
    case 'json':
      return JSON.stringify(data, null, 2)
    case 'categorized':
      return categorizeExtractedData(data)
    default:
      return data
  }
}

function categorizeExtractedData(data: any): any {
  const categorized: any = {
    people: [],
    organizations: [],
    locations: [],
    temporal: [],
    contact: [],
    other: []
  }

  Object.entries(data).forEach(([key, items]: [string, any]) => {
    if (!Array.isArray(items)) return

    items.forEach((item: any) => {
      switch (item.type || key) {
        case 'person':
        case 'people':
          categorized.people.push(item)
          break
        case 'organization':
        case 'organizations':
          categorized.organizations.push(item)
          break
        case 'location':
        case 'locations':
          categorized.locations.push(item)
          break
        case 'date':
        case 'dates':
        case 'event':
        case 'events':
          categorized.temporal.push(item)
          break
        case 'email':
        case 'emails':
        case 'phone_number':
        case 'phoneNumbers':
        case 'address':
        case 'addresses':
          categorized.contact.push(item)
          break
        default:
          categorized.other.push(item)
      }
    })
  })

  return categorized
}

function extractHTMLTables(content: string, includeHeaders: boolean, maxTables: number): any[] {
  const tables = []
  const tableRegex = /<table[^>]*>([\s\S]*?)<\/table>/gi
  let match

  while ((match = tableRegex.exec(content)) !== null && tables.length < maxTables) {
    const tableHTML = match[1]
    const table = parseHTMLTable(tableHTML, includeHeaders)
    if (table) {
      tables.push(table)
    }
  }

  return tables
}

function parseHTMLTable(tableHTML: string, includeHeaders: boolean): any | null {
  const rows = []
  let headers = []

  // Extract header rows
  if (includeHeaders) {
    const headerRegex = /<th[^>]*>([\s\S]*?)<\/th>/gi
    let headerMatch
    while ((headerMatch = headerRegex.exec(tableHTML)) !== null) {
      headers.push(extractTextFromHTML(headerMatch[1]).trim())
    }
  }

  // Extract data rows
  const rowRegex = /<tr[^>]*>([\s\S]*?)<\/tr>/gi
  let rowMatch
  while ((rowMatch = rowRegex.exec(tableHTML)) !== null) {
    const rowHTML = rowMatch[1]
    const cells = []
    const cellRegex = /<td[^>]*>([\s\S]*?)<\/td>/gi
    let cellMatch
    while ((cellMatch = cellRegex.exec(rowHTML)) !== null) {
      cells.push(extractTextFromHTML(cellMatch[1]).trim())
    }
    if (cells.length > 0) {
      rows.push(cells)
    }
  }

  if (rows.length === 0) return null

  return {
    headers: headers.length > 0 ? headers : undefined,
    rows,
    type: 'html_table'
  }
}

function extractTextTables(content: string, includeHeaders: boolean, maxTables: number): any[] {
  // Simple text table extraction - would be more sophisticated in production
  const lines = content.split('\n')
  const tables = []
  let currentTable: any = null

  for (const line of lines) {
    const trimmed = line.trim()
    
    // Detect table-like patterns (multiple columns separated by spaces/tabs)
    if (trimmed.includes('\t') || /\s{2,}/.test(trimmed)) {
      const cells = trimmed.split(/\s{2,}|\t/).map(cell => cell.trim()).filter(cell => cell.length > 0)
      
      if (cells.length > 1) {
        if (!currentTable) {
          currentTable = {
            headers: includeHeaders ? cells : undefined,
            rows: includeHeaders ? [] : [cells],
            type: 'text_table'
          }
        } else {
          currentTable.rows.push(cells)
        }
      } else if (currentTable) {
        // End of table
        tables.push(currentTable)
        currentTable = null
        if (tables.length >= maxTables) break
      }
    } else if (currentTable && trimmed.length === 0) {
      // Empty line might end table
      tables.push(currentTable)
      currentTable = null
      if (tables.length >= maxTables) break
    }
  }

  if (currentTable) {
    tables.push(currentTable)
  }

  return tables
}

function extractMarkdownTables(content: string, includeHeaders: boolean, maxTables: number): any[] {
  const tables = []
  const lines = content.split('\n')
  let i = 0

  while (i < lines.length && tables.length < maxTables) {
    const line = lines[i].trim()
    
    // Check for markdown table header
    if (line.includes('|') && lines[i + 1]?.includes('|') && lines[i + 1]?.includes('-')) {
      const table: any = {
        type: 'markdown_table',
        rows: []
      }

      // Extract headers
      if (includeHeaders) {
        table.headers = line.split('|').map(cell => cell.trim()).filter(cell => cell.length > 0)
        i += 2 // Skip header and separator line
      } else {
        table.rows.push(line.split('|').map(cell => cell.trim()).filter(cell => cell.length > 0))
        i += 2
      }

      // Extract data rows
      while (i < lines.length && lines[i].trim().includes('|')) {
        const row = lines[i].trim().split('|').map(cell => cell.trim()).filter(cell => cell.length > 0)
        table.rows.push(row)
        i++
      }

      if (table.rows.length > 0 || table.headers) {
        tables.push(table)
      }
    } else {
      i++
    }
  }

  return tables
}

function normalizeTableData(table: any): any {
  // Normalize table data (convert numbers, clean text, etc.)
  const normalizedTable = { ...table }

  if (normalizedTable.rows) {
    normalizedTable.rows = normalizedTable.rows.map((row: any[]) =>
      row.map(cell => {
        // Try to convert to number if possible
        const num = parseFloat(cell)
        if (!isNaN(num) && isFinite(num)) {
          return num
        }
        // Clean text
        return cell.replace(/\s+/g, ' ').trim()
      })
    )
  }

  return normalizedTable
}

function extractForms(content: string, options: any): any[] {
  const forms = []
  const formRegex = /<form[^>]*>([\s\S]*?)<\/form>/gi
  let match

  while ((match = formRegex.exec(content)) !== null) {
    const formHTML = match[1]
    const form = parseHTMLForm(formHTML, options)
    if (form) {
      forms.push(form)
    }
  }

  return forms
}

function parseHTMLForm(formHTML: string, options: any): any | null {
  const fields = []
  
  // Extract input fields
  const inputRegex = /<input[^>]*>/gi
  let inputMatch
  while ((inputMatch = inputRegex.exec(formHTML)) !== null) {
    const field = parseInputField(inputMatch[0], options)
    if (field) fields.push(field)
  }

  // Extract textarea fields
  const textareaRegex = /<textarea[^>]*>([\s\S]*?)<\/textarea>/gi
  let textareaMatch
  while ((textareaMatch = textareaRegex.exec(formHTML)) !== null) {
    const field = parseTextareaField(textareaMatch[0], options)
    if (field) fields.push(field)
  }

  // Extract select fields
  const selectRegex = /<select[^>]*>([\s\S]*?)<\/select>/gi
  let selectMatch
  while ((selectMatch = selectRegex.exec(formHTML)) !== null) {
    const field = parseSelectField(selectMatch[0], options)
    if (field) fields.push(field)
  }

  if (fields.length === 0) return null

  return {
    fields,
    fieldCount: fields.length
  }
}

function parseInputField(inputHTML: string, options: any): any | null {
  const typeMatch = inputHTML.match(/type=["']([^"']+)["']/i)
  const nameMatch = inputHTML.match(/name=["']([^"']+)["']/i)
  const idMatch = inputHTML.match(/id=["']([^"']+)["']/i)
  const placeholderMatch = inputHTML.match(/placeholder=["']([^"']+)["']/i)
  const requiredMatch = inputHTML.match(/required/i)

  return {
    type: 'input',
    inputType: typeMatch?.[1] || 'text',
    name: nameMatch?.[1],
    id: idMatch?.[1],
    placeholder: placeholderMatch?.[1],
    required: !!requiredMatch
  }
}

function parseTextareaField(textareaHTML: string, options: any): any | null {
  const nameMatch = textareaHTML.match(/name=["']([^"']+)["']/i)
  const idMatch = textareaHTML.match(/id=["']([^"']+)["']/i)
  const placeholderMatch = textareaHTML.match(/placeholder=["']([^"']+)["']/i)
  const requiredMatch = textareaHTML.match(/required/i)

  return {
    type: 'textarea',
    name: nameMatch?.[1],
    id: idMatch?.[1],
    placeholder: placeholderMatch?.[1],
    required: !!requiredMatch
  }
}

function parseSelectField(selectHTML: string, options: any): any | null {
  const nameMatch = selectHTML.match(/name=["']([^"']+)["']/i)
  const idMatch = selectHTML.match(/id=["']([^"']+)["']/i)
  const requiredMatch = selectHTML.match(/required/i)

  // Extract options
  const optionRegex = /<option[^>]*>([\s\S]*?)<\/option>/gi
  const optionOptions = []
  let optionMatch
  while ((optionMatch = optionRegex.exec(selectHTML)) !== null) {
    optionOptions.push(extractTextFromHTML(optionMatch[1]).trim())
  }

  return {
    type: 'select',
    name: nameMatch?.[1],
    id: idMatch?.[1],
    required: !!requiredMatch,
    options: optionOptions
  }
}

async function extractInTextCitations(content: string, styles: string[]): Promise<any[]> {
  const citations = []

  // APA style: (Author, Year)
  if (styles.includes('apa')) {
    const apaRegex = /\([A-Za-z\s&,]+,\s*\d{4}[a-z]?\)/g
    const matches = content.match(apaRegex) || []
    matches.forEach(match => {
      citations.push({
        text: match,
        style: 'apa',
        type: 'in_text'
      })
    })
  }

  // MLA style: (Author Page)
  if (styles.includes('mla')) {
    const mlaRegex = /\([A-Za-z\s]+\s+\d+\)/g
    const matches = content.match(mlaRegex) || []
    matches.forEach(match => {
      citations.push({
        text: match,
        style: 'mla',
        type: 'in_text'
      })
    })
  }

  return citations
}

async function extractBibliographyCitations(content: string, styles: string[]): Promise<any[]> {
  const citations = []

  // Look for reference sections
  const refSectionRegex = /(?:References|Bibliography|Works Cited|Literature Cited)[\s\S]*$/i
  const refMatch = content.match(refSectionRegex)
  
  if (refMatch) {
    const refSection = refMatch[0]
    const lines = refSection.split('\n').filter(line => line.trim().length > 20)
    
    lines.forEach(line => {
      if (line.trim().length > 20) {
        citations.push({
          text: line.trim(),
          style: 'unknown',
          type: 'bibliography'
        })
      }
    })
  }

  return citations
}

async function validateCitationFormats(citations: any, styles: string[]): Promise<any> {
  // Simple validation - would be more sophisticated in production
  const validation = {
    valid: 0,
    invalid: 0,
    warnings: []
  }

  citations.inText.forEach((citation: any) => {
    if (citation.text.length > 5) {
      validation.valid++
    } else {
      validation.invalid++
      validation.warnings.push(`Invalid in-text citation: ${citation.text}`)
    }
  })

  citations.bibliography.forEach((citation: any) => {
    if (citation.text.length > 20) {
      validation.valid++
    } else {
      validation.invalid++
      validation.warnings.push(`Invalid bibliography citation: ${citation.text}`)
    }
  })

  return validation
}