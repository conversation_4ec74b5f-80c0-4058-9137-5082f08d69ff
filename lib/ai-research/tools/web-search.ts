// Web Search Tools
// Intelligent web search capabilities with multiple providers and strategies

import { tool } from 'ai'
import { z } from 'zod'
import { SearchResult, ResearchQuery, SearchStrategy, SourceCredibility } from '../types'
import { BrowserlessClient } from '../../browserless/client'

// Search providers configuration
const SEARCH_PROVIDERS = {
  google: {
    baseUrl: 'https://www.googleapis.com/customsearch/v1',
    apiKey: process.env.GOOGLE_SEARCH_API_KEY,
    searchEngineId: process.env.GOOGLE_SEARCH_ENGINE_ID
  },
  bing: {
    baseUrl: 'https://api.bing.microsoft.com/v7.0/search',
    apiKey: process.env.BING_SEARCH_API_KEY
  },
  duckduckgo: {
    baseUrl: 'https://api.duckduckgo.com',
    instant: true
  },
  serp: {
    baseUrl: 'https://serpapi.com/search',
    apiKey: process.env.SERP_API_KEY
  }
}

// Browserless client for content extraction
const browserless = new BrowserlessClient({
  apiToken: process.env.BROWSERLESS_API_TOKEN || '',
  region: 'sfo'
})

/**
 * Intelligent Web Search Tool
 * Performs multi-provider web searches with relevance scoring
 */
export const webSearchTool = tool({
  description: 'Search the web using multiple providers with intelligent query optimization',
  parameters: z.object({
    query: z.string().describe('Search query'),
    maxResults: z.number().default(10).describe('Maximum number of results'),
    providers: z.array(z.enum(['google', 'bing', 'duckduckgo', 'serp'])).default(['google']).describe('Search providers to use'),
    timeframe: z.enum(['recent', 'past_week', 'past_month', 'past_year', 'any']).default('any').describe('Time filter'),
    language: z.string().default('en').describe('Language code'),
    region: z.string().default('us').describe('Region code'),
    safeSearch: z.boolean().default(true).describe('Enable safe search'),
    includeContent: z.boolean().default(false).describe('Extract full content from results')
  }),
  execute: async ({ 
    query, 
    maxResults, 
    providers, 
    timeframe, 
    language, 
    region, 
    safeSearch,
    includeContent 
  }) => {
    const results: SearchResult[] = []
    const errors: string[] = []

    // Search with each provider
    for (const provider of providers) {
      try {
        const providerResults = await searchWithProvider(
          provider,
          query,
          maxResults,
          { timeframe, language, region, safeSearch }
        )
        results.push(...providerResults)
      } catch (error) {
        errors.push(`${provider}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    // Remove duplicates and rank results
    const uniqueResults = deduplicateResults(results)
    const rankedResults = await rankResults(uniqueResults, query)

    // Extract full content if requested
    if (includeContent) {
      await extractContent(rankedResults.slice(0, Math.min(5, maxResults)))
    }

    return {
      results: rankedResults.slice(0, maxResults),
      totalFound: rankedResults.length,
      providers: providers,
      errors: errors.length > 0 ? errors : undefined
    }
  }
})

/**
 * Academic Search Tool
 * Specialized search for academic and research content
 */
export const academicSearchTool = tool({
  description: 'Search academic papers, journals, and research content',
  parameters: z.object({
    query: z.string().describe('Academic search query'),
    maxResults: z.number().default(10).describe('Maximum number of results'),
    sources: z.array(z.enum(['arxiv', 'pubmed', 'scholar', 'semantic_scholar'])).default(['scholar']).describe('Academic sources'),
    yearFrom: z.number().optional().describe('Start year filter'),
    yearTo: z.number().optional().describe('End year filter'),
    fieldOfStudy: z.string().optional().describe('Field of study filter'),
    includeAbstracts: z.boolean().default(true).describe('Include paper abstracts')
  }),
  execute: async ({ 
    query, 
    maxResults, 
    sources, 
    yearFrom, 
    yearTo, 
    fieldOfStudy,
    includeAbstracts 
  }) => {
    const results: SearchResult[] = []
    const errors: string[] = []

    for (const source of sources) {
      try {
        const sourceResults = await searchAcademicSource(
          source,
          query,
          maxResults,
          { yearFrom, yearTo, fieldOfStudy, includeAbstracts }
        )
        results.push(...sourceResults)
      } catch (error) {
        errors.push(`${source}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    const rankedResults = await rankAcademicResults(results, query)

    return {
      results: rankedResults.slice(0, maxResults),
      totalFound: rankedResults.length,
      sources: sources,
      errors: errors.length > 0 ? errors : undefined
    }
  }
})

/**
 * News Search Tool
 * Search current news and recent articles
 */
export const newsSearchTool = tool({
  description: 'Search current news and recent articles from reliable sources',
  parameters: z.object({
    query: z.string().describe('News search query'),
    maxResults: z.number().default(10).describe('Maximum number of results'),
    timeframe: z.enum(['1h', '24h', '7d', '30d']).default('24h').describe('Time filter'),
    sources: z.array(z.string()).optional().describe('Specific news sources'),
    category: z.enum(['general', 'business', 'technology', 'science', 'health', 'sports']).optional().describe('News category'),
    language: z.string().default('en').describe('Language code'),
    sortBy: z.enum(['relevance', 'date', 'popularity']).default('relevance').describe('Sort order')
  }),
  execute: async ({ 
    query, 
    maxResults, 
    timeframe, 
    sources, 
    category, 
    language, 
    sortBy 
  }) => {
    try {
      const newsResults = await searchNews(query, {
        maxResults,
        timeframe,
        sources,
        category,
        language,
        sortBy
      })

      // Analyze credibility of sources
      const resultsWithCredibility = await Promise.all(
        newsResults.map(async (result) => ({
          ...result,
          credibility: await getSourceCredibility(new URL(result.url).hostname)
        }))
      )

      return {
        results: resultsWithCredibility,
        totalFound: resultsWithCredibility.length,
        timeframe,
        category
      }
    } catch (error) {
      throw new Error(`News search failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Social Media Search Tool
 * Search social media platforms for mentions and discussions
 */
export const socialSearchTool = tool({
  description: 'Search social media platforms for mentions, discussions, and trends',
  parameters: z.object({
    query: z.string().describe('Social media search query'),
    platforms: z.array(z.enum(['twitter', 'reddit', 'linkedin', 'youtube'])).default(['twitter', 'reddit']).describe('Platforms to search'),
    maxResults: z.number().default(20).describe('Maximum number of results'),
    timeframe: z.enum(['1h', '24h', '7d', '30d']).default('24h').describe('Time filter'),
    sentiment: z.enum(['positive', 'negative', 'neutral', 'all']).default('all').describe('Sentiment filter'),
    includeMetrics: z.boolean().default(true).describe('Include engagement metrics')
  }),
  execute: async ({ 
    query, 
    platforms, 
    maxResults, 
    timeframe, 
    sentiment,
    includeMetrics 
  }) => {
    const results: SearchResult[] = []
    const errors: string[] = []

    for (const platform of platforms) {
      try {
        const platformResults = await searchSocialPlatform(
          platform,
          query,
          maxResults,
          { timeframe, sentiment, includeMetrics }
        )
        results.push(...platformResults)
      } catch (error) {
        errors.push(`${platform}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    // Analyze sentiment and engagement
    const analyzedResults = await analyzeSocialResults(results)

    return {
      results: analyzedResults.slice(0, maxResults),
      totalFound: analyzedResults.length,
      platforms,
      sentiment: sentiment,
      errors: errors.length > 0 ? errors : undefined
    }
  }
})

/**
 * Deep Web Search Tool
 * Search specific websites and databases deeply
 */
export const deepWebSearchTool = tool({
  description: 'Perform deep searches within specific websites and databases',
  parameters: z.object({
    query: z.string().describe('Search query'),
    targetSites: z.array(z.string()).describe('Target websites to search'),
    maxResults: z.number().default(10).describe('Maximum results per site'),
    searchDepth: z.enum(['shallow', 'medium', 'deep']).default('medium').describe('Search depth'),
    includeSubdomains: z.boolean().default(true).describe('Include subdomains'),
    extractContent: z.boolean().default(true).describe('Extract full content')
  }),
  execute: async ({ 
    query, 
    targetSites, 
    maxResults, 
    searchDepth, 
    includeSubdomains,
    extractContent 
  }) => {
    const results: SearchResult[] = []
    const errors: string[] = []

    for (const site of targetSites) {
      try {
        const siteResults = await searchSpecificSite(
          site,
          query,
          maxResults,
          { searchDepth, includeSubdomains, extractContent }
        )
        results.push(...siteResults)
      } catch (error) {
        errors.push(`${site}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return {
      results,
      totalFound: results.length,
      targetSites,
      searchDepth,
      errors: errors.length > 0 ? errors : undefined
    }
  }
})

// Helper functions

async function searchWithProvider(
  provider: string,
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  const config = SEARCH_PROVIDERS[provider as keyof typeof SEARCH_PROVIDERS]
  if (!config) {
    throw new Error(`Unknown search provider: ${provider}`)
  }

  switch (provider) {
    case 'google':
      return searchGoogle(query, maxResults, options)
    case 'bing':
      return searchBing(query, maxResults, options)
    case 'duckduckgo':
      return searchDuckDuckGo(query, maxResults, options)
    case 'serp':
      return searchSerpApi(query, maxResults, options)
    default:
      throw new Error(`Provider ${provider} not implemented`)
  }
}

async function searchGoogle(
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  const config = SEARCH_PROVIDERS.google
  if (!config.apiKey || !config.searchEngineId) {
    throw new Error('Google Search API key or Search Engine ID not configured')
  }

  const params = new URLSearchParams({
    key: config.apiKey,
    cx: config.searchEngineId,
    q: query,
    num: Math.min(maxResults, 10).toString(),
    lr: `lang_${options.language}`,
    gl: options.region,
    safe: options.safeSearch ? 'active' : 'off'
  })

  if (options.timeframe !== 'any') {
    const timeMap = {
      recent: 'd1',
      past_week: 'w1',
      past_month: 'm1',
      past_year: 'y1'
    }
    params.append('dateRestrict', timeMap[options.timeframe as keyof typeof timeMap])
  }

  const response = await fetch(`${config.baseUrl}?${params}`)
  const data = await response.json()

  if (!response.ok) {
    throw new Error(`Google Search API error: ${data.error?.message || 'Unknown error'}`)
  }

  return (data.items || []).map((item: any) => ({
    title: item.title,
    url: item.link,
    snippet: item.snippet,
    source: 'google',
    timestamp: new Date().toISOString(),
    metadata: {
      displayLink: item.displayLink,
      formattedUrl: item.formattedUrl
    }
  }))
}

async function searchBing(
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  const config = SEARCH_PROVIDERS.bing
  if (!config.apiKey) {
    throw new Error('Bing Search API key not configured')
  }

  const params = new URLSearchParams({
    q: query,
    count: Math.min(maxResults, 50).toString(),
    mkt: `${options.language}-${options.region}`,
    safeSearch: options.safeSearch ? 'Strict' : 'Off'
  })

  if (options.timeframe !== 'any') {
    const timeMap = {
      recent: 'Day',
      past_week: 'Week',
      past_month: 'Month',
      past_year: 'Year'
    }
    params.append('freshness', timeMap[options.timeframe as keyof typeof timeMap])
  }

  const response = await fetch(`${config.baseUrl}?${params}`, {
    headers: {
      'Ocp-Apim-Subscription-Key': config.apiKey
    }
  })

  const data = await response.json()

  if (!response.ok) {
    throw new Error(`Bing Search API error: ${data.error?.message || 'Unknown error'}`)
  }

  return (data.webPages?.value || []).map((item: any) => ({
    title: item.name,
    url: item.url,
    snippet: item.snippet,
    source: 'bing',
    timestamp: new Date().toISOString(),
    metadata: {
      displayUrl: item.displayUrl,
      dateLastCrawled: item.dateLastCrawled
    }
  }))
}

async function searchDuckDuckGo(
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  // DuckDuckGo Instant Answer API (limited)
  const params = new URLSearchParams({
    q: query,
    format: 'json',
    no_html: '1',
    skip_disambig: '1'
  })

  const response = await fetch(`${SEARCH_PROVIDERS.duckduckgo.baseUrl}/?${params}`)
  const data = await response.json()

  const results: SearchResult[] = []

  // Add abstract if available
  if (data.Abstract) {
    results.push({
      title: data.Heading || query,
      url: data.AbstractURL || '',
      snippet: data.Abstract,
      source: 'duckduckgo',
      timestamp: new Date().toISOString(),
      metadata: {
        type: 'abstract',
        source: data.AbstractSource
      }
    })
  }

  // Add related topics
  if (data.RelatedTopics) {
    data.RelatedTopics.slice(0, maxResults - results.length).forEach((topic: any) => {
      if (topic.Text && topic.FirstURL) {
        results.push({
          title: topic.Text.split(' - ')[0] || topic.Text,
          url: topic.FirstURL,
          snippet: topic.Text,
          source: 'duckduckgo',
          timestamp: new Date().toISOString(),
          metadata: {
            type: 'related_topic'
          }
        })
      }
    })
  }

  return results
}

async function searchSerpApi(
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  const config = SEARCH_PROVIDERS.serp
  if (!config.apiKey) {
    throw new Error('SerpApi key not configured')
  }

  const params = new URLSearchParams({
    api_key: config.apiKey,
    engine: 'google',
    q: query,
    num: Math.min(maxResults, 100).toString(),
    hl: options.language,
    gl: options.region,
    safe: options.safeSearch ? 'active' : 'off'
  })

  const response = await fetch(`${config.baseUrl}?${params}`)
  const data = await response.json()

  if (!response.ok || data.error) {
    throw new Error(`SerpApi error: ${data.error || 'Unknown error'}`)
  }

  return (data.organic_results || []).map((item: any) => ({
    title: item.title,
    url: item.link,
    snippet: item.snippet,
    source: 'serpapi',
    timestamp: new Date().toISOString(),
    metadata: {
      position: item.position,
      displayLink: item.displayed_link
    }
  }))
}

async function searchAcademicSource(
  source: string,
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  switch (source) {
    case 'arxiv':
      return searchArxiv(query, maxResults, options)
    case 'pubmed':
      return searchPubMed(query, maxResults, options)
    case 'scholar':
      return searchGoogleScholar(query, maxResults, options)
    case 'semantic_scholar':
      return searchSemanticScholar(query, maxResults, options)
    default:
      throw new Error(`Unknown academic source: ${source}`)
  }
}

async function searchArxiv(
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  const params = new URLSearchParams({
    search_query: `all:${query}`,
    start: '0',
    max_results: maxResults.toString(),
    sortBy: 'relevance',
    sortOrder: 'descending'
  })

  const response = await fetch(`http://export.arxiv.org/api/query?${params}`)
  const xmlText = await response.text()

  // Parse XML response (simplified)
  const results: SearchResult[] = []
  const entries = xmlText.match(/<entry>[\s\S]*?<\/entry>/g) || []

  entries.forEach(entry => {
    const title = entry.match(/<title>(.*?)<\/title>/)?.[1]?.trim()
    const summary = entry.match(/<summary>(.*?)<\/summary>/)?.[1]?.trim()
    const id = entry.match(/<id>(.*?)<\/id>/)?.[1]?.trim()
    const published = entry.match(/<published>(.*?)<\/published>/)?.[1]?.trim()

    if (title && id) {
      results.push({
        title,
        url: id,
        snippet: summary || '',
        source: 'arxiv',
        timestamp: published || new Date().toISOString(),
        metadata: {
          type: 'academic_paper',
          source: 'arxiv'
        }
      })
    }
  })

  return results
}

async function searchPubMed(
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  // PubMed E-utilities API
  const searchParams = new URLSearchParams({
    db: 'pubmed',
    term: query,
    retmax: maxResults.toString(),
    retmode: 'json'
  })

  const searchResponse = await fetch(`https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?${searchParams}`)
  const searchData = await searchResponse.json()

  if (!searchData.esearchresult?.idlist?.length) {
    return []
  }

  // Fetch details for the found IDs
  const ids = searchData.esearchresult.idlist.slice(0, maxResults)
  const summaryParams = new URLSearchParams({
    db: 'pubmed',
    id: ids.join(','),
    retmode: 'json'
  })

  const summaryResponse = await fetch(`https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi?${summaryParams}`)
  const summaryData = await summaryResponse.json()

  const results: SearchResult[] = []
  
  Object.values(summaryData.result || {}).forEach((item: any) => {
    if (item.title && item.pmid) {
      results.push({
        title: item.title,
        url: `https://pubmed.ncbi.nlm.nih.gov/${item.pmid}/`,
        snippet: item.abstract || '',
        source: 'pubmed',
        timestamp: item.pubdate || new Date().toISOString(),
        metadata: {
          type: 'medical_paper',
          pmid: item.pmid,
          authors: item.authors,
          journal: item.source
        }
      })
    }
  })

  return results
}

async function searchGoogleScholar(
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  // Note: Google Scholar doesn't have an official API
  // This would require web scraping or a third-party service
  // For now, return empty results with a note
  console.warn('Google Scholar search requires web scraping - not implemented in this example')
  return []
}

async function searchSemanticScholar(
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  const params = new URLSearchParams({
    query: query,
    limit: Math.min(maxResults, 100).toString(),
    fields: 'title,abstract,url,year,authors,venue,citationCount'
  })

  if (options.yearFrom) {
    params.append('year', `${options.yearFrom}-${options.yearTo || new Date().getFullYear()}`)
  }

  if (options.fieldOfStudy) {
    params.append('fieldsOfStudy', options.fieldOfStudy)
  }

  const response = await fetch(`https://api.semanticscholar.org/graph/v1/paper/search?${params}`)
  const data = await response.json()

  if (!response.ok) {
    throw new Error(`Semantic Scholar API error: ${data.error || 'Unknown error'}`)
  }

  return (data.data || []).map((paper: any) => ({
    title: paper.title,
    url: paper.url || `https://www.semanticscholar.org/paper/${paper.paperId}`,
    snippet: paper.abstract || '',
    source: 'semantic_scholar',
    timestamp: paper.year ? `${paper.year}-01-01` : new Date().toISOString(),
    metadata: {
      type: 'academic_paper',
      authors: paper.authors?.map((a: any) => a.name),
      venue: paper.venue,
      citationCount: paper.citationCount,
      year: paper.year
    }
  }))
}

async function searchNews(query: string, options: any): Promise<SearchResult[]> {
  // This would integrate with news APIs like NewsAPI, Bing News, etc.
  // For now, return a placeholder implementation
  console.warn('News search requires API integration - using placeholder')
  return []
}

async function searchSocialPlatform(
  platform: string,
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  // This would integrate with social media APIs
  // For now, return a placeholder implementation
  console.warn(`${platform} search requires API integration - using placeholder`)
  return []
}

async function searchSpecificSite(
  site: string,
  query: string,
  maxResults: number,
  options: any
): Promise<SearchResult[]> {
  // Use site-specific search with Google
  const siteQuery = `site:${site} ${query}`
  return searchGoogle(siteQuery, maxResults, options)
}

function deduplicateResults(results: SearchResult[]): SearchResult[] {
  const seen = new Set<string>()
  return results.filter(result => {
    const key = result.url.toLowerCase()
    if (seen.has(key)) {
      return false
    }
    seen.add(key)
    return true
  })
}

async function rankResults(results: SearchResult[], query: string): Promise<SearchResult[]> {
  // Simple relevance scoring based on title and snippet matching
  const queryTerms = query.toLowerCase().split(/\s+/)
  
  return results.map(result => {
    let score = 0
    const text = `${result.title} ${result.snippet}`.toLowerCase()
    
    queryTerms.forEach(term => {
      const titleMatches = (result.title.toLowerCase().match(new RegExp(term, 'g')) || []).length
      const snippetMatches = (result.snippet.toLowerCase().match(new RegExp(term, 'g')) || []).length
      
      score += titleMatches * 3 + snippetMatches * 1
    })
    
    return { ...result, relevanceScore: score }
  }).sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
}

async function rankAcademicResults(results: SearchResult[], query: string): Promise<SearchResult[]> {
  // Academic ranking considers citations, recency, and relevance
  return results.map(result => {
    let score = 0
    
    // Base relevance score
    const queryTerms = query.toLowerCase().split(/\s+/)
    const text = `${result.title} ${result.snippet}`.toLowerCase()
    
    queryTerms.forEach(term => {
      const matches = (text.match(new RegExp(term, 'g')) || []).length
      score += matches
    })
    
    // Citation count bonus
    if (result.metadata?.citationCount) {
      score += Math.log(result.metadata.citationCount + 1) * 2
    }
    
    // Recency bonus
    if (result.metadata?.year) {
      const currentYear = new Date().getFullYear()
      const yearDiff = currentYear - result.metadata.year
      score += Math.max(0, 10 - yearDiff) * 0.5
    }
    
    return { ...result, relevanceScore: score }
  }).sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
}

async function analyzeSocialResults(results: SearchResult[]): Promise<SearchResult[]> {
  // Analyze sentiment and engagement for social media results
  return results.map(result => ({
    ...result,
    metadata: {
      ...result.metadata,
      sentiment: 'neutral', // Placeholder - would use sentiment analysis
      engagement: Math.random() * 100 // Placeholder - would use real metrics
    }
  }))
}

async function extractContent(results: SearchResult[]): Promise<void> {
  // Extract full content using Browserless
  for (const result of results) {
    try {
      const contentResponse = await browserless.content({
        url: result.url,
        waitForSelector: {
          selector: 'body',
          timeout: 10000
        }
      })
      
      if (contentResponse.success && contentResponse.data) {
        result.content = contentResponse.data.data
      }
    } catch (error) {
      console.warn(`Failed to extract content from ${result.url}:`, error)
    }
  }
}

async function getSourceCredibility(domain: string): Promise<SourceCredibility> {
  // Placeholder implementation - would use a credibility database
  return {
    domain,
    credibilityScore: 0.8,
    factors: {
      authority: 0.8,
      accuracy: 0.8,
      objectivity: 0.7,
      currency: 0.9,
      coverage: 0.8
    },
    reputation: 'high',
    lastUpdated: new Date().toISOString()
  }
}