// Fact Verification Tools
// Tools for verifying claims and checking factual accuracy

import { tool } from 'ai'
import { z } from 'zod'
import { VerificationResult, SearchResult, SourceCredibility } from '../types'
import { webSearchTool } from './web-search'

/**
 * Fact Verification Tool
 * Verify claims against multiple reliable sources
 */
export const factVerificationTool = tool({
  description: 'Verify factual claims against multiple reliable sources',
  parameters: z.object({
    claim: z.string().describe('The claim to verify'),
    searchDepth: z.enum(['basic', 'thorough', 'comprehensive']).default('thorough').describe('Verification depth'),
    sources: z.array(z.string()).optional().describe('Specific sources to check'),
    timeframe: z.enum(['recent', 'past_week', 'past_month', 'past_year', 'any']).default('any').describe('Time filter for evidence'),
    requireMultipleSources: z.boolean().default(true).describe('Require multiple sources for verification'),
    minCredibilityScore: z.number().default(0.7).describe('Minimum credibility score for sources')
  }),
  execute: async ({ 
    claim, 
    searchDepth, 
    sources, 
    timeframe,
    requireMultipleSources,
    minCredibilityScore 
  }) => {
    try {
      // Generate search queries for the claim
      const searchQueries = generateVerificationQueries(claim)
      
      // Search for evidence
      const allEvidence: SearchResult[] = []
      const searchResults = await Promise.all(
        searchQueries.map(query => 
          webSearchTool.execute({
            query,
            maxResults: searchDepth === 'basic' ? 5 : searchDepth === 'thorough' ? 10 : 15,
            providers: ['google', 'bing'],
            timeframe,
            includeContent: true
          })
        )
      )

      searchResults.forEach(result => {
        if (result.results) {
          allEvidence.push(...result.results)
        }
      })

      // Filter by source credibility
      const credibleEvidence = await filterByCredibility(allEvidence, minCredibilityScore)

      // Analyze evidence for the claim
      const analysis = await analyzeEvidence(claim, credibleEvidence)

      // Determine verdict
      const verdict = determineVerdict(analysis, requireMultipleSources)

      // Calculate confidence score
      const confidence = calculateConfidence(analysis, verdict)

      // Generate reasoning
      const reasoning = generateReasoning(claim, analysis, verdict)

      return {
        claim,
        verdict,
        confidence,
        evidence: analysis.evidence,
        sources: analysis.sources,
        reasoning,
        searchQueries,
        totalEvidenceFound: allEvidence.length,
        credibleEvidenceUsed: credibleEvidence.length,
        timestamp: new Date().toISOString()
      } as VerificationResult
    } catch (error) {
      throw new Error(`Fact verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Claim Extraction Tool
 * Extract verifiable claims from text content
 */
export const claimExtractionTool = tool({
  description: 'Extract verifiable claims from text content',
  parameters: z.object({
    text: z.string().describe('Text to extract claims from'),
    claimTypes: z.array(z.enum(['factual', 'statistical', 'causal', 'comparative', 'temporal'])).optional().describe('Types of claims to extract'),
    minConfidence: z.number().default(0.6).describe('Minimum confidence for extracted claims'),
    maxClaims: z.number().default(10).describe('Maximum number of claims to extract'),
    prioritizeImportant: z.boolean().default(true).describe('Prioritize important claims')
  }),
  execute: async ({ 
    text, 
    claimTypes, 
    minConfidence,
    maxClaims,
    prioritizeImportant 
  }) => {
    try {
      // Extract potential claims from text
      const potentialClaims = await extractClaims(text)

      // Filter by type if specified
      const filteredClaims = claimTypes 
        ? potentialClaims.filter(claim => claimTypes.includes(claim.type))
        : potentialClaims

      // Score and rank claims
      const scoredClaims = await scoreClaims(filteredClaims, text, prioritizeImportant)

      // Filter by confidence and limit
      const finalClaims = scoredClaims
        .filter(claim => claim.confidence >= minConfidence)
        .slice(0, maxClaims)

      // Categorize claims
      const categorizedClaims = categorizeClaims(finalClaims)

      return {
        claims: finalClaims,
        categorized: categorizedClaims,
        statistics: {
          totalExtracted: potentialClaims.length,
          afterFiltering: finalClaims.length,
          averageConfidence: finalClaims.reduce((sum, c) => sum + c.confidence, 0) / finalClaims.length,
          byType: Object.fromEntries(
            Object.entries(categorizedClaims).map(([type, claims]) => [type, claims.length])
          )
        },
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`Claim extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Batch Fact Verification Tool
 * Verify multiple claims in batch
 */
export const batchFactVerificationTool = tool({
  description: 'Verify multiple claims in batch with parallel processing',
  parameters: z.object({
    claims: z.array(z.string()).describe('Claims to verify'),
    maxConcurrency: z.number().default(3).describe('Maximum concurrent verifications'),
    searchDepth: z.enum(['basic', 'thorough', 'comprehensive']).default('basic').describe('Verification depth'),
    aggregateResults: z.boolean().default(true).describe('Provide aggregate analysis'),
    stopOnFirstFalse: z.boolean().default(false).describe('Stop verification if first claim is false')
  }),
  execute: async ({ 
    claims, 
    maxConcurrency, 
    searchDepth,
    aggregateResults,
    stopOnFirstFalse 
  }) => {
    const results: VerificationResult[] = []
    const errors: string[] = []

    // Process claims in batches
    const batches = chunkArray(claims, maxConcurrency)
    
    for (const batch of batches) {
      const batchPromises = batch.map(async (claim) => {
        try {
          const result = await factVerificationTool.execute({
            claim,
            searchDepth,
            timeframe: 'any',
            requireMultipleSources: true,
            minCredibilityScore: 0.7
          })
          return result
        } catch (error) {
          errors.push(`${claim}: ${error instanceof Error ? error.message : 'Verification failed'}`)
          return null
        }
      })

      const batchResults = await Promise.all(batchPromises)
      const validResults = batchResults.filter((r): r is VerificationResult => r !== null)
      results.push(...validResults)

      // Check stop condition
      if (stopOnFirstFalse && validResults.some(r => r.verdict === 'false')) {
        break
      }

      // Small delay between batches
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    let aggregateAnalysis = null
    if (aggregateResults && results.length > 0) {
      aggregateAnalysis = generateAggregateAnalysis(results)
    }

    return {
      results,
      aggregate: aggregateAnalysis,
      summary: {
        total: claims.length,
        verified: results.length,
        failed: errors.length,
        verdicts: {
          true: results.filter(r => r.verdict === 'true').length,
          false: results.filter(r => r.verdict === 'false').length,
          partially_true: results.filter(r => r.verdict === 'partially_true').length,
          unverified: results.filter(r => r.verdict === 'unverified').length,
          disputed: results.filter(r => r.verdict === 'disputed').length
        }
      },
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString()
    }
  }
})

/**
 * Source Credibility Assessment Tool
 * Assess the credibility of information sources
 */
export const sourceCredibilityTool = tool({
  description: 'Assess the credibility and reliability of information sources',
  parameters: z.object({
    sources: z.array(z.string().url()).describe('URLs of sources to assess'),
    assessmentCriteria: z.array(z.enum(['authority', 'accuracy', 'objectivity', 'currency', 'coverage'])).default(['authority', 'accuracy', 'objectivity']).describe('Criteria for assessment'),
    includeReputationCheck: z.boolean().default(true).describe('Include reputation database check'),
    includeBiasAnalysis: z.boolean().default(true).describe('Include bias analysis'),
    detailedAnalysis: z.boolean().default(false).describe('Provide detailed analysis')
  }),
  execute: async ({ 
    sources, 
    assessmentCriteria, 
    includeReputationCheck,
    includeBiasAnalysis,
    detailedAnalysis 
  }) => {
    const assessments: SourceCredibility[] = []
    const errors: string[] = []

    for (const source of sources) {
      try {
        const assessment = await assessSourceCredibility(
          source,
          assessmentCriteria,
          includeReputationCheck,
          includeBiasAnalysis,
          detailedAnalysis
        )
        assessments.push(assessment)
      } catch (error) {
        errors.push(`${source}: ${error instanceof Error ? error.message : 'Assessment failed'}`)
      }
    }

    // Calculate aggregate metrics
    const aggregateMetrics = {
      averageCredibility: assessments.reduce((sum, a) => sum + a.credibilityScore, 0) / assessments.length,
      highCredibility: assessments.filter(a => a.credibilityScore >= 0.8).length,
      mediumCredibility: assessments.filter(a => a.credibilityScore >= 0.5 && a.credibilityScore < 0.8).length,
      lowCredibility: assessments.filter(a => a.credibilityScore < 0.5).length,
      biasDistribution: includeBiasAnalysis ? {
        left: assessments.filter(a => a.biasRating === 'left' || a.biasRating === 'lean_left').length,
        center: assessments.filter(a => a.biasRating === 'center').length,
        right: assessments.filter(a => a.biasRating === 'right' || a.biasRating === 'lean_right').length,
        mixed: assessments.filter(a => a.biasRating === 'mixed').length
      } : undefined
    }

    return {
      assessments,
      aggregateMetrics,
      summary: {
        total: sources.length,
        assessed: assessments.length,
        failed: errors.length
      },
      errors: errors.length > 0 ? errors : undefined,
      timestamp: new Date().toISOString()
    }
  }
})

/**
 * Contradiction Detection Tool
 * Detect contradictions between different sources or claims
 */
export const contradictionDetectionTool = tool({
  description: 'Detect contradictions between different sources or claims',
  parameters: z.object({
    sources: z.array(z.object({
      url: z.string().url(),
      content: z.string().optional()
    })).describe('Sources to analyze for contradictions'),
    topic: z.string().describe('Topic or subject to focus on'),
    sensitivityLevel: z.enum(['low', 'medium', 'high']).default('medium').describe('Sensitivity for detecting contradictions'),
    includePartialContradictions: z.boolean().default(true).describe('Include partial contradictions'),
    groupBySimilarity: z.boolean().default(true).describe('Group similar contradictions')
  }),
  execute: async ({ 
    sources, 
    topic, 
    sensitivityLevel,
    includePartialContradictions,
    groupBySimilarity 
  }) => {
    try {
      // Extract claims from each source
      const sourceClaims = await Promise.all(
        sources.map(async (source, index) => {
          let content = source.content
          if (!content) {
            // Extract content if not provided
            const contentResult = await webSearchTool.execute({
              query: `site:${new URL(source.url).hostname} ${topic}`,
              maxResults: 1,
              providers: ['google'],
              includeContent: true
            })
            content = contentResult.results?.[0]?.content || ''
          }

          const claims = await extractClaims(content)
          return {
            sourceIndex: index,
            url: source.url,
            claims: claims.filter(claim => 
              claim.text.toLowerCase().includes(topic.toLowerCase())
            )
          }
        })
      )

      // Find contradictions between sources
      const contradictions = await findContradictions(
        sourceClaims,
        sensitivityLevel,
        includePartialContradictions
      )

      // Group contradictions if requested
      const groupedContradictions = groupBySimilarity 
        ? groupContradictionsBySimilarity(contradictions)
        : { ungrouped: contradictions }

      // Analyze contradiction patterns
      const patterns = analyzeContradictionPatterns(contradictions, sources)

      return {
        contradictions: groupedContradictions,
        patterns,
        statistics: {
          totalSources: sources.length,
          totalClaims: sourceClaims.reduce((sum, sc) => sum + sc.claims.length, 0),
          contradictionsFound: contradictions.length,
          sourcesPairwise: calculateSourcePairwiseContradictions(contradictions, sources.length)
        },
        topic,
        sensitivityLevel,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`Contradiction detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

// Helper functions

function generateVerificationQueries(claim: string): string[] {
  const queries = [
    claim, // Direct claim
    `"${claim}"`, // Exact phrase
    `${claim} fact check`, // Fact-checking
    `${claim} verify`, // Verification
    `${claim} true false`, // Truth assessment
  ]

  // Extract key entities and create targeted queries
  const entities = extractKeyEntities(claim)
  entities.forEach(entity => {
    queries.push(`${entity} ${claim.replace(entity, '').trim()}`)
  })

  return queries.slice(0, 5) // Limit to 5 queries
}

function extractKeyEntities(text: string): string[] {
  // Simple entity extraction for verification queries
  const entities = []
  
  // Numbers and dates
  const numbers = text.match(/\b\d+(?:,\d{3})*(?:\.\d+)?\b/g) || []
  const dates = text.match(/\b\d{4}\b|\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b/g) || []
  
  // Proper nouns (capitalized words)
  const properNouns = text.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || []
  
  entities.push(...numbers, ...dates, ...properNouns)
  
  return [...new Set(entities)].slice(0, 3) // Unique entities, limit to 3
}

async function filterByCredibility(evidence: SearchResult[], minScore: number): Promise<SearchResult[]> {
  const credibleEvidence = []
  
  for (const result of evidence) {
    try {
      const domain = new URL(result.url).hostname
      const credibility = await getSourceCredibilityScore(domain)
      
      if (credibility >= minScore) {
        credibleEvidence.push({
          ...result,
          metadata: {
            ...result.metadata,
            credibilityScore: credibility
          }
        })
      }
    } catch (error) {
      // Skip invalid URLs
      continue
    }
  }
  
  return credibleEvidence
}

async function getSourceCredibilityScore(domain: string): Promise<number> {
  // Simplified credibility scoring
  const highCredibilityDomains = [
    'wikipedia.org', 'britannica.com', 'reuters.com', 'bbc.com', 'npr.org',
    'apnews.com', 'factcheck.org', 'snopes.com', 'politifact.com',
    'nature.com', 'science.org', 'nejm.org', 'who.int', 'cdc.gov'
  ]
  
  const mediumCredibilityDomains = [
    'cnn.com', 'nytimes.com', 'washingtonpost.com', 'theguardian.com',
    'wsj.com', 'economist.com', 'time.com', 'newsweek.com'
  ]
  
  if (highCredibilityDomains.some(trusted => domain.includes(trusted))) {
    return 0.9
  }
  
  if (mediumCredibilityDomains.some(medium => domain.includes(medium))) {
    return 0.7
  }
  
  // Government and educational domains
  if (domain.endsWith('.gov') || domain.endsWith('.edu')) {
    return 0.85
  }
  
  // Organization domains
  if (domain.endsWith('.org')) {
    return 0.6
  }
  
  return 0.5 // Default score
}

async function analyzeEvidence(claim: string, evidence: SearchResult[]): Promise<{
  evidence: {
    supporting: SearchResult[]
    contradicting: SearchResult[]
    neutral: SearchResult[]
  }
  sources: {
    reliable: number
    questionable: number
    unknown: number
  }
}> {
  const supporting: SearchResult[] = []
  const contradicting: SearchResult[] = []
  const neutral: SearchResult[] = []
  
  let reliable = 0
  let questionable = 0
  let unknown = 0
  
  for (const result of evidence) {
    const credibilityScore = result.metadata?.credibilityScore || 0.5
    
    // Categorize by credibility
    if (credibilityScore >= 0.8) {
      reliable++
    } else if (credibilityScore >= 0.5) {
      questionable++
    } else {
      unknown++
    }
    
    // Analyze stance towards claim
    const stance = await analyzeStance(claim, result.content || result.snippet)
    
    switch (stance) {
      case 'supporting':
        supporting.push(result)
        break
      case 'contradicting':
        contradicting.push(result)
        break
      default:
        neutral.push(result)
    }
  }
  
  return {
    evidence: { supporting, contradicting, neutral },
    sources: { reliable, questionable, unknown }
  }
}

async function analyzeStance(claim: string, content: string): Promise<'supporting' | 'contradicting' | 'neutral'> {
  // Simplified stance analysis
  const claimLower = claim.toLowerCase()
  const contentLower = content.toLowerCase()
  
  // Look for explicit agreement/disagreement
  const supportingPhrases = ['confirms', 'proves', 'shows that', 'demonstrates', 'validates']
  const contradictingPhrases = ['disproves', 'contradicts', 'false', 'incorrect', 'debunks']
  
  const hasSupporting = supportingPhrases.some(phrase => 
    contentLower.includes(phrase) && contentLower.includes(claimLower)
  )
  
  const hasContradicting = contradictingPhrases.some(phrase => 
    contentLower.includes(phrase) && contentLower.includes(claimLower)
  )
  
  if (hasSupporting && !hasContradicting) return 'supporting'
  if (hasContradicting && !hasSupporting) return 'contradicting'
  
  // Check for claim presence and context
  if (contentLower.includes(claimLower)) {
    // Simple sentiment around the claim
    const claimIndex = contentLower.indexOf(claimLower)
    const context = contentLower.substring(
      Math.max(0, claimIndex - 100),
      Math.min(contentLower.length, claimIndex + claimLower.length + 100)
    )
    
    const negativeWords = ['not', 'no', 'never', 'false', 'wrong', 'incorrect']
    const negativeCount = negativeWords.filter(word => context.includes(word)).length
    
    if (negativeCount > 2) return 'contradicting'
    if (negativeCount === 0) return 'supporting'
  }
  
  return 'neutral'
}

function determineVerdict(
  analysis: any,
  requireMultipleSources: boolean
): 'true' | 'false' | 'partially_true' | 'unverified' | 'disputed' {
  const { supporting, contradicting, neutral } = analysis.evidence
  const { reliable } = analysis.sources
  
  const totalEvidence = supporting.length + contradicting.length + neutral.length
  
  if (totalEvidence === 0) return 'unverified'
  
  const supportingRatio = supporting.length / totalEvidence
  const contradictingRatio = contradicting.length / totalEvidence
  
  // Check for disputes
  if (supporting.length > 0 && contradicting.length > 0 && 
      Math.abs(supportingRatio - contradictingRatio) < 0.3) {
    return 'disputed'
  }
  
  // Check source requirements
  if (requireMultipleSources && reliable < 2 && totalEvidence < 3) {
    return 'unverified'
  }
  
  // Determine verdict based on evidence
  if (supportingRatio >= 0.7) return 'true'
  if (contradictingRatio >= 0.7) return 'false'
  if (supportingRatio >= 0.4) return 'partially_true'
  
  return 'unverified'
}

function calculateConfidence(analysis: any, verdict: string): number {
  const { supporting, contradicting, neutral } = analysis.evidence
  const { reliable, questionable } = analysis.sources
  
  const totalEvidence = supporting.length + contradicting.length + neutral.length
  const totalSources = reliable + questionable
  
  if (totalEvidence === 0) return 0
  
  let confidence = 0.5 // Base confidence
  
  // Evidence quantity bonus
  confidence += Math.min(0.2, totalEvidence * 0.05)
  
  // Source quality bonus
  confidence += (reliable / totalSources) * 0.2
  
  // Verdict-specific adjustments
  switch (verdict) {
    case 'true':
    case 'false':
      confidence += 0.1
      break
    case 'disputed':
      confidence -= 0.1
      break
    case 'unverified':
      confidence -= 0.2
      break
  }
  
  // Consistency bonus
  const dominantEvidence = Math.max(supporting.length, contradicting.length)
  const consistency = dominantEvidence / totalEvidence
  confidence += (consistency - 0.5) * 0.2
  
  return Math.max(0, Math.min(1, confidence))
}

function generateReasoning(claim: string, analysis: any, verdict: string): string {
  const { supporting, contradicting, neutral } = analysis.evidence
  const { reliable, questionable, unknown } = analysis.sources
  
  let reasoning = `The claim "${claim}" has been assessed as ${verdict.replace('_', ' ')}. `
  
  reasoning += `Found ${supporting.length} supporting, ${contradicting.length} contradicting, and ${neutral.length} neutral sources. `
  
  reasoning += `Source credibility: ${reliable} reliable, ${questionable} questionable, ${unknown} unknown. `
  
  switch (verdict) {
    case 'true':
      reasoning += 'Strong supporting evidence from reliable sources with minimal contradiction.'
      break
    case 'false':
      reasoning += 'Strong contradicting evidence from reliable sources.'
      break
    case 'partially_true':
      reasoning += 'Mixed evidence suggests the claim has some truth but may be incomplete or context-dependent.'
      break
    case 'disputed':
      reasoning += 'Significant disagreement between sources, indicating ongoing debate or controversy.'
      break
    case 'unverified':
      reasoning += 'Insufficient reliable evidence to make a determination.'
      break
  }
  
  return reasoning
}

async function extractClaims(text: string): Promise<Array<{
  text: string
  type: 'factual' | 'statistical' | 'causal' | 'comparative' | 'temporal'
  confidence: number
  context: string
}>> {
  const claims = []
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10)
  
  for (const sentence of sentences) {
    const trimmed = sentence.trim()
    
    // Statistical claims
    if (/\b\d+(?:,\d{3})*(?:\.\d+)?%|\b\d+(?:,\d{3})*(?:\.\d+)?\s+(?:percent|million|billion|thousand)\b/i.test(trimmed)) {
      claims.push({
        text: trimmed,
        type: 'statistical' as const,
        confidence: 0.8,
        context: getContext(text, trimmed)
      })
    }
    
    // Causal claims
    else if (/\b(?:causes?|leads? to|results? in|due to|because of)\b/i.test(trimmed)) {
      claims.push({
        text: trimmed,
        type: 'causal' as const,
        confidence: 0.7,
        context: getContext(text, trimmed)
      })
    }
    
    // Comparative claims
    else if (/\b(?:more|less|better|worse|higher|lower|faster|slower)\s+than\b/i.test(trimmed)) {
      claims.push({
        text: trimmed,
        type: 'comparative' as const,
        confidence: 0.7,
        context: getContext(text, trimmed)
      })
    }
    
    // Temporal claims
    else if (/\b(?:in|during|since|before|after)\s+\d{4}\b|\b(?:first|last|earliest|latest)\b/i.test(trimmed)) {
      claims.push({
        text: trimmed,
        type: 'temporal' as const,
        confidence: 0.6,
        context: getContext(text, trimmed)
      })
    }
    
    // Factual claims (default)
    else if (/\b(?:is|are|was|were|has|have|will|can|cannot)\b/i.test(trimmed) && trimmed.length > 20) {
      claims.push({
        text: trimmed,
        type: 'factual' as const,
        confidence: 0.5,
        context: getContext(text, trimmed)
      })
    }
  }
  
  return claims
}

function getContext(text: string, sentence: string): string {
  const index = text.indexOf(sentence)
  if (index === -1) return ''
  
  const start = Math.max(0, index - 100)
  const end = Math.min(text.length, index + sentence.length + 100)
  
  return text.substring(start, end).trim()
}

async function scoreClaims(
  claims: Array<{ text: string; type: string; confidence: number; context: string }>,
  text: string,
  prioritizeImportant: boolean
): Promise<Array<{ text: string; type: string; confidence: number; context: string; score: number }>> {
  return claims.map(claim => {
    let score = claim.confidence
    
    // Boost important claims
    if (prioritizeImportant) {
      const importantWords = ['significant', 'important', 'major', 'key', 'critical', 'essential']
      if (importantWords.some(word => claim.context.toLowerCase().includes(word))) {
        score += 0.2
      }
    }
    
    // Boost specific claim types
    if (claim.type === 'statistical') score += 0.1
    if (claim.type === 'factual') score += 0.05
    
    // Boost claims with numbers or dates
    if (/\b\d+\b/.test(claim.text)) score += 0.1
    
    return { ...claim, score }
  }).sort((a, b) => b.score - a.score)
}

function categorizeClaims(claims: Array<{ type: string; [key: string]: any }>): Record<string, any[]> {
  return claims.reduce((categories, claim) => {
    if (!categories[claim.type]) {
      categories[claim.type] = []
    }
    categories[claim.type].push(claim)
    return categories
  }, {} as Record<string, any[]>)
}

function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }
  return chunks
}

function generateAggregateAnalysis(results: VerificationResult[]): any {
  const verdictCounts = results.reduce((counts, result) => {
    counts[result.verdict] = (counts[result.verdict] || 0) + 1
    return counts
  }, {} as Record<string, number>)
  
  const averageConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length
  
  const reliabilityScore = (
    (verdictCounts.true || 0) * 1.0 +
    (verdictCounts.partially_true || 0) * 0.7 +
    (verdictCounts.unverified || 0) * 0.3 +
    (verdictCounts.disputed || 0) * 0.2 +
    (verdictCounts.false || 0) * 0.0
  ) / results.length
  
  return {
    verdictDistribution: verdictCounts,
    averageConfidence,
    reliabilityScore,
    totalClaims: results.length,
    highConfidenceClaims: results.filter(r => r.confidence >= 0.8).length,
    controversialClaims: results.filter(r => r.verdict === 'disputed').length
  }
}

async function assessSourceCredibility(
  url: string,
  criteria: string[],
  includeReputation: boolean,
  includeBias: boolean,
  detailed: boolean
): Promise<SourceCredibility> {
  const domain = new URL(url).hostname
  
  // Base assessment
  const factors = {
    authority: await assessAuthority(domain),
    accuracy: await assessAccuracy(domain),
    objectivity: await assessObjectivity(domain),
    currency: await assessCurrency(domain),
    coverage: await assessCoverage(domain)
  }
  
  // Calculate overall score
  const relevantFactors = criteria.filter(c => c in factors)
  const credibilityScore = relevantFactors.reduce((sum, factor) => 
    sum + factors[factor as keyof typeof factors], 0
  ) / relevantFactors.length
  
  const assessment: SourceCredibility = {
    domain,
    credibilityScore,
    factors,
    reputation: credibilityScore >= 0.8 ? 'high' : credibilityScore >= 0.5 ? 'medium' : 'low',
    lastUpdated: new Date().toISOString()
  }
  
  if (includeBias) {
    assessment.biasRating = await assessBias(domain)
    assessment.factualReporting = await assessFactualReporting(domain)
  }
  
  return assessment
}

async function assessAuthority(domain: string): Promise<number> {
  // Simplified authority assessment
  if (domain.endsWith('.gov') || domain.endsWith('.edu')) return 0.9
  if (['wikipedia.org', 'britannica.com'].some(d => domain.includes(d))) return 0.85
  if (['reuters.com', 'bbc.com', 'npr.org'].some(d => domain.includes(d))) return 0.8
  return 0.5
}

async function assessAccuracy(domain: string): Promise<number> {
  // Simplified accuracy assessment
  const highAccuracy = ['factcheck.org', 'snopes.com', 'politifact.com', 'reuters.com']
  if (highAccuracy.some(d => domain.includes(d))) return 0.9
  return 0.6
}

async function assessObjectivity(domain: string): Promise<number> {
  // Simplified objectivity assessment
  const objective = ['reuters.com', 'bbc.com', 'npr.org', 'apnews.com']
  if (objective.some(d => domain.includes(d))) return 0.8
  return 0.6
}

async function assessCurrency(domain: string): Promise<number> {
  // Simplified currency assessment - would check last update dates
  return 0.7
}

async function assessCoverage(domain: string): Promise<number> {
  // Simplified coverage assessment
  return 0.7
}

async function assessBias(domain: string): Promise<'left' | 'lean_left' | 'center' | 'lean_right' | 'right' | 'mixed'> {
  // Simplified bias assessment
  const leftBias = ['cnn.com', 'msnbc.com', 'huffpost.com']
  const rightBias = ['foxnews.com', 'breitbart.com', 'dailywire.com']
  const center = ['reuters.com', 'bbc.com', 'npr.org', 'apnews.com']
  
  if (leftBias.some(d => domain.includes(d))) return 'lean_left'
  if (rightBias.some(d => domain.includes(d))) return 'lean_right'
  if (center.some(d => domain.includes(d))) return 'center'
  
  return 'mixed'
}

async function assessFactualReporting(domain: string): Promise<'very_high' | 'high' | 'mostly_factual' | 'mixed' | 'low'> {
  // Simplified factual reporting assessment
  const veryHigh = ['reuters.com', 'bbc.com', 'npr.org', 'factcheck.org']
  const high = ['cnn.com', 'nytimes.com', 'washingtonpost.com']
  
  if (veryHigh.some(d => domain.includes(d))) return 'very_high'
  if (high.some(d => domain.includes(d))) return 'high'
  
  return 'mostly_factual'
}

async function findContradictions(
  sourceClaims: Array<{ sourceIndex: number; url: string; claims: any[] }>,
  sensitivity: string,
  includePartial: boolean
): Promise<Array<{
  claim1: { source: number; text: string }
  claim2: { source: number; text: string }
  type: 'direct' | 'partial' | 'contextual'
  confidence: number
}>> {
  const contradictions = []
  
  for (let i = 0; i < sourceClaims.length; i++) {
    for (let j = i + 1; j < sourceClaims.length; j++) {
      const source1 = sourceClaims[i]
      const source2 = sourceClaims[j]
      
      for (const claim1 of source1.claims) {
        for (const claim2 of source2.claims) {
          const contradiction = await detectContradiction(
            claim1.text,
            claim2.text,
            sensitivity,
            includePartial
          )
          
          if (contradiction) {
            contradictions.push({
              claim1: { source: i, text: claim1.text },
              claim2: { source: j, text: claim2.text },
              type: contradiction.type,
              confidence: contradiction.confidence
            })
          }
        }
      }
    }
  }
  
  return contradictions
}

async function detectContradiction(
  claim1: string,
  claim2: string,
  sensitivity: string,
  includePartial: boolean
): Promise<{ type: 'direct' | 'partial' | 'contextual'; confidence: number } | null> {
  // Simplified contradiction detection
  const text1 = claim1.toLowerCase()
  const text2 = claim2.toLowerCase()
  
  // Direct contradictions
  const negationPairs = [
    ['is', 'is not'], ['are', 'are not'], ['was', 'was not'], ['were', 'were not'],
    ['can', 'cannot'], ['will', 'will not'], ['true', 'false'], ['yes', 'no']
  ]
  
  for (const [positive, negative] of negationPairs) {
    if ((text1.includes(positive) && text2.includes(negative)) ||
        (text1.includes(negative) && text2.includes(positive))) {
      return { type: 'direct', confidence: 0.9 }
    }
  }
  
  // Numerical contradictions
  const numbers1 = text1.match(/\b\d+(?:,\d{3})*(?:\.\d+)?\b/g) || []
  const numbers2 = text2.match(/\b\d+(?:,\d{3})*(?:\.\d+)?\b/g) || []
  
  if (numbers1.length > 0 && numbers2.length > 0) {
    const num1 = parseFloat(numbers1[0].replace(',', ''))
    const num2 = parseFloat(numbers2[0].replace(',', ''))
    
    if (!isNaN(num1) && !isNaN(num2) && Math.abs(num1 - num2) / Math.max(num1, num2) > 0.5) {
      return { type: 'partial', confidence: 0.7 }
    }
  }
  
  return null
}

function groupContradictionsBySimilarity(contradictions: any[]): Record<string, any[]> {
  // Simplified grouping - would use more sophisticated similarity measures
  const groups: Record<string, any[]> = {}
  let groupId = 0
  
  for (const contradiction of contradictions) {
    const key = `group_${groupId++}`
    groups[key] = [contradiction]
  }
  
  return groups
}

function analyzeContradictionPatterns(contradictions: any[], sources: any[]): any {
  const sourceContradictions: Record<number, number> = {}
  
  contradictions.forEach(contradiction => {
    sourceContradictions[contradiction.claim1.source] = 
      (sourceContradictions[contradiction.claim1.source] || 0) + 1
    sourceContradictions[contradiction.claim2.source] = 
      (sourceContradictions[contradiction.claim2.source] || 0) + 1
  })
  
  return {
    mostContradictory: Object.entries(sourceContradictions)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([sourceIndex, count]) => ({ sourceIndex: parseInt(sourceIndex), count })),
    averageContradictionsPerSource: Object.values(sourceContradictions).reduce((a, b) => a + b, 0) / sources.length
  }
}

function calculateSourcePairwiseContradictions(contradictions: any[], sourceCount: number): Record<string, number> {
  const pairwise: Record<string, number> = {}
  
  contradictions.forEach(contradiction => {
    const pair = [contradiction.claim1.source, contradiction.claim2.source].sort().join('-')
    pairwise[pair] = (pairwise[pair] || 0) + 1
  })
  
  return pairwise
}