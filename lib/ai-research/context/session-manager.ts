// Session Manager
// Manage research sessions and their lifecycle

import { ResearchSession, ResearchContext, ResearchAgent, Message, ToolCall } from '../types'

/**
 * Research Session Manager
 * Manages the lifecycle of research sessions
 */
export class SessionManager {
  private sessions: Map<string, ResearchSession> = new Map()
  private activeSessions: Set<string> = new Set()
  private sessionTimeouts: Map<string, NodeJS.Timeout> = new Map()

  constructor(
    private defaultTimeout: number = 3600000, // 1 hour
    private maxSessions: number = 100
  ) {}

  /**
   * Create a new research session
   */
  createSession(config: {
    topic: string
    agent: ResearchAgent
    timeout?: number
    metadata?: Record<string, any>
  }): ResearchSession {
    // Check session limit
    if (this.sessions.size >= this.maxSessions) {
      this.cleanupOldestSession()
    }

    const sessionId = this.generateSessionId()
    const now = new Date().toISOString()

    const session: ResearchSession = {
      id: sessionId,
      topic: config.topic,
      agent: config.agent,
      context: {
        id: `context_${sessionId}`,
        topic: config.topic,
        queries: [],
        results: [],
        insights: [],
        timeline: [],
        relationships: [],
        confidence: 0,
        lastUpdated: now,
        metadata: config.metadata || {}
      },
      toolCalls: [],
      messages: [],
      status: 'active',
      startTime: now
    }

    // Add initial system message
    this.addMessage(session, {
      role: 'system',
      content: config.agent.systemPrompt,
      timestamp: now
    })

    this.sessions.set(sessionId, session)
    this.activeSessions.add(sessionId)

    // Set timeout
    const timeout = config.timeout || this.defaultTimeout
    this.setSessionTimeout(sessionId, timeout)

    return session
  }

  /**
   * Get session by ID
   */
  getSession(sessionId: string): ResearchSession | null {
    return this.sessions.get(sessionId) || null
  }

  /**
   * Update session context
   */
  updateSessionContext(sessionId: string, updates: Partial<ResearchContext>): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    session.context = {
      ...session.context,
      ...updates,
      lastUpdated: new Date().toISOString()
    }

    return true
  }

  /**
   * Add message to session
   */
  addMessage(session: ResearchSession, message: Omit<Message, 'id'>): Message {
    const fullMessage: Message = {
      id: this.generateMessageId(),
      ...message,
      timestamp: message.timestamp || new Date().toISOString()
    }

    session.messages.push(fullMessage)
    return fullMessage
  }

  /**
   * Add tool call to session
   */
  addToolCall(sessionId: string, toolCall: Omit<ToolCall, 'id' | 'timestamp'>): ToolCall {
    const session = this.sessions.get(sessionId)
    if (!session) {
      throw new Error(`Session ${sessionId} not found`)
    }

    const fullToolCall: ToolCall = {
      id: this.generateToolCallId(),
      timestamp: new Date().toISOString(),
      ...toolCall
    }

    session.toolCalls.push(fullToolCall)
    return fullToolCall
  }

  /**
   * Update tool call result
   */
  updateToolCallResult(sessionId: string, toolCallId: string, result: any, error?: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    const toolCall = session.toolCalls.find(tc => tc.id === toolCallId)
    if (!toolCall) return false

    toolCall.result = result
    toolCall.error = error
    toolCall.duration = Date.now() - new Date(toolCall.timestamp).getTime()

    return true
  }

  /**
   * Pause session
   */
  pauseSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session || session.status !== 'active') return false

    session.status = 'paused'
    this.activeSessions.delete(sessionId)
    this.clearSessionTimeout(sessionId)

    return true
  }

  /**
   * Resume session
   */
  resumeSession(sessionId: string, timeout?: number): boolean {
    const session = this.sessions.get(sessionId)
    if (!session || session.status !== 'paused') return false

    session.status = 'active'
    this.activeSessions.add(sessionId)
    this.setSessionTimeout(sessionId, timeout || this.defaultTimeout)

    return true
  }

  /**
   * Complete session
   */
  completeSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    session.status = 'completed'
    session.endTime = new Date().toISOString()
    this.activeSessions.delete(sessionId)
    this.clearSessionTimeout(sessionId)

    return true
  }

  /**
   * Fail session
   */
  failSession(sessionId: string, error: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    session.status = 'failed'
    session.endTime = new Date().toISOString()
    this.activeSessions.delete(sessionId)
    this.clearSessionTimeout(sessionId)

    // Add error message
    this.addMessage(session, {
      role: 'system',
      content: `Session failed: ${error}`,
      timestamp: new Date().toISOString()
    })

    return true
  }

  /**
   * Delete session
   */
  deleteSession(sessionId: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    this.activeSessions.delete(sessionId)
    this.clearSessionTimeout(sessionId)
    this.sessions.delete(sessionId)

    return true
  }

  /**
   * List sessions with filters
   */
  listSessions(filters: {
    status?: 'active' | 'completed' | 'paused' | 'failed'
    topic?: string
    agentId?: string
    startDate?: Date
    endDate?: Date
    limit?: number
    offset?: number
  } = {}): ResearchSession[] {
    let sessions = Array.from(this.sessions.values())

    // Apply filters
    if (filters.status) {
      sessions = sessions.filter(s => s.status === filters.status)
    }

    if (filters.topic) {
      sessions = sessions.filter(s => 
        s.topic.toLowerCase().includes(filters.topic!.toLowerCase())
      )
    }

    if (filters.agentId) {
      sessions = sessions.filter(s => s.agent.id === filters.agentId)
    }

    if (filters.startDate) {
      sessions = sessions.filter(s => 
        new Date(s.startTime) >= filters.startDate!
      )
    }

    if (filters.endDate) {
      sessions = sessions.filter(s => 
        s.endTime ? new Date(s.endTime) <= filters.endDate! : true
      )
    }

    // Sort by start time (newest first)
    sessions.sort((a, b) => 
      new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
    )

    // Apply pagination
    const offset = filters.offset || 0
    const limit = filters.limit || sessions.length

    return sessions.slice(offset, offset + limit)
  }

  /**
   * Get session statistics
   */
  getSessionStatistics(sessionId?: string): any {
    if (sessionId) {
      return this.getIndividualSessionStats(sessionId)
    }

    return this.getOverallSessionStats()
  }

  /**
   * Export session data
   */
  exportSession(sessionId: string, format: 'json' | 'csv' = 'json'): any {
    const session = this.sessions.get(sessionId)
    if (!session) {
      throw new Error(`Session ${sessionId} not found`)
    }

    if (format === 'json') {
      return {
        session,
        exportedAt: new Date().toISOString(),
        version: '1.0'
      }
    } else {
      return this.convertSessionToCSV(session)
    }
  }

  /**
   * Import session data
   */
  importSession(data: any, format: 'json' = 'json'): string {
    if (format !== 'json') {
      throw new Error('Only JSON import is currently supported')
    }

    if (!data.session || !data.session.id) {
      throw new Error('Invalid session data')
    }

    const session = data.session
    this.sessions.set(session.id, session)

    if (session.status === 'active') {
      this.activeSessions.add(session.id)
      this.setSessionTimeout(session.id, this.defaultTimeout)
    }

    return session.id
  }

  /**
   * Cleanup expired sessions
   */
  cleanupExpiredSessions(): number {
    let cleaned = 0
    const now = Date.now()
    const maxAge = 24 * 60 * 60 * 1000 // 24 hours

    for (const [sessionId, session] of this.sessions.entries()) {
      const sessionAge = now - new Date(session.startTime).getTime()
      
      if (sessionAge > maxAge && session.status !== 'active') {
        this.deleteSession(sessionId)
        cleaned++
      }
    }

    return cleaned
  }

  /**
   * Get active session count
   */
  getActiveSessionCount(): number {
    return this.activeSessions.size
  }

  /**
   * Get total session count
   */
  getTotalSessionCount(): number {
    return this.sessions.size
  }

  // Private methods

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateToolCallId(): string {
    return `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private setSessionTimeout(sessionId: string, timeout: number): void {
    this.clearSessionTimeout(sessionId)
    
    const timeoutId = setTimeout(() => {
      this.pauseSession(sessionId)
    }, timeout)

    this.sessionTimeouts.set(sessionId, timeoutId)
  }

  private clearSessionTimeout(sessionId: string): void {
    const timeoutId = this.sessionTimeouts.get(sessionId)
    if (timeoutId) {
      clearTimeout(timeoutId)
      this.sessionTimeouts.delete(sessionId)
    }
  }

  private cleanupOldestSession(): void {
    const sessions = Array.from(this.sessions.values())
    const inactiveSessions = sessions.filter(s => s.status !== 'active')
    
    if (inactiveSessions.length > 0) {
      // Remove oldest inactive session
      const oldest = inactiveSessions.sort((a, b) => 
        new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
      )[0]
      
      this.deleteSession(oldest.id)
    } else {
      // Remove oldest session regardless of status
      const oldest = sessions.sort((a, b) => 
        new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
      )[0]
      
      this.deleteSession(oldest.id)
    }
  }

  private getIndividualSessionStats(sessionId: string): any {
    const session = this.sessions.get(sessionId)
    if (!session) return null

    const duration = session.endTime 
      ? new Date(session.endTime).getTime() - new Date(session.startTime).getTime()
      : Date.now() - new Date(session.startTime).getTime()

    const successfulToolCalls = session.toolCalls.filter(tc => !tc.error).length
    const failedToolCalls = session.toolCalls.filter(tc => tc.error).length

    return {
      sessionId,
      status: session.status,
      duration,
      messageCount: session.messages.length,
      toolCallCount: session.toolCalls.length,
      successfulToolCalls,
      failedToolCalls,
      successRate: session.toolCalls.length > 0 
        ? successfulToolCalls / session.toolCalls.length 
        : 0,
      resultsCount: session.context.results.length,
      insightsCount: session.context.insights.length,
      confidence: session.context.confidence
    }
  }

  private getOverallSessionStats(): any {
    const sessions = Array.from(this.sessions.values())
    const activeSessions = sessions.filter(s => s.status === 'active')
    const completedSessions = sessions.filter(s => s.status === 'completed')
    const failedSessions = sessions.filter(s => s.status === 'failed')

    const totalToolCalls = sessions.reduce((sum, s) => sum + s.toolCalls.length, 0)
    const successfulToolCalls = sessions.reduce((sum, s) => 
      sum + s.toolCalls.filter(tc => !tc.error).length, 0
    )

    const averageDuration = completedSessions.length > 0
      ? completedSessions.reduce((sum, s) => {
          const duration = new Date(s.endTime!).getTime() - new Date(s.startTime).getTime()
          return sum + duration
        }, 0) / completedSessions.length
      : 0

    return {
      totalSessions: sessions.length,
      activeSessions: activeSessions.length,
      completedSessions: completedSessions.length,
      failedSessions: failedSessions.length,
      pausedSessions: sessions.filter(s => s.status === 'paused').length,
      successRate: sessions.length > 0 ? completedSessions.length / sessions.length : 0,
      averageDuration,
      totalToolCalls,
      toolCallSuccessRate: totalToolCalls > 0 ? successfulToolCalls / totalToolCalls : 0,
      averageResultsPerSession: sessions.length > 0 
        ? sessions.reduce((sum, s) => sum + s.context.results.length, 0) / sessions.length
        : 0,
      averageInsightsPerSession: sessions.length > 0
        ? sessions.reduce((sum, s) => sum + s.context.insights.length, 0) / sessions.length
        : 0
    }
  }

  private convertSessionToCSV(session: ResearchSession): string {
    const headers = [
      'timestamp', 'type', 'role', 'content', 'tool_name', 'tool_result', 'error'
    ]

    let csv = headers.join(',') + '\n'

    // Add messages
    session.messages.forEach(message => {
      const row = [
        message.timestamp,
        'message',
        message.role,
        `"${message.content.replace(/"/g, '""')}"`,
        '',
        '',
        ''
      ]
      csv += row.join(',') + '\n'
    })

    // Add tool calls
    session.toolCalls.forEach(toolCall => {
      const row = [
        toolCall.timestamp,
        'tool_call',
        '',
        '',
        toolCall.name,
        toolCall.result ? `"${JSON.stringify(toolCall.result).replace(/"/g, '""')}"` : '',
        toolCall.error ? `"${toolCall.error.replace(/"/g, '""')}"` : ''
      ]
      csv += row.join(',') + '\n'
    })

    return csv
  }
}

/**
 * Global session manager instance
 */
export const globalSessionManager = new SessionManager()

/**
 * Session middleware for automatic session management
 */
export function withSession(sessionId?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const currentSessionId = sessionId || this.sessionId
      
      if (currentSessionId) {
        const session = globalSessionManager.getSession(currentSessionId)
        if (session) {
          // Add method call as message
          globalSessionManager.addMessage(session, {
            role: 'system',
            content: `Executing ${propertyName} with ${args.length} arguments`,
            timestamp: new Date().toISOString()
          })
        }
      }

      try {
        const result = await method.apply(this, args)
        
        if (currentSessionId) {
          const session = globalSessionManager.getSession(currentSessionId)
          if (session) {
            // Add success message
            globalSessionManager.addMessage(session, {
              role: 'system',
              content: `${propertyName} completed successfully`,
              timestamp: new Date().toISOString()
            })
          }
        }

        return result
      } catch (error) {
        if (currentSessionId) {
          const session = globalSessionManager.getSession(currentSessionId)
          if (session) {
            // Add error message
            globalSessionManager.addMessage(session, {
              role: 'system',
              content: `${propertyName} failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
              timestamp: new Date().toISOString()
            })
          }
        }

        throw error
      }
    }

    return descriptor
  }
}

/**
 * Session-aware base class
 */
export class SessionAware {
  protected sessionId?: string

  constructor(sessionId?: string) {
    this.sessionId = sessionId
  }

  setSession(sessionId: string): void {
    this.sessionId = sessionId
  }

  getSession(): ResearchSession | null {
    return this.sessionId ? globalSessionManager.getSession(this.sessionId) : null
  }

  addMessage(message: Omit<Message, 'id'>): Message | null {
    const session = this.getSession()
    return session ? globalSessionManager.addMessage(session, message) : null
  }

  addToolCall(toolCall: Omit<ToolCall, 'id' | 'timestamp'>): ToolCall | null {
    return this.sessionId ? globalSessionManager.addToolCall(this.sessionId, toolCall) : null
  }

  updateContext(updates: Partial<ResearchContext>): boolean {
    return this.sessionId ? globalSessionManager.updateSessionContext(this.sessionId, updates) : false
  }
}