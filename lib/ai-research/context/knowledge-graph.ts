// Knowledge Graph Builder
// Build and manage knowledge graphs from research data

import { KnowledgeGraph, KnowledgeEntity, KnowledgeRelationship, KnowledgeTopic, ResearchContext } from '../types'

/**
 * Knowledge Graph Builder
 * Constructs knowledge graphs from research data
 */
export class KnowledgeGraphBuilder {
  private graph: KnowledgeGraph
  private entityIndex: Map<string, KnowledgeEntity> = new Map()
  private relationshipIndex: Map<string, KnowledgeRelationship> = new Map()
  private topicIndex: Map<string, KnowledgeTopic> = new Map()

  constructor() {
    this.graph = {
      entities: [],
      relationships: [],
      topics: [],
      timeline: []
    }
  }

  /**
   * Build knowledge graph from research context
   */
  async buildFromResearch(context: ResearchContext): Promise<KnowledgeGraph> {
    // Reset graph
    this.resetGraph()

    // Extract entities from results
    await this.extractEntitiesFromResults(context.results)

    // Extract topics from insights
    await this.extractTopicsFromInsights(context.insights)

    // Build relationships from context
    await this.buildRelationshipsFromContext(context)

    // Add timeline events
    this.addTimelineEvents(context.timeline)

    // Finalize graph
    this.finalizeGraph()

    return this.graph
  }

  /**
   * Add entity to graph
   */
  addEntity(entity: Partial<KnowledgeEntity>): KnowledgeEntity {
    const id = entity.id || this.generateEntityId(entity.name || '')
    
    const fullEntity: KnowledgeEntity = {
      id,
      name: entity.name || '',
      type: entity.type || 'unknown',
      description: entity.description || '',
      aliases: entity.aliases || [],
      properties: entity.properties || {},
      confidence: entity.confidence || 0.5,
      sources: entity.sources || []
    }

    // Check if entity already exists
    const existing = this.entityIndex.get(fullEntity.name.toLowerCase())
    if (existing) {
      // Merge with existing entity
      return this.mergeEntities(existing, fullEntity)
    }

    this.entityIndex.set(fullEntity.name.toLowerCase(), fullEntity)
    this.graph.entities.push(fullEntity)
    
    return fullEntity
  }

  /**
   * Add relationship to graph
   */
  addRelationship(relationship: Partial<KnowledgeRelationship>): KnowledgeRelationship {
    const id = relationship.id || this.generateRelationshipId(
      relationship.source || '',
      relationship.target || '',
      relationship.type || ''
    )

    const fullRelationship: KnowledgeRelationship = {
      id,
      source: relationship.source || '',
      target: relationship.target || '',
      type: relationship.type || 'related',
      properties: relationship.properties || {},
      confidence: relationship.confidence || 0.5,
      evidence: relationship.evidence || []
    }

    // Check for duplicate relationships
    const existing = this.relationshipIndex.get(id)
    if (existing) {
      // Merge evidence and update confidence
      existing.evidence = [...new Set([...existing.evidence, ...fullRelationship.evidence])]
      existing.confidence = Math.max(existing.confidence, fullRelationship.confidence)
      return existing
    }

    this.relationshipIndex.set(id, fullRelationship)
    this.graph.relationships.push(fullRelationship)
    
    return fullRelationship
  }

  /**
   * Add topic to graph
   */
  addTopic(topic: Partial<KnowledgeTopic>): KnowledgeTopic {
    const id = topic.id || this.generateTopicId(topic.name || '')
    
    const fullTopic: KnowledgeTopic = {
      id,
      name: topic.name || '',
      description: topic.description || '',
      keywords: topic.keywords || [],
      entities: topic.entities || [],
      subtopics: topic.subtopics || [],
      confidence: topic.confidence || 0.5
    }

    // Check if topic already exists
    const existing = this.topicIndex.get(fullTopic.name.toLowerCase())
    if (existing) {
      return this.mergeTopics(existing, fullTopic)
    }

    this.topicIndex.set(fullTopic.name.toLowerCase(), fullTopic)
    this.graph.topics.push(fullTopic)
    
    return fullTopic
  }

  /**
   * Find entity by name or alias
   */
  findEntity(name: string): KnowledgeEntity | null {
    const entity = this.entityIndex.get(name.toLowerCase())
    if (entity) return entity

    // Search by aliases
    for (const entity of this.graph.entities) {
      if (entity.aliases.some(alias => alias.toLowerCase() === name.toLowerCase())) {
        return entity
      }
    }

    return null
  }

  /**
   * Find relationships for entity
   */
  findRelationships(entityId: string): KnowledgeRelationship[] {
    return this.graph.relationships.filter(rel => 
      rel.source === entityId || rel.target === entityId
    )
  }

  /**
   * Get entity neighbors
   */
  getEntityNeighbors(entityId: string): KnowledgeEntity[] {
    const relationships = this.findRelationships(entityId)
    const neighborIds = new Set<string>()

    relationships.forEach(rel => {
      if (rel.source === entityId) {
        neighborIds.add(rel.target)
      } else {
        neighborIds.add(rel.source)
      }
    })

    return this.graph.entities.filter(entity => neighborIds.has(entity.id))
  }

  /**
   * Calculate entity centrality
   */
  calculateEntityCentrality(): Map<string, number> {
    const centrality = new Map<string, number>()

    this.graph.entities.forEach(entity => {
      const relationships = this.findRelationships(entity.id)
      centrality.set(entity.id, relationships.length)
    })

    return centrality
  }

  /**
   * Find shortest path between entities
   */
  findShortestPath(sourceId: string, targetId: string): KnowledgeEntity[] | null {
    const visited = new Set<string>()
    const queue: Array<{ entityId: string; path: string[] }> = [{ entityId: sourceId, path: [sourceId] }]

    while (queue.length > 0) {
      const { entityId, path } = queue.shift()!

      if (entityId === targetId) {
        return path.map(id => this.graph.entities.find(e => e.id === id)!).filter(Boolean)
      }

      if (visited.has(entityId)) continue
      visited.add(entityId)

      const neighbors = this.getEntityNeighbors(entityId)
      neighbors.forEach(neighbor => {
        if (!visited.has(neighbor.id)) {
          queue.push({
            entityId: neighbor.id,
            path: [...path, neighbor.id]
          })
        }
      })
    }

    return null
  }

  /**
   * Detect communities in the graph
   */
  detectCommunities(): Array<{ entities: KnowledgeEntity[]; strength: number }> {
    const communities: Array<{ entities: KnowledgeEntity[]; strength: number }> = []
    const visited = new Set<string>()

    this.graph.entities.forEach(entity => {
      if (visited.has(entity.id)) return

      const community = this.expandCommunity(entity.id, visited)
      if (community.length > 1) {
        const strength = this.calculateCommunityStrength(community)
        communities.push({
          entities: community.map(id => this.graph.entities.find(e => e.id === id)!).filter(Boolean),
          strength
        })
      }
    })

    return communities.sort((a, b) => b.strength - a.strength)
  }

  /**
   * Export graph in various formats
   */
  export(format: 'json' | 'cytoscape' | 'd3' | 'graphml' = 'json'): any {
    switch (format) {
      case 'cytoscape':
        return this.exportToCytoscape()
      case 'd3':
        return this.exportToD3()
      case 'graphml':
        return this.exportToGraphML()
      default:
        return this.graph
    }
  }

  /**
   * Import graph data
   */
  import(data: any, format: 'json' | 'cytoscape' | 'd3' = 'json'): void {
    switch (format) {
      case 'cytoscape':
        this.importFromCytoscape(data)
        break
      case 'd3':
        this.importFromD3(data)
        break
      default:
        this.importFromJSON(data)
    }
  }

  /**
   * Get graph statistics
   */
  getStatistics(): {
    entityCount: number
    relationshipCount: number
    topicCount: number
    averageDegree: number
    density: number
    components: number
  } {
    const entityCount = this.graph.entities.length
    const relationshipCount = this.graph.relationships.length
    const topicCount = this.graph.topics.length

    const totalDegree = this.graph.entities.reduce((sum, entity) => {
      return sum + this.findRelationships(entity.id).length
    }, 0)

    const averageDegree = entityCount > 0 ? totalDegree / entityCount : 0
    const maxPossibleEdges = entityCount * (entityCount - 1) / 2
    const density = maxPossibleEdges > 0 ? relationshipCount / maxPossibleEdges : 0

    const components = this.countConnectedComponents()

    return {
      entityCount,
      relationshipCount,
      topicCount,
      averageDegree,
      density,
      components
    }
  }

  // Private methods

  private resetGraph(): void {
    this.graph = {
      entities: [],
      relationships: [],
      topics: [],
      timeline: []
    }
    this.entityIndex.clear()
    this.relationshipIndex.clear()
    this.topicIndex.clear()
  }

  private async extractEntitiesFromResults(results: any[]): Promise<void> {
    for (const result of results) {
      // Extract entities from title and snippet
      const text = `${result.title} ${result.snippet}`
      const entities = await this.extractEntitiesFromText(text)
      
      entities.forEach(entityData => {
        this.addEntity({
          name: entityData.name,
          type: entityData.type,
          confidence: entityData.confidence,
          sources: [result.url]
        })
      })
    }
  }

  private async extractTopicsFromInsights(insights: any[]): Promise<void> {
    insights.forEach(insight => {
      // Extract topics from insight content
      const topics = this.extractTopicsFromText(insight.content)
      
      topics.forEach(topicName => {
        this.addTopic({
          name: topicName,
          description: insight.content,
          confidence: insight.confidence,
          keywords: insight.tags || []
        })
      })
    })
  }

  private async buildRelationshipsFromContext(context: ResearchContext): Promise<void> {
    // Build relationships from existing relationships in context
    context.relationships.forEach(rel => {
      const sourceEntity = this.findEntity(rel.entity1) || this.addEntity({ name: rel.entity1 })
      const targetEntity = this.findEntity(rel.entity2) || this.addEntity({ name: rel.entity2 })

      this.addRelationship({
        source: sourceEntity.id,
        target: targetEntity.id,
        type: rel.relationship,
        confidence: rel.strength,
        evidence: rel.evidence.map(e => e.url)
      })
    })

    // Infer additional relationships from co-occurrence
    await this.inferRelationshipsFromCooccurrence(context)
  }

  private async inferRelationshipsFromCooccurrence(context: ResearchContext): Promise<void> {
    const entities = this.graph.entities
    
    for (let i = 0; i < entities.length; i++) {
      for (let j = i + 1; j < entities.length; j++) {
        const entity1 = entities[i]
        const entity2 = entities[j]

        // Count co-occurrences in results
        const cooccurrences = context.results.filter(result => {
          const text = `${result.title} ${result.snippet}`.toLowerCase()
          return text.includes(entity1.name.toLowerCase()) && 
                 text.includes(entity2.name.toLowerCase())
        })

        if (cooccurrences.length > 0) {
          const confidence = Math.min(0.9, cooccurrences.length * 0.2)
          
          this.addRelationship({
            source: entity1.id,
            target: entity2.id,
            type: 'co_occurs',
            confidence,
            evidence: cooccurrences.map(r => r.url)
          })
        }
      }
    }
  }

  private addTimelineEvents(timeline: any[]): void {
    this.graph.timeline = timeline.map(event => ({
      ...event,
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }))
  }

  private finalizeGraph(): void {
    // Remove low-confidence entities and relationships
    this.graph.entities = this.graph.entities.filter(entity => entity.confidence >= 0.3)
    this.graph.relationships = this.graph.relationships.filter(rel => rel.confidence >= 0.3)
    this.graph.topics = this.graph.topics.filter(topic => topic.confidence >= 0.3)

    // Update indices
    this.rebuildIndices()
  }

  private rebuildIndices(): void {
    this.entityIndex.clear()
    this.relationshipIndex.clear()
    this.topicIndex.clear()

    this.graph.entities.forEach(entity => {
      this.entityIndex.set(entity.name.toLowerCase(), entity)
    })

    this.graph.relationships.forEach(rel => {
      const id = this.generateRelationshipId(rel.source, rel.target, rel.type)
      this.relationshipIndex.set(id, rel)
    })

    this.graph.topics.forEach(topic => {
      this.topicIndex.set(topic.name.toLowerCase(), topic)
    })
  }

  private mergeEntities(existing: KnowledgeEntity, newEntity: KnowledgeEntity): KnowledgeEntity {
    existing.aliases = [...new Set([...existing.aliases, ...newEntity.aliases])]
    existing.sources = [...new Set([...existing.sources, ...newEntity.sources])]
    existing.confidence = Math.max(existing.confidence, newEntity.confidence)
    
    // Merge properties
    existing.properties = { ...existing.properties, ...newEntity.properties }
    
    return existing
  }

  private mergeTopics(existing: KnowledgeTopic, newTopic: KnowledgeTopic): KnowledgeTopic {
    existing.keywords = [...new Set([...existing.keywords, ...newTopic.keywords])]
    existing.entities = [...new Set([...existing.entities, ...newTopic.entities])]
    existing.subtopics = [...new Set([...existing.subtopics, ...newTopic.subtopics])]
    existing.confidence = Math.max(existing.confidence, newTopic.confidence)
    
    return existing
  }

  private generateEntityId(name: string): string {
    return `entity_${name.toLowerCase().replace(/\s+/g, '_')}_${Date.now()}`
  }

  private generateRelationshipId(source: string, target: string, type: string): string {
    return `rel_${source}_${target}_${type}`.replace(/\s+/g, '_')
  }

  private generateTopicId(name: string): string {
    return `topic_${name.toLowerCase().replace(/\s+/g, '_')}_${Date.now()}`
  }

  private async extractEntitiesFromText(text: string): Promise<Array<{ name: string; type: string; confidence: number }>> {
    // Simple entity extraction - would use NER in production
    const entities = []
    
    // Extract proper nouns
    const properNouns = text.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || []
    properNouns.forEach(noun => {
      entities.push({
        name: noun,
        type: 'unknown',
        confidence: 0.6
      })
    })

    return entities
  }

  private extractTopicsFromText(text: string): string[] {
    // Simple topic extraction - would use topic modeling in production
    const words = text.toLowerCase().split(/\s+/)
    const topics = []
    
    // Extract meaningful phrases
    for (let i = 0; i < words.length - 1; i++) {
      const phrase = `${words[i]} ${words[i + 1]}`
      if (phrase.length > 6 && !this.isStopPhrase(phrase)) {
        topics.push(phrase)
      }
    }

    return [...new Set(topics)]
  }

  private isStopPhrase(phrase: string): boolean {
    const stopPhrases = ['the of', 'in the', 'to the', 'and the', 'for the', 'on the']
    return stopPhrases.includes(phrase)
  }

  private expandCommunity(startEntityId: string, visited: Set<string>): string[] {
    const community = [startEntityId]
    const queue = [startEntityId]
    visited.add(startEntityId)

    while (queue.length > 0) {
      const entityId = queue.shift()!
      const neighbors = this.getEntityNeighbors(entityId)

      neighbors.forEach(neighbor => {
        if (!visited.has(neighbor.id)) {
          visited.add(neighbor.id)
          community.push(neighbor.id)
          queue.push(neighbor.id)
        }
      })
    }

    return community
  }

  private calculateCommunityStrength(community: string[]): number {
    let internalEdges = 0
    let externalEdges = 0

    community.forEach(entityId => {
      const relationships = this.findRelationships(entityId)
      relationships.forEach(rel => {
        const otherId = rel.source === entityId ? rel.target : rel.source
        if (community.includes(otherId)) {
          internalEdges++
        } else {
          externalEdges++
        }
      })
    })

    return internalEdges / (internalEdges + externalEdges + 1)
  }

  private countConnectedComponents(): number {
    const visited = new Set<string>()
    let components = 0

    this.graph.entities.forEach(entity => {
      if (!visited.has(entity.id)) {
        this.dfsVisit(entity.id, visited)
        components++
      }
    })

    return components
  }

  private dfsVisit(entityId: string, visited: Set<string>): void {
    visited.add(entityId)
    const neighbors = this.getEntityNeighbors(entityId)
    
    neighbors.forEach(neighbor => {
      if (!visited.has(neighbor.id)) {
        this.dfsVisit(neighbor.id, visited)
      }
    })
  }

  private exportToCytoscape(): any {
    return {
      elements: {
        nodes: this.graph.entities.map(entity => ({
          data: {
            id: entity.id,
            label: entity.name,
            type: entity.type,
            confidence: entity.confidence
          }
        })),
        edges: this.graph.relationships.map(rel => ({
          data: {
            id: rel.id,
            source: rel.source,
            target: rel.target,
            label: rel.type,
            confidence: rel.confidence
          }
        }))
      }
    }
  }

  private exportToD3(): any {
    return {
      nodes: this.graph.entities.map(entity => ({
        id: entity.id,
        name: entity.name,
        type: entity.type,
        confidence: entity.confidence
      })),
      links: this.graph.relationships.map(rel => ({
        source: rel.source,
        target: rel.target,
        type: rel.type,
        confidence: rel.confidence
      }))
    }
  }

  private exportToGraphML(): string {
    // Simple GraphML export - would be more complete in production
    let graphml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    graphml += '<graphml xmlns="http://graphml.graphdrawing.org/xmlns">\n'
    graphml += '<graph id="knowledge_graph" edgedefault="undirected">\n'

    // Add nodes
    this.graph.entities.forEach(entity => {
      graphml += `<node id="${entity.id}">\n`
      graphml += `<data key="name">${entity.name}</data>\n`
      graphml += `<data key="type">${entity.type}</data>\n`
      graphml += '</node>\n'
    })

    // Add edges
    this.graph.relationships.forEach(rel => {
      graphml += `<edge source="${rel.source}" target="${rel.target}">\n`
      graphml += `<data key="type">${rel.type}</data>\n`
      graphml += '</edge>\n'
    })

    graphml += '</graph>\n</graphml>'
    return graphml
  }

  private importFromJSON(data: any): void {
    this.graph = data
    this.rebuildIndices()
  }

  private importFromCytoscape(data: any): void {
    this.resetGraph()
    
    if (data.elements) {
      // Import nodes
      data.elements.nodes?.forEach((node: any) => {
        this.addEntity({
          id: node.data.id,
          name: node.data.label || node.data.id,
          type: node.data.type || 'unknown',
          confidence: node.data.confidence || 0.5
        })
      })

      // Import edges
      data.elements.edges?.forEach((edge: any) => {
        this.addRelationship({
          id: edge.data.id,
          source: edge.data.source,
          target: edge.data.target,
          type: edge.data.label || 'related',
          confidence: edge.data.confidence || 0.5
        })
      })
    }
  }

  private importFromD3(data: any): void {
    this.resetGraph()
    
    // Import nodes
    data.nodes?.forEach((node: any) => {
      this.addEntity({
        id: node.id,
        name: node.name || node.id,
        type: node.type || 'unknown',
        confidence: node.confidence || 0.5
      })
    })

    // Import links
    data.links?.forEach((link: any) => {
      this.addRelationship({
        source: link.source,
        target: link.target,
        type: link.type || 'related',
        confidence: link.confidence || 0.5
      })
    })
  }
}