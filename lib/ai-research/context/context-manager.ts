// Research Context Manager
// Manages research context, state, and cross-context relationships

import { ResearchContext, ResearchSession, KnowledgeGraph, Insight, EntityRelationship } from '../types'

/**
 * Research Context Manager
 * Manages multiple research contexts and their relationships
 */
export class ContextManager {
  private contexts: Map<string, ResearchContext> = new Map()
  private sessions: Map<string, ResearchSession> = new Map()
  private relationships: Map<string, EntityRelationship[]> = new Map()

  /**
   * Add a research context
   */
  addContext(contextId: string, context: ResearchContext): void {
    this.contexts.set(contextId, context)
  }

  /**
   * Get a research context
   */
  getContext(contextId: string): ResearchContext | undefined {
    return this.contexts.get(contextId)
  }

  /**
   * Update a research context
   */
  updateContext(contextId: string, updates: Partial<ResearchContext>): void {
    const context = this.contexts.get(contextId)
    if (context) {
      this.contexts.set(contextId, { ...context, ...updates, lastUpdated: new Date().toISOString() })
    }
  }

  /**
   * Delete a research context
   */
  deleteContext(contextId: string): boolean {
    return this.contexts.delete(contextId)
  }

  /**
   * List all contexts
   */
  listContexts(): ResearchContext[] {
    return Array.from(this.contexts.values())
  }

  /**
   * Find relationships between contexts
   */
  async findRelationships(contextIds: string[]): Promise<EntityRelationship[]> {
    const relationships: EntityRelationship[] = []
    const contexts = contextIds.map(id => this.contexts.get(id)).filter(Boolean) as ResearchContext[]

    if (contexts.length < 2) {
      return relationships
    }

    // Find entity overlaps between contexts
    for (let i = 0; i < contexts.length; i++) {
      for (let j = i + 1; j < contexts.length; j++) {
        const context1 = contexts[i]
        const context2 = contexts[j]

        // Find common entities in results
        const entities1 = this.extractEntitiesFromContext(context1)
        const entities2 = this.extractEntitiesFromContext(context2)

        const commonEntities = entities1.filter(e1 => 
          entities2.some(e2 => e1.toLowerCase() === e2.toLowerCase())
        )

        commonEntities.forEach(entity => {
          relationships.push({
            entity1: context1.topic,
            entity2: context2.topic,
            relationship: `shares_entity_${entity}`,
            strength: 0.7,
            evidence: []
          })
        })

        // Find topic overlaps
        const topics1 = context1.insights.filter(i => i.type === 'fact').map(i => i.content)
        const topics2 = context2.insights.filter(i => i.type === 'fact').map(i => i.content)

        const topicSimilarity = this.calculateTopicSimilarity(topics1, topics2)
        if (topicSimilarity > 0.3) {
          relationships.push({
            entity1: context1.topic,
            entity2: context2.topic,
            relationship: 'related_topics',
            strength: topicSimilarity,
            evidence: []
          })
        }
      }
    }

    return relationships
  }

  /**
   * Generate cross-context insights
   */
  async generateCrossContextInsights(): Promise<Insight[]> {
    const contexts = Array.from(this.contexts.values())
    const insights: Insight[] = []

    if (contexts.length < 2) {
      return insights
    }

    // Find patterns across contexts
    const allTopics = contexts.flatMap(c => c.insights.map(i => i.content))
    const topicFrequency = this.calculateTopicFrequency(allTopics)

    // Generate insights about common themes
    Object.entries(topicFrequency)
      .filter(([, frequency]) => frequency > 1)
      .forEach(([topic, frequency]) => {
        insights.push({
          id: `cross-insight-${Date.now()}-${Math.random()}`,
          type: 'correlation',
          content: `"${topic}" appears across ${frequency} different research contexts`,
          evidence: [],
          confidence: Math.min(0.9, frequency * 0.3),
          tags: ['cross-context', 'pattern'],
          timestamp: new Date().toISOString()
        })
      })

    // Find contradictions across contexts
    const contradictions = await this.findCrossContextContradictions(contexts)
    contradictions.forEach(contradiction => {
      insights.push({
        id: `contradiction-${Date.now()}-${Math.random()}`,
        type: 'contradiction',
        content: contradiction.description,
        evidence: [],
        confidence: contradiction.confidence,
        tags: ['cross-context', 'contradiction'],
        timestamp: new Date().toISOString()
      })
    })

    return insights
  }

  /**
   * Merge multiple contexts
   */
  mergeContexts(contextIds: string[], newContextId: string): ResearchContext {
    const contexts = contextIds.map(id => this.contexts.get(id)).filter(Boolean) as ResearchContext[]
    
    if (contexts.length === 0) {
      throw new Error('No valid contexts to merge')
    }

    const mergedContext: ResearchContext = {
      id: newContextId,
      topic: contexts.map(c => c.topic).join(' + '),
      queries: contexts.flatMap(c => c.queries),
      results: contexts.flatMap(c => c.results),
      insights: contexts.flatMap(c => c.insights),
      timeline: contexts.flatMap(c => c.timeline).sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      ),
      relationships: contexts.flatMap(c => c.relationships),
      confidence: contexts.reduce((sum, c) => sum + c.confidence, 0) / contexts.length,
      lastUpdated: new Date().toISOString(),
      metadata: {
        mergedFrom: contextIds,
        mergedAt: new Date().toISOString()
      }
    }

    // Remove duplicates
    mergedContext.results = this.deduplicateResults(mergedContext.results)
    mergedContext.insights = this.deduplicateInsights(mergedContext.insights)

    this.contexts.set(newContextId, mergedContext)
    return mergedContext
  }

  /**
   * Search across contexts
   */
  searchContexts(query: string): {
    contexts: ResearchContext[]
    results: any[]
    insights: Insight[]
  } {
    const queryLower = query.toLowerCase()
    const matchingContexts: ResearchContext[] = []
    const matchingResults: any[] = []
    const matchingInsights: Insight[] = []

    this.contexts.forEach(context => {
      // Check if context topic matches
      if (context.topic.toLowerCase().includes(queryLower)) {
        matchingContexts.push(context)
      }

      // Check results
      context.results.forEach(result => {
        if (result.title.toLowerCase().includes(queryLower) || 
            result.snippet.toLowerCase().includes(queryLower)) {
          matchingResults.push({ ...result, contextId: context.id })
        }
      })

      // Check insights
      context.insights.forEach(insight => {
        if (insight.content.toLowerCase().includes(queryLower) ||
            insight.tags.some(tag => tag.toLowerCase().includes(queryLower))) {
          matchingInsights.push({ ...insight, contextId: context.id })
        }
      })
    })

    return {
      contexts: matchingContexts,
      results: matchingResults,
      insights: matchingInsights
    }
  }

  /**
   * Get context statistics
   */
  getStatistics(): {
    totalContexts: number
    totalResults: number
    totalInsights: number
    averageConfidence: number
    topTopics: string[]
  } {
    const contexts = Array.from(this.contexts.values())
    
    const totalResults = contexts.reduce((sum, c) => sum + c.results.length, 0)
    const totalInsights = contexts.reduce((sum, c) => sum + c.insights.length, 0)
    const averageConfidence = contexts.length > 0 
      ? contexts.reduce((sum, c) => sum + c.confidence, 0) / contexts.length 
      : 0

    // Get top topics
    const allTopics = contexts.flatMap(c => 
      c.insights.map(i => i.content).concat([c.topic])
    )
    const topicFrequency = this.calculateTopicFrequency(allTopics)
    const topTopics = Object.entries(topicFrequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([topic]) => topic)

    return {
      totalContexts: contexts.length,
      totalResults,
      totalInsights,
      averageConfidence,
      topTopics
    }
  }

  /**
   * Export context data
   */
  exportContext(contextId: string): any {
    const context = this.contexts.get(contextId)
    if (!context) {
      throw new Error(`Context ${contextId} not found`)
    }

    return {
      context,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    }
  }

  /**
   * Import context data
   */
  importContext(data: any): string {
    if (!data.context || !data.context.id) {
      throw new Error('Invalid context data')
    }

    const contextId = data.context.id
    this.contexts.set(contextId, data.context)
    return contextId
  }

  // Helper methods

  private extractEntitiesFromContext(context: ResearchContext): string[] {
    const entities = new Set<string>()

    // Extract from results
    context.results.forEach(result => {
      const text = `${result.title} ${result.snippet}`.toLowerCase()
      const properNouns = text.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || []
      properNouns.forEach(noun => entities.add(noun))
    })

    // Extract from insights
    context.insights.forEach(insight => {
      const properNouns = insight.content.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || []
      properNouns.forEach(noun => entities.add(noun))
    })

    return Array.from(entities)
  }

  private calculateTopicSimilarity(topics1: string[], topics2: string[]): number {
    if (topics1.length === 0 || topics2.length === 0) {
      return 0
    }

    const words1 = new Set(topics1.join(' ').toLowerCase().split(/\s+/))
    const words2 = new Set(topics2.join(' ').toLowerCase().split(/\s+/))

    const intersection = new Set([...words1].filter(word => words2.has(word)))
    const union = new Set([...words1, ...words2])

    return intersection.size / union.size
  }

  private calculateTopicFrequency(topics: string[]): Record<string, number> {
    const frequency: Record<string, number> = {}
    
    topics.forEach(topic => {
      const words = topic.toLowerCase().split(/\s+/)
      words.forEach(word => {
        if (word.length > 3) { // Filter out short words
          frequency[word] = (frequency[word] || 0) + 1
        }
      })
    })

    return frequency
  }

  private async findCrossContextContradictions(contexts: ResearchContext[]): Promise<Array<{
    description: string
    confidence: number
    contexts: string[]
  }>> {
    const contradictions = []

    for (let i = 0; i < contexts.length; i++) {
      for (let j = i + 1; j < contexts.length; j++) {
        const context1 = contexts[i]
        const context2 = contexts[j]

        // Look for contradictory insights
        context1.insights.forEach(insight1 => {
          context2.insights.forEach(insight2 => {
            if (this.areInsightsContradictory(insight1, insight2)) {
              contradictions.push({
                description: `Contradiction found: "${insight1.content}" vs "${insight2.content}"`,
                confidence: Math.min(insight1.confidence, insight2.confidence),
                contexts: [context1.id, context2.id]
              })
            }
          })
        })
      }
    }

    return contradictions
  }

  private areInsightsContradictory(insight1: Insight, insight2: Insight): boolean {
    // Simple contradiction detection - would use more sophisticated methods in production
    const content1 = insight1.content.toLowerCase()
    const content2 = insight2.content.toLowerCase()

    const contradictoryPairs = [
      ['increase', 'decrease'],
      ['rise', 'fall'],
      ['improve', 'worsen'],
      ['positive', 'negative'],
      ['true', 'false'],
      ['yes', 'no']
    ]

    return contradictoryPairs.some(([word1, word2]) =>
      (content1.includes(word1) && content2.includes(word2)) ||
      (content1.includes(word2) && content2.includes(word1))
    )
  }

  private deduplicateResults(results: any[]): any[] {
    const seen = new Set<string>()
    return results.filter(result => {
      const key = result.url || result.title || ''
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  private deduplicateInsights(insights: Insight[]): Insight[] {
    const seen = new Set<string>()
    return insights.filter(insight => {
      const key = insight.content.toLowerCase()
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }
}