// Analysis Workflow
// Specialized workflow for content analysis and insights

import { ResearchWorkflowEngine } from './research-workflow'

/**
 * Analysis Workflow Engine
 * Specialized for content analysis operations
 */
export class AnalysisWorkflow extends ResearchWorkflowEngine {
  
  /**
   * Create content analysis workflow
   */
  createContentAnalysisWorkflow(config: {
    urls?: string[]
    searchQuery?: string
    analysisDepth?: 'basic' | 'detailed' | 'comprehensive'
    includeComparison?: boolean
    focusAreas?: string[]
  }) {
    const {
      urls = [],
      searchQuery,
      analysisDepth = 'detailed',
      includeComparison = false,
      focusAreas = []
    } = config

    const steps = []

    // Step 1: Content gathering (if no URLs provided)
    if (urls.length === 0 && searchQuery) {
      steps.push({
        name: 'Gather Content',
        type: 'search' as const,
        config: {
          query: searchQuery,
          includeWeb: true,
          maxResults: analysisDepth === 'basic' ? 5 : analysisDepth === 'detailed' ? 10 : 15,
          providers: ['google'],
          includeContent: true
        }
      })
    }

    // Step 2: Content analysis
    steps.push({
      name: 'Analyze Content',
      type: 'analyze' as const,
      config: {
        urls: urls.length > 0 ? urls : undefined,
        depth: analysisDepth,
        includeTopics: true,
        includeEntities: true,
        includeSentiment: true,
        includeCredibility: true,
        focusAreas,
        maxAnalyses: analysisDepth === 'basic' ? 5 : analysisDepth === 'detailed' ? 10 : 20
      },
      dependencies: urls.length === 0 ? ['step-1'] : undefined
    })

    // Step 3: Topic extraction and analysis
    if (analysisDepth !== 'basic') {
      steps.push({
        name: 'Extract Topics',
        type: 'extract' as const,
        config: {
          extractType: 'topics',
          maxTopics: analysisDepth === 'detailed' ? 15 : 25,
          includeSubtopics: analysisDepth === 'comprehensive',
          minRelevance: 0.1
        },
        dependencies: ['step-2']
      })
    }

    // Step 4: Entity extraction and relationships
    if (analysisDepth === 'comprehensive') {
      steps.push({
        name: 'Extract Entities and Relationships',
        type: 'extract' as const,
        config: {
          extractType: 'entities',
          includeRelationships: true,
          entityTypes: ['person', 'organization', 'location', 'concept'],
          minConfidence: 0.5
        },
        dependencies: ['step-2']
      })
    }

    // Step 5: Comparative analysis (if requested)
    if (includeComparison && (urls.length > 1 || searchQuery)) {
      steps.push({
        name: 'Comparative Analysis',
        type: 'compare' as const,
        config: {
          compareType: 'content_comparison',
          dimensions: ['sentiment', 'credibility', 'topics', 'coverage'],
          includeAnalysis: true
        },
        dependencies: ['step-2']
      })
    }

    // Step 6: Synthesis and insights
    const synthesisDependencies = ['step-2']
    if (analysisDepth !== 'basic') synthesisDependencies.push('step-3')
    if (analysisDepth === 'comprehensive') synthesisDependencies.push('step-4')
    if (includeComparison) synthesisDependencies.push('step-5')

    steps.push({
      name: 'Generate Insights',
      type: 'synthesize' as const,
      config: {
        format: 'analysis_report',
        includeRecommendations: true,
        includeVisualizationSuggestions: analysisDepth === 'comprehensive',
        focusAreas
      },
      dependencies: synthesisDependencies
    })

    return this.createWorkflow({
      name: 'Content Analysis',
      description: `${analysisDepth} content analysis workflow`,
      steps
    })
  }

  /**
   * Create sentiment analysis workflow
   */
  createSentimentAnalysisWorkflow(config: {
    sources: string[]
    timeframe?: string
    includeComparison?: boolean
    trackChanges?: boolean
  }) {
    const {
      sources,
      timeframe = 'past_month',
      includeComparison = true,
      trackChanges = false
    } = config

    const steps = [
      {
        name: 'Gather Content',
        type: 'search' as const,
        config: {
          sources,
          timeframe,
          includeContent: true,
          maxResults: 20
        }
      },
      {
        name: 'Sentiment Analysis',
        type: 'analyze' as const,
        config: {
          depth: 'detailed',
          includeSentiment: true,
          includeTopics: true,
          focus: 'sentiment_analysis'
        },
        dependencies: ['step-1']
      }
    ]

    if (includeComparison) {
      steps.push({
        name: 'Compare Sentiment Across Sources',
        type: 'compare' as const,
        config: {
          compareType: 'sentiment_comparison',
          dimensions: ['sentiment', 'emotional_tone', 'subjectivity']
        },
        dependencies: ['step-2']
      })
    }

    if (trackChanges) {
      steps.push({
        name: 'Track Sentiment Changes',
        type: 'extract' as const,
        config: {
          extractType: 'timeline',
          focus: 'sentiment_changes',
          timeframe
        },
        dependencies: ['step-2']
      })
    }

    steps.push({
      name: 'Generate Sentiment Report',
      type: 'synthesize' as const,
      config: {
        format: 'sentiment_report',
        includeVisualizationSuggestions: true,
        includeRecommendations: true
      },
      dependencies: includeComparison && trackChanges ? 
        ['step-2', 'step-3', 'step-4'] : 
        includeComparison ? ['step-2', 'step-3'] : 
        trackChanges ? ['step-2', 'step-4'] : ['step-2']
    })

    return this.createWorkflow({
      name: 'Sentiment Analysis',
      description: 'Comprehensive sentiment analysis across sources',
      steps
    })
  }

  /**
   * Create trend analysis workflow
   */
  createTrendAnalysisWorkflow(config: {
    topic: string
    timeframe?: string
    sources?: string[]
    includeForecasting?: boolean
    granularity?: 'daily' | 'weekly' | 'monthly'
  }) {
    const {
      topic,
      timeframe = 'past_year',
      sources = [],
      includeForecasting = false,
      granularity = 'weekly'
    } = config

    const steps = [
      {
        name: 'Historical Data Collection',
        type: 'search' as const,
        config: {
          query: topic,
          timeframe,
          sources,
          includeContent: true,
          maxResults: 50,
          sortBy: 'date'
        }
      },
      {
        name: 'Temporal Analysis',
        type: 'extract' as const,
        config: {
          extractType: 'timeline',
          granularity,
          includeMetrics: true,
          focus: 'trend_analysis'
        },
        dependencies: ['step-1']
      },
      {
        name: 'Pattern Recognition',
        type: 'analyze' as const,
        config: {
          depth: 'detailed',
          includeTopics: true,
          focus: 'pattern_recognition',
          temporalAnalysis: true
        },
        dependencies: ['step-2']
      }
    ]

    if (includeForecasting) {
      steps.push({
        name: 'Trend Forecasting',
        type: 'synthesize' as const,
        config: {
          format: 'forecast_analysis',
          includeConfidenceIntervals: true,
          forecastPeriod: granularity === 'daily' ? '30d' : granularity === 'weekly' ? '12w' : '6m'
        },
        dependencies: ['step-3']
      })
    }

    steps.push({
      name: 'Generate Trend Report',
      type: 'synthesize' as const,
      config: {
        format: 'trend_report',
        includeVisualizationSuggestions: true,
        includeRecommendations: true,
        includeForecasting
      },
      dependencies: includeForecasting ? ['step-3', 'step-4'] : ['step-3']
    })

    return this.createWorkflow({
      name: 'Trend Analysis',
      description: `Trend analysis for ${topic} over ${timeframe}`,
      steps
    })
  }

  /**
   * Create competitive analysis workflow
   */
  createCompetitiveAnalysisWorkflow(config: {
    competitors: string[]
    dimensions?: string[]
    includeMarketData?: boolean
    timeframe?: string
  }) {
    const {
      competitors,
      dimensions = ['reputation', 'market_presence', 'innovation', 'customer_sentiment'],
      includeMarketData = true,
      timeframe = 'past_quarter'
    } = config

    const steps = [
      {
        name: 'Competitor Data Collection',
        type: 'search' as const,
        config: {
          batchQueries: competitors,
          timeframe,
          includeContent: true,
          maxResults: 15,
          includeNews: true
        }
      },
      {
        name: 'Individual Competitor Analysis',
        type: 'analyze' as const,
        config: {
          depth: 'detailed',
          includeTopics: true,
          includeSentiment: true,
          includeCredibility: true,
          batchAnalysis: true
        },
        dependencies: ['step-1']
      },
      {
        name: 'Competitive Comparison',
        type: 'compare' as const,
        config: {
          subjects: competitors,
          dimensions,
          includeAnalysis: true,
          outputFormat: 'matrix'
        },
        dependencies: ['step-2']
      }
    ]

    if (includeMarketData) {
      steps.push({
        name: 'Market Context Analysis',
        type: 'search' as const,
        config: {
          query: `${competitors.join(' OR ')} market analysis industry trends`,
          timeframe,
          includeContent: true,
          maxResults: 10,
          focus: 'market_data'
        },
        dependencies: ['step-1']
      })

      steps.push({
        name: 'Market-Informed Insights',
        type: 'synthesize' as const,
        config: {
          format: 'competitive_analysis_report',
          includeMarketContext: true,
          includeRecommendations: true,
          includeVisualizationSuggestions: true
        },
        dependencies: ['step-3', 'step-4']
      })
    } else {
      steps.push({
        name: 'Generate Competitive Report',
        type: 'synthesize' as const,
        config: {
          format: 'competitive_analysis_report',
          includeRecommendations: true,
          includeVisualizationSuggestions: true
        },
        dependencies: ['step-3']
      })
    }

    return this.createWorkflow({
      name: 'Competitive Analysis',
      description: `Competitive analysis of ${competitors.join(', ')}`,
      steps
    })
  }
}