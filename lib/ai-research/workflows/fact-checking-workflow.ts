// Fact-Checking Workflow
// Specialized workflow for comprehensive fact-checking

import { ResearchWorkflowEngine } from './research-workflow'
import { factVerificationTool, batchFactVerificationTool } from '../tools/fact-verification'
import { claimExtractionTool } from '../tools/fact-verification'
import { webSearchTool } from '../tools/web-search'

/**
 * Fact-Checking Workflow Engine
 * Specialized for fact-checking operations
 */
export class FactCheckingWorkflow extends ResearchWorkflowEngine {
  
  /**
   * Create a comprehensive fact-checking workflow
   */
  createFactCheckWorkflow(config: {
    claims?: string[]
    sources?: string[]
    thoroughness?: 'basic' | 'standard' | 'comprehensive'
    requireMultipleSources?: boolean
    minCredibilityScore?: number
  }) {
    const {
      claims = [],
      sources = [],
      thoroughness = 'standard',
      requireMultipleSources = true,
      minCredibilityScore = 0.7
    } = config

    const steps = []

    // Step 1: Claim extraction (if no claims provided)
    if (claims.length === 0) {
      steps.push({
        name: 'Extract Claims',
        type: 'extract' as const,
        config: {
          extractType: 'claims',
          maxClaims: thoroughness === 'basic' ? 5 : thoroughness === 'standard' ? 10 : 15,
          prioritizeImportant: true
        }
      })
    }

    // Step 2: Evidence gathering
    steps.push({
      name: 'Gather Evidence',
      type: 'search' as const,
      config: {
        includeWeb: true,
        includeAcademic: thoroughness !== 'basic',
        maxResults: thoroughness === 'basic' ? 10 : thoroughness === 'standard' ? 15 : 25,
        providers: ['google', 'bing'],
        timeframe: 'any',
        includeContent: true,
        sources
      },
      dependencies: claims.length === 0 ? ['step-1'] : undefined
    })

    // Step 3: Fact verification
    steps.push({
      name: 'Verify Facts',
      type: 'verify' as const,
      config: {
        claims: claims.length > 0 ? claims : undefined,
        extractClaims: claims.length === 0,
        searchDepth: thoroughness === 'basic' ? 'basic' : 'thorough',
        requireMultipleSources,
        minCredibilityScore,
        maxClaims: thoroughness === 'basic' ? 5 : thoroughness === 'standard' ? 10 : 15
      },
      dependencies: ['step-2']
    })

    // Step 4: Source credibility analysis
    if (thoroughness !== 'basic') {
      steps.push({
        name: 'Analyze Source Credibility',
        type: 'analyze' as const,
        config: {
          depth: 'basic',
          includeCredibility: true,
          maxAnalyses: 5,
          focus: 'credibility_assessment'
        },
        dependencies: ['step-2']
      })
    }

    // Step 5: Contradiction detection
    if (thoroughness === 'comprehensive') {
      steps.push({
        name: 'Detect Contradictions',
        type: 'compare' as const,
        config: {
          compareType: 'contradiction_detection',
          sensitivityLevel: 'medium',
          includePartialContradictions: true
        },
        dependencies: ['step-3']
      })
    }

    // Step 6: Generate fact-check report
    steps.push({
      name: 'Generate Report',
      type: 'synthesize' as const,
      config: {
        format: 'fact_check_report',
        includeRecommendations: true,
        includeSourceAttribution: true,
        confidenceThreshold: 0.6
      },
      dependencies: thoroughness === 'comprehensive' ? 
        ['step-3', 'step-4', 'step-5'] : 
        thoroughness === 'standard' ? ['step-3', 'step-4'] : ['step-3']
    })

    return this.createWorkflow({
      name: 'Comprehensive Fact Check',
      description: `${thoroughness} fact-checking workflow`,
      steps
    })
  }

  /**
   * Quick fact-check workflow for single claims
   */
  createQuickFactCheck(claim: string, options: {
    timeframe?: string
    minSources?: number
    maxSearchResults?: number
  } = {}) {
    const {
      timeframe = 'any',
      minSources = 2,
      maxSearchResults = 10
    } = options

    return this.createWorkflow({
      name: 'Quick Fact Check',
      description: `Quick verification of: "${claim}"`,
      steps: [
        {
          name: 'Search Evidence',
          type: 'search' as const,
          config: {
            query: claim,
            includeWeb: true,
            maxResults: maxSearchResults,
            providers: ['google'],
            timeframe,
            includeContent: false
          }
        },
        {
          name: 'Verify Claim',
          type: 'verify' as const,
          config: {
            claims: [claim],
            searchDepth: 'basic',
            requireMultipleSources: minSources > 1,
            minCredibilityScore: 0.6
          },
          dependencies: ['step-1']
        }
      ]
    })
  }

  /**
   * Batch fact-checking workflow
   */
  createBatchFactCheck(claims: string[], options: {
    maxConcurrency?: number
    thoroughness?: 'basic' | 'standard'
    stopOnFirstFalse?: boolean
  } = {}) {
    const {
      maxConcurrency = 3,
      thoroughness = 'basic',
      stopOnFirstFalse = false
    } = options

    return this.createWorkflow({
      name: 'Batch Fact Check',
      description: `Batch verification of ${claims.length} claims`,
      steps: [
        {
          name: 'Batch Evidence Search',
          type: 'search' as const,
          config: {
            batchQueries: claims,
            maxConcurrency,
            includeWeb: true,
            maxResults: thoroughness === 'basic' ? 5 : 10,
            providers: ['google'],
            timeframe: 'any'
          }
        },
        {
          name: 'Batch Verification',
          type: 'verify' as const,
          config: {
            claims,
            maxConcurrency,
            searchDepth: thoroughness,
            stopOnFirstFalse,
            aggregateResults: true
          },
          dependencies: ['step-1']
        },
        {
          name: 'Generate Batch Report',
          type: 'synthesize' as const,
          config: {
            format: 'batch_fact_check_report',
            includeStatistics: true,
            includeRecommendations: true
          },
          dependencies: ['step-2']
        }
      ]
    })
  }

  /**
   * Real-time fact-checking workflow
   */
  createRealTimeFactCheck(config: {
    monitorSources?: string[]
    keywords?: string[]
    alertThreshold?: number
    checkInterval?: number
  } = {}) {
    const {
      monitorSources = [],
      keywords = [],
      alertThreshold = 0.3,
      checkInterval = 300000 // 5 minutes
    } = config

    return this.createWorkflow({
      name: 'Real-time Fact Check',
      description: 'Continuous monitoring and fact-checking',
      steps: [
        {
          name: 'Monitor Sources',
          type: 'search' as const,
          config: {
            monitorMode: true,
            sources: monitorSources,
            keywords,
            checkInterval,
            includeContent: true
          }
        },
        {
          name: 'Extract New Claims',
          type: 'extract' as const,
          config: {
            extractType: 'claims',
            realTime: true,
            prioritizeNew: true,
            maxClaims: 10
          },
          dependencies: ['step-1']
        },
        {
          name: 'Quick Verification',
          type: 'verify' as const,
          config: {
            extractClaims: true,
            searchDepth: 'basic',
            alertThreshold,
            realTimeMode: true
          },
          dependencies: ['step-2']
        },
        {
          name: 'Generate Alerts',
          type: 'synthesize' as const,
          config: {
            format: 'alert_report',
            includeUrgentOnly: true,
            notificationChannels: ['email', 'webhook']
          },
          dependencies: ['step-3']
        }
      ]
    })
  }

  /**
   * Cross-reference fact-checking workflow
   */
  createCrossReferenceFactCheck(primarySource: string, options: {
    referenceSources?: string[]
    crossReferenceDepth?: 'shallow' | 'deep'
    includeHistoricalData?: boolean
  } = {}) {
    const {
      referenceSources = [],
      crossReferenceDepth = 'shallow',
      includeHistoricalData = false
    } = options

    return this.createWorkflow({
      name: 'Cross-reference Fact Check',
      description: `Cross-referencing facts from ${primarySource}`,
      steps: [
        {
          name: 'Extract Primary Claims',
          type: 'extract' as const,
          config: {
            source: primarySource,
            extractType: 'claims',
            includeContext: true,
            maxClaims: crossReferenceDepth === 'shallow' ? 10 : 20
          }
        },
        {
          name: 'Search Reference Sources',
          type: 'search' as const,
          config: {
            sources: referenceSources,
            extractClaims: true,
            includeHistorical: includeHistoricalData,
            maxResults: crossReferenceDepth === 'shallow' ? 15 : 30,
            timeframe: includeHistoricalData ? 'any' : 'past_year'
          },
          dependencies: ['step-1']
        },
        {
          name: 'Cross-reference Verification',
          type: 'verify' as const,
          config: {
            extractClaims: true,
            crossReference: true,
            requireMultipleSources: true,
            minCredibilityScore: 0.8
          },
          dependencies: ['step-2']
        },
        {
          name: 'Detect Discrepancies',
          type: 'compare' as const,
          config: {
            compareType: 'discrepancy_detection',
            primarySource,
            referenceSources,
            sensitivityLevel: 'high'
          },
          dependencies: ['step-3']
        },
        {
          name: 'Generate Cross-reference Report',
          type: 'synthesize' as const,
          config: {
            format: 'cross_reference_report',
            includeDiscrepancies: true,
            includeRecommendations: true,
            includeSourceComparison: true
          },
          dependencies: ['step-4']
        }
      ]
    })
  }

  /**
   * Execute fact-checking with specific parameters
   */
  async executeFactCheck(
    workflowId: string,
    context: {
      topic?: string
      claims?: string[]
      sources?: string[]
      urgency?: 'low' | 'medium' | 'high'
    }
  ) {
    const workflow = await this.executeWorkflow(workflowId, {
      topic: context.topic || 'Fact Check',
      urgency: context.urgency || 'medium',
      ...context
    })

    // Post-process results for fact-checking specific insights
    if (workflow.results) {
      workflow.results.factCheckSummary = this.generateFactCheckSummary(workflow.results)
      workflow.results.credibilityAssessment = this.assessOverallCredibility(workflow.results)
      workflow.results.recommendations = this.generateFactCheckRecommendations(workflow.results)
    }

    return workflow
  }

  // Helper methods for fact-checking specific processing

  private generateFactCheckSummary(results: any): any {
    const verifications = results.verifications || []
    
    const summary = {
      totalClaims: verifications.length,
      verdicts: {
        true: verifications.filter((v: any) => v.verdict === 'true').length,
        false: verifications.filter((v: any) => v.verdict === 'false').length,
        partially_true: verifications.filter((v: any) => v.verdict === 'partially_true').length,
        unverified: verifications.filter((v: any) => v.verdict === 'unverified').length,
        disputed: verifications.filter((v: any) => v.verdict === 'disputed').length
      },
      averageConfidence: verifications.length > 0 
        ? verifications.reduce((sum: number, v: any) => sum + v.confidence, 0) / verifications.length
        : 0,
      highConfidenceClaims: verifications.filter((v: any) => v.confidence >= 0.8).length,
      problematicClaims: verifications.filter((v: any) => 
        v.verdict === 'false' || v.verdict === 'disputed'
      ).length
    }

    return summary
  }

  private assessOverallCredibility(results: any): any {
    const analyses = results.analyses || []
    const verifications = results.verifications || []

    const sourceCredibility = analyses.length > 0
      ? analyses.reduce((sum: number, a: any) => sum + (a.analysis?.credibility || 0), 0) / analyses.length
      : 0

    const verificationCredibility = verifications.length > 0
      ? verifications.reduce((sum: number, v: any) => sum + v.confidence, 0) / verifications.length
      : 0

    return {
      sourceCredibility,
      verificationCredibility,
      overallCredibility: (sourceCredibility + verificationCredibility) / 2,
      credibilityLevel: this.getCredibilityLevel((sourceCredibility + verificationCredibility) / 2)
    }
  }

  private getCredibilityLevel(score: number): string {
    if (score >= 0.8) return 'high'
    if (score >= 0.6) return 'medium'
    if (score >= 0.4) return 'low'
    return 'very_low'
  }

  private generateFactCheckRecommendations(results: any): string[] {
    const recommendations = []
    const summary = this.generateFactCheckSummary(results)
    const credibility = this.assessOverallCredibility(results)

    if (summary.problematicClaims > 0) {
      recommendations.push(`${summary.problematicClaims} claims require attention (false or disputed)`)
    }

    if (summary.verdicts.unverified > summary.totalClaims * 0.3) {
      recommendations.push('High number of unverified claims - consider additional sources')
    }

    if (credibility.overallCredibility < 0.6) {
      recommendations.push('Low overall credibility - verify with more authoritative sources')
    }

    if (summary.averageConfidence < 0.7) {
      recommendations.push('Low average confidence - consider more thorough verification')
    }

    if (recommendations.length === 0) {
      recommendations.push('Fact-checking results appear reliable')
    }

    return recommendations
  }
}