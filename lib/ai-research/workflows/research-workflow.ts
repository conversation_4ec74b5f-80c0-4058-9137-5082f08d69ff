// Research Workflow Engine
// Orchestrates complex research workflows with multiple steps

import { ResearchWorkflow, WorkflowStep, ResearchContext } from '../types'
import { GeneralResearchAgent } from '../agents/research-agent'
import { webSearchTool, academicSearchTool } from '../tools/web-search'
import { contentAnalysisTool } from '../tools/content-analysis'
import { factVerificationTool } from '../tools/fact-verification'

/**
 * Research Workflow Engine
 * Orchestrates multi-step research processes
 */
export class ResearchWorkflowEngine {
  private workflows: Map<string, ResearchWorkflow> = new Map()
  private agent: GeneralResearchAgent

  constructor(agent?: GeneralResearchAgent) {
    this.agent = agent || new GeneralResearchAgent()
  }

  /**
   * Create a new research workflow
   */
  createWorkflow(config: {
    name: string
    description: string
    steps: Omit<WorkflowStep, 'id' | 'status' | 'result' | 'error'>[]
  }): ResearchWorkflow {
    const workflow: ResearchWorkflow = {
      id: `workflow-${Date.now()}`,
      name: config.name,
      description: config.description,
      steps: config.steps.map((step, index) => ({
        ...step,
        id: `step-${index + 1}`,
        status: 'pending'
      })),
      status: 'pending',
      progress: 0
    }

    this.workflows.set(workflow.id, workflow)
    return workflow
  }

  /**
   * Execute a research workflow
   */
  async executeWorkflow(workflowId: string, context?: Partial<ResearchContext>): Promise<ResearchWorkflow> {
    const workflow = this.workflows.get(workflowId)
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`)
    }

    workflow.status = 'running'
    workflow.progress = 0

    try {
      const workflowContext: any = {
        results: [],
        insights: [],
        verifications: [],
        analyses: [],
        ...context
      }

      for (let i = 0; i < workflow.steps.length; i++) {
        const step = workflow.steps[i]
        
        // Check dependencies
        if (step.dependencies) {
          const dependenciesMet = step.dependencies.every(depId => {
            const depStep = workflow.steps.find(s => s.id === depId)
            return depStep?.status === 'completed'
          })

          if (!dependenciesMet) {
            throw new Error(`Dependencies not met for step ${step.id}`)
          }
        }

        step.status = 'running'
        
        try {
          step.result = await this.executeStep(step, workflowContext)
          step.status = 'completed'
        } catch (error) {
          step.status = 'failed'
          step.error = error instanceof Error ? error.message : 'Unknown error'
          throw error
        }

        workflow.progress = ((i + 1) / workflow.steps.length) * 100
      }

      workflow.status = 'completed'
      workflow.results = workflowContext

    } catch (error) {
      workflow.status = 'failed'
      workflow.error = error instanceof Error ? error.message : 'Unknown error'
      throw error
    }

    return workflow
  }

  /**
   * Execute a single workflow step
   */
  private async executeStep(step: WorkflowStep, context: any): Promise<any> {
    switch (step.type) {
      case 'search':
        return this.executeSearchStep(step, context)
      case 'analyze':
        return this.executeAnalyzeStep(step, context)
      case 'verify':
        return this.executeVerifyStep(step, context)
      case 'synthesize':
        return this.executeSynthesizeStep(step, context)
      case 'compare':
        return this.executeCompareStep(step, context)
      case 'extract':
        return this.executeExtractStep(step, context)
      default:
        throw new Error(`Unknown step type: ${step.type}`)
    }
  }

  /**
   * Execute search step
   */
  private async executeSearchStep(step: WorkflowStep, context: any): Promise<any> {
    const config = step.config
    const query = config.query || context.topic || ''
    
    if (!query) {
      throw new Error('No query provided for search step')
    }

    const searchResults = []

    // Web search
    if (config.includeWeb !== false) {
      const webResults = await webSearchTool.execute({
        query,
        maxResults: config.maxResults || 10,
        providers: config.providers || ['google'],
        timeframe: config.timeframe || 'any',
        includeContent: config.includeContent || false
      })
      
      if (webResults.results) {
        searchResults.push(...webResults.results.map(r => ({ ...r, type: 'web' })))
      }
    }

    // Academic search
    if (config.includeAcademic) {
      const academicResults = await academicSearchTool.execute({
        query,
        maxResults: config.maxAcademicResults || 5,
        sources: config.academicSources || ['semantic_scholar'],
        includeAbstracts: true
      })
      
      if (academicResults.results) {
        searchResults.push(...academicResults.results.map(r => ({ ...r, type: 'academic' })))
      }
    }

    context.results = [...(context.results || []), ...searchResults]
    return searchResults
  }

  /**
   * Execute analyze step
   */
  private async executeAnalyzeStep(step: WorkflowStep, context: any): Promise<any> {
    const config = step.config
    const results = context.results || []
    const analyses = []

    const urlsToAnalyze = results
      .filter((r: any) => r.url && r.type !== 'academic')
      .slice(0, config.maxAnalyses || 5)
      .map((r: any) => r.url)

    for (const url of urlsToAnalyze) {
      try {
        const analysis = await contentAnalysisTool.execute({
          url,
          analysisDepth: config.depth || 'detailed',
          includeTopics: config.includeTopics !== false,
          includeEntities: config.includeEntities !== false,
          includeSentiment: config.includeSentiment !== false,
          includeCredibility: config.includeCredibility !== false
        })

        analyses.push({
          url,
          analysis: analysis.analysis
        })
      } catch (error) {
        console.warn(`Analysis failed for ${url}:`, error)
      }
    }

    context.analyses = [...(context.analyses || []), ...analyses]
    return analyses
  }

  /**
   * Execute verify step
   */
  private async executeVerifyStep(step: WorkflowStep, context: any): Promise<any> {
    const config = step.config
    let claims: string[] = []

    // Extract claims from config or context
    if (config.claims) {
      claims = Array.isArray(config.claims) ? config.claims : [config.claims]
    } else if (config.extractClaims && context.analyses) {
      // Extract claims from analysis results
      claims = context.analyses
        .flatMap((a: any) => a.analysis?.keyPoints || [])
        .slice(0, config.maxClaims || 5)
    }

    if (claims.length === 0) {
      return []
    }

    const verifications = []

    for (const claim of claims) {
      try {
        const verification = await factVerificationTool.execute({
          claim,
          searchDepth: config.searchDepth || 'basic',
          timeframe: config.timeframe || 'any',
          requireMultipleSources: config.requireMultipleSources !== false,
          minCredibilityScore: config.minCredibilityScore || 0.7
        })

        verifications.push(verification)
      } catch (error) {
        console.warn(`Verification failed for claim "${claim}":`, error)
      }
    }

    context.verifications = [...(context.verifications || []), ...verifications]
    return verifications
  }

  /**
   * Execute synthesize step
   */
  private async executeSynthesizeStep(step: WorkflowStep, context: any): Promise<any> {
    const config = step.config
    
    // Use the research agent to synthesize information
    const session = await this.agent.startSession(context.topic || 'Research Synthesis')
    
    // Combine all context information
    const synthesisContext = {
      results: context.results || [],
      analyses: context.analyses || [],
      verifications: context.verifications || [],
      insights: context.insights || []
    }

    // Generate synthesis based on configuration
    const synthesis = {
      summary: await this.generateSummary(synthesisContext, config),
      keyFindings: await this.extractKeyFindings(synthesisContext, config),
      recommendations: await this.generateRecommendations(synthesisContext, config),
      confidence: this.calculateSynthesisConfidence(synthesisContext)
    }

    context.synthesis = synthesis
    return synthesis
  }

  /**
   * Execute compare step
   */
  private async executeCompareStep(step: WorkflowStep, context: any): Promise<any> {
    const config = step.config
    const subjects = config.subjects || []
    
    if (subjects.length < 2) {
      throw new Error('Compare step requires at least 2 subjects')
    }

    // Implementation would depend on comparison tools
    // For now, return a placeholder
    const comparison = {
      subjects,
      dimensions: [],
      summary: `Comparison of ${subjects.join(', ')}`,
      confidence: 0.5
    }

    context.comparison = comparison
    return comparison
  }

  /**
   * Execute extract step
   */
  private async executeExtractStep(step: WorkflowStep, context: any): Promise<any> {
    const config = step.config
    const extractType = config.extractType || 'entities'
    
    const results = context.results || []
    const extracted: any = {
      entities: [],
      topics: [],
      timeline: [],
      relationships: []
    }

    // Extract information based on type
    if (extractType === 'entities' || extractType === 'all') {
      // Extract entities from results
      results.forEach((result: any) => {
        if (result.content || result.snippet) {
          const entities = this.extractEntitiesFromText(result.content || result.snippet)
          extracted.entities.push(...entities)
        }
      })
    }

    if (extractType === 'timeline' || extractType === 'all') {
      // Extract timeline events
      results.forEach((result: any) => {
        if (result.content || result.snippet) {
          const events = this.extractTimelineEvents(result.content || result.snippet, result.url)
          extracted.timeline.push(...events)
        }
      })
    }

    context.extracted = extracted
    return extracted
  }

  /**
   * Get workflow status
   */
  getWorkflow(workflowId: string): ResearchWorkflow | undefined {
    return this.workflows.get(workflowId)
  }

  /**
   * List all workflows
   */
  listWorkflows(): ResearchWorkflow[] {
    return Array.from(this.workflows.values())
  }

  /**
   * Delete a workflow
   */
  deleteWorkflow(workflowId: string): boolean {
    return this.workflows.delete(workflowId)
  }

  // Helper methods

  private async generateSummary(context: any, config: any): Promise<string> {
    const results = context.results || []
    const analyses = context.analyses || []
    
    if (results.length === 0) {
      return 'No results to summarize'
    }

    // Simple summary generation - would use AI in production
    const keyPoints = analyses.flatMap((a: any) => a.analysis?.keyPoints || [])
    return keyPoints.slice(0, 3).join('. ') + '.'
  }

  private async extractKeyFindings(context: any, config: any): Promise<string[]> {
    const analyses = context.analyses || []
    const verifications = context.verifications || []
    
    const findings = []
    
    // Add verified facts
    verifications
      .filter((v: any) => v.verdict === 'true')
      .forEach((v: any) => findings.push(`Verified: ${v.claim}`))
    
    // Add key insights from analyses
    analyses.forEach((a: any) => {
      if (a.analysis?.keyPoints) {
        findings.push(...a.analysis.keyPoints.slice(0, 2))
      }
    })
    
    return findings.slice(0, 5)
  }

  private async generateRecommendations(context: any, config: any): Promise<string[]> {
    const verifications = context.verifications || []
    const recommendations = []
    
    // Generate recommendations based on verification results
    const unverifiedClaims = verifications.filter((v: any) => v.verdict === 'unverified')
    if (unverifiedClaims.length > 0) {
      recommendations.push('Further research needed for unverified claims')
    }
    
    const disputedClaims = verifications.filter((v: any) => v.verdict === 'disputed')
    if (disputedClaims.length > 0) {
      recommendations.push('Review disputed claims with additional sources')
    }
    
    return recommendations
  }

  private calculateSynthesisConfidence(context: any): number {
    const results = context.results || []
    const verifications = context.verifications || []
    
    if (results.length === 0) return 0
    
    let confidence = 0.5 // Base confidence
    
    // Boost for verified facts
    const verifiedCount = verifications.filter((v: any) => v.verdict === 'true').length
    confidence += Math.min(0.3, verifiedCount * 0.1)
    
    // Boost for result quantity
    confidence += Math.min(0.2, results.length * 0.02)
    
    return Math.min(1, confidence)
  }

  private extractEntitiesFromText(text: string): any[] {
    // Simple entity extraction - would use NER in production
    const entities = []
    const properNouns = text.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || []
    
    properNouns.forEach(noun => {
      entities.push({
        name: noun,
        type: 'unknown',
        mentions: 1
      })
    })
    
    return entities
  }

  private extractTimelineEvents(text: string, source: string): any[] {
    // Simple timeline extraction - would use more sophisticated methods in production
    const events = []
    const dateMatches = text.match(/\b\d{4}\b|\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b/g) || []
    
    dateMatches.forEach(date => {
      const context = this.extractContext(text, date, 100)
      if (context.length > 20) {
        events.push({
          date,
          event: context,
          source,
          importance: 0.5
        })
      }
    })
    
    return events
  }

  private extractContext(text: string, target: string, contextLength: number): string {
    const index = text.indexOf(target)
    if (index === -1) return ''

    const start = Math.max(0, index - contextLength / 2)
    const end = Math.min(text.length, index + target.length + contextLength / 2)

    return text.substring(start, end).trim()
  }
}

/**
 * Predefined workflow templates
 */
export const WorkflowTemplates = {
  /**
   * Basic research workflow
   */
  basicResearch: {
    name: 'Basic Research',
    description: 'Comprehensive research with search, analysis, and verification',
    steps: [
      {
        name: 'Initial Search',
        type: 'search' as const,
        config: {
          includeWeb: true,
          includeAcademic: true,
          maxResults: 10,
          maxAcademicResults: 5
        }
      },
      {
        name: 'Content Analysis',
        type: 'analyze' as const,
        config: {
          depth: 'detailed',
          maxAnalyses: 5
        },
        dependencies: ['step-1']
      },
      {
        name: 'Fact Verification',
        type: 'verify' as const,
        config: {
          extractClaims: true,
          maxClaims: 5,
          searchDepth: 'basic'
        },
        dependencies: ['step-2']
      },
      {
        name: 'Synthesis',
        type: 'synthesize' as const,
        config: {
          includeRecommendations: true
        },
        dependencies: ['step-1', 'step-2', 'step-3']
      }
    ]
  },

  /**
   * Fact-checking focused workflow
   */
  factChecking: {
    name: 'Fact Checking',
    description: 'Focused on verifying specific claims',
    steps: [
      {
        name: 'Targeted Search',
        type: 'search' as const,
        config: {
          includeWeb: true,
          maxResults: 15,
          timeframe: 'any'
        }
      },
      {
        name: 'Claim Verification',
        type: 'verify' as const,
        config: {
          searchDepth: 'thorough',
          requireMultipleSources: true,
          minCredibilityScore: 0.8
        },
        dependencies: ['step-1']
      },
      {
        name: 'Source Analysis',
        type: 'analyze' as const,
        config: {
          depth: 'basic',
          includeCredibility: true,
          maxAnalyses: 3
        },
        dependencies: ['step-1']
      }
    ]
  },

  /**
   * Academic research workflow
   */
  academicResearch: {
    name: 'Academic Research',
    description: 'Research focused on academic and scholarly sources',
    steps: [
      {
        name: 'Academic Search',
        type: 'search' as const,
        config: {
          includeWeb: false,
          includeAcademic: true,
          maxAcademicResults: 20,
          academicSources: ['semantic_scholar', 'arxiv']
        }
      },
      {
        name: 'Literature Analysis',
        type: 'analyze' as const,
        config: {
          depth: 'comprehensive',
          includeTopics: true,
          includeEntities: true
        },
        dependencies: ['step-1']
      },
      {
        name: 'Knowledge Extraction',
        type: 'extract' as const,
        config: {
          extractType: 'all'
        },
        dependencies: ['step-2']
      },
      {
        name: 'Research Synthesis',
        type: 'synthesize' as const,
        config: {
          includeRecommendations: true,
          format: 'academic'
        },
        dependencies: ['step-1', 'step-2', 'step-3']
      }
    ]
  }
}