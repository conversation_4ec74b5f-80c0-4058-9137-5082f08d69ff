// AI Research Types
// Type definitions for research tools and workflows

export interface SearchResult {
  title: string
  url: string
  snippet: string
  content?: string
  relevanceScore?: number
  timestamp?: string
  source?: string
  metadata?: Record<string, any>
}

export interface ResearchQuery {
  query: string
  intent: 'factual' | 'comparative' | 'analytical' | 'exploratory' | 'verification'
  depth: 'shallow' | 'medium' | 'deep'
  timeframe?: 'recent' | 'past_week' | 'past_month' | 'past_year' | 'any'
  sources?: string[]
  excludeSources?: string[]
  language?: string
  region?: string
}

export interface ResearchContext {
  id: string
  topic: string
  queries: ResearchQuery[]
  results: SearchResult[]
  insights: Insight[]
  timeline: TimelineEvent[]
  relationships: EntityRelationship[]
  confidence: number
  lastUpdated: string
  metadata: Record<string, any>
}

export interface Insight {
  id: string
  type: 'fact' | 'trend' | 'contradiction' | 'gap' | 'correlation' | 'prediction'
  content: string
  evidence: SearchResult[]
  confidence: number
  tags: string[]
  timestamp: string
}

export interface TimelineEvent {
  date: string
  event: string
  source: string
  importance: number
  category?: string
}

export interface EntityRelationship {
  entity1: string
  entity2: string
  relationship: string
  strength: number
  evidence: SearchResult[]
}

export interface ContentAnalysis {
  summary: string
  keyPoints: string[]
  sentiment: 'positive' | 'negative' | 'neutral' | 'mixed'
  topics: Topic[]
  entities: Entity[]
  readability: number
  credibility: number
  bias?: 'left' | 'right' | 'center' | 'unknown'
  factualness: number
}

export interface Topic {
  name: string
  relevance: number
  mentions: number
  sentiment: number
}

export interface Entity {
  name: string
  type: 'person' | 'organization' | 'location' | 'event' | 'concept' | 'product'
  mentions: number
  sentiment: number
  aliases?: string[]
}

export interface ResearchWorkflow {
  id: string
  name: string
  description: string
  steps: WorkflowStep[]
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  results?: any
  error?: string
}

export interface WorkflowStep {
  id: string
  name: string
  type: 'search' | 'analyze' | 'synthesize' | 'verify' | 'compare' | 'extract'
  config: Record<string, any>
  dependencies?: string[]
  status: 'pending' | 'running' | 'completed' | 'failed'
  result?: any
  error?: string
}

export interface VerificationResult {
  claim: string
  verdict: 'true' | 'false' | 'partially_true' | 'unverified' | 'disputed'
  confidence: number
  evidence: {
    supporting: SearchResult[]
    contradicting: SearchResult[]
    neutral: SearchResult[]
  }
  sources: {
    reliable: number
    questionable: number
    unknown: number
  }
  reasoning: string
}

export interface ComparisonResult {
  subjects: string[]
  dimensions: ComparisonDimension[]
  summary: string
  winner?: string
  confidence: number
}

export interface ComparisonDimension {
  name: string
  values: Record<string, any>
  winner?: string
  reasoning: string
  evidence: SearchResult[]
}

export interface ResearchAgent {
  id: string
  name: string
  description: string
  capabilities: string[]
  tools: string[]
  model: string
  systemPrompt: string
  temperature: number
  maxTokens: number
}

export interface ToolCall {
  id: string
  name: string
  parameters: Record<string, any>
  result?: any
  error?: string
  timestamp: string
  duration?: number
}

export interface ResearchSession {
  id: string
  topic: string
  agent: ResearchAgent
  context: ResearchContext
  toolCalls: ToolCall[]
  messages: Message[]
  status: 'active' | 'completed' | 'paused' | 'failed'
  startTime: string
  endTime?: string
}

export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system' | 'tool'
  content: string
  toolCalls?: ToolCall[]
  timestamp: string
}

export interface SourceCredibility {
  domain: string
  credibilityScore: number
  factors: {
    authority: number
    accuracy: number
    objectivity: number
    currency: number
    coverage: number
  }
  reputation: 'high' | 'medium' | 'low' | 'unknown'
  biasRating?: 'left' | 'lean_left' | 'center' | 'lean_right' | 'right' | 'mixed'
  factualReporting?: 'very_high' | 'high' | 'mostly_factual' | 'mixed' | 'low'
  lastUpdated: string
}

export interface SearchStrategy {
  name: string
  description: string
  queries: string[]
  sources: string[]
  filters: Record<string, any>
  maxResults: number
  diversityThreshold: number
}

export interface KnowledgeGraph {
  entities: KnowledgeEntity[]
  relationships: KnowledgeRelationship[]
  topics: KnowledgeTopic[]
  timeline: TimelineEvent[]
}

export interface KnowledgeEntity {
  id: string
  name: string
  type: string
  description: string
  aliases: string[]
  properties: Record<string, any>
  confidence: number
  sources: string[]
}

export interface KnowledgeRelationship {
  id: string
  source: string
  target: string
  type: string
  properties: Record<string, any>
  confidence: number
  evidence: string[]
}

export interface KnowledgeTopic {
  id: string
  name: string
  description: string
  keywords: string[]
  entities: string[]
  subtopics: string[]
  confidence: number
}

export interface ResearchMetrics {
  totalQueries: number
  totalResults: number
  averageRelevance: number
  sourcesDiversity: number
  factualAccuracy: number
  completeness: number
  timeliness: number
  credibilityScore: number
}

export interface ToolConfig {
  name: string
  description: string
  parameters: Record<string, any>
  required: string[]
  examples?: any[]
}

export interface AgentCapability {
  name: string
  description: string
  tools: string[]
  prompts: Record<string, string>
  config: Record<string, any>
}