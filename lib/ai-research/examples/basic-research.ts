// Basic Research Example
// Demonstrates core research capabilities

import { GeneralResearchAgent } from '../agents/research-agent'
import { webSearchTool, academicSearchTool } from '../tools/web-search'
import { contentAnalysisTool } from '../tools/content-analysis'
import { factVerificationTool } from '../tools/fact-verification'

/**
 * Basic Research Example
 * Shows how to conduct comprehensive research on a topic
 */
async function basicResearchExample() {
  console.log('🔍 Starting Basic Research Example...\n')

  // Initialize research agent
  const agent = new GeneralResearchAgent({
    model: 'gpt-4-turbo',
    temperature: 0.3,
    maxTokens: 4000
  })

  try {
    // Start research session
    console.log('📋 Starting research session...')
    const session = await agent.startSession('Renewable Energy Storage Technologies')

    // Conduct comprehensive research
    console.log('🔬 Conducting research...')
    const results = await agent.research('Renewable Energy Storage Technologies', {
      depth: 'medium',
      timeframe: 'past_year',
      includeAcademic: true,
      includeNews: true,
      verifyFacts: true
    })

    // Display results
    console.log('\n📊 Research Results:')
    console.log('===================')
    
    console.log(`\n📈 Confidence Score: ${(results.confidence * 100).toFixed(1)}%`)
    console.log(`📚 Total Sources: ${results.results.length}`)
    console.log(`💡 Insights Generated: ${results.insights.length}`)
    console.log(`📅 Timeline Events: ${results.timeline.length}`)
    console.log(`🔗 Relationships: ${results.relationships.length}`)

    // Show top insights
    console.log('\n🧠 Key Insights:')
    results.insights.slice(0, 5).forEach((insight, index) => {
      console.log(`${index + 1}. [${insight.type.toUpperCase()}] ${insight.content}`)
      console.log(`   Confidence: ${(insight.confidence * 100).toFixed(1)}%`)
      console.log(`   Tags: ${insight.tags.join(', ')}\n`)
    })

    // Show timeline highlights
    console.log('📅 Timeline Highlights:')
    results.timeline.slice(0, 3).forEach((event, index) => {
      console.log(`${index + 1}. ${event.date}: ${event.event.substring(0, 100)}...`)
      console.log(`   Category: ${event.category}, Importance: ${(event.importance * 100).toFixed(1)}%\n`)
    })

    // Show key relationships
    console.log('🔗 Key Relationships:')
    results.relationships.slice(0, 3).forEach((rel, index) => {
      console.log(`${index + 1}. ${rel.entity1} ${rel.relationship} ${rel.entity2}`)
      console.log(`   Strength: ${(rel.strength * 100).toFixed(1)}%\n`)
    })

    return results

  } catch (error) {
    console.error('❌ Research failed:', error)
    throw error
  }
}

/**
 * Individual Tool Examples
 * Shows how to use specific tools independently
 */
async function individualToolExamples() {
  console.log('\n🛠️ Individual Tool Examples...\n')

  try {
    // 1. Web Search Example
    console.log('1️⃣ Web Search Tool:')
    const searchResults = await webSearchTool.execute({
      query: 'battery storage technology 2024',
      maxResults: 5,
      providers: ['google', 'bing'],
      timeframe: 'past_month',
      includeContent: false
    })

    console.log(`   Found ${searchResults.results?.length || 0} results`)
    searchResults.results?.slice(0, 2).forEach((result, index) => {
      console.log(`   ${index + 1}. ${result.title}`)
      console.log(`      ${result.url}`)
      console.log(`      ${result.snippet.substring(0, 100)}...\n`)
    })

    // 2. Academic Search Example
    console.log('2️⃣ Academic Search Tool:')
    const academicResults = await academicSearchTool.execute({
      query: 'lithium ion battery efficiency',
      maxResults: 3,
      sources: ['semantic_scholar'],
      yearFrom: 2022,
      includeAbstracts: true
    })

    console.log(`   Found ${academicResults.results?.length || 0} academic papers`)
    academicResults.results?.slice(0, 2).forEach((paper, index) => {
      console.log(`   ${index + 1}. ${paper.title}`)
      console.log(`      Citations: ${paper.metadata?.citationCount || 'N/A'}`)
      console.log(`      Year: ${paper.metadata?.year || 'N/A'}\n`)
    })

    // 3. Content Analysis Example
    console.log('3️⃣ Content Analysis Tool:')
    if (searchResults.results && searchResults.results.length > 0) {
      const analysisResult = await contentAnalysisTool.execute({
        url: searchResults.results[0].url,
        analysisDepth: 'basic',
        includeTopics: true,
        includeSentiment: true,
        includeCredibility: true
      })

      console.log(`   Analysis completed for: ${searchResults.results[0].title}`)
      console.log(`   Sentiment: ${analysisResult.analysis?.sentiment || 'N/A'}`)
      console.log(`   Credibility: ${((analysisResult.analysis?.credibility || 0) * 100).toFixed(1)}%`)
      console.log(`   Topics found: ${analysisResult.analysis?.topics.length || 0}`)
      
      if (analysisResult.analysis?.topics) {
        console.log('   Top topics:')
        analysisResult.analysis.topics.slice(0, 3).forEach((topic, index) => {
          console.log(`     ${index + 1}. ${topic.name} (relevance: ${(topic.relevance * 100).toFixed(1)}%)`)
        })
      }
    }

    // 4. Fact Verification Example
    console.log('\n4️⃣ Fact Verification Tool:')
    const factResult = await factVerificationTool.execute({
      claim: 'Lithium-ion batteries can retain 80% capacity after 1000 charge cycles',
      searchDepth: 'basic',
      timeframe: 'any',
      requireMultipleSources: false,
      minCredibilityScore: 0.6
    })

    console.log(`   Claim: "${factResult.claim}"`)
    console.log(`   Verdict: ${factResult.verdict}`)
    console.log(`   Confidence: ${(factResult.confidence * 100).toFixed(1)}%`)
    console.log(`   Supporting evidence: ${factResult.evidence.supporting.length}`)
    console.log(`   Contradicting evidence: ${factResult.evidence.contradicting.length}`)
    console.log(`   Reasoning: ${factResult.reasoning.substring(0, 150)}...`)

  } catch (error) {
    console.error('❌ Tool example failed:', error)
  }
}

/**
 * Research Workflow Example
 * Shows a complete research workflow
 */
async function researchWorkflowExample() {
  console.log('\n🔄 Research Workflow Example...\n')

  const topic = 'Solar panel efficiency improvements 2024'
  
  try {
    console.log(`📋 Research Topic: ${topic}`)
    
    // Step 1: Initial search
    console.log('\n1️⃣ Initial Web Search...')
    const initialSearch = await webSearchTool.execute({
      query: topic,
      maxResults: 8,
      providers: ['google'],
      timeframe: 'past_year',
      includeContent: true
    })
    
    console.log(`   Found ${initialSearch.results?.length || 0} initial results`)

    // Step 2: Academic research
    console.log('\n2️⃣ Academic Research...')
    const academicSearch = await academicSearchTool.execute({
      query: 'solar panel efficiency perovskite silicon',
      maxResults: 5,
      sources: ['semantic_scholar'],
      yearFrom: 2023,
      includeAbstracts: true
    })
    
    console.log(`   Found ${academicSearch.results?.length || 0} academic papers`)

    // Step 3: Content analysis of top results
    console.log('\n3️⃣ Content Analysis...')
    const topUrls = initialSearch.results?.slice(0, 3).map(r => r.url).filter(Boolean) || []
    
    for (let i = 0; i < Math.min(2, topUrls.length); i++) {
      try {
        const analysis = await contentAnalysisTool.execute({
          url: topUrls[i],
          analysisDepth: 'detailed',
          includeTopics: true,
          includeEntities: true,
          includeCredibility: true
        })
        
        console.log(`   Analyzed: ${topUrls[i]}`)
        console.log(`   Key topics: ${analysis.analysis?.topics.slice(0, 3).map(t => t.name).join(', ') || 'None'}`)
        console.log(`   Credibility: ${((analysis.analysis?.credibility || 0) * 100).toFixed(1)}%`)
      } catch (error) {
        console.log(`   Failed to analyze: ${topUrls[i]}`)
      }
    }

    // Step 4: Fact verification
    console.log('\n4️⃣ Fact Verification...')
    const claims = [
      'Solar panel efficiency has improved by 20% in the last 5 years',
      'Perovskite solar cells can achieve over 25% efficiency'
    ]

    for (const claim of claims) {
      try {
        const verification = await factVerificationTool.execute({
          claim,
          searchDepth: 'basic',
          timeframe: 'past_year',
          requireMultipleSources: false,
          minCredibilityScore: 0.6
        })
        
        console.log(`   Claim: "${claim}"`)
        console.log(`   Verdict: ${verification.verdict} (${(verification.confidence * 100).toFixed(1)}% confidence)`)
      } catch (error) {
        console.log(`   Failed to verify: "${claim}"`)
      }
    }

    // Step 5: Summary
    console.log('\n📋 Research Summary:')
    console.log(`   Total sources analyzed: ${(initialSearch.results?.length || 0) + (academicSearch.results?.length || 0)}`)
    console.log(`   Academic papers: ${academicSearch.results?.length || 0}`)
    console.log(`   Claims verified: ${claims.length}`)
    console.log(`   Research completed successfully ✅`)

  } catch (error) {
    console.error('❌ Research workflow failed:', error)
  }
}

/**
 * Run all examples
 */
async function runAllExamples() {
  console.log('🚀 AI Research Tools - Basic Examples')
  console.log('=====================================\n')

  try {
    // Run basic research example
    await basicResearchExample()
    
    // Run individual tool examples
    await individualToolExamples()
    
    // Run workflow example
    await researchWorkflowExample()
    
    console.log('\n✅ All examples completed successfully!')
    
  } catch (error) {
    console.error('\n❌ Examples failed:', error)
    process.exit(1)
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  runAllExamples()
}

export {
  basicResearchExample,
  individualToolExamples,
  researchWorkflowExample,
  runAllExamples
}