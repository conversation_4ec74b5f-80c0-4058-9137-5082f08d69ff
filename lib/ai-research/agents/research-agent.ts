// Research Agent
// Intelligent agent for comprehensive research tasks

import { generateObject, generateText, tool } from 'ai'
import { openai } from '@ai-sdk/openai'
import { z } from 'zod'
import { ResearchAgent, ResearchSession, ResearchContext, ResearchQuery } from '../types'
import { webSearchTool, academicSearchTool, newsSearchTool } from '../tools/web-search'
import { contentAnalysisTool, topicExtractionTool } from '../tools/content-analysis'
import { factVerificationTool } from '../tools/fact-verification'

/**
 * General Research Agent
 * Conducts comprehensive research on any topic
 */
export class GeneralResearchAgent {
  private agent: ResearchAgent
  private session: ResearchSession | null = null

  constructor(config: Partial<ResearchAgent> = {}) {
    this.agent = {
      id: config.id || `research-agent-${Date.now()}`,
      name: config.name || 'General Research Agent',
      description: config.description || 'Conducts comprehensive research on any topic using multiple sources and analysis methods',
      capabilities: [
        'web_search',
        'academic_search',
        'content_analysis',
        'fact_verification',
        'synthesis',
        'timeline_creation'
      ],
      tools: [
        'webSearchTool',
        'academicSearchTool',
        'newsSearchTool',
        'contentAnalysisTool',
        'topicExtractionTool',
        'factVerificationTool'
      ],
      model: config.model || 'gpt-4-turbo',
      systemPrompt: this.getSystemPrompt(),
      temperature: config.temperature || 0.3,
      maxTokens: config.maxTokens || 4000
    }
  }

  /**
   * Start a new research session
   */
  async startSession(topic: string): Promise<ResearchSession> {
    this.session = {
      id: `session-${Date.now()}`,
      topic,
      agent: this.agent,
      context: {
        id: `context-${Date.now()}`,
        topic,
        queries: [],
        results: [],
        insights: [],
        timeline: [],
        relationships: [],
        confidence: 0,
        lastUpdated: new Date().toISOString(),
        metadata: {}
      },
      toolCalls: [],
      messages: [],
      status: 'active',
      startTime: new Date().toISOString()
    }

    // Add initial system message
    this.session.messages.push({
      id: `msg-${Date.now()}`,
      role: 'system',
      content: this.agent.systemPrompt,
      timestamp: new Date().toISOString()
    })

    return this.session
  }

  /**
   * Conduct comprehensive research on a topic
   */
  async research(topic: string, options: {
    depth?: 'shallow' | 'medium' | 'deep'
    timeframe?: 'recent' | 'past_week' | 'past_month' | 'past_year' | 'any'
    sources?: string[]
    includeAcademic?: boolean
    includeNews?: boolean
    verifyFacts?: boolean
  } = {}): Promise<ResearchContext> {
    if (!this.session) {
      await this.startSession(topic)
    }

    const {
      depth = 'medium',
      timeframe = 'any',
      sources = [],
      includeAcademic = true,
      includeNews = true,
      verifyFacts = true
    } = options

    try {
      // Phase 1: Initial exploration and query generation
      const queries = await this.generateResearchQueries(topic, depth)
      this.session!.context.queries = queries

      // Phase 2: Multi-source search
      const searchResults = await this.conductMultiSourceSearch(queries, {
        timeframe,
        sources,
        includeAcademic,
        includeNews
      })

      // Phase 3: Content analysis
      const analysisResults = await this.analyzeContent(searchResults)

      // Phase 4: Fact verification (if enabled)
      if (verifyFacts) {
        await this.verifyKeyFacts(analysisResults.keyFacts)
      }

      // Phase 5: Synthesis and insight generation
      const insights = await this.generateInsights(analysisResults)

      // Phase 6: Timeline and relationship extraction
      const timeline = await this.extractTimeline(searchResults)
      const relationships = await this.extractRelationships(searchResults)

      // Update context
      this.session!.context = {
        ...this.session!.context,
        results: searchResults,
        insights,
        timeline,
        relationships,
        confidence: this.calculateConfidence(searchResults, insights),
        lastUpdated: new Date().toISOString()
      }

      return this.session!.context
    } catch (error) {
      throw new Error(`Research failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generate targeted research queries
   */
  private async generateResearchQueries(topic: string, depth: string): Promise<ResearchQuery[]> {
    const queryCount = depth === 'shallow' ? 3 : depth === 'medium' ? 5 : 8

    const result = await generateObject({
      model: openai(this.agent.model),
      prompt: `Generate ${queryCount} diverse research queries for the topic: "${topic}". 
               Include different perspectives, subtopics, and question types.
               Consider factual, analytical, comparative, and exploratory angles.`,
      schema: z.object({
        queries: z.array(z.object({
          query: z.string(),
          intent: z.enum(['factual', 'comparative', 'analytical', 'exploratory', 'verification']),
          depth: z.enum(['shallow', 'medium', 'deep']),
          priority: z.number().min(1).max(10)
        }))
      })
    })

    return result.object.queries.map(q => ({
      ...q,
      timeframe: 'any' as const,
      sources: [],
      excludeSources: []
    }))
  }

  /**
   * Conduct multi-source search
   */
  private async conductMultiSourceSearch(
    queries: ResearchQuery[],
    options: any
  ): Promise<any[]> {
    const allResults = []

    for (const query of queries) {
      try {
        // Web search
        const webResults = await webSearchTool.execute({
          query: query.query,
          maxResults: query.depth === 'shallow' ? 5 : query.depth === 'medium' ? 8 : 12,
          providers: ['google', 'bing'],
          timeframe: options.timeframe,
          includeContent: true
        })

        if (webResults.results) {
          allResults.push(...webResults.results.map(r => ({ ...r, queryIntent: query.intent })))
        }

        // Academic search (if enabled)
        if (options.includeAcademic) {
          const academicResults = await academicSearchTool.execute({
            query: query.query,
            maxResults: 5,
            sources: ['scholar', 'semantic_scholar'],
            includeAbstracts: true
          })

          if (academicResults.results) {
            allResults.push(...academicResults.results.map(r => ({ ...r, queryIntent: query.intent, type: 'academic' })))
          }
        }

        // News search (if enabled and relevant)
        if (options.includeNews && ['recent', 'past_week', 'past_month'].includes(options.timeframe)) {
          const newsResults = await newsSearchTool.execute({
            query: query.query,
            maxResults: 5,
            timeframe: '7d',
            sortBy: 'relevance'
          })

          if (newsResults.results) {
            allResults.push(...newsResults.results.map(r => ({ ...r, queryIntent: query.intent, type: 'news' })))
          }
        }

        // Small delay between queries
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.warn(`Search failed for query "${query.query}":`, error)
      }
    }

    return this.deduplicateAndRankResults(allResults)
  }

  /**
   * Analyze content from search results
   */
  private async analyzeContent(results: any[]): Promise<{
    summaries: any[]
    topics: any[]
    entities: any[]
    keyFacts: string[]
    sentiment: any
  }> {
    const topResults = results.slice(0, 10) // Analyze top 10 results
    const summaries = []
    const allTopics = []
    const allEntities = []
    const keyFacts = []

    for (const result of topResults) {
      try {
        if (result.url) {
          const analysis = await contentAnalysisTool.execute({
            url: result.url,
            analysisDepth: 'detailed',
            includeReadability: true,
            includeSentiment: true,
            includeEntities: true,
            includeTopics: true,
            includeCredibility: true
          })

          if (analysis.analysis) {
            summaries.push({
              url: result.url,
              summary: analysis.analysis.summary,
              keyPoints: analysis.analysis.keyPoints,
              credibility: analysis.analysis.credibility
            })

            allTopics.push(...analysis.analysis.topics)
            allEntities.push(...analysis.analysis.entities)
            keyFacts.push(...analysis.analysis.keyPoints)
          }
        }
      } catch (error) {
        console.warn(`Content analysis failed for ${result.url}:`, error)
      }
    }

    // Aggregate topics and entities
    const aggregatedTopics = this.aggregateTopics(allTopics)
    const aggregatedEntities = this.aggregateEntities(allEntities)

    return {
      summaries,
      topics: aggregatedTopics,
      entities: aggregatedEntities,
      keyFacts: [...new Set(keyFacts)], // Remove duplicates
      sentiment: this.analyzeSentiment(summaries)
    }
  }

  /**
   * Verify key facts
   */
  private async verifyKeyFacts(facts: string[]): Promise<any[]> {
    const verificationResults = []
    const topFacts = facts.slice(0, 5) // Verify top 5 facts

    for (const fact of topFacts) {
      try {
        const verification = await factVerificationTool.execute({
          claim: fact,
          searchDepth: 'basic',
          timeframe: 'any',
          requireMultipleSources: true,
          minCredibilityScore: 0.7
        })

        verificationResults.push(verification)
      } catch (error) {
        console.warn(`Fact verification failed for "${fact}":`, error)
      }
    }

    return verificationResults
  }

  /**
   * Generate insights from analysis
   */
  private async generateInsights(analysisResults: any): Promise<any[]> {
    const prompt = `Based on the research analysis results, generate key insights about the topic.
                   Consider patterns, trends, contradictions, gaps, and correlations.
                   
                   Analysis Results:
                   - Topics: ${JSON.stringify(analysisResults.topics.slice(0, 10))}
                   - Key Facts: ${JSON.stringify(analysisResults.keyFacts.slice(0, 10))}
                   - Entities: ${JSON.stringify(analysisResults.entities.slice(0, 10))}
                   
                   Generate 5-8 meaningful insights with evidence and confidence scores.`

    const result = await generateObject({
      model: openai(this.agent.model),
      prompt,
      schema: z.object({
        insights: z.array(z.object({
          type: z.enum(['fact', 'trend', 'contradiction', 'gap', 'correlation', 'prediction']),
          content: z.string(),
          confidence: z.number().min(0).max(1),
          tags: z.array(z.string()),
          evidence: z.array(z.string())
        }))
      })
    })

    return result.object.insights.map(insight => ({
      id: `insight-${Date.now()}-${Math.random()}`,
      ...insight,
      timestamp: new Date().toISOString()
    }))
  }

  /**
   * Extract timeline events
   */
  private async extractTimeline(results: any[]): Promise<any[]> {
    const timelineEvents = []

    for (const result of results.slice(0, 15)) {
      // Extract dates and events from content
      const content = result.content || result.snippet || ''
      const dateMatches = content.match(/\b\d{4}\b|\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b/g) || []

      dateMatches.forEach(date => {
        const context = this.extractContext(content, date, 100)
        if (context.length > 20) {
          timelineEvents.push({
            date,
            event: context,
            source: result.url || result.source,
            importance: this.calculateEventImportance(context),
            category: this.categorizeEvent(context)
          })
        }
      })
    }

    return timelineEvents
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(0, 20) // Limit to 20 events
  }

  /**
   * Extract entity relationships
   */
  private async extractRelationships(results: any[]): Promise<any[]> {
    const relationships = []
    const entities = new Set<string>()

    // Extract entities from all results
    results.forEach(result => {
      const content = result.content || result.snippet || ''
      const extractedEntities = this.extractEntitiesFromText(content)
      extractedEntities.forEach(entity => entities.add(entity))
    })

    const entityArray = Array.from(entities).slice(0, 20) // Limit entities

    // Find relationships between entities
    for (let i = 0; i < entityArray.length; i++) {
      for (let j = i + 1; j < entityArray.length; j++) {
        const entity1 = entityArray[i]
        const entity2 = entityArray[j]

        const relationship = await this.findEntityRelationship(entity1, entity2, results)
        if (relationship) {
          relationships.push(relationship)
        }
      }
    }

    return relationships.slice(0, 15) // Limit relationships
  }

  /**
   * Calculate research confidence
   */
  private calculateConfidence(results: any[], insights: any[]): number {
    let confidence = 0.5 // Base confidence

    // Source diversity bonus
    const uniqueDomains = new Set(results.map(r => {
      try {
        return new URL(r.url).hostname
      } catch {
        return r.source || 'unknown'
      }
    }))
    confidence += Math.min(0.2, uniqueDomains.size * 0.02)

    // Result quantity bonus
    confidence += Math.min(0.1, results.length * 0.005)

    // Insight quality bonus
    const highConfidenceInsights = insights.filter(i => i.confidence > 0.7).length
    confidence += Math.min(0.15, highConfidenceInsights * 0.03)

    // Credibility bonus
    const credibleResults = results.filter(r => r.metadata?.credibilityScore > 0.7).length
    confidence += Math.min(0.15, (credibleResults / results.length) * 0.15)

    return Math.min(1, confidence)
  }

  /**
   * Get system prompt for the agent
   */
  private getSystemPrompt(): string {
    return `You are an expert research agent capable of conducting comprehensive research on any topic.

Your capabilities include:
- Multi-source web searching with intelligent query generation
- Academic and scholarly research
- Content analysis and synthesis
- Fact verification and credibility assessment
- Timeline and relationship extraction
- Insight generation and pattern recognition

Your approach:
1. Break down complex topics into focused research queries
2. Search multiple sources for diverse perspectives
3. Analyze and synthesize information critically
4. Verify important claims and facts
5. Identify patterns, trends, and relationships
6. Generate actionable insights with confidence scores

Always:
- Prioritize credible and authoritative sources
- Consider multiple perspectives and potential biases
- Distinguish between facts, opinions, and speculation
- Provide evidence for your conclusions
- Acknowledge limitations and uncertainties
- Maintain objectivity and analytical rigor

When presenting findings:
- Structure information clearly and logically
- Highlight key insights and their significance
- Provide source attribution and credibility assessment
- Include confidence levels for major conclusions
- Suggest areas for further investigation if relevant`
  }

  // Helper methods

  private deduplicateAndRankResults(results: any[]): any[] {
    const seen = new Set<string>()
    const unique = results.filter(result => {
      const key = result.url?.toLowerCase() || result.title?.toLowerCase() || ''
      if (seen.has(key)) return false
      seen.add(key)
      return true
    })

    return unique.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
  }

  private aggregateTopics(topics: any[]): any[] {
    const topicMap = new Map<string, any>()

    topics.forEach(topic => {
      const key = topic.name.toLowerCase()
      if (topicMap.has(key)) {
        const existing = topicMap.get(key)!
        existing.relevance = Math.max(existing.relevance, topic.relevance)
        existing.mentions += topic.mentions
      } else {
        topicMap.set(key, { ...topic })
      }
    })

    return Array.from(topicMap.values())
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, 15)
  }

  private aggregateEntities(entities: any[]): any[] {
    const entityMap = new Map<string, any>()

    entities.forEach(entity => {
      const key = entity.name.toLowerCase()
      if (entityMap.has(key)) {
        const existing = entityMap.get(key)!
        existing.mentions += entity.mentions
      } else {
        entityMap.set(key, { ...entity })
      }
    })

    return Array.from(entityMap.values())
      .sort((a, b) => b.mentions - a.mentions)
      .slice(0, 20)
  }

  private analyzeSentiment(summaries: any[]): any {
    const sentiments = summaries.map(s => s.sentiment).filter(Boolean)
    if (sentiments.length === 0) return { overall: 'neutral', distribution: {} }

    const distribution = sentiments.reduce((acc, sentiment) => {
      acc[sentiment] = (acc[sentiment] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const dominant = Object.entries(distribution)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'neutral'

    return { overall: dominant, distribution }
  }

  private extractContext(text: string, target: string, contextLength: number): string {
    const index = text.indexOf(target)
    if (index === -1) return ''

    const start = Math.max(0, index - contextLength / 2)
    const end = Math.min(text.length, index + target.length + contextLength / 2)

    return text.substring(start, end).trim()
  }

  private calculateEventImportance(context: string): number {
    const importantWords = ['significant', 'major', 'important', 'breakthrough', 'historic', 'landmark']
    const matches = importantWords.filter(word => 
      context.toLowerCase().includes(word)
    ).length

    return Math.min(1, matches * 0.3 + 0.1)
  }

  private categorizeEvent(context: string): string {
    const categories = {
      political: ['election', 'government', 'policy', 'law', 'congress', 'president'],
      economic: ['market', 'economy', 'financial', 'trade', 'business', 'stock'],
      technology: ['technology', 'innovation', 'software', 'AI', 'digital', 'tech'],
      social: ['social', 'culture', 'society', 'community', 'movement', 'protest'],
      scientific: ['research', 'study', 'discovery', 'science', 'experiment', 'finding']
    }

    const contextLower = context.toLowerCase()
    
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => contextLower.includes(keyword))) {
        return category
      }
    }

    return 'general'
  }

  private extractEntitiesFromText(text: string): string[] {
    // Simple entity extraction - would use more sophisticated NER in production
    const entities = []
    
    // Proper nouns (capitalized words)
    const properNouns = text.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g) || []
    entities.push(...properNouns)
    
    // Organizations
    const orgs = text.match(/\b[A-Z][a-z]+\s+(?:Inc|Corp|LLC|Ltd|Company|Corporation|Organization)\b/g) || []
    entities.push(...orgs)
    
    return [...new Set(entities)].filter(entity => entity.length > 2)
  }

  private async findEntityRelationship(entity1: string, entity2: string, results: any[]): Promise<any | null> {
    // Find results that mention both entities
    const relevantResults = results.filter(result => {
      const content = (result.content || result.snippet || '').toLowerCase()
      return content.includes(entity1.toLowerCase()) && content.includes(entity2.toLowerCase())
    })

    if (relevantResults.length === 0) return null

    // Determine relationship type (simplified)
    const combinedContent = relevantResults.map(r => r.content || r.snippet).join(' ').toLowerCase()
    
    let relationship = 'related'
    if (combinedContent.includes('ceo') || combinedContent.includes('founder')) {
      relationship = 'leads'
    } else if (combinedContent.includes('partner') || combinedContent.includes('collaboration')) {
      relationship = 'partners_with'
    } else if (combinedContent.includes('acquired') || combinedContent.includes('bought')) {
      relationship = 'acquired'
    }

    return {
      entity1,
      entity2,
      relationship,
      strength: relevantResults.length / results.length,
      evidence: relevantResults.slice(0, 3).map(r => ({
        title: r.title,
        url: r.url,
        snippet: r.snippet
      }))
    }
  }
}