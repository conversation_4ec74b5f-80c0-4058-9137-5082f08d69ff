# AI Research Tools

A comprehensive suite of intelligent tools and agents for AI-powered web searching, deep research, and context understanding. Built with the AI SDK for seamless integration with language models and tool calling.

## Features

- 🔍 **Intelligent Web Search** - Multi-provider search with relevance scoring
- 📚 **Academic Research** - Specialized tools for scholarly content
- 📰 **News Analysis** - Real-time news search and credibility assessment
- 🔬 **Content Analysis** - Deep content understanding and extraction
- ✅ **Fact Verification** - Multi-source claim verification
- 🤖 **Research Agents** - Autonomous research workflows
- 📊 **Knowledge Synthesis** - Intelligent information synthesis
- 🕸️ **Relationship Mapping** - Entity and concept relationship extraction
- 📈 **Trend Analysis** - Pattern and trend identification
- 🎯 **Context Understanding** - Advanced context and meaning extraction

## Installation

```bash
npm install @coco-milk-store/ai-research
# or
yarn add @coco-milk-store/ai-research
```

## Quick Start

```typescript
import { GeneralResearchAgent } from '@coco-milk-store/ai-research'

// Initialize research agent
const agent = new GeneralResearchAgent({
  model: 'gpt-4-turbo',
  temperature: 0.3
})

// Conduct research
const session = await agent.startSession('Climate change impacts on agriculture')
const results = await agent.research('Climate change impacts on agriculture', {
  depth: 'deep',
  includeAcademic: true,
  includeNews: true,
  verifyFacts: true
})

console.log('Research Results:', results)
```

## Tools

### Web Search Tools

#### Multi-Provider Web Search
```typescript
import { webSearchTool } from '@coco-milk-store/ai-research/tools'

const results = await webSearchTool.execute({
  query: 'artificial intelligence trends 2024',
  maxResults: 10,
  providers: ['google', 'bing', 'duckduckgo'],
  timeframe: 'past_month',
  includeContent: true
})
```

#### Academic Search
```typescript
import { academicSearchTool } from '@coco-milk-store/ai-research/tools'

const papers = await academicSearchTool.execute({
  query: 'machine learning healthcare applications',
  maxResults: 15,
  sources: ['arxiv', 'pubmed', 'semantic_scholar'],
  yearFrom: 2020,
  includeAbstracts: true
})
```

#### News Search
```typescript
import { newsSearchTool } from '@coco-milk-store/ai-research/tools'

const news = await newsSearchTool.execute({
  query: 'renewable energy policy',
  maxResults: 20,
  timeframe: '24h',
  category: 'technology',
  sortBy: 'relevance'
})
```

### Content Analysis Tools

#### Comprehensive Content Analysis
```typescript
import { contentAnalysisTool } from '@coco-milk-store/ai-research/tools'

const analysis = await contentAnalysisTool.execute({
  url: 'https://example.com/article',
  analysisDepth: 'comprehensive',
  includeReadability: true,
  includeSentiment: true,
  includeEntities: true,
  includeTopics: true,
  includeCredibility: true
})
```

#### Batch Content Analysis
```typescript
import { batchContentAnalysisTool } from '@coco-milk-store/ai-research/tools'

const batchResults = await batchContentAnalysisTool.execute({
  urls: [
    'https://site1.com/article1',
    'https://site2.com/article2',
    'https://site3.com/article3'
  ],
  analysisDepth: 'detailed',
  maxConcurrency: 3,
  includeComparison: true
})
```

#### Text Summarization
```typescript
import { textSummarizationTool } from '@coco-milk-store/ai-research/tools'

const summary = await textSummarizationTool.execute({
  text: longArticleText,
  summaryLength: 'medium',
  summaryType: 'abstractive',
  focusAreas: ['key findings', 'methodology', 'conclusions']
})
```

### Fact Verification Tools

#### Single Fact Verification
```typescript
import { factVerificationTool } from '@coco-milk-store/ai-research/tools'

const verification = await factVerificationTool.execute({
  claim: 'The global temperature has increased by 1.1°C since pre-industrial times',
  searchDepth: 'thorough',
  requireMultipleSources: true,
  minCredibilityScore: 0.8
})
```

#### Batch Fact Verification
```typescript
import { batchFactVerificationTool } from '@coco-milk-store/ai-research/tools'

const verifications = await batchFactVerificationTool.execute({
  claims: [
    'Claim 1 to verify',
    'Claim 2 to verify',
    'Claim 3 to verify'
  ],
  maxConcurrency: 2,
  searchDepth: 'basic',
  aggregateResults: true
})
```

#### Source Credibility Assessment
```typescript
import { sourceCredibilityTool } from '@coco-milk-store/ai-research/tools'

const credibility = await sourceCredibilityTool.execute({
  sources: [
    'https://reuters.com',
    'https://example-news.com',
    'https://research-journal.org'
  ],
  assessmentCriteria: ['authority', 'accuracy', 'objectivity'],
  includeReputationCheck: true,
  includeBiasAnalysis: true
})
```

## Research Agents

### General Research Agent

The General Research Agent conducts comprehensive research using multiple tools and sources:

```typescript
import { GeneralResearchAgent } from '@coco-milk-store/ai-research/agents'

const agent = new GeneralResearchAgent({
  model: 'gpt-4-turbo',
  temperature: 0.3,
  maxTokens: 4000
})

// Start research session
const session = await agent.startSession('Quantum computing applications')

// Conduct research with options
const context = await agent.research('Quantum computing applications', {
  depth: 'deep',
  timeframe: 'past_year',
  includeAcademic: true,
  includeNews: true,
  verifyFacts: true
})

// Access research results
console.log('Insights:', context.insights)
console.log('Timeline:', context.timeline)
console.log('Relationships:', context.relationships)
console.log('Confidence:', context.confidence)
```

### Specialized Agents

```typescript
// Fact-checking specialist
import { FactCheckerAgent } from '@coco-milk-store/ai-research/agents'

const factChecker = new FactCheckerAgent()
const factCheck = await factChecker.verifyStatement(
  'Statement to verify',
  { thoroughness: 'high' }
)

// Content analyst
import { AnalystAgent } from '@coco-milk-store/ai-research/agents'

const analyst = new AnalystAgent()
const analysis = await analyst.analyzeContent(
  'https://example.com/content',
  { focus: 'trends_and_patterns' }
)
```

## Advanced Features

### Knowledge Graph Construction

```typescript
import { KnowledgeGraphBuilder } from '@coco-milk-store/ai-research/utils'

const builder = new KnowledgeGraphBuilder()
const graph = await builder.buildFromResearch(researchContext)

console.log('Entities:', graph.entities)
console.log('Relationships:', graph.relationships)
console.log('Topics:', graph.topics)
```

### Research Workflows

```typescript
import { ResearchWorkflow } from '@coco-milk-store/ai-research/workflows'

const workflow = new ResearchWorkflow([
  { type: 'search', config: { query: 'AI ethics', depth: 'deep' } },
  { type: 'analyze', config: { focus: 'sentiment_and_bias' } },
  { type: 'verify', config: { claims: 'auto_extract' } },
  { type: 'synthesize', config: { format: 'report' } }
])

const results = await workflow.execute()
```

### Context Management

```typescript
import { ContextManager } from '@coco-milk-store/ai-research/context'

const contextManager = new ContextManager()

// Add research context
await contextManager.addContext('topic1', researchResults1)
await contextManager.addContext('topic2', researchResults2)

// Find relationships between contexts
const relationships = await contextManager.findRelationships(['topic1', 'topic2'])

// Generate insights across contexts
const insights = await contextManager.generateCrossContextInsights()
```

## Configuration

### Environment Variables

```bash
# Search API Keys
GOOGLE_SEARCH_API_KEY=your_google_api_key
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id
BING_SEARCH_API_KEY=your_bing_api_key
SERP_API_KEY=your_serpapi_key

# AI Model Configuration
OPENAI_API_KEY=your_openai_api_key

# Browserless (for content extraction)
BROWSERLESS_API_TOKEN=your_browserless_token
```

### Tool Configuration

```typescript
import { configureTools } from '@coco-milk-store/ai-research/config'

configureTools({
  search: {
    defaultProviders: ['google', 'bing'],
    maxResultsPerQuery: 10,
    timeoutMs: 30000
  },
  analysis: {
    defaultDepth: 'detailed',
    enableCaching: true,
    cacheExpiryHours: 24
  },
  verification: {
    minCredibilityScore: 0.7,
    requireMultipleSources: true,
    maxVerificationTime: 60000
  }
})
```

## Use Cases

### 1. Academic Research
```typescript
const academicAgent = new GeneralResearchAgent()
const research = await academicAgent.research('CRISPR gene editing ethics', {
  depth: 'deep',
  includeAcademic: true,
  sources: ['pubmed', 'arxiv', 'semantic_scholar'],
  verifyFacts: true
})
```

### 2. Market Research
```typescript
const marketResearch = await agent.research('Electric vehicle market trends', {
  depth: 'medium',
  timeframe: 'past_month',
  includeNews: true,
  sources: ['business_sources']
})
```

### 3. Fact-Checking Pipeline
```typescript
const claims = await claimExtractionTool.execute({ text: articleText })
const verifications = await batchFactVerificationTool.execute({
  claims: claims.claims.map(c => c.text),
  searchDepth: 'thorough'
})
```

### 4. Content Intelligence
```typescript
const intelligence = await contentAnalysisTool.execute({
  url: 'https://competitor-site.com',
  analysisDepth: 'comprehensive',
  includeTopics: true,
  includeEntities: true
})
```

### 5. Trend Analysis
```typescript
const trendAnalysis = await agent.research('AI industry trends 2024', {
  depth: 'deep',
  timeframe: 'past_year',
  includeNews: true,
  includeAcademic: true
})

const trends = trendAnalysis.insights.filter(i => i.type === 'trend')
```

## Best Practices

### 1. Search Strategy
- Use diverse query formulations
- Combine multiple search providers
- Apply appropriate time filters
- Consider source credibility

### 2. Content Analysis
- Analyze content depth based on needs
- Use batch processing for efficiency
- Apply filtering criteria
- Consider language and region

### 3. Fact Verification
- Require multiple sources for important claims
- Set appropriate credibility thresholds
- Consider source bias and reliability
- Verify numerical and statistical claims

### 4. Agent Configuration
- Choose appropriate model temperature
- Set reasonable token limits
- Configure timeout values
- Enable caching for repeated queries

### 5. Error Handling
```typescript
try {
  const results = await agent.research(topic, options)
} catch (error) {
  if (error.message.includes('rate limit')) {
    // Handle rate limiting
    await delay(60000)
    return retry()
  }
  // Handle other errors
}
```

## API Reference

### Tools
- `webSearchTool` - Multi-provider web search
- `academicSearchTool` - Academic and scholarly search
- `newsSearchTool` - News and current events search
- `contentAnalysisTool` - Comprehensive content analysis
- `textSummarizationTool` - Intelligent text summarization
- `topicExtractionTool` - Topic and theme extraction
- `entityRecognitionTool` - Named entity recognition
- `factVerificationTool` - Claim verification
- `sourceCredibilityTool` - Source reliability assessment

### Agents
- `GeneralResearchAgent` - Comprehensive research agent
- `FactCheckerAgent` - Specialized fact-checking agent
- `AnalystAgent` - Content analysis specialist
- `InvestigatorAgent` - Deep investigation workflows
- `SynthesizerAgent` - Information synthesis specialist

### Utilities
- `ContextManager` - Research context management
- `KnowledgeGraphBuilder` - Knowledge graph construction
- `ResearchWorkflow` - Automated research workflows
- `CredibilityAssessor` - Source credibility assessment

## Examples

See the `/examples` directory for complete usage examples:

- [Basic Research](./examples/basic-research.ts)
- [Fact Checking](./examples/fact-checking.ts)
- [Content Analysis](./examples/content-analysis.ts)
- [Academic Research](./examples/academic-research.ts)
- [News Analysis](./examples/news-analysis.ts)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

- [Documentation](https://docs.example.com/ai-research)
- [GitHub Issues](https://github.com/your-org/coco-milk-store/issues)
- [Discord Community](https://discord.gg/your-community)