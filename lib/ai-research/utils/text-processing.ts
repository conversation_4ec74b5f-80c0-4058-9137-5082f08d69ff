// Text Processing Utilities
// Common text processing functions for research tools

/**
 * Extract clean text from HTML content
 */
export function extractTextFromHTML(html: string): string {
  // Remove script and style elements
  let text = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  text = text.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
  
  // Remove HTML tags
  text = text.replace(/<[^>]*>/g, ' ')
  
  // Decode HTML entities
  text = text.replace(/&nbsp;/g, ' ')
  text = text.replace(/&amp;/g, '&')
  text = text.replace(/&lt;/g, '<')
  text = text.replace(/&gt;/g, '>')
  text = text.replace(/&quot;/g, '"')
  text = text.replace(/&#39;/g, "'")
  text = text.replace(/&#x27;/g, "'")
  text = text.replace(/&#x2F;/g, '/')
  
  // Clean up whitespace
  text = text.replace(/\s+/g, ' ').trim()
  
  return text
}

/**
 * Extract context around a target phrase
 */
export function extractContext(text: string, target: string, contextLength: number = 200): string {
  const index = text.toLowerCase().indexOf(target.toLowerCase())
  if (index === -1) return ''

  const start = Math.max(0, index - contextLength / 2)
  const end = Math.min(text.length, index + target.length + contextLength / 2)

  return text.substring(start, end).trim()
}

/**
 * Split text into sentences
 */
export function splitIntoSentences(text: string): string[] {
  return text
    .split(/[.!?]+/)
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 10)
}

/**
 * Extract keywords from text
 */
export function extractKeywords(text: string, maxKeywords: number = 10): string[] {
  const words = text.toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3 && !isStopWord(word))

  const frequency: Record<string, number> = {}
  words.forEach(word => {
    frequency[word] = (frequency[word] || 0) + 1
  })

  return Object.entries(frequency)
    .sort(([, a], [, b]) => b - a)
    .slice(0, maxKeywords)
    .map(([word]) => word)
}

/**
 * Check if a word is a stop word
 */
export function isStopWord(word: string): boolean {
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that',
    'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her',
    'us', 'them', 'my', 'your', 'his', 'its', 'our', 'their', 'what', 'which', 'who',
    'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most',
    'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than',
    'too', 'very', 'just', 'now'
  ])
  
  return stopWords.has(word.toLowerCase())
}

/**
 * Calculate text similarity using Jaccard index
 */
export function calculateTextSimilarity(text1: string, text2: string): number {
  const words1 = new Set(text1.toLowerCase().split(/\s+/).filter(word => !isStopWord(word)))
  const words2 = new Set(text2.toLowerCase().split(/\s+/).filter(word => !isStopWord(word)))

  const intersection = new Set([...words1].filter(word => words2.has(word)))
  const union = new Set([...words1, ...words2])

  return union.size > 0 ? intersection.size / union.size : 0
}

/**
 * Extract dates from text
 */
export function extractDates(text: string): string[] {
  const datePatterns = [
    /\b\d{4}\b/g, // Years
    /\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}\b/g, // Month Day, Year
    /\b\d{1,2}\/\d{1,2}\/\d{4}\b/g, // MM/DD/YYYY
    /\b\d{1,2}-\d{1,2}-\d{4}\b/g, // MM-DD-YYYY
    /\b\d{4}-\d{1,2}-\d{1,2}\b/g // YYYY-MM-DD
  ]

  const dates: string[] = []
  datePatterns.forEach(pattern => {
    const matches = text.match(pattern) || []
    dates.push(...matches)
  })

  return [...new Set(dates)] // Remove duplicates
}

/**
 * Extract numbers from text
 */
export function extractNumbers(text: string): number[] {
  const numberPattern = /\b\d+(?:,\d{3})*(?:\.\d+)?\b/g
  const matches = text.match(numberPattern) || []
  
  return matches
    .map(match => parseFloat(match.replace(/,/g, '')))
    .filter(num => !isNaN(num))
}

/**
 * Extract URLs from text
 */
export function extractUrls(text: string): string[] {
  const urlPattern = /https?:\/\/[^\s<>"{}|\\^`[\]]+/g
  return text.match(urlPattern) || []
}

/**
 * Extract email addresses from text
 */
export function extractEmails(text: string): string[] {
  const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g
  return text.match(emailPattern) || []
}

/**
 * Extract phone numbers from text
 */
export function extractPhoneNumbers(text: string): string[] {
  const phonePatterns = [
    /\b\d{3}-\d{3}-\d{4}\b/g, // XXX-XXX-XXXX
    /\b\(\d{3}\)\s*\d{3}-\d{4}\b/g, // (XXX) XXX-XXXX
    /\b\d{3}\.\d{3}\.\d{4}\b/g, // XXX.XXX.XXXX
    /\b\+\d{1,3}\s*\d{3,4}\s*\d{3,4}\s*\d{3,4}\b/g // International format
  ]

  const phoneNumbers: string[] = []
  phonePatterns.forEach(pattern => {
    const matches = text.match(pattern) || []
    phoneNumbers.push(...matches)
  })

  return [...new Set(phoneNumbers)]
}

/**
 * Calculate readability score (Flesch Reading Ease)
 */
export function calculateReadabilityScore(text: string): number {
  const sentences = splitIntoSentences(text)
  const words = text.split(/\s+/)
  const syllables = countSyllables(text)

  if (sentences.length === 0 || words.length === 0) return 0

  const avgSentenceLength = words.length / sentences.length
  const avgSyllablesPerWord = syllables / words.length

  const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord)
  return Math.max(0, Math.min(100, score))
}

/**
 * Count syllables in text
 */
export function countSyllables(text: string): number {
  const words = text.toLowerCase().split(/\s+/)
  let totalSyllables = 0

  words.forEach(word => {
    // Remove non-alphabetic characters
    word = word.replace(/[^a-z]/g, '')
    if (word.length === 0) return

    // Count vowel groups
    const vowelGroups = word.match(/[aeiouy]+/g) || []
    let syllables = vowelGroups.length

    // Adjust for silent e
    if (word.endsWith('e') && syllables > 1) {
      syllables--
    }

    // Minimum of 1 syllable per word
    syllables = Math.max(1, syllables)
    totalSyllables += syllables
  })

  return totalSyllables
}

/**
 * Truncate text to a maximum length
 */
export function truncateText(text: string, maxLength: number, suffix: string = '...'): string {
  if (text.length <= maxLength) return text
  
  const truncated = text.substring(0, maxLength - suffix.length)
  const lastSpace = truncated.lastIndexOf(' ')
  
  // Try to break at word boundary
  if (lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + suffix
  }
  
  return truncated + suffix
}

/**
 * Clean and normalize text
 */
export function normalizeText(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ') // Replace punctuation with spaces
    .replace(/\s+/g, ' ') // Collapse multiple spaces
    .trim()
}

/**
 * Extract quoted text
 */
export function extractQuotes(text: string): string[] {
  const quotePatterns = [
    /"([^"]+)"/g, // Double quotes
    /'([^']+)'/g, // Single quotes
    /[""]([^""]+)["]/g, // Smart quotes
    /['']([^'']+)[']/g // Smart single quotes
  ]

  const quotes: string[] = []
  quotePatterns.forEach(pattern => {
    let match
    while ((match = pattern.exec(text)) !== null) {
      quotes.push(match[1])
    }
  })

  return [...new Set(quotes)]
}

/**
 * Calculate word frequency
 */
export function calculateWordFrequency(text: string): Record<string, number> {
  const words = normalizeText(text)
    .split(/\s+/)
    .filter(word => word.length > 2 && !isStopWord(word))

  const frequency: Record<string, number> = {}
  words.forEach(word => {
    frequency[word] = (frequency[word] || 0) + 1
  })

  return frequency
}

/**
 * Find text patterns using regex
 */
export function findPatterns(text: string, patterns: Record<string, RegExp>): Record<string, string[]> {
  const results: Record<string, string[]> = {}

  Object.entries(patterns).forEach(([name, pattern]) => {
    const matches = text.match(pattern) || []
    results[name] = matches
  })

  return results
}

/**
 * Highlight text matches
 */
export function highlightMatches(text: string, searchTerms: string[], highlightTag: string = 'mark'): string {
  let highlightedText = text

  searchTerms.forEach(term => {
    const regex = new RegExp(`(${term})`, 'gi')
    highlightedText = highlightedText.replace(regex, `<${highlightTag}>$1</${highlightTag}>`)
  })

  return highlightedText
}

/**
 * Generate text summary statistics
 */
export function getTextStatistics(text: string): {
  characters: number
  charactersNoSpaces: number
  words: number
  sentences: number
  paragraphs: number
  averageWordsPerSentence: number
  averageCharactersPerWord: number
  readabilityScore: number
} {
  const characters = text.length
  const charactersNoSpaces = text.replace(/\s/g, '').length
  const words = text.split(/\s+/).filter(word => word.length > 0).length
  const sentences = splitIntoSentences(text).length
  const paragraphs = text.split(/\n\s*\n/).filter(para => para.trim().length > 0).length

  return {
    characters,
    charactersNoSpaces,
    words,
    sentences,
    paragraphs,
    averageWordsPerSentence: sentences > 0 ? words / sentences : 0,
    averageCharactersPerWord: words > 0 ? charactersNoSpaces / words : 0,
    readabilityScore: calculateReadabilityScore(text)
  }
}