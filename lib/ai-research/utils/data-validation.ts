// Data Validation Utilities
// Validation functions for research data and inputs

import { z } from 'zod'

/**
 * Validate search query
 */
export function validateSearchQuery(query: string): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!query || query.trim().length === 0) {
    errors.push('Query cannot be empty')
  }

  if (query.length > 500) {
    errors.push('Query too long (max 500 characters)')
  }

  if (query.trim().length < 3) {
    errors.push('Query too short (min 3 characters)')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Validate URL
 */
export function validateUrl(url: string): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  try {
    new URL(url)
  } catch {
    errors.push('Invalid URL format')
  }

  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    errors.push('URL must start with http:// or https://')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Validate research context
 */
export function validateResearchContext(context: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!context.topic || context.topic.trim().length === 0) {
    errors.push('Research topic is required')
  }

  if (!Array.isArray(context.queries)) {
    errors.push('Queries must be an array')
  }

  if (!Array.isArray(context.results)) {
    errors.push('Results must be an array')
  }

  if (typeof context.confidence !== 'number' || context.confidence < 0 || context.confidence > 1) {
    errors.push('Confidence must be a number between 0 and 1')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Validate search result
 */
export function validateSearchResult(result: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!result.title || result.title.trim().length === 0) {
    errors.push('Result title is required')
  }

  if (!result.url || !validateUrl(result.url).valid) {
    errors.push('Valid result URL is required')
  }

  if (!result.snippet || result.snippet.trim().length === 0) {
    errors.push('Result snippet is required')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Validate insight
 */
export function validateInsight(insight: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!insight.content || insight.content.trim().length === 0) {
    errors.push('Insight content is required')
  }

  const validTypes = ['fact', 'trend', 'contradiction', 'gap', 'correlation', 'prediction']
  if (!validTypes.includes(insight.type)) {
    errors.push(`Invalid insight type. Must be one of: ${validTypes.join(', ')}`)
  }

  if (typeof insight.confidence !== 'number' || insight.confidence < 0 || insight.confidence > 1) {
    errors.push('Confidence must be a number between 0 and 1')
  }

  if (!Array.isArray(insight.tags)) {
    errors.push('Tags must be an array')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Sanitize text input
 */
export function sanitizeText(text: string): string {
  return text
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/[^\w\s\-.,!?;:()"']/g, '') // Remove special characters except common punctuation
    .trim()
}

/**
 * Validate email address
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate phone number
 */
export function validatePhoneNumber(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

/**
 * Validate date string
 */
export function validateDate(dateString: string): boolean {
  const date = new Date(dateString)
  return !isNaN(date.getTime())
}

/**
 * Validate confidence score
 */
export function validateConfidence(confidence: number): boolean {
  return typeof confidence === 'number' && confidence >= 0 && confidence <= 1
}

/**
 * Validate array of strings
 */
export function validateStringArray(arr: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!Array.isArray(arr)) {
    errors.push('Must be an array')
    return { valid: false, errors }
  }

  arr.forEach((item, index) => {
    if (typeof item !== 'string') {
      errors.push(`Item at index ${index} must be a string`)
    }
  })

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Validate research workflow step
 */
export function validateWorkflowStep(step: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!step.name || step.name.trim().length === 0) {
    errors.push('Step name is required')
  }

  const validTypes = ['search', 'analyze', 'synthesize', 'verify', 'compare', 'extract']
  if (!validTypes.includes(step.type)) {
    errors.push(`Invalid step type. Must be one of: ${validTypes.join(', ')}`)
  }

  if (!step.config || typeof step.config !== 'object') {
    errors.push('Step config is required and must be an object')
  }

  if (step.dependencies && !Array.isArray(step.dependencies)) {
    errors.push('Dependencies must be an array')
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Validate source credibility data
 */
export function validateSourceCredibility(credibility: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!credibility.domain || credibility.domain.trim().length === 0) {
    errors.push('Domain is required')
  }

  if (!validateConfidence(credibility.credibilityScore)) {
    errors.push('Credibility score must be between 0 and 1')
  }

  if (!credibility.factors || typeof credibility.factors !== 'object') {
    errors.push('Factors object is required')
  } else {
    const requiredFactors = ['authority', 'accuracy', 'objectivity', 'currency', 'coverage']
    requiredFactors.forEach(factor => {
      if (!validateConfidence(credibility.factors[factor])) {
        errors.push(`${factor} factor must be between 0 and 1`)
      }
    })
  }

  const validReputations = ['high', 'medium', 'low', 'unknown']
  if (!validReputations.includes(credibility.reputation)) {
    errors.push(`Invalid reputation. Must be one of: ${validReputations.join(', ')}`)
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Batch validate array of items
 */
export function batchValidate<T>(
  items: T[],
  validator: (item: T) => { valid: boolean; errors: string[] }
): { valid: boolean; errors: Array<{ index: number; errors: string[] }> } {
  const allErrors: Array<{ index: number; errors: string[] }> = []

  items.forEach((item, index) => {
    const validation = validator(item)
    if (!validation.valid) {
      allErrors.push({ index, errors: validation.errors })
    }
  })

  return {
    valid: allErrors.length === 0,
    errors: allErrors
  }
}

/**
 * Create validation schema for research data
 */
export const researchDataSchema = z.object({
  topic: z.string().min(1, 'Topic is required'),
  queries: z.array(z.object({
    query: z.string().min(3, 'Query must be at least 3 characters'),
    intent: z.enum(['factual', 'comparative', 'analytical', 'exploratory', 'verification']),
    depth: z.enum(['shallow', 'medium', 'deep'])
  })),
  results: z.array(z.object({
    title: z.string().min(1, 'Title is required'),
    url: z.string().url('Valid URL is required'),
    snippet: z.string().min(1, 'Snippet is required'),
    relevanceScore: z.number().min(0).max(1).optional()
  })),
  insights: z.array(z.object({
    type: z.enum(['fact', 'trend', 'contradiction', 'gap', 'correlation', 'prediction']),
    content: z.string().min(1, 'Content is required'),
    confidence: z.number().min(0).max(1),
    tags: z.array(z.string())
  })),
  confidence: z.number().min(0).max(1)
})

/**
 * Validate using Zod schema
 */
export function validateWithSchema<T>(data: any, schema: z.ZodSchema<T>): { valid: boolean; errors: string[]; data?: T } {
  try {
    const validatedData = schema.parse(data)
    return { valid: true, errors: [], data: validatedData }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        valid: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`)
      }
    }
    return { valid: false, errors: ['Unknown validation error'] }
  }
}