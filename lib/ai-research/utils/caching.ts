// Caching Utilities
// Intelligent caching for research operations

interface CacheEntry<T> {
  key: string
  value: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
  metadata?: Record<string, any>
}

interface CacheStats {
  hits: number
  misses: number
  evictions: number
  totalEntries: number
  memoryUsage: number
}

/**
 * Intelligent Cache Manager
 */
export class CacheManager<T = any> {
  private cache: Map<string, CacheEntry<T>> = new Map()
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalEntries: 0,
    memoryUsage: 0
  }
  private maxSize: number
  private defaultTTL: number
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(options: {
    maxSize?: number
    defaultTTL?: number
    enableAutoCleanup?: boolean
    cleanupInterval?: number
  } = {}) {
    this.maxSize = options.maxSize || 1000
    this.defaultTTL = options.defaultTTL || 3600000 // 1 hour
    
    if (options.enableAutoCleanup !== false) {
      this.startAutoCleanup(options.cleanupInterval || 300000) // 5 minutes
    }
  }

  /**
   * Get value from cache
   */
  get(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      this.stats.misses++
      return null
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      this.stats.misses++
      this.stats.evictions++
      return null
    }

    // Update access statistics
    entry.accessCount++
    entry.lastAccessed = Date.now()
    this.stats.hits++

    return entry.value
  }

  /**
   * Set value in cache
   */
  set(key: string, value: T, ttl?: number, metadata?: Record<string, any>): void {
    const now = Date.now()
    const entryTTL = ttl || this.defaultTTL

    // Check if we need to evict entries
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU()
    }

    const entry: CacheEntry<T> = {
      key,
      value,
      timestamp: now,
      ttl: entryTTL,
      accessCount: 0,
      lastAccessed: now,
      metadata
    }

    this.cache.set(key, entry)
    this.updateStats()
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false
    
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      this.stats.evictions++
      return false
    }
    
    return true
  }

  /**
   * Delete entry from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      this.updateStats()
    }
    return deleted
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
    this.resetStats()
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    this.updateStats()
    return { ...this.stats }
  }

  /**
   * Get cache hit ratio
   */
  getHitRatio(): number {
    const total = this.stats.hits + this.stats.misses
    return total > 0 ? this.stats.hits / total : 0
  }

  /**
   * Get all cache keys
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * Get cache entries with metadata
   */
  entries(): Array<{ key: string; value: T; metadata: any }> {
    return Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      value: entry.value,
      metadata: {
        timestamp: entry.timestamp,
        ttl: entry.ttl,
        accessCount: entry.accessCount,
        lastAccessed: entry.lastAccessed,
        ...entry.metadata
      }
    }))
  }

  /**
   * Cleanup expired entries
   */
  cleanup(): number {
    let cleaned = 0
    const now = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key)
        cleaned++
      }
    }

    this.stats.evictions += cleaned
    this.updateStats()
    return cleaned
  }

  /**
   * Get or set with factory function
   */
  async getOrSet(
    key: string,
    factory: () => Promise<T> | T,
    ttl?: number,
    metadata?: Record<string, any>
  ): Promise<T> {
    const cached = this.get(key)
    if (cached !== null) {
      return cached
    }

    const value = await factory()
    this.set(key, value, ttl, metadata)
    return value
  }

  /**
   * Invalidate entries by pattern
   */
  invalidatePattern(pattern: RegExp): number {
    let invalidated = 0
    
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key)
        invalidated++
      }
    }

    this.updateStats()
    return invalidated
  }

  /**
   * Invalidate entries by metadata
   */
  invalidateByMetadata(predicate: (metadata: any) => boolean): number {
    let invalidated = 0
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.metadata && predicate(entry.metadata)) {
        this.cache.delete(key)
        invalidated++
      }
    }

    this.updateStats()
    return invalidated
  }

  /**
   * Export cache data
   */
  export(): any {
    return {
      entries: Array.from(this.cache.entries()),
      stats: this.stats,
      exportedAt: new Date().toISOString()
    }
  }

  /**
   * Import cache data
   */
  import(data: any): void {
    this.cache.clear()
    
    if (data.entries) {
      for (const [key, entry] of data.entries) {
        this.cache.set(key, entry)
      }
    }
    
    this.updateStats()
  }

  /**
   * Destroy cache and cleanup
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.clear()
  }

  // Private methods

  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > entry.ttl
  }

  private evictLRU(): void {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
      this.stats.evictions++
    }
  }

  private updateStats(): void {
    this.stats.totalEntries = this.cache.size
    this.stats.memoryUsage = this.estimateMemoryUsage()
  }

  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalEntries: 0,
      memoryUsage: 0
    }
  }

  private estimateMemoryUsage(): number {
    // Rough estimation of memory usage
    let size = 0
    for (const [key, entry] of this.cache.entries()) {
      size += key.length * 2 // UTF-16
      size += JSON.stringify(entry.value).length * 2
      size += 64 // Overhead for entry object
    }
    return size
  }

  private startAutoCleanup(interval: number): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, interval)
  }
}

/**
 * Research-specific cache with intelligent invalidation
 */
export class ResearchCache extends CacheManager {
  constructor() {
    super({
      maxSize: 500,
      defaultTTL: 1800000, // 30 minutes
      enableAutoCleanup: true
    })
  }

  /**
   * Cache search results with query-based invalidation
   */
  cacheSearchResults(query: string, results: any[], ttl?: number): void {
    const key = this.generateSearchKey(query)
    this.set(key, results, ttl, {
      type: 'search_results',
      query,
      resultCount: results.length
    })
  }

  /**
   * Get cached search results
   */
  getSearchResults(query: string): any[] | null {
    const key = this.generateSearchKey(query)
    return this.get(key)
  }

  /**
   * Cache content analysis
   */
  cacheContentAnalysis(url: string, analysis: any, ttl?: number): void {
    const key = this.generateContentKey(url)
    this.set(key, analysis, ttl, {
      type: 'content_analysis',
      url,
      analyzedAt: new Date().toISOString()
    })
  }

  /**
   * Get cached content analysis
   */
  getContentAnalysis(url: string): any | null {
    const key = this.generateContentKey(url)
    return this.get(key)
  }

  /**
   * Cache fact verification results
   */
  cacheFactVerification(claim: string, verification: any, ttl?: number): void {
    const key = this.generateFactKey(claim)
    this.set(key, verification, ttl, {
      type: 'fact_verification',
      claim,
      verdict: verification.verdict
    })
  }

  /**
   * Get cached fact verification
   */
  getFactVerification(claim: string): any | null {
    const key = this.generateFactKey(claim)
    return this.get(key)
  }

  /**
   * Invalidate search caches for similar queries
   */
  invalidateSimilarSearches(query: string, similarity: number = 0.8): number {
    const queryWords = new Set(query.toLowerCase().split(/\s+/))
    
    return this.invalidateByMetadata((metadata) => {
      if (metadata.type !== 'search_results') return false
      
      const cachedWords = new Set(metadata.query.toLowerCase().split(/\s+/))
      const intersection = new Set([...queryWords].filter(word => cachedWords.has(word)))
      const union = new Set([...queryWords, ...cachedWords])
      
      const jaccardSimilarity = intersection.size / union.size
      return jaccardSimilarity >= similarity
    })
  }

  /**
   * Invalidate content caches for domain
   */
  invalidateDomainContent(domain: string): number {
    return this.invalidateByMetadata((metadata) => {
      if (metadata.type !== 'content_analysis') return false
      try {
        const url = new URL(metadata.url)
        return url.hostname === domain
      } catch {
        return false
      }
    })
  }

  private generateSearchKey(query: string): string {
    return `search:${this.hashString(query.toLowerCase().trim())}`
  }

  private generateContentKey(url: string): string {
    return `content:${this.hashString(url)}`
  }

  private generateFactKey(claim: string): string {
    return `fact:${this.hashString(claim.toLowerCase().trim())}`
  }

  private hashString(str: string): string {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString(36)
  }
}

/**
 * Multi-level cache with different TTLs
 */
export class TieredCache {
  private l1Cache: CacheManager // Fast, small cache
  private l2Cache: CacheManager // Slower, larger cache

  constructor() {
    this.l1Cache = new CacheManager({
      maxSize: 100,
      defaultTTL: 300000, // 5 minutes
      enableAutoCleanup: true
    })

    this.l2Cache = new CacheManager({
      maxSize: 1000,
      defaultTTL: 3600000, // 1 hour
      enableAutoCleanup: true
    })
  }

  async get(key: string): Promise<any> {
    // Try L1 cache first
    let value = this.l1Cache.get(key)
    if (value !== null) {
      return value
    }

    // Try L2 cache
    value = this.l2Cache.get(key)
    if (value !== null) {
      // Promote to L1 cache
      this.l1Cache.set(key, value)
      return value
    }

    return null
  }

  set(key: string, value: any, ttl?: number): void {
    this.l1Cache.set(key, value, ttl)
    this.l2Cache.set(key, value, ttl ? ttl * 2 : undefined) // L2 has longer TTL
  }

  delete(key: string): boolean {
    const l1Deleted = this.l1Cache.delete(key)
    const l2Deleted = this.l2Cache.delete(key)
    return l1Deleted || l2Deleted
  }

  clear(): void {
    this.l1Cache.clear()
    this.l2Cache.clear()
  }

  getStats(): { l1: CacheStats; l2: CacheStats } {
    return {
      l1: this.l1Cache.getStats(),
      l2: this.l2Cache.getStats()
    }
  }

  destroy(): void {
    this.l1Cache.destroy()
    this.l2Cache.destroy()
  }
}

/**
 * Global cache instances
 */
export const researchCache = new ResearchCache()
export const tieredCache = new TieredCache()

/**
 * Cache decorator for methods
 */
export function cached(ttl?: number, keyGenerator?: (...args: any[]) => string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    const cache = new CacheManager({ defaultTTL: ttl })

    descriptor.value = async function (...args: any[]) {
      const key = keyGenerator 
        ? keyGenerator(...args)
        : `${target.constructor.name}.${propertyName}:${JSON.stringify(args)}`

      const cached = cache.get(key)
      if (cached !== null) {
        return cached
      }

      const result = await method.apply(this, args)
      cache.set(key, result, ttl)
      return result
    }

    return descriptor
  }
}

/**
 * Memoization utility
 */
export function memoize<T extends (...args: any[]) => any>(
  fn: T,
  options: {
    ttl?: number
    maxSize?: number
    keyGenerator?: (...args: Parameters<T>) => string
  } = {}
): T {
  const cache = new CacheManager({
    maxSize: options.maxSize || 100,
    defaultTTL: options.ttl || 3600000
  })

  return ((...args: Parameters<T>) => {
    const key = options.keyGenerator 
      ? options.keyGenerator(...args)
      : JSON.stringify(args)

    const cached = cache.get(key)
    if (cached !== null) {
      return cached
    }

    const result = fn(...args)
    cache.set(key, result)
    return result
  }) as T
}