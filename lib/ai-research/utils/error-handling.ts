// Error Handling Utilities
// Comprehensive error handling for research operations

/**
 * Research-specific error types
 */
export class ResearchError extends Error {
  public readonly code: string
  public readonly context?: Record<string, any>
  public readonly retryable: boolean
  public readonly timestamp: string

  constructor(
    message: string,
    code: string,
    context?: Record<string, any>,
    retryable: boolean = false
  ) {
    super(message)
    this.name = 'ResearchError'
    this.code = code
    this.context = context
    this.retryable = retryable
    this.timestamp = new Date().toISOString()
  }
}

export class SearchError extends ResearchError {
  constructor(message: string, context?: Record<string, any>, retryable: boolean = true) {
    super(message, 'SEARCH_ERROR', context, retryable)
    this.name = 'SearchError'
  }
}

export class AnalysisError extends ResearchError {
  constructor(message: string, context?: Record<string, any>, retryable: boolean = false) {
    super(message, 'ANALYSIS_ERROR', context, retryable)
    this.name = 'AnalysisError'
  }
}

export class VerificationError extends ResearchError {
  constructor(message: string, context?: Record<string, any>, retryable: boolean = true) {
    super(message, 'VERIFICATION_ERROR', context, retryable)
    this.name = 'VerificationError'
  }
}

export class RateLimitError extends ResearchError {
  public readonly retryAfter?: number

  constructor(message: string, retryAfter?: number, context?: Record<string, any>) {
    super(message, 'RATE_LIMIT_ERROR', context, true)
    this.name = 'RateLimitError'
    this.retryAfter = retryAfter
  }
}

export class ValidationError extends ResearchError {
  public readonly field?: string

  constructor(message: string, field?: string, context?: Record<string, any>) {
    super(message, 'VALIDATION_ERROR', context, false)
    this.name = 'ValidationError'
    this.field = field
  }
}

export class NetworkError extends ResearchError {
  public readonly statusCode?: number

  constructor(message: string, statusCode?: number, context?: Record<string, any>) {
    super(message, 'NETWORK_ERROR', context, true)
    this.name = 'NetworkError'
    this.statusCode = statusCode
  }
}

/**
 * Error handler with retry logic
 */
export class ErrorHandler {
  private retryAttempts: Map<string, number> = new Map()
  private maxRetries: number
  private baseDelay: number
  private maxDelay: number

  constructor(options: {
    maxRetries?: number
    baseDelay?: number
    maxDelay?: number
  } = {}) {
    this.maxRetries = options.maxRetries || 3
    this.baseDelay = options.baseDelay || 1000
    this.maxDelay = options.maxDelay || 30000
  }

  /**
   * Handle error with automatic retry logic
   */
  async handleWithRetry<T>(
    operation: () => Promise<T>,
    operationId: string,
    context?: Record<string, any>
  ): Promise<T> {
    const attempts = this.retryAttempts.get(operationId) || 0

    try {
      const result = await operation()
      // Reset retry count on success
      this.retryAttempts.delete(operationId)
      return result
    } catch (error) {
      const researchError = this.normalizeError(error, context)
      
      // Check if error is retryable and we haven't exceeded max retries
      if (researchError.retryable && attempts < this.maxRetries) {
        this.retryAttempts.set(operationId, attempts + 1)
        
        // Calculate delay with exponential backoff
        const delay = this.calculateDelay(attempts, researchError)
        
        console.warn(`Operation ${operationId} failed, retrying in ${delay}ms. Attempt ${attempts + 1}/${this.maxRetries}`)
        
        await this.sleep(delay)
        return this.handleWithRetry(operation, operationId, context)
      }

      // Max retries exceeded or non-retryable error
      this.retryAttempts.delete(operationId)
      throw researchError
    }
  }

  /**
   * Handle error without retry
   */
  handle(error: any, context?: Record<string, any>): ResearchError {
    return this.normalizeError(error, context)
  }

  /**
   * Normalize any error to ResearchError
   */
  private normalizeError(error: any, context?: Record<string, any>): ResearchError {
    if (error instanceof ResearchError) {
      return error
    }

    if (error instanceof Error) {
      // Check for specific error patterns
      if (error.message.includes('rate limit') || error.message.includes('429')) {
        const retryAfter = this.extractRetryAfter(error.message)
        return new RateLimitError(error.message, retryAfter, context)
      }

      if (error.message.includes('network') || error.message.includes('fetch')) {
        return new NetworkError(error.message, undefined, context)
      }

      if (error.message.includes('validation') || error.message.includes('invalid')) {
        return new ValidationError(error.message, undefined, context)
      }

      // Default to generic research error
      return new ResearchError(error.message, 'UNKNOWN_ERROR', context, true)
    }

    // Handle non-Error objects
    const message = typeof error === 'string' ? error : 'Unknown error occurred'
    return new ResearchError(message, 'UNKNOWN_ERROR', context, false)
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateDelay(attempt: number, error: ResearchError): number {
    // Special handling for rate limit errors
    if (error instanceof RateLimitError && error.retryAfter) {
      return error.retryAfter * 1000 // Convert to milliseconds
    }

    // Exponential backoff with jitter
    const exponentialDelay = this.baseDelay * Math.pow(2, attempt)
    const jitter = Math.random() * 0.1 * exponentialDelay
    const delay = exponentialDelay + jitter

    return Math.min(delay, this.maxDelay)
  }

  /**
   * Extract retry-after value from error message
   */
  private extractRetryAfter(message: string): number | undefined {
    const match = message.match(/retry.after[:\s]+(\d+)/i)
    return match ? parseInt(match[1], 10) : undefined
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Get retry statistics
   */
  getRetryStats(): Record<string, number> {
    return Object.fromEntries(this.retryAttempts.entries())
  }

  /**
   * Clear retry history
   */
  clearRetryHistory(): void {
    this.retryAttempts.clear()
  }
}

/**
 * Circuit breaker for preventing cascading failures
 */
export class CircuitBreaker {
  private failures: number = 0
  private lastFailureTime: number = 0
  private state: 'closed' | 'open' | 'half-open' = 'closed'
  
  constructor(
    private threshold: number = 5,
    private timeout: number = 60000, // 1 minute
    private resetTimeout: number = 30000 // 30 seconds
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'half-open'
      } else {
        throw new ResearchError(
          'Circuit breaker is open',
          'CIRCUIT_BREAKER_OPEN',
          { state: this.state, failures: this.failures }
        )
      }
    }

    try {
      const result = await operation()
      this.onSuccess()
      return result
    } catch (error) {
      this.onFailure()
      throw error
    }
  }

  private onSuccess(): void {
    this.failures = 0
    this.state = 'closed'
  }

  private onFailure(): void {
    this.failures++
    this.lastFailureTime = Date.now()

    if (this.failures >= this.threshold) {
      this.state = 'open'
    }
  }

  getState(): { state: string; failures: number; lastFailureTime: number } {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime
    }
  }

  reset(): void {
    this.failures = 0
    this.lastFailureTime = 0
    this.state = 'closed'
  }
}

/**
 * Error aggregator for collecting and analyzing errors
 */
export class ErrorAggregator {
  private errors: Array<{
    error: ResearchError
    timestamp: number
    context?: Record<string, any>
  }> = []

  private maxErrors: number

  constructor(maxErrors: number = 1000) {
    this.maxErrors = maxErrors
  }

  /**
   * Add error to aggregator
   */
  addError(error: ResearchError, context?: Record<string, any>): void {
    this.errors.push({
      error,
      timestamp: Date.now(),
      context
    })

    // Keep only recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors)
    }
  }

  /**
   * Get error statistics
   */
  getStats(timeWindow: number = 3600000): {
    totalErrors: number
    errorsByType: Record<string, number>
    errorsByCode: Record<string, number>
    recentErrors: number
    errorRate: number
  } {
    const now = Date.now()
    const recentErrors = this.errors.filter(e => now - e.timestamp <= timeWindow)

    const errorsByType = recentErrors.reduce((acc, { error }) => {
      acc[error.name] = (acc[error.name] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const errorsByCode = recentErrors.reduce((acc, { error }) => {
      acc[error.code] = (acc[error.code] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      totalErrors: this.errors.length,
      errorsByType,
      errorsByCode,
      recentErrors: recentErrors.length,
      errorRate: recentErrors.length / (timeWindow / 1000) // errors per second
    }
  }

  /**
   * Get recent errors
   */
  getRecentErrors(count: number = 10): Array<{
    error: ResearchError
    timestamp: number
    context?: Record<string, any>
  }> {
    return this.errors.slice(-count)
  }

  /**
   * Clear error history
   */
  clear(): void {
    this.errors = []
  }

  /**
   * Export error data
   */
  export(): any {
    return {
      errors: this.errors.map(({ error, timestamp, context }) => ({
        name: error.name,
        message: error.message,
        code: error.code,
        retryable: error.retryable,
        timestamp,
        context
      })),
      exportedAt: new Date().toISOString()
    }
  }
}

/**
 * Global error handler instance
 */
export const globalErrorHandler = new ErrorHandler()
export const globalErrorAggregator = new ErrorAggregator()

/**
 * Error handling decorator
 */
export function handleErrors(
  retryable: boolean = true,
  maxRetries: number = 3
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    const errorHandler = new ErrorHandler({ maxRetries })

    descriptor.value = async function (...args: any[]) {
      const operationId = `${target.constructor.name}.${propertyName}`
      
      try {
        if (retryable) {
          return await errorHandler.handleWithRetry(
            () => method.apply(this, args),
            operationId,
            { method: propertyName, args: args.length }
          )
        } else {
          return await method.apply(this, args)
        }
      } catch (error) {
        const researchError = errorHandler.handle(error, {
          method: propertyName,
          args: args.length
        })
        
        globalErrorAggregator.addError(researchError, {
          method: propertyName,
          class: target.constructor.name
        })
        
        throw researchError
      }
    }

    return descriptor
  }
}

/**
 * Safe execution wrapper
 */
export async function safeExecute<T>(
  operation: () => Promise<T>,
  fallback?: T,
  context?: Record<string, any>
): Promise<{ success: boolean; result?: T; error?: ResearchError }> {
  try {
    const result = await operation()
    return { success: true, result }
  } catch (error) {
    const researchError = globalErrorHandler.handle(error, context)
    globalErrorAggregator.addError(researchError, context)
    
    return {
      success: false,
      error: researchError,
      result: fallback
    }
  }
}

/**
 * Batch error handling
 */
export async function executeBatch<T>(
  operations: Array<() => Promise<T>>,
  options: {
    maxConcurrency?: number
    continueOnError?: boolean
    aggregateErrors?: boolean
  } = {}
): Promise<{
  results: Array<{ success: boolean; result?: T; error?: ResearchError }>
  summary: { successful: number; failed: number; total: number }
}> {
  const {
    maxConcurrency = 3,
    continueOnError = true,
    aggregateErrors = true
  } = options

  const results: Array<{ success: boolean; result?: T; error?: ResearchError }> = []
  const errors: ResearchError[] = []

  // Process operations in batches
  for (let i = 0; i < operations.length; i += maxConcurrency) {
    const batch = operations.slice(i, i + maxConcurrency)
    
    const batchPromises = batch.map(async (operation, index) => {
      try {
        const result = await operation()
        return { success: true, result }
      } catch (error) {
        const researchError = globalErrorHandler.handle(error, {
          batchIndex: i + index,
          operationIndex: index
        })
        
        if (aggregateErrors) {
          errors.push(researchError)
        }
        
        if (!continueOnError) {
          throw researchError
        }
        
        return { success: false, error: researchError }
      }
    })

    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
  }

  // Add errors to global aggregator
  if (aggregateErrors) {
    errors.forEach(error => globalErrorAggregator.addError(error))
  }

  const successful = results.filter(r => r.success).length
  const failed = results.filter(r => !r.success).length

  return {
    results,
    summary: {
      successful,
      failed,
      total: operations.length
    }
  }
}

/**
 * Error recovery strategies
 */
export const ErrorRecoveryStrategies = {
  /**
   * Retry with exponential backoff
   */
  exponentialBackoff: (attempt: number, baseDelay: number = 1000): number => {
    return baseDelay * Math.pow(2, attempt) + Math.random() * 1000
  },

  /**
   * Linear backoff
   */
  linearBackoff: (attempt: number, baseDelay: number = 1000): number => {
    return baseDelay * (attempt + 1)
  },

  /**
   * Fixed delay
   */
  fixedDelay: (baseDelay: number = 1000): number => {
    return baseDelay
  },

  /**
   * Jittered delay
   */
  jitteredDelay: (baseDelay: number = 1000, jitterFactor: number = 0.1): number => {
    const jitter = Math.random() * jitterFactor * baseDelay
    return baseDelay + jitter
  }
}