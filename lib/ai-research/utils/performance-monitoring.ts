// Performance Monitoring Utilities
// Tools for monitoring and optimizing research performance

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  timestamp: string
  metadata?: Record<string, any>
}

interface PerformanceSession {
  id: string
  startTime: number
  endTime?: number
  metrics: PerformanceMetric[]
  operations: PerformanceOperation[]
}

interface PerformanceOperation {
  id: string
  name: string
  startTime: number
  endTime?: number
  duration?: number
  success: boolean
  error?: string
  metadata?: Record<string, any>
}

/**
 * Performance Monitor Class
 */
export class PerformanceMonitor {
  private sessions: Map<string, PerformanceSession> = new Map()
  private currentSession: string | null = null
  private operations: Map<string, PerformanceOperation> = new Map()

  /**
   * Start a new performance monitoring session
   */
  startSession(sessionId?: string): string {
    const id = sessionId || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const session: PerformanceSession = {
      id,
      startTime: performance.now(),
      metrics: [],
      operations: []
    }

    this.sessions.set(id, session)
    this.currentSession = id
    
    return id
  }

  /**
   * End the current session
   */
  endSession(sessionId?: string): PerformanceSession | null {
    const id = sessionId || this.currentSession
    if (!id) return null

    const session = this.sessions.get(id)
    if (!session) return null

    session.endTime = performance.now()
    
    if (id === this.currentSession) {
      this.currentSession = null
    }

    return session
  }

  /**
   * Start tracking an operation
   */
  startOperation(name: string, metadata?: Record<string, any>): string {
    const operationId = `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const operation: PerformanceOperation = {
      id: operationId,
      name,
      startTime: performance.now(),
      success: false,
      metadata
    }

    this.operations.set(operationId, operation)

    // Add to current session if exists
    if (this.currentSession) {
      const session = this.sessions.get(this.currentSession)
      if (session) {
        session.operations.push(operation)
      }
    }

    return operationId
  }

  /**
   * End tracking an operation
   */
  endOperation(operationId: string, success: boolean = true, error?: string): PerformanceOperation | null {
    const operation = this.operations.get(operationId)
    if (!operation) return null

    operation.endTime = performance.now()
    operation.duration = operation.endTime - operation.startTime
    operation.success = success
    operation.error = error

    return operation
  }

  /**
   * Record a metric
   */
  recordMetric(name: string, value: number, unit: string = 'ms', metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date().toISOString(),
      metadata
    }

    // Add to current session if exists
    if (this.currentSession) {
      const session = this.sessions.get(this.currentSession)
      if (session) {
        session.metrics.push(metric)
      }
    }
  }

  /**
   * Get session statistics
   */
  getSessionStats(sessionId?: string): any {
    const id = sessionId || this.currentSession
    if (!id) return null

    const session = this.sessions.get(id)
    if (!session) return null

    const totalDuration = session.endTime ? session.endTime - session.startTime : performance.now() - session.startTime
    const successfulOps = session.operations.filter(op => op.success).length
    const failedOps = session.operations.filter(op => !op.success).length
    const avgOpDuration = session.operations.length > 0 
      ? session.operations.reduce((sum, op) => sum + (op.duration || 0), 0) / session.operations.length
      : 0

    return {
      sessionId: id,
      totalDuration,
      operationsCount: session.operations.length,
      successfulOperations: successfulOps,
      failedOperations: failedOps,
      successRate: session.operations.length > 0 ? successfulOps / session.operations.length : 0,
      averageOperationDuration: avgOpDuration,
      metricsCount: session.metrics.length,
      isActive: !session.endTime
    }
  }

  /**
   * Get operation statistics
   */
  getOperationStats(operationName?: string): any {
    const allOperations = Array.from(this.operations.values())
    const filteredOps = operationName 
      ? allOperations.filter(op => op.name === operationName)
      : allOperations

    if (filteredOps.length === 0) return null

    const completedOps = filteredOps.filter(op => op.endTime)
    const successfulOps = completedOps.filter(op => op.success)
    const durations = completedOps.map(op => op.duration || 0)

    return {
      operationName,
      totalOperations: filteredOps.length,
      completedOperations: completedOps.length,
      successfulOperations: successfulOps.length,
      failedOperations: completedOps.length - successfulOps.length,
      successRate: completedOps.length > 0 ? successfulOps.length / completedOps.length : 0,
      averageDuration: durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0,
      minDuration: durations.length > 0 ? Math.min(...durations) : 0,
      maxDuration: durations.length > 0 ? Math.max(...durations) : 0,
      medianDuration: durations.length > 0 ? this.calculateMedian(durations) : 0
    }
  }

  /**
   * Get performance trends
   */
  getPerformanceTrends(timeWindow: number = 3600000): any { // Default 1 hour
    const now = Date.now()
    const cutoff = now - timeWindow

    const recentSessions = Array.from(this.sessions.values())
      .filter(session => session.startTime > cutoff)

    const recentOperations = Array.from(this.operations.values())
      .filter(op => op.startTime > cutoff && op.endTime)

    // Calculate trends
    const operationsByType = this.groupBy(recentOperations, 'name')
    const trends: any = {}

    Object.entries(operationsByType).forEach(([opName, ops]) => {
      const durations = (ops as PerformanceOperation[]).map(op => op.duration || 0)
      const successRate = (ops as PerformanceOperation[]).filter(op => op.success).length / ops.length

      trends[opName] = {
        count: ops.length,
        averageDuration: durations.reduce((a, b) => a + b, 0) / durations.length,
        successRate,
        trend: this.calculateTrend(durations)
      }
    })

    return {
      timeWindow,
      sessionsCount: recentSessions.length,
      operationsCount: recentOperations.length,
      trends
    }
  }

  /**
   * Export performance data
   */
  exportData(format: 'json' | 'csv' = 'json'): string {
    const data = {
      sessions: Array.from(this.sessions.values()),
      operations: Array.from(this.operations.values()),
      exportedAt: new Date().toISOString()
    }

    if (format === 'json') {
      return JSON.stringify(data, null, 2)
    } else {
      return this.convertToCSV(data)
    }
  }

  /**
   * Clear old data
   */
  cleanup(maxAge: number = 86400000): void { // Default 24 hours
    const cutoff = Date.now() - maxAge

    // Remove old sessions
    for (const [id, session] of this.sessions.entries()) {
      if (session.startTime < cutoff) {
        this.sessions.delete(id)
      }
    }

    // Remove old operations
    for (const [id, operation] of this.operations.entries()) {
      if (operation.startTime < cutoff) {
        this.operations.delete(id)
      }
    }
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats(): any {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage()
      return {
        heapUsed: usage.heapUsed,
        heapTotal: usage.heapTotal,
        external: usage.external,
        rss: usage.rss,
        arrayBuffers: usage.arrayBuffers
      }
    }

    // Browser environment
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      }
    }

    return null
  }

  // Helper methods

  private calculateMedian(numbers: number[]): number {
    const sorted = numbers.slice().sort((a, b) => a - b)
    const middle = Math.floor(sorted.length / 2)

    if (sorted.length % 2 === 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2
    }

    return sorted[middle]
  }

  private calculateTrend(values: number[]): 'improving' | 'degrading' | 'stable' {
    if (values.length < 2) return 'stable'

    const firstHalf = values.slice(0, Math.floor(values.length / 2))
    const secondHalf = values.slice(Math.floor(values.length / 2))

    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length

    const change = (secondAvg - firstAvg) / firstAvg

    if (change < -0.1) return 'improving' // Duration decreased (better)
    if (change > 0.1) return 'degrading' // Duration increased (worse)
    return 'stable'
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const group = String(item[key])
      groups[group] = groups[group] || []
      groups[group].push(item)
      return groups
    }, {} as Record<string, T[]>)
  }

  private convertToCSV(data: any): string {
    // Simple CSV conversion - would be more sophisticated in production
    const operations = data.operations
    const headers = ['id', 'name', 'startTime', 'endTime', 'duration', 'success', 'error']
    
    let csv = headers.join(',') + '\n'
    
    operations.forEach((op: PerformanceOperation) => {
      const row = [
        op.id,
        op.name,
        op.startTime,
        op.endTime || '',
        op.duration || '',
        op.success,
        op.error || ''
      ]
      csv += row.join(',') + '\n'
    })

    return csv
  }
}

/**
 * Global performance monitor instance
 */
export const performanceMonitor = new PerformanceMonitor()

/**
 * Decorator for monitoring function performance
 */
export function monitorPerformance(operationName?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const opName = operationName || `${target.constructor.name}.${propertyName}`
      const operationId = performanceMonitor.startOperation(opName, { args: args.length })

      try {
        const result = await method.apply(this, args)
        performanceMonitor.endOperation(operationId, true)
        return result
      } catch (error) {
        performanceMonitor.endOperation(operationId, false, error instanceof Error ? error.message : 'Unknown error')
        throw error
      }
    }

    return descriptor
  }
}

/**
 * Utility function to measure execution time
 */
export async function measureTime<T>(
  operation: () => Promise<T> | T,
  operationName?: string
): Promise<{ result: T; duration: number }> {
  const startTime = performance.now()
  
  try {
    const result = await operation()
    const duration = performance.now() - startTime
    
    if (operationName) {
      performanceMonitor.recordMetric(`${operationName}_duration`, duration, 'ms')
    }
    
    return { result, duration }
  } catch (error) {
    const duration = performance.now() - startTime
    
    if (operationName) {
      performanceMonitor.recordMetric(`${operationName}_duration`, duration, 'ms')
      performanceMonitor.recordMetric(`${operationName}_error`, 1, 'count')
    }
    
    throw error
  }
}

/**
 * Performance threshold checker
 */
export class PerformanceThresholds {
  private thresholds: Map<string, number> = new Map()

  setThreshold(operationName: string, maxDuration: number): void {
    this.thresholds.set(operationName, maxDuration)
  }

  checkThreshold(operationName: string, duration: number): boolean {
    const threshold = this.thresholds.get(operationName)
    return threshold ? duration <= threshold : true
  }

  getViolations(timeWindow: number = 3600000): Array<{ operation: string; threshold: number; actual: number; timestamp: string }> {
    const violations = []
    const stats = performanceMonitor.getPerformanceTrends(timeWindow)

    for (const [operation, threshold] of this.thresholds.entries()) {
      const trend = stats.trends[operation]
      if (trend && trend.averageDuration > threshold) {
        violations.push({
          operation,
          threshold,
          actual: trend.averageDuration,
          timestamp: new Date().toISOString()
        })
      }
    }

    return violations
  }
}

/**
 * Resource usage monitor
 */
export class ResourceMonitor {
  private intervals: Map<string, NodeJS.Timeout> = new Map()

  startMonitoring(intervalMs: number = 5000): void {
    const interval = setInterval(() => {
      const memStats = performanceMonitor.getMemoryStats()
      if (memStats) {
        Object.entries(memStats).forEach(([key, value]) => {
          performanceMonitor.recordMetric(`memory_${key}`, value as number, 'bytes')
        })
      }
    }, intervalMs)

    this.intervals.set('memory', interval)
  }

  stopMonitoring(): void {
    for (const interval of this.intervals.values()) {
      clearInterval(interval)
    }
    this.intervals.clear()
  }
}

/**
 * Performance alert system
 */
export class PerformanceAlerts {
  private callbacks: Map<string, (alert: any) => void> = new Map()

  onAlert(alertType: string, callback: (alert: any) => void): void {
    this.callbacks.set(alertType, callback)
  }

  checkAlerts(): void {
    // Check for performance degradation
    const trends = performanceMonitor.getPerformanceTrends()
    
    Object.entries(trends.trends).forEach(([operation, trend]: [string, any]) => {
      if (trend.trend === 'degrading') {
        this.triggerAlert('performance_degradation', {
          operation,
          averageDuration: trend.averageDuration,
          successRate: trend.successRate,
          timestamp: new Date().toISOString()
        })
      }

      if (trend.successRate < 0.9) {
        this.triggerAlert('low_success_rate', {
          operation,
          successRate: trend.successRate,
          timestamp: new Date().toISOString()
        })
      }
    })
  }

  private triggerAlert(alertType: string, alert: any): void {
    const callback = this.callbacks.get(alertType)
    if (callback) {
      callback(alert)
    }
  }
}