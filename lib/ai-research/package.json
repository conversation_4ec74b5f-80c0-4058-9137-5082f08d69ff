{"name": "@coco-milk-store/ai-research", "version": "1.0.0", "description": "Intelligent AI tools for web searching, deep research, and context understanding", "main": "index.js", "types": "index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint . --ext .ts", "example:research": "ts-node examples/research-example.ts", "example:fact-check": "ts-node examples/fact-check-example.ts"}, "keywords": ["ai", "research", "web-search", "fact-checking", "content-analysis", "knowledge-extraction", "ai-sdk", "agents", "tools", "verification"], "author": "Coco Milk Store", "license": "MIT", "dependencies": {"ai": "^3.0.0", "@ai-sdk/openai": "^0.0.0", "zod": "^3.22.0", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "peerDependencies": {"ai": ">=3.0.0", "@ai-sdk/openai": ">=0.0.0"}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/your-org/coco-milk-store"}, "bugs": {"url": "https://github.com/your-org/coco-milk-store/issues"}, "homepage": "https://github.com/your-org/coco-milk-store#readme", "engines": {"node": ">=18.0.0"}}