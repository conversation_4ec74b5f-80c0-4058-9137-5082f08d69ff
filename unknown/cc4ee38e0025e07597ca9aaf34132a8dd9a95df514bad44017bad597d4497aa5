'use client'

import { useState, useCallback } from 'react'
import { codebaseAnalyzerClient } from '../services/codebase-analyzer-client'
import type {
  CodebaseAnalysisOptions,
  ComponentUsagePatterns,
  ArchitecturalPatterns
} from '../services/codebase-analyzer-client'
import type { CodebaseComponent } from '../services/codebase-analyzer'

interface UseCodebaseAnalysisOptions {
  onSuccess?: (result: any) => void
  onError?: (error: Error) => void
  autoCache?: boolean
  cacheTimeout?: number
}

export function useCodebaseAnalysis(options: UseCodebaseAnalysisOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [components, setComponents] = useState<CodebaseComponent[]>([])
  const [usagePatterns, setUsagePatterns] = useState<ComponentUsagePatterns | null>(null)
  const [architecturalPatterns, setArchitecturalPatterns] = useState<ArchitecturalPatterns | null>(null)
  const [lastAnalysis, setLastAnalysis] = useState<Date | null>(null)

  const discoverComponents = useCallback(async (analysisOptions: CodebaseAnalysisOptions = {}) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const discoveredComponents = await codebaseAnalyzerClient.discoverComponents(analysisOptions)
      setComponents(discoveredComponents)
      setLastAnalysis(new Date())
      options.onSuccess?.(discoveredComponents)
      
      return discoveredComponents
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const analyzeComponent = useCallback(async (filePath: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const component = await codebaseAnalyzerClient.analyzeComponentFile(filePath)
      options.onSuccess?.(component)
      
      return component
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const getUsagePatterns = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const patterns = await codebaseAnalyzerClient.getComponentUsagePatterns()
      setUsagePatterns(patterns)
      options.onSuccess?.(patterns)
      
      return patterns
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const getArchitecturalPatterns = useCallback(async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const patterns = await codebaseAnalyzerClient.getArchitecturalPatterns()
      setArchitecturalPatterns(patterns)
      options.onSuccess?.(patterns)
      
      return patterns
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const performFullAnalysis = useCallback(async (analysisOptions: CodebaseAnalysisOptions = {}) => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Run all analyses in parallel
      const [discoveredComponents, patterns, architectural] = await Promise.all([
        codebaseAnalyzerClient.discoverComponents(analysisOptions),
        codebaseAnalyzerClient.getComponentUsagePatterns(),
        codebaseAnalyzerClient.getArchitecturalPatterns()
      ])
      
      setComponents(discoveredComponents)
      setUsagePatterns(patterns)
      setArchitecturalPatterns(architectural)
      setLastAnalysis(new Date())
      
      const fullAnalysis = {
        components: discoveredComponents,
        usagePatterns: patterns,
        architecturalPatterns: architectural,
        timestamp: new Date()
      }
      
      options.onSuccess?.(fullAnalysis)
      return fullAnalysis
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const findComponentsByType = useCallback((type: string) => {
    return components.filter(component => 
      component.category === type || 
      component.name.toLowerCase().includes(type.toLowerCase())
    )
  }, [components])

  const findComponentsByPattern = useCallback((pattern: string) => {
    return components.filter(component =>
      component.patterns.some(p => p.pattern.includes(pattern)) ||
      component.stylePatterns.some(sp => sp.pattern.includes(pattern))
    )
  }, [components])

  const getComponentStats = useCallback(() => {
    if (components.length === 0) return null
    
    const stats = {
      total: components.length,
      byCategory: components.reduce((acc, comp) => {
        acc[comp.category] = (acc[comp.category] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      byComplexity: components.reduce((acc, comp) => {
        acc[comp.complexity] = (acc[comp.complexity] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      averageComplexity: components.reduce((sum, comp) => {
        const complexityScore = comp.complexity === 'simple' ? 1 : comp.complexity === 'moderate' ? 2 : 3
        return sum + complexityScore
      }, 0) / components.length,
      mostUsedShadcnComponents: usagePatterns?.shadcnUsage ? 
        Object.entries(usagePatterns.shadcnUsage)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 10) : []
    }
    
    return stats
  }, [components, usagePatterns])

  const reset = useCallback(() => {
    setComponents([])
    setUsagePatterns(null)
    setArchitecturalPatterns(null)
    setLastAnalysis(null)
    setError(null)
    setIsLoading(false)
  }, [])

  const refresh = useCallback(async (analysisOptions?: CodebaseAnalysisOptions) => {
    return performFullAnalysis(analysisOptions)
  }, [performFullAnalysis])

  return {
    // State
    isLoading,
    error,
    components,
    usagePatterns,
    architecturalPatterns,
    lastAnalysis,
    
    // Actions
    discoverComponents,
    analyzeComponent,
    getUsagePatterns,
    getArchitecturalPatterns,
    performFullAnalysis,
    reset,
    refresh,
    
    // Utilities
    findComponentsByType,
    findComponentsByPattern,
    getComponentStats,
    
    // Computed
    hasComponents: components.length > 0,
    hasUsagePatterns: !!usagePatterns,
    hasArchitecturalPatterns: !!architecturalPatterns,
    hasError: !!error,
    isAnalysisComplete: !!(components.length > 0 && usagePatterns && architecturalPatterns),
    timeSinceLastAnalysis: lastAnalysis ? Date.now() - lastAnalysis.getTime() : null
  }
}

// Hook for real-time component monitoring
export function useComponentMonitoring(
  watchPaths: string[] = [],
  options: { onComponentChange?: (component: CodebaseComponent) => void } = {}
) {
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [changedComponents, setChangedComponents] = useState<CodebaseComponent[]>([])
  const [lastChange, setLastChange] = useState<Date | null>(null)

  const startMonitoring = useCallback(async () => {
    setIsMonitoring(true)
    // In a real implementation, this would set up file system watchers
    // For now, we'll simulate with periodic checks
  }, [])

  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false)
  }, [])

  const clearChanges = useCallback(() => {
    setChangedComponents([])
    setLastChange(null)
  }, [])

  return {
    isMonitoring,
    changedComponents,
    lastChange,
    startMonitoring,
    stopMonitoring,
    clearChanges,
    hasChanges: changedComponents.length > 0
  }
}
