'use client'

import { useState, useCallback } from 'react'
import type { 
  LayoutGenerationParams, 
  PageGenerationParams,
  NextJSLayout,
  NextJSPage,
  GeneratedNextJSStructure
} from '../types/nextjs-types'

interface UseNextJSGeneratorOptions {
  onSuccess?: (result: any) => void
  onError?: (error: Error) => void
}

export function useNextJSGenerator(options: UseNextJSGeneratorOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [layout, setLayout] = useState<NextJSLayout | null>(null)
  const [page, setPage] = useState<NextJSPage | null>(null)
  const [project, setProject] = useState<GeneratedNextJSStructure | null>(null)

  const generateLayout = useCallback(async (params: LayoutGenerationParams) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/nextjs-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'generate-layout',
          ...params
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate layout')
      }

      const data = await response.json()
      setLayout(data.layout)
      options.onSuccess?.(data.layout)
      
      return data.layout
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const generatePage = useCallback(async (params: PageGenerationParams) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/nextjs-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'generate-page',
          ...params
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate page')
      }

      const data = await response.json()
      setPage(data.page)
      options.onSuccess?.(data.page)
      
      return data.page
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const generateProject = useCallback(async (
    layouts: LayoutGenerationParams[],
    pages: PageGenerationParams[]
  ) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/nextjs-generator', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'generate-project',
          layouts,
          pages
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate project')
      }

      const data = await response.json()
      setProject(data.project)
      options.onSuccess?.(data.project)
      
      return data.project
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const getTemplates = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/nextjs-generator?action=templates')
      
      if (!response.ok) {
        throw new Error('Failed to get templates')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get templates')
      setError(error)
      throw error
    }
  }, [])

  const getFileStructure = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/nextjs-generator?action=file-structure')
      
      if (!response.ok) {
        throw new Error('Failed to get file structure')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get file structure')
      setError(error)
      throw error
    }
  }, [])

  const checkHealth = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/nextjs-generator?action=health')
      
      if (!response.ok) {
        throw new Error('Health check failed')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Health check failed')
      setError(error)
      throw error
    }
  }, [])

  const reset = useCallback(() => {
    setLayout(null)
    setPage(null)
    setProject(null)
    setError(null)
    setIsLoading(false)
  }, [])

  return {
    // State
    isLoading,
    error,
    layout,
    page,
    project,
    
    // Actions
    generateLayout,
    generatePage,
    generateProject,
    getTemplates,
    getFileStructure,
    checkHealth,
    reset,
    
    // Computed
    hasLayout: !!layout,
    hasPage: !!page,
    hasProject: !!project,
    hasError: !!error
  }
}

// Hook for progressive project building
export function useProgressiveNextJSBuilder(options: UseNextJSGeneratorOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [layouts, setLayouts] = useState<NextJSLayout[]>([])
  const [pages, setPages] = useState<NextJSPage[]>([])
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState<'layouts' | 'pages' | 'complete'>('layouts')

  const buildProject = useCallback(async (
    layoutParams: LayoutGenerationParams[],
    pageParams: PageGenerationParams[]
  ) => {
    setIsLoading(true)
    setError(null)
    setLayouts([])
    setPages([])
    setProgress(0)
    setCurrentStep('layouts')
    
    try {
      const totalSteps = layoutParams.length + pageParams.length
      let completedSteps = 0
      
      // Generate layouts first
      const generatedLayouts: NextJSLayout[] = []
      for (const params of layoutParams) {
        const response = await fetch('/api/node/ai-visual-editor/nextjs-generator', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'generate-layout',
            ...params
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to generate layout')
        }

        const data = await response.json()
        generatedLayouts.push(data.layout)
        setLayouts([...generatedLayouts])
        
        completedSteps++
        setProgress((completedSteps / totalSteps) * 100)
      }
      
      setCurrentStep('pages')
      
      // Generate pages
      const generatedPages: NextJSPage[] = []
      for (const params of pageParams) {
        const response = await fetch('/api/node/ai-visual-editor/nextjs-generator', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'generate-page',
            ...params
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to generate page')
        }

        const data = await response.json()
        generatedPages.push(data.page)
        setPages([...generatedPages])
        
        completedSteps++
        setProgress((completedSteps / totalSteps) * 100)
      }
      
      setCurrentStep('complete')
      
      const result = {
        layouts: generatedLayouts,
        pages: generatedPages
      }
      
      options.onSuccess?.(result)
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Progressive build failed')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const reset = useCallback(() => {
    setLayouts([])
    setPages([])
    setError(null)
    setIsLoading(false)
    setProgress(0)
    setCurrentStep('layouts')
  }, [])

  return {
    // State
    isLoading,
    error,
    layouts,
    pages,
    progress,
    currentStep,
    
    // Actions
    buildProject,
    reset,
    
    // Computed
    hasLayouts: layouts.length > 0,
    hasPages: pages.length > 0,
    hasError: !!error,
    isComplete: currentStep === 'complete' && !isLoading
  }
}
