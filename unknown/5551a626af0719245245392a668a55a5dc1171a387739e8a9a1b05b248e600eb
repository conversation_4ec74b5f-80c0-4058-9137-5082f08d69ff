import { openai } from '@ai-sdk/openai'
import { streamText } from 'ai'
import { 
  generateIntelligentBlockTool,
  analyzeCodebasePatternsToolTool,
  generateContextAwareComponentTool
} from '@/lib/ai-visual-editor/tools/intelligent-block-generator'
import { intelligentBlockService } from '@/lib/ai-visual-editor/services/intelligent-block-service'
import { NextRequest } from 'next/server'

export const maxDuration = 120

export async function POST(req: NextRequest) {
  try {
    const { messages, mode = 'chat' } = await req.json()

    if (mode === 'direct') {
      // Direct API mode for programmatic access
      return handleDirectMode(req)
    }

    // Chat mode with AI assistance
    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      system: `You are an Intelligent Shadcn Block Generator AI that learns from existing codebases to generate contextually appropriate components.

## Your Capabilities:

### 🧠 Codebase Intelligence:
- **Pattern Recognition**: Analyze existing components to understand architectural patterns
- **Style Consistency**: Generate blocks that match existing code style and conventions
- **Component Usage**: Learn from how shadcn/ui components are actually used in the project
- **Context Awareness**: Understand the project structure and generate appropriate components

### 🔍 Analysis Features:
- **Real-time Codebase Scanning**: Discover and analyze all React components in the project
- **Usage Pattern Detection**: Identify common patterns, styling approaches, and component compositions
- **Architectural Understanding**: Learn naming conventions, import styles, and file structures
- **Component Relationships**: Understand how components interact and depend on each other

### 🎯 Intelligent Generation:
- **Template-Free Generation**: No pre-built templates - everything is learned from the codebase
- **Context-Aware Components**: Generate components that fit naturally into the existing architecture
- **Style Matching**: Automatically match existing code style, naming, and patterns
- **Smart Component Selection**: Choose optimal shadcn components based on actual usage patterns

### 🛠️ Available Tools:

1. **generateIntelligentBlock**: Generate blocks by analyzing codebase patterns
   - Learns from existing component usage
   - Matches code style and conventions
   - Selects optimal shadcn components
   - Generates contextually appropriate code

2. **analyzeCodebasePatterns**: Analyze the entire codebase for patterns
   - Discover all React components
   - Extract usage patterns and architectural decisions
   - Identify common styling approaches
   - Build component usage statistics

3. **generateContextAwareComponent**: Generate components for specific contexts
   - Analyze the target location in the codebase
   - Find similar existing components
   - Inherit patterns from nearby components
   - Generate components that fit the local architecture

## Generation Philosophy:

### 🎨 Style Consistency:
- **Learn, Don't Assume**: Extract actual patterns from the codebase rather than using defaults
- **Match Conventions**: Follow existing naming, import, and export patterns
- **Respect Architecture**: Generate components that fit the existing file structure
- **Maintain Quality**: Ensure generated code meets the same standards as existing code

### 🔧 Technical Excellence:
- **TypeScript First**: Generate properly typed components with interfaces
- **Accessibility**: Include ARIA labels and keyboard navigation
- **Performance**: Optimize for rendering performance and bundle size
- **Responsive**: Mobile-first design with proper breakpoints

### 📊 Data-Driven Decisions:
- **Usage Statistics**: Prioritize components based on actual usage frequency
- **Pattern Frequency**: Apply the most common patterns found in the codebase
- **Success Metrics**: Track style consistency and pattern matching scores
- **Continuous Learning**: Improve recommendations based on codebase evolution

## Workflow:

1. **Analyze Request**: Understand what the user wants to build
2. **Scan Codebase**: Discover existing components and patterns (if enabled)
3. **Learn Patterns**: Extract architectural decisions and style preferences
4. **Select Components**: Choose optimal shadcn components based on usage data
5. **Generate Code**: Create components that match existing patterns
6. **Validate Consistency**: Ensure the generated code fits the codebase style
7. **Provide Recommendations**: Suggest improvements and optimizations

## Best Practices:

- **Always analyze the codebase first** unless explicitly told not to
- **Explain your reasoning** for component and pattern choices
- **Provide style consistency scores** to show how well the generated code matches
- **Offer alternatives** based on different patterns found in the codebase
- **Include recommendations** for improving consistency and maintainability

Remember: You're not just generating components - you're learning from and extending an existing codebase intelligently.`,
      tools: {
        generateIntelligentBlock: generateIntelligentBlockTool,
        analyzeCodebasePatterns: analyzeCodebasePatternsToolTool,
        generateContextAwareComponent: generateContextAwareComponentTool,
      },
      maxSteps: 5,
      toolChoice: 'auto',
    })

    return result.toDataStreamResponse({
      getErrorMessage: (error) => {
        if (error == null) {
          return 'An unknown error occurred while generating intelligent blocks.'
        }

        if (typeof error === 'string') {
          return error
        }

        if (error instanceof Error) {
          return error.message
        }

        return 'An error occurred during intelligent block generation.'
      },
    })
  } catch (error) {
    console.error('Intelligent blocks API error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}

async function handleDirectMode(req: NextRequest) {
  try {
    const body = await req.json()
    const { action, ...params } = body

    switch (action) {
      case 'generateBlock':
        return await handleGenerateBlock(params)
      
      case 'analyzeCodebase':
        return await handleAnalyzeCodebase(params)
      
      case 'getComponentRegistry':
        return await handleGetComponentRegistry(params)
      
      case 'generateContextAware':
        return await handleGenerateContextAware(params)
      
      default:
        return new Response(
          JSON.stringify({ success: false, error: 'Invalid action' }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Direct mode error:', error)
    return new Response(
      JSON.stringify({ success: false, error: 'Failed to process request' }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}

async function handleGenerateBlock(params: any) {
  const result = await intelligentBlockService.generateIntelligentBlock(params)
  
  return new Response(
    JSON.stringify(result),
    { headers: { 'Content-Type': 'application/json' } }
  )
}

async function handleAnalyzeCodebase(params: any) {
  const analysis = await intelligentBlockService.analyzeCodebase(params.contextPath)
  
  return new Response(
    JSON.stringify({
      success: true,
      analysis,
      timestamp: new Date().toISOString()
    }),
    { headers: { 'Content-Type': 'application/json' } }
  )
}

async function handleGetComponentRegistry(params: any) {
  try {
    // Fetch component registry from codebase analysis
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/codebase/analyze-components`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })
    
    const data = await response.json()
    
    return new Response(
      JSON.stringify({
        success: true,
        registry: data.analysis?.componentRegistry || {},
        shadcnUsage: data.analysis?.shadcnUsage || {},
        totalComponents: data.analysis?.totalComponents || 0
      }),
      { headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to get component registry'
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}

async function handleGenerateContextAware(params: any) {
  const {
    componentType,
    contextPath,
    requirements,
    inheritPatterns = true
  } = params

  try {
    // Analyze the context area
    const contextAnalysis = await intelligentBlockService.analyzeCodebase(contextPath)
    
    // Generate context-aware component
    const result = await intelligentBlockService.generateIntelligentBlock({
      description: requirements,
      blockType: componentType,
      contextPath,
      learnFromCodebase: true,
      matchExistingStyle: inheritPatterns,
      complexity: 'moderate'
    })

    return new Response(
      JSON.stringify({
        success: true,
        component: result.block,
        contextAnalysis: {
          totalComponents: contextAnalysis.totalComponents,
          commonPatterns: contextAnalysis.commonPatterns.slice(0, 5),
          shadcnUsage: Object.entries(contextAnalysis.shadcnUsage).slice(0, 10)
        },
        styleConsistency: result.styleConsistency,
        recommendations: result.recommendations
      }),
      { headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to generate context-aware component'
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}

// Health check endpoint
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const action = searchParams.get('action')

    if (action === 'health') {
      return new Response(
        JSON.stringify({
          success: true,
          status: 'healthy',
          timestamp: new Date().toISOString(),
          features: {
            codebaseAnalysis: true,
            intelligentGeneration: true,
            contextAwareness: true,
            realTimeMonitoring: true
          }
        }),
        { headers: { 'Content-Type': 'application/json' } }
      )
    }

    if (action === 'stats') {
      // Get basic stats about the intelligent block service
      const analysis = await intelligentBlockService.analyzeCodebase()
      
      return new Response(
        JSON.stringify({
          success: true,
          stats: {
            totalComponents: analysis.totalComponents,
            shadcnComponentsUsed: Object.keys(analysis.shadcnUsage).length,
            mostUsedComponents: Object.entries(analysis.shadcnUsage)
              .sort(([, a], [, b]) => b - a)
              .slice(0, 5)
              .map(([comp, count]) => ({ component: comp, usage: count })),
            commonPatterns: analysis.commonPatterns.slice(0, 5),
            lastAnalyzed: new Date().toISOString()
          }
        }),
        { headers: { 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Invalid action. Use ?action=health or ?action=stats'
      }),
      { status: 400, headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('GET endpoint error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to process GET request'
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    )
  }
}
