'use client'

import { useState, useCallback } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  ShoppingCart,
  Plus,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Package,
  Truck,
  Clock,
  CheckCircle,
  AlertTriangle,
  Activity,
  Warehouse,
  Zap,
  Users,
  BarChart3,
  RefreshCw,
  Download,
  Filter,
  Search,
  Eye,
  Edit,
  Calendar,
  MapPin,
  CreditCard,
  Star,
  Target,
  Sparkles
} from 'lucide-react'
import { useOrders, useOrderCounts } from '@/lib/ecommerce/hooks/use-orders'
import { EnhancedOrderList } from './enhanced-order-list'
import { OrderAnalytics } from './order-analytics'
import { OrderProcessingDashboard } from './order-processing-dashboard'
import { BulkOrderOperations } from './bulk-order-operations'
import { OrderAutomationCenter } from './order-automation-center'
import { formatCurrency } from '@/lib/utils'
import type { Order } from '@/lib/ecommerce/types/order'

interface OrderStats {
  total: number
  pending: number
  processing: number
  shipped: number
  delivered: number
  cancelled: number
  totalRevenue: number
  averageOrderValue: number
  todayOrders: number
  weeklyGrowth: number
}

export function EnhancedOrdersDashboard() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('overview')
  const orderCounts = useOrderCounts()

  // Mock stats - in real app, this would come from API
  const stats: OrderStats = {
    total: orderCounts.total || 1247,
    pending: orderCounts.pending || 23,
    processing: orderCounts.processing || 45,
    shipped: orderCounts.shipped || 67,
    delivered: orderCounts.delivered || 1089,
    cancelled: orderCounts.cancelled || 23,
    totalRevenue: 156789.50,
    averageOrderValue: 125.75,
    todayOrders: 12,
    weeklyGrowth: 8.5
  }

  const quickActions = [
    {
      label: 'Create Order',
      href: '/admin/e-commerce/orders/new',
      icon: Plus,
      description: 'Create a new order manually',
      variant: 'default' as const
    },
    {
      label: 'Processing Dashboard',
      href: '/admin/e-commerce/orders/processing',
      icon: Activity,
      description: 'Monitor order processing',
      variant: 'secondary' as const,
      badge: stats.processing > 0 ? stats.processing.toString() : undefined
    },
    {
      label: 'Fulfillment Center',
      href: '/admin/e-commerce/orders/fulfillment',
      icon: Package,
      description: 'Process fulfillments',
      variant: 'outline' as const,
      badge: stats.pending > 0 ? stats.pending.toString() : undefined
    },
    {
      label: 'Analytics',
      href: '/admin/e-commerce/orders/analytics',
      icon: BarChart3,
      description: 'Order insights & reports',
      variant: 'outline' as const
    }
  ]

  const statCards = [
    {
      title: 'Total Orders',
      value: stats.total,
      change: `+${stats.weeklyGrowth}%`,
      trend: 'up' as const,
      icon: ShoppingCart,
      description: 'All time orders'
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(stats.totalRevenue, 'ZAR'),
      change: '+12%',
      trend: 'up' as const,
      icon: DollarSign,
      description: 'Total sales revenue'
    },
    {
      title: 'Average Order Value',
      value: formatCurrency(stats.averageOrderValue, 'ZAR'),
      change: '+5%',
      trend: 'up' as const,
      icon: TrendingUp,
      description: 'Average order value'
    },
    {
      title: 'Today\'s Orders',
      value: stats.todayOrders,
      change: '+3',
      trend: 'up' as const,
      icon: Calendar,
      description: 'Orders placed today'
    }
  ]

  const statusCards = [
    {
      title: 'Pending',
      value: stats.pending,
      icon: Clock,
      variant: 'warning' as const,
      action: 'Process Now',
      href: '/admin/e-commerce/orders?status=pending'
    },
    {
      title: 'Processing',
      value: stats.processing,
      icon: RefreshCw,
      variant: 'default' as const,
      action: 'Monitor',
      href: '/admin/e-commerce/orders/processing'
    },
    {
      title: 'Shipped',
      value: stats.shipped,
      icon: Truck,
      variant: 'secondary' as const,
      action: 'Track',
      href: '/admin/e-commerce/orders?status=shipped'
    },
    {
      title: 'Delivered',
      value: stats.delivered,
      icon: CheckCircle,
      variant: 'success' as const,
      action: 'View',
      href: '/admin/e-commerce/orders?status=delivered'
    }
  ]

  const handleCreateOrder = useCallback(() => {
    router.push('/admin/e-commerce/orders/new')
  }, [router])

  const handleEditOrder = useCallback((order: Order) => {
    router.push(`/admin/e-commerce/orders/${order.id}/edit`)
  }, [router])

  const handleViewOrder = useCallback((order: Order) => {
    router.push(`/admin/e-commerce/orders/${order.id}`)
  }, [router])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Order Management</h1>
          <p className="text-muted-foreground">
            Process and manage customer orders with advanced tools
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <Button
                key={action.href}
                variant={action.variant}
                size="sm"
                onClick={() => router.push(action.href)}
                className="relative"
              >
                <Icon className="mr-2 h-4 w-4" />
                {action.label}
                {action.badge && (
                  <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                    {action.badge}
                  </Badge>
                )}
              </Button>
            )
          })}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => {
          const Icon = stat.icon
          const TrendIcon = stat.trend === 'up' ? TrendingUp : TrendingDown
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <TrendIcon className={`mr-1 h-3 w-3 ${
                    stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
                  }`} />
                  {stat.change} from last week
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Order Status Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        {statusCards.map((status) => {
          const Icon = status.icon
          return (
            <Card key={status.title} className="border-l-4 border-l-blue-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Icon className="mr-2 h-4 w-4" />
                    {status.title}
                  </CardTitle>
                  <Badge variant={status.variant}>{status.value}</Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => router.push(status.href)}
                >
                  {status.action}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Processing Alerts */}
      {(stats.pending > 10 || stats.processing > 20) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {stats.pending > 10 && `${stats.pending} orders are pending processing. `}
            {stats.processing > 20 && `${stats.processing} orders are currently being processed.`}
            Consider reviewing your processing workflow.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <ShoppingCart className="h-4 w-4" />
            <span>Orders</span>
          </TabsTrigger>
          <TabsTrigger value="processing" className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Processing</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center space-x-2">
            <Zap className="h-4 w-4" />
            <span>Bulk Ops</span>
          </TabsTrigger>
          <TabsTrigger value="automation" className="flex items-center space-x-2">
            <Sparkles className="h-4 w-4" />
            <span>Automation</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <EnhancedOrderList
            onCreateOrder={handleCreateOrder}
            onEditOrder={handleEditOrder}
            onViewOrder={handleViewOrder}
          />
        </TabsContent>

        <TabsContent value="processing" className="space-y-4">
          <OrderProcessingDashboard />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <OrderAnalytics />
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Operations</CardTitle>
              <CardDescription>
                Perform actions on multiple orders at once
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkOrderOperations
                selectedOrders={[]}
                orders={[]}
                onOperationComplete={() => {}}
                onClearSelection={() => {}}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="automation" className="space-y-4">
          <OrderAutomationCenter />
        </TabsContent>
      </Tabs>
    </div>
  )
}
