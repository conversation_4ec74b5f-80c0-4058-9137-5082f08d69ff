'use client'

import { useState, useCallback, useEffect } from 'react'
import type { 
  UserInteraction, 
  UserJourney, 
  UXInsights, 
  PersonalizationProfile 
} from '../services/user-experience-service'

interface UseUserExperienceOptions {
  userId?: string
  sessionId?: string
  autoTrack?: boolean
  onInsights?: (insights: UXInsights) => void
  onError?: (error: Error) => void
}

export function useUserExperience(options: UseUserExperienceOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [currentJourney, setCurrentJourney] = useState<UserJourney | null>(null)
  const [insights, setInsights] = useState<UXInsights | null>(null)
  const [personalization, setPersonalization] = useState<PersonalizationProfile | null>(null)
  const [interactions, setInteractions] = useState<UserInteraction[]>([])

  const trackInteraction = useCallback(async (
    sessionId: string,
    interaction: UserInteraction
  ) => {
    try {
      // Add to local state immediately for responsiveness
      setInteractions(prev => [...prev, interaction])

      const response = await fetch('/api/node/ai-visual-editor/user-experience', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'track-interaction',
          sessionId,
          interaction
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to track interaction')
      }

      return true
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      
      // Remove from local state if tracking failed
      setInteractions(prev => prev.filter(i => i !== interaction))
      throw error
    }
  }, [options])

  const startJourney = useCallback(async (
    userId: string,
    journeyType: string,
    context?: any
  ) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/user-experience', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'start-journey',
          userId,
          journeyType,
          context
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to start journey')
      }

      const data = await response.json()
      setCurrentJourney(data.journey)
      
      return data.journey
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const completeJourney = useCallback(async (
    journeyId: string,
    outcome: 'success' | 'partial' | 'failure' | 'abandoned',
    feedback?: string
  ) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/user-experience', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'complete-journey',
          journeyId,
          outcome,
          feedback
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to complete journey')
      }

      const data = await response.json()
      setCurrentJourney(null) // Clear current journey
      
      return data.result
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const personalizeExperience = useCallback(async (
    userId: string,
    context?: any
  ) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/node/ai-visual-editor/user-experience', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'personalize-experience',
          userId,
          context
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to personalize experience')
      }

      const data = await response.json()
      setPersonalization(data.personalization)
      
      return data.personalization
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const submitFeedback = useCallback(async (
    userId: string,
    feedback: string,
    context?: any
  ) => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/user-experience', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'submit-feedback',
          userId,
          feedback,
          context
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit feedback')
      }

      return true
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error occurred')
      setError(error)
      options.onError?.(error)
      throw error
    }
  }, [options])

  const getUserInsights = useCallback(async (userId: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/node/ai-visual-editor/user-experience?action=user-insights&userId=${encodeURIComponent(userId)}`)
      
      if (!response.ok) {
        throw new Error('Failed to get user insights')
      }

      const data = await response.json()
      setInsights(data.insights)
      options.onInsights?.(data.insights)
      
      return data.insights
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get user insights')
      setError(error)
      options.onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }, [options])

  const getJourneyAnalytics = useCallback(async (
    timeframe = '7d',
    journeyType?: string
  ) => {
    try {
      const params = new URLSearchParams({
        action: 'journey-analytics',
        timeframe
      })
      
      if (journeyType) {
        params.append('journeyType', journeyType)
      }

      const response = await fetch(`/api/node/ai-visual-editor/user-experience?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to get journey analytics')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get journey analytics')
      setError(error)
      throw error
    }
  }, [])

  const getInteractionPatterns = useCallback(async (
    userId?: string,
    timeframe = '30d'
  ) => {
    try {
      const params = new URLSearchParams({
        action: 'interaction-patterns',
        timeframe
      })
      
      if (userId) {
        params.append('userId', userId)
      }

      const response = await fetch(`/api/node/ai-visual-editor/user-experience?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to get interaction patterns')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to get interaction patterns')
      setError(error)
      throw error
    }
  }, [])

  const checkHealth = useCallback(async () => {
    try {
      const response = await fetch('/api/node/ai-visual-editor/user-experience?action=health')
      
      if (!response.ok) {
        throw new Error('Health check failed')
      }

      return await response.json()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Health check failed')
      setError(error)
      throw error
    }
  }, [])

  const reset = useCallback(() => {
    setCurrentJourney(null)
    setInsights(null)
    setPersonalization(null)
    setInteractions([])
    setError(null)
    setIsLoading(false)
  }, [])

  // Auto-track page views and interactions if enabled
  useEffect(() => {
    if (!options.autoTrack || !options.sessionId) return

    const handlePageView = () => {
      if (options.sessionId) {
        trackInteraction(options.sessionId, {
          action: 'page-view',
          timestamp: new Date(),
          context: { url: window.location.href },
          duration: 0,
          success: true
        })
      }
    }

    // Track initial page view
    handlePageView()

    // Track page visibility changes
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && options.sessionId) {
        trackInteraction(options.sessionId, {
          action: 'page-focus',
          timestamp: new Date(),
          context: { url: window.location.href },
          duration: 0,
          success: true
        })
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [options.autoTrack, options.sessionId, trackInteraction])

  return {
    // State
    isLoading,
    error,
    currentJourney,
    insights,
    personalization,
    interactions,
    
    // Actions
    trackInteraction,
    startJourney,
    completeJourney,
    personalizeExperience,
    submitFeedback,
    getUserInsights,
    getJourneyAnalytics,
    getInteractionPatterns,
    checkHealth,
    reset,
    
    // Computed
    hasCurrentJourney: !!currentJourney,
    hasInsights: !!insights,
    hasPersonalization: !!personalization,
    hasError: !!error,
    interactionCount: interactions.length
  }
}
