'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { <PERSON><PERSON><PERSON>t, AlertTriangle, Loader2 } from 'lucide-react'
import { toast } from 'sonner'
import { OrderList } from './order-list'
import { OrderDetailsView } from './order-details-view'
import { OrderManagementForm } from './order-management-form'
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import type { Order } from '@/lib/ecommerce/types/order'

type ViewMode = 'list' | 'details' | 'create' | 'edit'

interface OrderManagementPageProps {
  initialOrders?: Order[]
}

export function OrderManagementPage({ initialOrders = [] }: OrderManagementPageProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [orders, setOrders] = useState<Order[]>(initialOrders)

  const { 
    orders: fetchedOrders, 
    loading, 
    error, 
    refetch 
  } = useOrders()

  // Update orders when fetched data changes
  useEffect(() => {
    if (fetchedOrders && fetchedOrders.length > 0) {
      setOrders(fetchedOrders)
    }
  }, [fetchedOrders])

  // Use initial orders if no fetched orders yet
  const displayOrders = fetchedOrders && fetchedOrders.length > 0 ? fetchedOrders : orders

  const handleCreateOrder = () => {
    setSelectedOrder(null)
    setViewMode('create')
  }

  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order)
    setViewMode('details')
  }

  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order)
    setViewMode('edit')
  }

  const handleBackToList = () => {
    setSelectedOrder(null)
    setViewMode('list')
  }

  const handleOrderSuccess = (order: Order) => {
    // Update the orders list with the new/updated order
    setOrders(prevOrders => {
      const existingIndex = prevOrders.findIndex(o => o.id === order.id)
      if (existingIndex >= 0) {
        // Update existing order
        const newOrders = [...prevOrders]
        newOrders[existingIndex] = order
        return newOrders
      } else {
        // Add new order
        return [order, ...prevOrders]
      }
    })

    // Go back to list view
    handleBackToList()
    
    // Refresh data from server
    if (refetch) {
      refetch()
    }
  }

  const handleStatusUpdate = (updatedOrder: Order) => {
    // Update the order in the list
    setOrders(prevOrders => 
      prevOrders.map(order => 
        order.id === updatedOrder.id ? updatedOrder : order
      )
    )
    
    // Update selected order if it's the same one
    if (selectedOrder?.id === updatedOrder.id) {
      setSelectedOrder(updatedOrder)
    }

    toast.success(`Order #${updatedOrder.orderNumber} status updated successfully!`)
    
    // Refresh data from server
    if (refetch) {
      refetch()
    }
  }

  const handleRefresh = () => {
    if (refetch) {
      refetch()
      toast.success('Orders refreshed successfully!')
    }
  }

  // Show loading state on initial load
  if (loading && displayOrders.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="text-muted-foreground">Loading orders...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error && displayOrders.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Management</h1>
            <p className="text-muted-foreground">Manage and process customer orders</p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error Loading Orders</AlertTitle>
          <AlertDescription>
            {error}
            <div className="mt-4">
              <Button variant="outline" onClick={handleRefresh}>
                <Loader2 className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Try Again
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Render based on current view mode
  switch (viewMode) {
    case 'create':
      return (
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={handleBackToList}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Orders
            </Button>
          </div>
          
          <OrderManagementForm
            onSuccess={handleOrderSuccess}
            onCancel={handleBackToList}
          />
        </div>
      )

    case 'edit':
      return (
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={handleBackToList}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Orders
            </Button>
          </div>
          
          {selectedOrder && (
            <OrderManagementForm
              order={selectedOrder}
              onSuccess={handleOrderSuccess}
              onCancel={handleBackToList}
            />
          )}
        </div>
      )

    case 'details':
      return (
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={handleBackToList}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Orders
            </Button>
          </div>
          
          {selectedOrder && (
            <OrderDetailsView
              order={selectedOrder}
              onEdit={() => handleEditOrder(selectedOrder)}
              onRefresh={handleRefresh}
              onStatusUpdate={handleStatusUpdate}
            />
          )}
        </div>
      )

    case 'list':
    default:
      return (
        <OrderList
          orders={displayOrders}
          loading={loading}
          onCreateOrder={handleCreateOrder}
          onViewOrder={handleViewOrder}
          onEditOrder={handleEditOrder}
          onRefresh={handleRefresh}
        />
      )
  }
}

// Export individual components for use elsewhere
export { OrderList, OrderDetailsView, OrderManagementForm, OrderStatusManager } from './index'
