'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  ShoppingCart, 
  User, 
  MapPin, 
  Package, 
  CreditCard, 
  Truck, 
  Calendar,
  Edit,
  RefreshCw,
  Download,
  Mail,
  Phone,
  ExternalLink
} from 'lucide-react'
import type { Order } from '@/lib/ecommerce/types/order'
import { OrderStatusManager } from './order-status-manager'

interface OrderDetailsViewProps {
  order: Order
  onEdit?: () => void
  onRefresh?: () => void
  onStatusUpdate?: (order: Order) => void
}

// Status configuration with colors
const statusConfig = {
  pending: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', label: 'Pending' },
  confirmed: { color: 'bg-blue-100 text-blue-800 border-blue-200', label: 'Confirmed' },
  processing: { color: 'bg-purple-100 text-purple-800 border-purple-200', label: 'Processing' },
  shipped: { color: 'bg-indigo-100 text-indigo-800 border-indigo-200', label: 'Shipped' },
  delivered: { color: 'bg-green-100 text-green-800 border-green-200', label: 'Delivered' },
  cancelled: { color: 'bg-red-100 text-red-800 border-red-200', label: 'Cancelled' },
  refunded: { color: 'bg-orange-100 text-orange-800 border-orange-200', label: 'Refunded' },
  returned: { color: 'bg-gray-100 text-gray-800 border-gray-200', label: 'Returned' },
}

const financialStatusConfig = {
  pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Pending' },
  authorized: { color: 'bg-blue-100 text-blue-800', label: 'Authorized' },
  partially_paid: { color: 'bg-orange-100 text-orange-800', label: 'Partially Paid' },
  paid: { color: 'bg-green-100 text-green-800', label: 'Paid' },
  partially_refunded: { color: 'bg-yellow-100 text-yellow-800', label: 'Partially Refunded' },
  refunded: { color: 'bg-red-100 text-red-800', label: 'Refunded' },
  voided: { color: 'bg-gray-100 text-gray-800', label: 'Voided' },
}

const fulfillmentStatusConfig = {
  unfulfilled: { color: 'bg-yellow-100 text-yellow-800', label: 'Unfulfilled' },
  partially_fulfilled: { color: 'bg-orange-100 text-orange-800', label: 'Partially Fulfilled' },
  fulfilled: { color: 'bg-blue-100 text-blue-800', label: 'Fulfilled' },
  shipped: { color: 'bg-indigo-100 text-indigo-800', label: 'Shipped' },
  delivered: { color: 'bg-green-100 text-green-800', label: 'Delivered' },
  returned: { color: 'bg-gray-100 text-gray-800', label: 'Returned' },
  cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled' },
}

export function OrderDetailsView({ order, onEdit, onRefresh, onStatusUpdate }: OrderDetailsViewProps) {
  const [showStatusManager, setShowStatusManager] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'N/A'
    return new Date(date).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount: number | undefined, currency = 'ZAR') => {
    if (amount === undefined || amount === null) return 'R 0.00'
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount)
  }

  if (showStatusManager) {
    return (
      <OrderStatusManager
        order={order}
        onStatusUpdate={(updatedOrder) => {
          setShowStatusManager(false)
          if (onStatusUpdate) {
            onStatusUpdate(updatedOrder)
          }
        }}
        onClose={() => setShowStatusManager(false)}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Order #{order.orderNumber}
          </h1>
          <p className="text-muted-foreground">
            Created {formatDate(order.createdAt)}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {onRefresh && (
            <Button variant="outline" onClick={onRefresh}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          )}
          <Button variant="outline" onClick={() => setShowStatusManager(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Update Status
          </Button>
          {onEdit && (
            <Button variant="outline" onClick={onEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Order
            </Button>
          )}
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ShoppingCart className="mr-2 h-5 w-5" />
            Order Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Order Status</p>
              <Badge className={statusConfig[order.status]?.color}>
                {statusConfig[order.status]?.label}
              </Badge>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Payment Status</p>
              <Badge className={financialStatusConfig[order.financialStatus]?.color}>
                {financialStatusConfig[order.financialStatus]?.label}
              </Badge>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">Fulfillment Status</p>
              <Badge className={fulfillmentStatusConfig[order.fulfillmentStatus]?.color}>
                {fulfillmentStatusConfig[order.fulfillmentStatus]?.label}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Details Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="customer">Customer</TabsTrigger>
          <TabsTrigger value="items">Items</TabsTrigger>
          <TabsTrigger value="shipping">Shipping</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(order.subtotal?.amount)}</span>
                </div>
                {order.totalDiscount && order.totalDiscount.amount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount:</span>
                    <span>-{formatCurrency(order.totalDiscount.amount)}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Shipping:</span>
                  <span>{formatCurrency(order.totalShipping?.amount)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax:</span>
                  <span>{formatCurrency(order.totalTax?.amount)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total:</span>
                  <span>{formatCurrency(order.total?.amount)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" />
                  Payment Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Payment Method</p>
                  <p>{order.paymentMethod || 'Not specified'}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Financial Status</p>
                  <Badge className={financialStatusConfig[order.financialStatus]?.color}>
                    {financialStatusConfig[order.financialStatus]?.label}
                  </Badge>
                </div>
                {order.confirmedAt && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">Confirmed At</p>
                    <p>{formatDate(order.confirmedAt)}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="customer" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Name</p>
                  <p>{order.customer?.firstName} {order.customer?.lastName}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Email</p>
                  <div className="flex items-center space-x-2">
                    <p>{order.customer?.email}</p>
                    <Button variant="ghost" size="sm">
                      <Mail className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                {order.customer?.phone && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">Phone</p>
                    <div className="flex items-center space-x-2">
                      <p>{order.customer.phone}</p>
                      <Button variant="ghost" size="sm">
                        <Phone className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Shipping Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="mr-2 h-5 w-5" />
                  Shipping Address
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-1">
                  <p>{order.shippingAddress?.firstName} {order.shippingAddress?.lastName}</p>
                  {order.shippingAddress?.company && <p>{order.shippingAddress.company}</p>}
                  <p>{order.shippingAddress?.address1}</p>
                  {order.shippingAddress?.address2 && <p>{order.shippingAddress.address2}</p>}
                  <p>
                    {order.shippingAddress?.city}, {order.shippingAddress?.province} {order.shippingAddress?.postalCode}
                  </p>
                  <p>{order.shippingAddress?.country}</p>
                  {order.shippingAddress?.phone && <p>Phone: {order.shippingAddress.phone}</p>}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Customer Note */}
          {order.customerNote && (
            <Card>
              <CardHeader>
                <CardTitle>Customer Note</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{order.customerNote}</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="items" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Order Items ({order.itemCount} items)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items?.map((item, index) => (
                  <div key={item.id || index} className="flex items-center space-x-4 p-4 border rounded-lg">
                    {item.productImage && (
                      <img
                        src={item.productImage}
                        alt={item.productTitle}
                        className="w-16 h-16 object-cover rounded-md"
                      />
                    )}
                    <div className="flex-1 space-y-1">
                      <h4 className="font-medium">{item.productTitle}</h4>
                      {item.variantTitle && (
                        <p className="text-sm text-muted-foreground">{item.variantTitle}</p>
                      )}
                      {item.sku && (
                        <p className="text-xs text-muted-foreground">SKU: {item.sku}</p>
                      )}
                    </div>
                    <div className="text-right space-y-1">
                      <p className="font-medium">Qty: {item.quantity}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatCurrency(item.unitPrice?.amount)} each
                      </p>
                      <p className="font-medium">
                        {formatCurrency(item.totalPrice?.amount)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="shipping" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Truck className="mr-2 h-5 w-5" />
                Shipping Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Shipping Method</p>
                  <p>{order.shippingMethod?.title || 'Not specified'}</p>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Shipping Cost</p>
                  <p>{formatCurrency(order.totalShipping?.amount)}</p>
                </div>
              </div>

              {order.shippingMethod?.trackingNumber && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Tracking Number</p>
                  <div className="flex items-center space-x-2">
                    <p className="font-mono">{order.shippingMethod.trackingNumber}</p>
                    {order.shippingMethod.trackingUrl && (
                      <Button variant="ghost" size="sm" asChild>
                        <a href={order.shippingMethod.trackingUrl} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </Button>
                    )}
                  </div>
                </div>
              )}

              {order.shippingMethod?.estimatedDelivery && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Estimated Delivery</p>
                  <p>{formatDate(order.shippingMethod.estimatedDelivery)}</p>
                </div>
              )}

              {order.shippedAt && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Shipped At</p>
                  <p>{formatDate(order.shippedAt)}</p>
                </div>
              )}

              {order.deliveredAt && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">Delivered At</p>
                  <p>{formatDate(order.deliveredAt)}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                Order Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="font-medium">Order Created</p>
                    <p className="text-sm text-muted-foreground">{formatDate(order.createdAt)}</p>
                  </div>
                </div>

                {order.confirmedAt && (
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Order Confirmed</p>
                      <p className="text-sm text-muted-foreground">{formatDate(order.confirmedAt)}</p>
                    </div>
                  </div>
                )}

                {order.processedAt && (
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Order Processed</p>
                      <p className="text-sm text-muted-foreground">{formatDate(order.processedAt)}</p>
                    </div>
                  </div>
                )}

                {order.shippedAt && (
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Order Shipped</p>
                      <p className="text-sm text-muted-foreground">{formatDate(order.shippedAt)}</p>
                    </div>
                  </div>
                )}

                {order.deliveredAt && (
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                    <div>
                      <p className="font-medium">Order Delivered</p>
                      <p className="text-sm text-muted-foreground">{formatDate(order.deliveredAt)}</p>
                    </div>
                  </div>
                )}

                {order.cancelledAt && (
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <div>
                      <p className="font-medium">Order Cancelled</p>
                      <p className="text-sm text-muted-foreground">{formatDate(order.cancelledAt)}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Internal Notes */}
          {order.internalNotes && order.internalNotes.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Internal Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {order.internalNotes.map((note, index) => (
                    <div key={index} className="p-3 bg-muted rounded-md">
                      <p className="text-sm">{note}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
