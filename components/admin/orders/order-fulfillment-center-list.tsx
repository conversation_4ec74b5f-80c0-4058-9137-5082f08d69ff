'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Filter, 
  Package, 
  Truck, 
  CheckCircle, 
  AlertTriangle, 
  Clock,
  RefreshCw,
  Printer,
  Loader2,
  Eye
} from 'lucide-react'
import { toast } from 'sonner'
import type { Order } from '@/lib/ecommerce/types/order'

interface OrderFulfillmentCenterProps {
  orders: Order[]
  loading?: boolean
  error?: string | null
  onRefresh?: () => void
  onViewOrder?: (order: Order) => void
}

// Status configuration
const statusConfig = {
  pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Pending' },
  confirmed: { color: 'bg-blue-100 text-blue-800', icon: CheckCircle, label: 'Confirmed' },
  processing: { color: 'bg-purple-100 text-purple-800', icon: Package, label: 'Processing' },
  shipped: { color: 'bg-indigo-100 text-indigo-800', icon: Truck, label: 'Shipped' },
  delivered: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Delivered' },
  cancelled: { color: 'bg-red-100 text-red-800', icon: AlertTriangle, label: 'Cancelled' },
}

export function OrderFulfillmentCenter({ 
  orders, 
  loading = false, 
  error = null,
  onRefresh, 
  onViewOrder 
}: OrderFulfillmentCenterProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [fulfillmentFilter, setFulfillmentFilter] = useState<string>('unfulfilled')
  const [activeTab, setActiveTab] = useState('pending')

  // Filter orders based on search and filters
  const filteredOrders = useMemo(() => {
    let filtered = orders

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(order => 
        order.orderNumber?.toLowerCase().includes(searchLower) ||
        order.customer?.email?.toLowerCase().includes(searchLower) ||
        order.customer?.firstName?.toLowerCase().includes(searchLower) ||
        order.customer?.lastName?.toLowerCase().includes(searchLower)
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Fulfillment filter
    if (fulfillmentFilter !== 'all') {
      filtered = filtered.filter(order => order.fulfillmentStatus === fulfillmentFilter)
    }

    // Tab filter
    switch (activeTab) {
      case 'pending':
        filtered = filtered.filter(order => 
          order.status === 'confirmed' && order.fulfillmentStatus === 'unfulfilled'
        )
        break
      case 'processing':
        filtered = filtered.filter(order => 
          order.status === 'processing' && order.fulfillmentStatus && ['unfulfilled', 'partially_fulfilled'].includes(order.fulfillmentStatus)
        )
        break
      case 'ready':
        filtered = filtered.filter(order => 
          order.status === 'processing' && order.fulfillmentStatus === 'fulfilled'
        )
        break
      case 'shipped':
        filtered = filtered.filter(order => 
          ['shipped', 'delivered'].includes(order.status)
        )
        break
    }

    return filtered.sort((a, b) => 
      new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    )
  }, [orders, searchTerm, statusFilter, fulfillmentFilter, activeTab])

  // Calculate metrics
  const metrics = useMemo(() => {
    const pendingFulfillment = orders.filter(o => 
      o.status === 'confirmed' && o.fulfillmentStatus === 'unfulfilled'
    ).length
    
    const processing = orders.filter(o => 
      o.status === 'processing' && o.fulfillmentStatus && ['unfulfilled', 'partially_fulfilled'].includes(o.fulfillmentStatus)
    ).length
    
    const readyToShip = orders.filter(o => 
      o.status === 'processing' && o.fulfillmentStatus === 'fulfilled'
    ).length
    
    const shipped = orders.filter(o => 
      ['shipped', 'delivered'].includes(o.status)
    ).length

    return {
      pendingFulfillment,
      processing,
      readyToShip,
      shipped
    }
  }, [orders])

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'N/A'
    return new Date(date).toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatCurrency = (amount: number | undefined, currency = 'ZAR') => {
    if (amount === undefined || amount === null) return 'R 0.00'
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount)
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <p>{error}</p>
          </div>
          {onRefresh && (
            <Button onClick={onRefresh} className="mt-4">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Fulfillment Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className={activeTab === 'pending' ? 'border-blue-200 bg-blue-50' : ''}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Fulfillment</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.pendingFulfillment}</div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="mt-2 p-0 h-auto text-xs text-muted-foreground"
              onClick={() => setActiveTab('pending')}
            >
              View Orders
            </Button>
          </CardContent>
        </Card>
        
        <Card className={activeTab === 'processing' ? 'border-blue-200 bg-blue-50' : ''}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing</CardTitle>
            <Package className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.processing}</div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="mt-2 p-0 h-auto text-xs text-muted-foreground"
              onClick={() => setActiveTab('processing')}
            >
              View Orders
            </Button>
          </CardContent>
        </Card>
        
        <Card className={activeTab === 'ready' ? 'border-blue-200 bg-blue-50' : ''}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ready to Ship</CardTitle>
            <Truck className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.readyToShip}</div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="mt-2 p-0 h-auto text-xs text-muted-foreground"
              onClick={() => setActiveTab('ready')}
            >
              View Orders
            </Button>
          </CardContent>
        </Card>
        
        <Card className={activeTab === 'shipped' ? 'border-blue-200 bg-blue-50' : ''}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Shipped</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.shipped}</div>
            <Button 
              variant="ghost" 
              size="sm" 
              className="mt-2 p-0 h-auto text-xs text-muted-foreground"
              onClick={() => setActiveTab('shipped')}
            >
              View Orders
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search orders..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Order Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Fulfillment Status</label>
              <Select value={fulfillmentFilter} onValueChange={setFulfillmentFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Fulfillment Statuses</SelectItem>
                  <SelectItem value="unfulfilled">Unfulfilled</SelectItem>
                  <SelectItem value="partially_fulfilled">Partially Fulfilled</SelectItem>
                  <SelectItem value="fulfilled">Fulfilled</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Fulfillment Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="processing">Processing</TabsTrigger>
          <TabsTrigger value="ready">Ready to Ship</TabsTrigger>
          <TabsTrigger value="shipped">Shipped</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>
                {activeTab === 'pending' && 'Pending Fulfillment'}
                {activeTab === 'processing' && 'Processing Orders'}
                {activeTab === 'ready' && 'Ready to Ship'}
                {activeTab === 'shipped' && 'Shipped Orders'}
              </CardTitle>
              <CardDescription>
                {activeTab === 'pending' && 'Orders that need to be processed'}
                {activeTab === 'processing' && 'Orders currently being processed'}
                {activeTab === 'ready' && 'Orders ready for shipping'}
                {activeTab === 'shipped' && 'Orders that have been shipped'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : filteredOrders.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="mx-auto h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-2 text-sm font-medium">No orders found</h3>
                  <p className="mt-1 text-sm">
                    There are no orders matching your current filters.
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Order #</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Items</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Total</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredOrders.map((order) => {
                        const StatusIcon = statusConfig[order.status]?.icon || Clock;
                        return (
                          <TableRow key={order.id}>
                            <TableCell className="font-medium">{order.orderNumber}</TableCell>
                            <TableCell>{formatDate(order.createdAt)}</TableCell>
                            <TableCell>
                              {order.customer?.firstName} {order.customer?.lastName}
                              <div className="text-xs text-muted-foreground">{order.customer?.email}</div>
                            </TableCell>
                            <TableCell>{order.items?.length || 0}</TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Badge className={statusConfig[order.status]?.color}>
                                  <StatusIcon className="mr-1 h-3 w-3" />
                                  {statusConfig[order.status]?.label}
                                </Badge>
                              </div>
                            </TableCell>
                            <TableCell>{formatCurrency(order.total?.amount)}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end space-x-2">
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => onViewOrder && onViewOrder(order)}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                >
                                  <Printer className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="default" 
                                  size="sm"
                                  onClick={() => onViewOrder && onViewOrder(order)}
                                >
                                  Process
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}