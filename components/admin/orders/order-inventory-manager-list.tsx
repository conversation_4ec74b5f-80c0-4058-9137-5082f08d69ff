'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Search, 
  Filter, 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  Minus, 
  Plus,
  RefreshCw,
  Download,
  Upload,
  Loader2,
  TrendingDown,
  TrendingUp,
  BarChart3,
  DollarSign
} from 'lucide-react'
import { toast } from 'sonner'
import type { Order } from '@/lib/ecommerce/types/order'

interface InventoryItem {
  id: string
  productId: string
  variantId?: string
  sku: string
  productTitle: string
  variantTitle?: string
  locationId: string
  locationName: string
  quantityAvailable: number
  quantityReserved: number
  quantityOnHand: number
  reorderPoint: number
  maxStock: number
  lastUpdated: Date
  binLocation?: string
  price: number
}

interface OrderInventoryManagerListProps {
  orders: Order[]
  inventory: InventoryItem[]
  loading?: boolean
  error?: string | null
  onRefresh?: () => void
  onViewOrder?: (order: Order) => void
}

export function OrderInventoryManager({ 
  orders, 
  inventory = [], 
  loading = false, 
  error = null,
  onRefresh, 
  onViewOrder 
}: OrderInventoryManagerListProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [locationFilter, setLocationFilter] = useState<string>('all')
  const [stockFilter, setStockFilter] = useState<string>('all')
  const [activeTab, setActiveTab] = useState('inventory')

  // Filter inventory items
  const filteredInventory = useMemo(() => {
    let filtered = inventory

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(item => 
        item.sku.toLowerCase().includes(searchLower) ||
        item.productTitle.toLowerCase().includes(searchLower) ||
        item.variantTitle?.toLowerCase().includes(searchLower)
      )
    }

    // Location filter
    if (locationFilter !== 'all') {
      filtered = filtered.filter(item => item.locationId === locationFilter)
    }

    // Stock level filter
    if (stockFilter !== 'all') {
      switch (stockFilter) {
        case 'low':
          filtered = filtered.filter(item => 
            item.quantityAvailable <= item.reorderPoint && item.quantityAvailable > 0
          )
          break
        case 'out':
          filtered = filtered.filter(item => item.quantityAvailable === 0)
          break
        case 'overstock':
          filtered = filtered.filter(item => item.quantityOnHand > item.maxStock)
          break
      }
    }

    return filtered
  }, [inventory, searchTerm, locationFilter, stockFilter])

  // Calculate inventory metrics
  const inventoryMetrics = useMemo(() => {
    const totalItems = inventory.length
    const lowStockItems = inventory.filter(item => 
      item.quantityAvailable <= item.reorderPoint && item.quantityAvailable > 0
    ).length
    const outOfStockItems = inventory.filter(item => 
      item.quantityAvailable === 0
    ).length
    const overStockItems = inventory.filter(item => 
      item.quantityOnHand > item.maxStock
    ).length
    
    const totalValue = inventory.reduce((sum, item) => 
      sum + (item.quantityOnHand * item.price), 0
    )
    
    const reservedQuantity = inventory.reduce((sum, item) => 
      sum + item.quantityReserved, 0
    )

    return {
      totalItems,
      lowStockItems,
      outOfStockItems,
      overStockItems,
      totalValue,
      reservedQuantity,
    }
  }, [inventory])

  // Get stock status for an item
  const getStockStatus = (item: InventoryItem) => {
    if (item.quantityAvailable === 0) {
      return { status: 'out', label: 'Out of Stock', color: 'bg-red-100 text-red-800' }
    } else if (item.quantityAvailable <= item.reorderPoint) {
      return { status: 'low', label: 'Low Stock', color: 'bg-yellow-100 text-yellow-800' }
    } else if (item.quantityOnHand > item.maxStock) {
      return { status: 'over', label: 'Overstock', color: 'bg-purple-100 text-purple-800' }
    } else {
      return { status: 'good', label: 'In Stock', color: 'bg-green-100 text-green-800' }
    }
  }

  // Calculate stock level percentage
  const getStockPercentage = (item: InventoryItem) => {
    return Math.min((item.quantityAvailable / item.maxStock) * 100, 100)
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0
    }).format(amount)
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <p>{error}</p>
          </div>
          {onRefresh && (
            <Button onClick={onRefresh} className="mt-4">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Inventory Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inventoryMetrics.totalItems}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
            <TrendingDown className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{inventoryMetrics.lowStockItems}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{inventoryMetrics.outOfStockItems}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overstock</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{inventoryMetrics.overStockItems}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reserved</CardTitle>
            <Package className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{inventoryMetrics.reservedQuantity}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(inventoryMetrics.totalValue)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Header Actions */}
      <div className="flex justify-end space-x-2">
        <Button variant="outline">
          <Upload className="mr-2 h-4 w-4" />
          Import
        </Button>
        <Button variant="outline">
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button variant="outline" onClick={onRefresh}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search inventory..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Location</label>
              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  <SelectItem value="warehouse-1">Main Warehouse</SelectItem>
                  <SelectItem value="warehouse-2">Johannesburg Hub</SelectItem>
                  <SelectItem value="store-1">V&A Waterfront Store</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Stock Level</label>
              <Select value={stockFilter} onValueChange={setStockFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Stock Levels</SelectItem>
                  <SelectItem value="low">Low Stock</SelectItem>
                  <SelectItem value="out">Out of Stock</SelectItem>
                  <SelectItem value="overstock">Overstock</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="reservations">Order Reservations</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Items</CardTitle>
              <CardDescription>
                Current stock levels across all locations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : filteredInventory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="mx-auto h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-2 text-sm font-medium">No inventory items found</h3>
                  <p className="mt-1 text-sm">
                    There are no inventory items matching your current filters.
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>SKU</TableHead>
                        <TableHead>Product</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Available</TableHead>
                        <TableHead>Reserved</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Stock Level</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredInventory.map((item) => {
                        const stockStatus = getStockStatus(item)
                        return (
                          <TableRow key={item.id}>
                            <TableCell className="font-medium">{item.sku}</TableCell>
                            <TableCell>
                              {item.productTitle}
                              {item.variantTitle && (
                                <div className="text-xs text-muted-foreground">{item.variantTitle}</div>
                              )}
                            </TableCell>
                            <TableCell>
                              {item.locationName}
                              {item.binLocation && (
                                <div className="text-xs text-muted-foreground">Bin: {item.binLocation}</div>
                              )}
                            </TableCell>
                            <TableCell>{item.quantityAvailable}</TableCell>
                            <TableCell>{item.quantityReserved}</TableCell>
                            <TableCell>
                              <Badge className={stockStatus.color}>
                                {stockStatus.label}
                              </Badge>
                            </TableCell>
                            <TableCell className="w-[180px]">
                              <div className="flex items-center space-x-2">
                                <Progress value={getStockPercentage(item)} className="h-2" />
                                <span className="text-xs text-muted-foreground">
                                  {item.quantityOnHand}/{item.maxStock}
                                </span>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reservations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order Reservations</CardTitle>
              <CardDescription>
                Inventory reserved for pending orders
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : orders.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Package className="mx-auto h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-2 text-sm font-medium">No order reservations</h3>
                  <p className="mt-1 text-sm">
                    There are no active order reservations at this time.
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Order #</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Customer</TableHead>
                        <TableHead>Items</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {orders
                        .filter(order => ['pending', 'confirmed', 'processing'].includes(order.status))
                        .map((order) => (
                          <TableRow key={order.id}>
                            <TableCell className="font-medium">{order.orderNumber}</TableCell>
                            <TableCell>{new Date(order.createdAt).toLocaleDateString()}</TableCell>
                            <TableCell>
                              {order.customer?.firstName} {order.customer?.lastName}
                              <div className="text-xs text-muted-foreground">{order.customer?.email}</div>
                            </TableCell>
                            <TableCell>{order.itemCount}</TableCell>
                            <TableCell>
                              <Badge variant={order.status === 'pending' ? 'outline' : 'default'}>
                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => onViewOrder && onViewOrder(order)}
                              >
                                View Order
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Analytics</CardTitle>
              <CardDescription>
                Insights and trends for inventory management
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-64 text-muted-foreground">
                <div className="text-center">
                  <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-2 text-sm font-medium">Analytics Dashboard</h3>
                  <p className="mt-1 text-sm">
                    Detailed inventory analytics will be available soon.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}