'use client'

import React, { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Upload,
  Search,
  Grid3X3,
  List,
  Check,
  Eye,
  Trash2,
  Image,
  Video,
  Music,
  FileText,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'

import { MediaFile } from '@/hooks/use-media'
import { formatFileSize } from '@/lib/utils'

interface MediaLibraryModalProps {
  isOpen: boolean
  onClose: () => void
  onSelect: (file: MediaFile | MediaFile[]) => void
  multiple?: boolean
  accept?: string[]
  title?: string
  file?: MediaFile
  onDownload?: (file: MediaFile) => void
  onDelete?: (file: MediaFile) => void
  onEdit?: (file: MediaFile) => void
}

export function MediaLibraryModal({
  isOpen,
  onClose,
  onSelect,
  multiple = false,
  accept = [],
  title = 'Media Library',
  file,
  onDownload,
  onDelete,
  onEdit
}: MediaLibraryModalProps) {
  const [files, setFiles] = useState<MediaFile[]>([])
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([])
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [filters, setFilters] = useState<{ type: string }>({ type: 'all' })
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalFiles, setTotalFiles] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const dropZoneRef = useRef<HTMLDivElement>(null)
  const filesPerPage = 24

  // Load files on mount and filter changes
  useEffect(() => {
    if (isOpen) {
      loadFiles()
    }
  }, [isOpen, filters, searchQuery, currentPage])

  const loadFiles = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: filesPerPage.toString(),
        ...(searchQuery && { search: searchQuery }),
        ...(filters.type !== 'all' && { type: filters.type })
      })

      const response = await fetch(`/api/admin/media?${params}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch files')
      }
      
      setFiles(result.data.files)
      setTotalFiles(result.data.pagination.total)
    } catch (err) {
      setError('Failed to load media files')
      console.error('Error loading files:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (uploadFiles: File[]) => {
    setUploading(true)
    setError(null)

    try {
      const formData = new FormData()
      uploadFiles.forEach(file => formData.append('files', file))

      const response = await fetch('/api/admin/media', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to upload files')
      }

      const uploadedFiles = result.data.files.filter((file: any) => !file.error)
      
      // Refresh file list
      await loadFiles()
      
      // Auto-select uploaded files if in selection mode
      if (multiple) {
        setSelectedFiles(prev => [...prev, ...uploadedFiles])
      } else if (uploadedFiles.length > 0) {
        setSelectedFiles([uploadedFiles[0]])
      }
    } catch (err) {
      setError('Failed to upload files')
      console.error('Error uploading files:', err)
    } finally {
      setUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    
    const droppedFiles = Array.from(e.dataTransfer.files)
    if (droppedFiles.length > 0) {
      handleFileUpload(droppedFiles)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    if (!dropZoneRef.current?.contains(e.relatedTarget as Node)) {
      setIsDragging(false)
    }
  }

  const handleFileSelect = (file: MediaFile) => {
    if (multiple) {
      setSelectedFiles(prev => {
        const isSelected = prev.some(f => f.id === file.id)
        if (isSelected) {
          return prev.filter(f => f.id !== file.id)
        } else {
          return [...prev, file]
        }
      })
    } else {
      setSelectedFiles([file])
    }
  }

  const handleDeleteFile = async (fileId: string) => {
    try {
      const response = await fetch(`/api/admin/media/${fileId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete file')
      }

      await loadFiles()
      setSelectedFiles(prev => prev.filter(f => f.id !== fileId))
    } catch (err) {
      setError('Failed to delete file')
      console.error('Error deleting file:', err)
    }
  }

  const handleConfirmSelection = () => {
    if (selectedFiles.length > 0) {
      onSelect(multiple ? selectedFiles : selectedFiles[0])
      onClose()
    }
  }

  const isFileSelected = (file: MediaFile) => {
    return selectedFiles.some(f => f.id === file.id)
  }

  const getFileTypeFilter = (type: string) => {
    setFilters(prev => ({ ...prev, type: type as any }))
    setCurrentPage(1)
  }

  const totalPages = Math.ceil(totalFiles / filesPerPage)

  // Utility functions
  const getFileTypeIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return '🖼️'
    if (mimeType.startsWith('video/')) return '🎥'
    if (mimeType.startsWith('audio/')) return '🎵'
    if (mimeType.startsWith('application/') || mimeType.startsWith('text/')) return '📄'
    return '📁'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Image className="h-5 w-5" />
            {title}
            {selectedFiles.length > 0 && (
              <Badge variant="secondary">
                {selectedFiles.length} selected
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Toolbar */}
          <div className="border-b p-4 space-y-4">
            {/* Search and filters */}
            <div className="flex items-center gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search media files..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Type filters */}
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm text-muted-foreground">Filter by type:</span>
              {[
                { key: 'all', label: 'All Files', icon: FileText },
                { key: 'image', label: 'Images', icon: Image },
                { key: 'video', label: 'Videos', icon: Video },
                { key: 'audio', label: 'Audio', icon: Music },
                { key: 'document', label: 'Documents', icon: FileText }
              ].map(({ key, label, icon: Icon }) => (
                <Button
                  key={key}
                  variant={filters.type === key ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => getFileTypeFilter(key)}
                  className="text-xs"
                >
                  <Icon className="h-3 w-3 mr-1" />
                  {label}
                </Button>
              ))}
            </div>
          </div>

          {/* Content */}
          <Tabs defaultValue="library" className="flex-1 flex flex-col">
            <TabsList className="mx-4 mt-4">
              <TabsTrigger value="library">Media Library</TabsTrigger>
              <TabsTrigger value="upload">Upload Files</TabsTrigger>
            </TabsList>

            <TabsContent value="library" className="flex-1 flex flex-col mt-4">
              {/* Error display */}
              {error && (
                <Alert variant="destructive" className="mx-4 mb-4">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Files grid/list */}
              <div className="flex-1 overflow-auto px-4">
                {loading ? (
                  <div className="flex items-center justify-center h-64">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : files.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 text-center">
                    <Image className="h-16 w-16 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-2">No media files found</h3>
                    <p className="text-muted-foreground mb-4">
                      Upload some files to get started
                    </p>
                    <Button onClick={() => fileInputRef.current?.click()}>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Files
                    </Button>
                  </div>
                ) : (
                  <div className={cn(
                    viewMode === 'grid' 
                      ? 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4'
                      : 'space-y-2'
                  )}>
                    {files.map((file) => (
                      <MediaFileItem
                        key={file.id}
                        file={file}
                        viewMode={viewMode}
                        isSelected={isFileSelected(file)}
                        onSelect={() => handleFileSelect(file)}
                        onDelete={() => handleDeleteFile(file.id)}
                        getFileTypeIcon={getFileTypeIcon}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="border-t p-4 flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * filesPerPage) + 1} to {Math.min(currentPage * filesPerPage, totalFiles)} of {totalFiles} files
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <span className="text-sm">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="upload" className="flex-1 flex flex-col mt-4">
              <div className="flex-1 p-4">
                {/* Upload area */}
                <div
                  ref={dropZoneRef}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  className={cn(
                    'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
                    isDragging ? 'border-primary bg-primary/5' : 'border-muted-foreground/25',
                    uploading && 'opacity-50 pointer-events-none'
                  )}
                >
                  {uploading ? (
                    <div className="space-y-4">
                      <Loader2 className="h-12 w-12 animate-spin mx-auto text-primary" />
                      <p className="text-lg font-medium">Uploading files...</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Upload className={cn(
                        'h-12 w-12 mx-auto',
                        isDragging ? 'text-primary' : 'text-muted-foreground'
                      )} />
                      <div>
                        <p className="text-lg font-medium mb-2">
                          {isDragging ? 'Drop files here' : 'Drag & drop files here'}
                        </p>
                        <p className="text-muted-foreground mb-4">
                          or click to browse files
                        </p>
                        <Button
                          onClick={() => fileInputRef.current?.click()}
                          disabled={uploading}
                        >
                          Choose Files
                        </Button>
                      </div>
                    </div>
                  )}
                </div>

                <Input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept={accept.join(',')}
                  onChange={(e) => {
                    const files = Array.from(e.target.files || [])
                    if (files.length > 0) {
                      handleFileUpload(files)
                    }
                  }}
                  className="hidden"
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Footer */}
        <div className="border-t p-4 flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            {selectedFiles.length > 0 && (
              <span>
                {selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''} selected
              </span>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmSelection}
              disabled={selectedFiles.length === 0}
            >
              {multiple ? `Select ${selectedFiles.length} Files` : 'Select File'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

interface MediaFileItemProps {
  file: MediaFile
  viewMode: 'grid' | 'list'
  isSelected: boolean
  onSelect: () => void
  onDelete: () => void
  getFileTypeIcon: (mimeType: string) => string
}

function MediaFileItem({ file, viewMode, isSelected, onSelect, onDelete, getFileTypeIcon }: MediaFileItemProps) {
  const isImage = file.mimeType.startsWith('image/')
  
  if (viewMode === 'grid') {
    return (
      <div
        className={cn(
          'relative group border rounded-lg overflow-hidden cursor-pointer transition-all hover:shadow-md',
          isSelected && 'ring-2 ring-primary'
        )}
        onClick={onSelect}
      >
        {/* File preview */}
        <div className="aspect-square bg-muted flex items-center justify-center">
          {isImage && file.previewUrl ? (
            <img
              src={file.previewUrl}
              alt={file.metadata?.alt || file.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="text-4xl">
              {getFileTypeIcon(file.mimeType)}
            </div>
          )}
        </div>

        {/* Selection indicator */}
        {isSelected && (
          <div className="absolute top-2 right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
            <Check className="h-4 w-4 text-primary-foreground" />
          </div>
        )}

        {/* File info */}
        <div className="p-2">
          <p className="text-xs font-medium truncate" title={file.name}>
            {file.name}
          </p>
          <p className="text-xs text-muted-foreground">
            {formatFileSize(file.size)}
          </p>
        </div>

        {/* Actions overlay */}
        <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
          <Button size="sm" variant="secondary" onClick={(e) => {
            e.stopPropagation()
            window.open(file.url, '_blank')
          }}>
            <Eye className="h-3 w-3" />
          </Button>
          <Button size="sm" variant="secondary" onClick={(e) => {
            e.stopPropagation()
            onDelete()
          }}>
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div
      className={cn(
        'flex items-center gap-4 p-3 border rounded-lg cursor-pointer transition-all hover:bg-muted/50',
        isSelected && 'bg-primary/5 border-primary'
      )}
      onClick={onSelect}
    >
      {/* File thumbnail */}
      <div className="w-12 h-12 bg-muted rounded flex items-center justify-center flex-shrink-0">
        {isImage && file.previewUrl ? (
          <img
            src={file.previewUrl}
            alt={file.metadata?.alt || file.name}
            className="w-full h-full object-cover rounded"
          />
        ) : (
          <span className="text-xl">
            {getFileTypeIcon(file.mimeType)}
          </span>
        )}
      </div>

      {/* File info */}
      <div className="flex-1 min-w-0">
        <p className="font-medium truncate">{file.name}</p>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>{formatFileSize(file.size)}</span>
          <span>{new Date(file.createdAt).toLocaleDateString()}</span>
          {file.metadata?.width && file.metadata?.height && (
            <span>{file.metadata.width} × {file.metadata.height}</span>
          )}
        </div>
      </div>

      {/* Selection indicator */}
      <div className={cn(
        'w-5 h-5 rounded border-2 flex items-center justify-center',
        isSelected ? 'bg-primary border-primary' : 'border-muted-foreground'
      )}>
        {isSelected && <Check className="h-3 w-3 text-primary-foreground" />}
      </div>

      {/* Actions */}
      <div className="flex items-center gap-1">
        <Button size="sm" variant="ghost" onClick={(e) => {
          e.stopPropagation()
          window.open(file.url, '_blank')
        }}>
          <Eye className="h-3 w-3" />
        </Button>
        <Button size="sm" variant="ghost" onClick={(e) => {
          e.stopPropagation()
          onDelete()
        }}>
          <Trash2 className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}
