'use client'

import { useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Warehouse,
  Plus,
  TrendingUp,
  TrendingDown,
  Package,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Download,
  Upload,
  RefreshCw,
  Search,
  Filter,
  Eye,
  Edit,
  Truck,
  ShoppingCart,
  Target,
  Zap,
  Settings,
  Archive,
  Box,
  Layers,
  MapPin,
  Calendar,
  DollarSign,
  Activity,
  Sparkles
} from 'lucide-react'
import { useInventory } from '@/lib/ecommerce/hooks/use-inventory'
import { EnhancedInventoryList } from './enhanced-inventory-list'
import { InventoryAnalytics } from './inventory-analytics'
import { StockMovements } from './stock-movements'
import { BulkInventoryOperations } from './bulk-inventory-operations'
import { InventoryAlerts } from './inventory-alerts'
import { formatCurrency } from '@/lib/utils'
import type { InventoryItem } from '@/lib/ecommerce/types/inventory'

interface InventoryStats {
  totalItems: number
  totalValue: number
  lowStockItems: number
  outOfStockItems: number
  inTransit: number
  reserved: number
  averageStockLevel: number
  stockTurnover: number
  warehouseLocations: number
}

export function EnhancedInventoryDashboard() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('overview')

  // Mock stats - in real app, this would come from API
  const stats: InventoryStats = {
    totalItems: 1456,
    totalValue: 234567.89,
    lowStockItems: 23,
    outOfStockItems: 8,
    inTransit: 45,
    reserved: 67,
    averageStockLevel: 125,
    stockTurnover: 4.2,
    warehouseLocations: 3
  }

  const quickActions = [
    {
      label: 'Add Stock',
      href: '/admin/e-commerce/inventory/add-stock',
      icon: Plus,
      description: 'Add inventory items',
      variant: 'default' as const
    },
    {
      label: 'Stock Adjustment',
      href: '/admin/e-commerce/inventory/adjustment',
      icon: RefreshCw,
      description: 'Adjust stock levels',
      variant: 'secondary' as const
    },
    {
      label: 'Import Inventory',
      href: '/admin/e-commerce/inventory/import',
      icon: Upload,
      description: 'Bulk import inventory',
      variant: 'outline' as const
    },
    {
      label: 'Analytics',
      href: '/admin/e-commerce/inventory/analytics',
      icon: BarChart3,
      description: 'Inventory insights',
      variant: 'outline' as const
    }
  ]

  const statCards = [
    {
      title: 'Total Items',
      value: stats.totalItems,
      change: '+5%',
      trend: 'up' as const,
      icon: Package,
      description: 'Items in inventory'
    },
    {
      title: 'Total Value',
      value: formatCurrency(stats.totalValue, 'ZAR'),
      change: '+12%',
      trend: 'up' as const,
      icon: DollarSign,
      description: 'Total inventory value'
    },
    {
      title: 'Stock Turnover',
      value: `${stats.stockTurnover}x`,
      change: '+8%',
      trend: 'up' as const,
      icon: TrendingUp,
      description: 'Annual turnover rate'
    },
    {
      title: 'Avg Stock Level',
      value: stats.averageStockLevel,
      change: '+3%',
      trend: 'up' as const,
      icon: Archive,
      description: 'Average units per item'
    }
  ]

  const alertCards = [
    {
      title: 'Low Stock',
      value: stats.lowStockItems,
      icon: AlertTriangle,
      variant: 'warning' as const,
      action: 'Reorder Now',
      href: '/admin/e-commerce/inventory?filter=low-stock',
      description: 'Items below reorder point'
    },
    {
      title: 'Out of Stock',
      value: stats.outOfStockItems,
      icon: AlertTriangle,
      variant: 'destructive' as const,
      action: 'Urgent Restock',
      href: '/admin/e-commerce/inventory?filter=out-of-stock',
      description: 'Items completely out of stock'
    },
    {
      title: 'In Transit',
      value: stats.inTransit,
      icon: Truck,
      variant: 'secondary' as const,
      action: 'Track Shipments',
      href: '/admin/e-commerce/inventory?filter=in-transit',
      description: 'Items being shipped'
    },
    {
      title: 'Reserved',
      value: stats.reserved,
      icon: Clock,
      variant: 'default' as const,
      action: 'View Orders',
      href: '/admin/e-commerce/inventory?filter=reserved',
      description: 'Items reserved for orders'
    }
  ]

  const handleCreateItem = useCallback(() => {
    router.push('/admin/e-commerce/inventory/new')
  }, [router])

  const handleEditItem = useCallback((item: InventoryItem) => {
    router.push(`/admin/e-commerce/inventory/${item.id}/edit`)
  }, [router])

  const handleViewItem = useCallback((item: InventoryItem) => {
    router.push(`/admin/e-commerce/inventory/${item.id}`)
  }, [router])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Inventory Management</h1>
          <p className="text-muted-foreground">
            Track stock levels, manage warehouses, and optimize inventory
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <Button
                key={action.href}
                variant={action.variant}
                size="sm"
                onClick={() => router.push(action.href)}
              >
                <Icon className="mr-2 h-4 w-4" />
                {action.label}
              </Button>
            )
          })}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => {
          const Icon = stat.icon
          const TrendIcon = stat.trend === 'up' ? TrendingUp : TrendingDown
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <TrendIcon className={`mr-1 h-3 w-3 ${
                    stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
                  }`} />
                  {stat.change} from last month
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Alert Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        {alertCards.map((alert) => {
          const Icon = alert.icon
          return (
            <Card key={alert.title} className="border-l-4 border-l-orange-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Icon className="mr-2 h-4 w-4" />
                    {alert.title}
                  </CardTitle>
                  <Badge variant={alert.variant}>{alert.value}</Badge>
                </div>
                <CardDescription className="text-xs">
                  {alert.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => router.push(alert.href)}
                >
                  {alert.action}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Critical Alerts */}
      {(stats.outOfStockItems > 5 || stats.lowStockItems > 20) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {stats.outOfStockItems > 5 && `${stats.outOfStockItems} items are out of stock. `}
            {stats.lowStockItems > 20 && `${stats.lowStockItems} items are running low.`}
            Consider setting up automated reorder points.
          </AlertDescription>
        </Alert>
      )}

      {/* Warehouse Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Warehouse className="mr-2 h-5 w-5" />
            Warehouse Overview
          </CardTitle>
          <CardDescription>
            Multi-location inventory distribution
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Main Warehouse</span>
                <Badge variant="default">Primary</Badge>
              </div>
              <Progress value={75} className="h-2" />
              <p className="text-xs text-muted-foreground">75% capacity</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Distribution Center</span>
                <Badge variant="secondary">Secondary</Badge>
              </div>
              <Progress value={45} className="h-2" />
              <p className="text-xs text-muted-foreground">45% capacity</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Overflow Storage</span>
                <Badge variant="outline">Backup</Badge>
              </div>
              <Progress value={20} className="h-2" />
              <p className="text-xs text-muted-foreground">20% capacity</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Package className="h-4 w-4" />
            <span>Inventory</span>
          </TabsTrigger>
          <TabsTrigger value="movements" className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>Movements</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="alerts" className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4" />
            <span>Alerts</span>
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center space-x-2">
            <Zap className="h-4 w-4" />
            <span>Bulk Ops</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <EnhancedInventoryList
            onCreateItem={handleCreateItem}
            onEditItem={handleEditItem}
            onViewItem={handleViewItem}
          />
        </TabsContent>

        <TabsContent value="movements" className="space-y-4">
          <StockMovements />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <InventoryAnalytics />
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <InventoryAlerts />
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Operations</CardTitle>
              <CardDescription>
                Perform actions on multiple inventory items at once
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkInventoryOperations
                selectedItems={[]}
                items={[]}
                onOperationComplete={() => {}}
                onClearSelection={() => {}}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
