'use client'

import { forwardRef } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { AlertCircle, CheckCircle, Info } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FormFieldProps {
  label: string
  name: string
  type?: 'text' | 'email' | 'number' | 'textarea' | 'password'
  placeholder?: string
  description?: string
  error?: string
  required?: boolean
  disabled?: boolean
  className?: string
  rows?: number
  min?: number
  max?: number
  step?: number
  value?: string | number
  onChange?: (value: string | number) => void
  onBlur?: () => void
  characterCount?: {
    current: number
    max: number
  }
  icon?: React.ComponentType<{ className?: string }>
  success?: boolean
  warning?: string
}

export const FormField = forwardRef<
  HTMLInputElement | HTMLTextAreaElement,
  FormFieldProps
>(({
  label,
  name,
  type = 'text',
  placeholder,
  description,
  error,
  required = false,
  disabled = false,
  className,
  rows = 4,
  min,
  max,
  step,
  value,
  onChange,
  onBlur,
  characterCount,
  icon: Icon,
  success = false,
  warning,
  ...props
}, ref) => {
  const hasError = !!error
  const hasWarning = !!warning && !hasError
  const hasSuccess = success && !hasError && !hasWarning

  const inputClassName = cn(
    "transition-colors",
    hasError && "border-destructive focus:border-destructive",
    hasWarning && "border-orange-500 focus:border-orange-500",
    hasSuccess && "border-green-500 focus:border-green-500",
    Icon && "pl-10",
    className
  )

  const renderInput = () => {
    const commonProps = {
      id: name,
      name,
      placeholder,
      disabled,
      className: inputClassName,
      value,
      onChange: onChange ? (e: any) => onChange(e.target.value) : undefined,
      onBlur,
      ...props
    }

    if (type === 'textarea') {
      return (
        <Textarea
          {...commonProps}
          rows={rows}
          ref={ref as React.Ref<HTMLTextAreaElement>}
        />
      )
    }

    return (
      <Input
        {...commonProps}
        type={type}
        min={min}
        max={max}
        step={step}
        ref={ref as React.Ref<HTMLInputElement>}
      />
    )
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={name} className="flex items-center justify-between">
        <span className="flex items-center">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </span>
        {characterCount && (
          <span className={cn(
            "text-xs",
            characterCount.current > characterCount.max 
              ? "text-destructive" 
              : characterCount.current > characterCount.max * 0.8
                ? "text-orange-500"
                : "text-muted-foreground"
          )}>
            {characterCount.current}/{characterCount.max}
          </span>
        )}
      </Label>
      
      <div className="relative">
        {Icon && (
          <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        )}
        {renderInput()}
        
        {/* Status Icons */}
        {(hasError || hasWarning || hasSuccess) && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {hasError && <AlertCircle className="h-4 w-4 text-destructive" />}
            {hasWarning && <AlertCircle className="h-4 w-4 text-orange-500" />}
            {hasSuccess && <CheckCircle className="h-4 w-4 text-green-500" />}
          </div>
        )}
      </div>
      
      {/* Messages */}
      {error && (
        <p className="text-sm text-destructive flex items-center">
          <AlertCircle className="mr-1 h-3 w-3" />
          {error}
        </p>
      )}
      
      {warning && !error && (
        <p className="text-sm text-orange-600 flex items-center">
          <AlertCircle className="mr-1 h-3 w-3" />
          {warning}
        </p>
      )}
      
      {description && !error && !warning && (
        <p className="text-sm text-muted-foreground flex items-center">
          <Info className="mr-1 h-3 w-3" />
          {description}
        </p>
      )}
    </div>
  )
})

FormField.displayName = 'FormField'

// Enhanced Select Field Component
interface SelectFieldProps {
  label: string
  name: string
  value: string
  onValueChange: (value: string) => void
  options: Array<{
    value: string
    label: string
    description?: string
    icon?: React.ComponentType<{ className?: string }>
    color?: string
  }>
  placeholder?: string
  description?: string
  error?: string
  required?: boolean
  disabled?: boolean
  className?: string
}

export function SelectField({
  label,
  name,
  value,
  onValueChange,
  options,
  placeholder,
  description,
  error,
  required = false,
  disabled = false,
  className
}: SelectFieldProps) {
  return (
    <div className="space-y-2">
      <Label htmlFor={name}>
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>
      
      <select
        id={name}
        name={name}
        value={value}
        onChange={(e) => onValueChange(e.target.value)}
        disabled={disabled}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          error && "border-destructive focus:border-destructive",
          className
        )}
      >
        {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      
      {error && (
        <p className="text-sm text-destructive flex items-center">
          <AlertCircle className="mr-1 h-3 w-3" />
          {error}
        </p>
      )}
      
      {description && !error && (
        <p className="text-sm text-muted-foreground flex items-center">
          <Info className="mr-1 h-3 w-3" />
          {description}
        </p>
      )}
    </div>
  )
}

// Switch Field Component
interface SwitchFieldProps {
  label: string
  name: string
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  description?: string
  disabled?: boolean
  className?: string
}

export function SwitchField({
  label,
  name,
  checked,
  onCheckedChange,
  description,
  disabled = false,
  className
}: SwitchFieldProps) {
  return (
    <div className={cn("flex items-center justify-between p-4 border rounded-lg", className)}>
      <div className="space-y-1">
        <Label htmlFor={name} className="text-base font-medium">
          {label}
        </Label>
        {description && (
          <p className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
      </div>
      <input
        type="checkbox"
        id={name}
        name={name}
        checked={checked}
        onChange={(e) => onCheckedChange(e.target.checked)}
        disabled={disabled}
        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
      />
    </div>
  )
}
