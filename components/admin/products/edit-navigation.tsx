'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent } from '@/components/ui/card'
import { 
  ArrowLeft, 
  Package, 
  DollarSign, 
  Archive, 
  Palette, 
  Image, 
  Search,
  CheckCircle,
  Clock,
  Eye,
  Save
} from 'lucide-react'
import { cn } from '@/lib/utils'
import type { Product } from '@/lib/ecommerce/types'

interface TabConfig {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  description: string
  required?: boolean
}

interface EditNavigationProps {
  product: Product
  productId: string
  currentTab?: string
  showProgress?: boolean
  showQuickActions?: boolean
  className?: string
}

export function EditNavigation({ 
  product, 
  productId, 
  currentTab,
  showProgress = true,
  showQuickActions = true,
  className 
}: EditNavigationProps) {
  const pathname = usePathname()
  const [completionStatus, setCompletionStatus] = useState<Record<string, boolean>>({})

  const tabs: TabConfig[] = [
    {
      id: 'general',
      label: 'General',
      icon: Package,
      href: `/admin/e-commerce/products/${productId}/edit/general`,
      description: 'Basic product information',
      required: true
    },
    {
      id: 'pricing',
      label: 'Pricing',
      icon: DollarSign,
      href: `/admin/e-commerce/products/${productId}/edit/pricing`,
      description: 'Pricing and cost settings',
      required: true
    },
    {
      id: 'inventory',
      label: 'Inventory',
      icon: Archive,
      href: `/admin/e-commerce/products/${productId}/edit/inventory`,
      description: 'Stock and inventory management'
    },
    {
      id: 'variants',
      label: 'Variants',
      icon: Palette,
      href: `/admin/e-commerce/products/${productId}/edit/variants`,
      description: 'Product variations and options'
    },
    {
      id: 'media',
      label: 'Media',
      icon: Image,
      href: `/admin/e-commerce/products/${productId}/edit/media`,
      description: 'Product images and media'
    },
    {
      id: 'seo',
      label: 'SEO',
      icon: Search,
      href: `/admin/e-commerce/products/${productId}/edit/seo`,
      description: 'Search engine optimization'
    }
  ]

  // Calculate completion status
  useEffect(() => {
    if (product) {
      const status = {
        general: !!(product.title && product.description),
        pricing: !!(product.price?.amount && product.price.amount > 0),
        inventory: true, // Always considered complete
        variants: true, // Optional, so always complete
        media: !!(product.images && product.images.length > 0),
        seo: !!(product.seo?.title || product.seo?.description)
      }
      setCompletionStatus(status)
    }
  }, [product])

  const completionPercentage = Object.values(completionStatus).filter(Boolean).length / tabs.length * 100
  const currentTabId = currentTab || pathname.split('/').pop()

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/e-commerce/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
              <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                {product.status}
              </Badge>
            </div>
            <p className="text-muted-foreground">{product.title}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}`}>
              <Eye className="mr-2 h-4 w-4" />
              View Product
            </Link>
          </Button>
        </div>
      </div>

      {/* Progress Indicator */}
      {showProgress && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Setup Progress</span>
              <span className="text-sm text-muted-foreground">
                {Math.round(completionPercentage)}% complete
              </span>
            </div>
            <Progress value={completionPercentage} className="h-2" />
            <p className="text-xs text-muted-foreground mt-2">
              Complete all required sections to publish your product
            </p>
          </CardContent>
        </Card>
      )}

      {/* Tab Navigation */}
      <Card>
        <CardContent className="p-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-0 border-b">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isCompleted = completionStatus[tab.id]
              const isRequired = tab.required
              const isActive = currentTabId === tab.id
              
              return (
                <Link
                  key={tab.id}
                  href={tab.href}
                  className={cn(
                    "flex flex-col items-center p-4 text-center border-r border-b md:border-b-0 last:border-r-0 transition-colors",
                    "group relative",
                    isActive 
                      ? "bg-primary/5 border-b-2 border-b-primary" 
                      : "hover:bg-muted/50"
                  )}
                >
                  <div className="flex items-center justify-center w-10 h-10 rounded-full mb-2 transition-colors group-hover:scale-105">
                    <Icon className={cn(
                      "h-5 w-5 transition-colors",
                      isActive
                        ? "text-primary"
                        : isCompleted 
                          ? "text-green-600" 
                          : isRequired 
                            ? "text-orange-500" 
                            : "text-muted-foreground"
                    )} />
                    {isCompleted && !isActive && (
                      <CheckCircle className="absolute -top-1 -right-1 h-4 w-4 text-green-600 bg-background rounded-full" />
                    )}
                    {isRequired && !isCompleted && !isActive && (
                      <Clock className="absolute -top-1 -right-1 h-4 w-4 text-orange-500 bg-background rounded-full" />
                    )}
                  </div>
                  <span className={cn(
                    "text-sm font-medium mb-1",
                    isActive ? "text-primary" : ""
                  )}>
                    {tab.label}
                  </span>
                  <span className="text-xs text-muted-foreground leading-tight">
                    {tab.description}
                  </span>
                  {isRequired && (
                    <Badge variant="outline" className="mt-2 text-xs">
                      Required
                    </Badge>
                  )}
                </Link>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      {showQuickActions && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Quick Actions</h3>
                <p className="text-sm text-muted-foreground">
                  Common tasks for product management
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/admin/e-commerce/products/${productId}/edit/general`}>
                    <Package className="mr-2 h-4 w-4" />
                    Edit Details
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/admin/e-commerce/products/${productId}/edit/media`}>
                    <Image className="mr-2 h-4 w-4" />
                    Add Images
                  </Link>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/admin/e-commerce/products/${productId}/edit/variants`}>
                    <Palette className="mr-2 h-4 w-4" />
                    Manage Variants
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Simplified header component for individual tab pages
interface TabHeaderProps {
  title: string
  description?: string
  product: Product
  productId: string
  actions?: React.ReactNode
  alerts?: React.ReactNode
}

export function TabHeader({ 
  title, 
  description, 
  product, 
  productId, 
  actions,
  alerts 
}: TabHeaderProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
              <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                {product.status}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              {description || product.title}
            </p>
          </div>
        </div>
        {actions}
      </div>
      {alerts}
    </div>
  )
}
