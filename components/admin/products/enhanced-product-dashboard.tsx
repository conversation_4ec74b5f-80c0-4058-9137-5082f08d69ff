'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  Package, 
  Plus, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Eye,
  Edit,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Star,
  ShoppingCart,
  Users,
  Image,
  Search,
  Filter,
  Download,
  Upload,
  RefreshCw,
  Settings,
  Sparkles
} from 'lucide-react'
import { EnhancedProductList } from './enhanced-product-list'
import { ProductAnalytics } from './product-analytics'
import { BulkProductOperations } from './bulk-product-operations'
import { ProductImportExport } from './product-import-export'

interface ProductStats {
  total: number
  active: number
  draft: number
  lowStock: number
  outOfStock: number
  totalValue: number
  averagePrice: number
  recentlyUpdated: number
}

interface EnhancedProductDashboardProps {
  initialStats?: ProductStats
}

export function EnhancedProductDashboard({ initialStats }: EnhancedProductDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [showBulkOperations, setShowBulkOperations] = useState(false)

  // Mock stats - in real app, this would come from API
  const stats: ProductStats = initialStats || {
    total: 156,
    active: 142,
    draft: 8,
    lowStock: 12,
    outOfStock: 6,
    totalValue: 45670.50,
    averagePrice: 292.75,
    recentlyUpdated: 23
  }

  const quickActions = [
    {
      label: 'Add Product',
      href: '/admin/e-commerce/products/new',
      icon: Plus,
      description: 'Create a new product',
      variant: 'default' as const
    },
    {
      label: 'Enhanced View',
      href: '/admin/e-commerce/products/enhanced',
      icon: Sparkles,
      description: 'Advanced product management',
      variant: 'secondary' as const
    },
    {
      label: 'Import Products',
      href: '/admin/e-commerce/products/import',
      icon: Upload,
      description: 'Bulk import products',
      variant: 'outline' as const
    },
    {
      label: 'Analytics',
      href: '/admin/e-commerce/products/analytics',
      icon: BarChart3,
      description: 'Product performance insights',
      variant: 'outline' as const
    }
  ]

  const statCards = [
    {
      title: 'Total Products',
      value: stats.total,
      change: '+12%',
      trend: 'up' as const,
      icon: Package,
      description: 'All products in catalog'
    },
    {
      title: 'Active Products',
      value: stats.active,
      change: '+8%',
      trend: 'up' as const,
      icon: CheckCircle,
      description: 'Currently available for sale'
    },
    {
      title: 'Total Value',
      value: `R ${stats.totalValue.toLocaleString()}`,
      change: '+15%',
      trend: 'up' as const,
      icon: DollarSign,
      description: 'Total inventory value'
    },
    {
      title: 'Average Price',
      value: `R ${stats.averagePrice}`,
      change: '+3%',
      trend: 'up' as const,
      icon: TrendingUp,
      description: 'Average product price'
    }
  ]

  const alertCards = [
    {
      title: 'Low Stock',
      value: stats.lowStock,
      icon: AlertTriangle,
      variant: 'warning' as const,
      action: 'View Products',
      href: '/admin/e-commerce/products?filter=low-stock'
    },
    {
      title: 'Out of Stock',
      value: stats.outOfStock,
      icon: AlertTriangle,
      variant: 'destructive' as const,
      action: 'Restock Now',
      href: '/admin/e-commerce/products?filter=out-of-stock'
    },
    {
      title: 'Draft Products',
      value: stats.draft,
      icon: Clock,
      variant: 'secondary' as const,
      action: 'Review Drafts',
      href: '/admin/e-commerce/products?status=draft'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Product Management</h1>
          <p className="text-muted-foreground">
            Manage your product catalog with advanced tools and insights
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <Button
                key={action.href}
                variant={action.variant}
                size="sm"
                asChild
              >
                <Link href={action.href}>
                  <Icon className="mr-2 h-4 w-4" />
                  {action.label}
                </Link>
              </Button>
            )
          })}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => {
          const Icon = stat.icon
          const TrendIcon = stat.trend === 'up' ? TrendingUp : TrendingDown
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <TrendIcon className={`mr-1 h-3 w-3 ${
                    stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
                  }`} />
                  {stat.change} from last month
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Alerts */}
      <div className="grid gap-4 md:grid-cols-3">
        {alertCards.map((alert) => {
          const Icon = alert.icon
          return (
            <Card key={alert.title} className="border-l-4 border-l-orange-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Icon className="mr-2 h-4 w-4" />
                    {alert.title}
                  </CardTitle>
                  <Badge variant={alert.variant}>{alert.value}</Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <Button variant="outline" size="sm" asChild>
                  <Link href={alert.href}>
                    {alert.action}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Package className="h-4 w-4" />
            <span>Products</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center space-x-2">
            <Zap className="h-4 w-4" />
            <span>Bulk Operations</span>
          </TabsTrigger>
          <TabsTrigger value="import" className="flex items-center space-x-2">
            <Upload className="h-4 w-4" />
            <span>Import/Export</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <EnhancedProductList
            onCreateProduct={() => window.location.href = '/admin/e-commerce/products/new'}
            onEditProduct={(product) => window.location.href = `/admin/e-commerce/products/${product.id}/edit`}
            onViewProduct={(product) => window.location.href = `/admin/e-commerce/products/${product.id}`}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <ProductAnalytics />
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Operations</CardTitle>
              <CardDescription>
                Perform actions on multiple products at once
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkProductOperations
                selectedProducts={[]}
                products={[]}
                onOperationComplete={() => {}}
                onClearSelection={() => {}}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="import" className="space-y-4">
          <ProductImportExport />
        </TabsContent>
      </Tabs>
    </div>
  )
}
