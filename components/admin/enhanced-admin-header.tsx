'use client'

import { useState } from 'react'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Search, 
  Plus, 
  Bell, 
  Settings, 
  User, 
  Package, 
  ShoppingCart,
  BarChart3,
  Command,
  Zap,
  Globe,
  Eye,
  Edit,
  Save,
  RefreshCw
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuShortcut,
} from '@/components/ui/dropdown-menu'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { AdminBreadcrumb } from './admin-breadcrumb'
import { NotificationCenter } from './notification-center'

interface QuickAction {
  label: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  shortcut?: string
  description?: string
}

interface EnhancedAdminHeaderProps {
  showQuickActions?: boolean
  showSearch?: boolean
  showNotifications?: boolean
  customActions?: React.ReactNode
}

export function EnhancedAdminHeader({
  showQuickActions = true,
  showSearch = true,
  showNotifications = true,
  customActions
}: EnhancedAdminHeaderProps) {
  const pathname = usePathname()
  const [commandOpen, setCommandOpen] = useState(false)

  // Quick actions based on current context
  const getQuickActions = (): QuickAction[] => {
    const baseActions: QuickAction[] = [
      {
        label: 'Add Product',
        href: '/admin/e-commerce/products/new',
        icon: Package,
        shortcut: '⌘P',
        description: 'Create a new product'
      },
      {
        label: 'View Orders',
        href: '/admin/e-commerce/orders',
        icon: ShoppingCart,
        shortcut: '⌘O',
        description: 'Manage customer orders'
      },
      {
        label: 'Analytics',
        href: '/admin/analytics',
        icon: BarChart3,
        shortcut: '⌘A',
        description: 'View store analytics'
      },
      {
        label: 'Settings',
        href: '/admin/settings',
        icon: Settings,
        shortcut: '⌘,',
        description: 'Admin settings'
      }
    ]

    // Add context-specific actions
    if (pathname.includes('/products/')) {
      const productId = pathname.split('/products/')[1]?.split('/')[0]
      if (productId && productId !== 'new') {
        baseActions.unshift(
          {
            label: 'Edit Product',
            href: `/admin/e-commerce/products/${productId}/edit`,
            icon: Edit,
            description: 'Edit current product'
          },
          {
            label: 'View Product',
            href: `/admin/e-commerce/products/${productId}`,
            icon: Eye,
            description: 'View product details'
          }
        )
      }
    }

    return baseActions
  }

  const quickActions = getQuickActions()

  // Command palette items
  const commandItems = [
    ...quickActions,
    {
      label: 'Enhanced Products',
      href: '/admin/e-commerce/products/enhanced',
      icon: Zap,
      description: 'Advanced product management'
    },
    {
      label: 'AI Visual Editor',
      href: '/admin/ai-visual-editor',
      icon: Zap,
      description: 'AI-powered component builder'
    },
    {
      label: 'Store Front',
      href: '/',
      icon: Globe,
      description: 'View your store'
    }
  ]

  // Keyboard shortcuts
  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.metaKey || e.ctrlKey) {
      switch (e.key) {
        case 'k':
          e.preventDefault()
          setCommandOpen(true)
          break
        case 'p':
          e.preventDefault()
          window.location.href = '/admin/e-commerce/products/new'
          break
        case 'o':
          e.preventDefault()
          window.location.href = '/admin/e-commerce/orders'
          break
        case 'a':
          e.preventDefault()
          window.location.href = '/admin/analytics'
          break
      }
    }
  }

  // Add keyboard event listener
  useState(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  })

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <AdminBreadcrumb />
        </div>

        {/* Search and Quick Actions */}
        <div className="flex-1 flex items-center justify-center max-w-md mx-auto">
          {showSearch && (
            <Button
              variant="outline"
              className="relative w-full justify-start text-sm text-muted-foreground"
              onClick={() => setCommandOpen(true)}
            >
              <Search className="mr-2 h-4 w-4" />
              Search admin...
              <kbd className="pointer-events-none absolute right-1.5 top-1.5 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
                <span className="text-xs">⌘</span>K
              </kbd>
            </Button>
          )}
        </div>

        {/* Right side actions */}
        <div className="flex items-center gap-2 px-4">
          {/* Quick Actions */}
          {showQuickActions && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Quick Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {quickActions.slice(0, 4).map((action) => {
                  const Icon = action.icon
                  return (
                    <DropdownMenuItem key={action.href} asChild>
                      <Link href={action.href}>
                        <Icon className="mr-2 h-4 w-4" />
                        {action.label}
                        {action.shortcut && (
                          <DropdownMenuShortcut>{action.shortcut}</DropdownMenuShortcut>
                        )}
                      </Link>
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Custom Actions */}
          {customActions}

          {/* Notifications */}
          {showNotifications && <NotificationCenter />}

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="relative">
                <User className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Admin Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/admin/profile">
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/admin/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/">
                  <Globe className="mr-2 h-4 w-4" />
                  View Store
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Store Info */}
          <div className="text-sm text-muted-foreground hidden md:block">
            Coco Milk Kids Admin
          </div>
        </div>
      </header>

      {/* Command Palette */}
      <CommandDialog open={commandOpen} onOpenChange={setCommandOpen}>
        <CommandInput placeholder="Type a command or search..." />
        <CommandList>
          <CommandEmpty>No results found.</CommandEmpty>
          <CommandGroup heading="Quick Actions">
            {commandItems.map((item) => {
              const Icon = item.icon
              return (
                <CommandItem
                  key={item.href}
                  onSelect={() => {
                    setCommandOpen(false)
                    window.location.href = item.href
                  }}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  <span>{item.label}</span>
                  {item.description && (
                    <span className="ml-auto text-xs text-muted-foreground">
                      {item.description}
                    </span>
                  )}
                </CommandItem>
              )
            })}
          </CommandGroup>
          <CommandSeparator />
          <CommandGroup heading="Navigation">
            <CommandItem
              onSelect={() => {
                setCommandOpen(false)
                window.location.href = '/admin'
              }}
            >
              <BarChart3 className="mr-2 h-4 w-4" />
              Dashboard
            </CommandItem>
            <CommandItem
              onSelect={() => {
                setCommandOpen(false)
                window.location.href = '/admin/e-commerce/products'
              }}
            >
              <Package className="mr-2 h-4 w-4" />
              Products
            </CommandItem>
            <CommandItem
              onSelect={() => {
                setCommandOpen(false)
                window.location.href = '/admin/e-commerce/orders'
              }}
            >
              <ShoppingCart className="mr-2 h-4 w-4" />
              Orders
            </CommandItem>
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  )
}
