'use client'

import { useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Users,
  Plus,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Mail,
  Phone,
  MapPin,
  Star,
  UserCheck,
  UserX,
  UserPlus,
  BarChart3,
  Download,
  Upload,
  Filter,
  Search,
  Eye,
  Edit,
  Calendar,
  Target,
  Heart,
  Gift,
  Crown,
  Zap,
  Sparkles,
  MessageSquare,
  Bell,
  Settings
} from 'lucide-react'
import { useCustomers } from '@/lib/ecommerce/hooks/use-customers'
import { EnhancedCustomerList } from './enhanced-customer-list'
import { CustomerAnalytics } from './customer-analytics'
import { CustomerSegmentation } from './customer-segmentation'
import { BulkCustomerOperations } from './bulk-customer-operations'
import { CustomerCommunication } from './customer-communication'
import { formatCurrency } from '@/lib/utils'
import type { Customer } from '@/lib/ecommerce/types/customer'

interface CustomerStats {
  total: number
  active: number
  inactive: number
  vip: number
  newThisMonth: number
  totalLifetimeValue: number
  averageOrderValue: number
  repeatCustomers: number
  churnRate: number
}

export function EnhancedCustomersDashboard() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('overview')

  // Mock stats - in real app, this would come from API
  const stats: CustomerStats = {
    total: 2847,
    active: 2156,
    inactive: 691,
    vip: 89,
    newThisMonth: 234,
    totalLifetimeValue: 456789.50,
    averageOrderValue: 125.75,
    repeatCustomers: 1456,
    churnRate: 12.5
  }

  const quickActions = [
    {
      label: 'Add Customer',
      href: '/admin/e-commerce/customers/new',
      icon: Plus,
      description: 'Create a new customer profile',
      variant: 'default' as const
    },
    {
      label: 'Import Customers',
      href: '/admin/e-commerce/customers/import',
      icon: Upload,
      description: 'Bulk import customers',
      variant: 'secondary' as const
    },
    {
      label: 'Email Campaign',
      href: '/admin/e-commerce/customers/campaigns',
      icon: Mail,
      description: 'Send marketing emails',
      variant: 'outline' as const
    },
    {
      label: 'Analytics',
      href: '/admin/e-commerce/customers/analytics',
      icon: BarChart3,
      description: 'Customer insights & reports',
      variant: 'outline' as const
    }
  ]

  const statCards = [
    {
      title: 'Total Customers',
      value: stats.total,
      change: '+12%',
      trend: 'up' as const,
      icon: Users,
      description: 'All registered customers'
    },
    {
      title: 'Active Customers',
      value: stats.active,
      change: '+8%',
      trend: 'up' as const,
      icon: UserCheck,
      description: 'Customers with recent activity'
    },
    {
      title: 'Total LTV',
      value: formatCurrency(stats.totalLifetimeValue, 'ZAR'),
      change: '+15%',
      trend: 'up' as const,
      icon: DollarSign,
      description: 'Total customer lifetime value'
    },
    {
      title: 'Average Order Value',
      value: formatCurrency(stats.averageOrderValue, 'ZAR'),
      change: '+5%',
      trend: 'up' as const,
      icon: ShoppingCart,
      description: 'Average order value'
    }
  ]

  const segmentCards = [
    {
      title: 'VIP Customers',
      value: stats.vip,
      icon: Crown,
      variant: 'default' as const,
      action: 'Manage VIPs',
      href: '/admin/e-commerce/customers?segment=vip',
      description: 'High-value customers'
    },
    {
      title: 'New This Month',
      value: stats.newThisMonth,
      icon: UserPlus,
      variant: 'secondary' as const,
      action: 'Welcome Campaign',
      href: '/admin/e-commerce/customers?filter=new',
      description: 'Recently registered'
    },
    {
      title: 'Repeat Customers',
      value: stats.repeatCustomers,
      icon: Heart,
      variant: 'success' as const,
      action: 'Loyalty Program',
      href: '/admin/e-commerce/customers?segment=repeat',
      description: 'Multiple purchases'
    },
    {
      title: 'At Risk',
      value: Math.round(stats.total * (stats.churnRate / 100)),
      icon: UserX,
      variant: 'warning' as const,
      action: 'Re-engage',
      href: '/admin/e-commerce/customers?segment=at-risk',
      description: 'Potential churn risk'
    }
  ]

  const handleCreateCustomer = useCallback(() => {
    router.push('/admin/e-commerce/customers/new')
  }, [router])

  const handleEditCustomer = useCallback((customer: Customer) => {
    router.push(`/admin/e-commerce/customers/${customer.id}/edit`)
  }, [router])

  const handleViewCustomer = useCallback((customer: Customer) => {
    router.push(`/admin/e-commerce/customers/${customer.id}`)
  }, [router])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Customer Management</h1>
          <p className="text-muted-foreground">
            Manage customer relationships and drive engagement
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <Button
                key={action.href}
                variant={action.variant}
                size="sm"
                onClick={() => router.push(action.href)}
              >
                <Icon className="mr-2 h-4 w-4" />
                {action.label}
              </Button>
            )
          })}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => {
          const Icon = stat.icon
          const TrendIcon = stat.trend === 'up' ? TrendingUp : TrendingDown
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <TrendIcon className={`mr-1 h-3 w-3 ${
                    stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
                  }`} />
                  {stat.change} from last month
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Customer Segments */}
      <div className="grid gap-4 md:grid-cols-4">
        {segmentCards.map((segment) => {
          const Icon = segment.icon
          return (
            <Card key={segment.title} className="border-l-4 border-l-purple-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Icon className="mr-2 h-4 w-4" />
                    {segment.title}
                  </CardTitle>
                  <Badge variant={segment.variant}>{segment.value}</Badge>
                </div>
                <CardDescription className="text-xs">
                  {segment.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => router.push(segment.href)}
                >
                  {segment.action}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Churn Risk Alert */}
      {stats.churnRate > 10 && (
        <Alert variant="destructive">
          <UserX className="h-4 w-4" />
          <AlertDescription>
            Customer churn rate is {stats.churnRate}%. Consider implementing retention campaigns 
            for at-risk customers to improve customer lifetime value.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>Customers</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="segments" className="flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <span>Segments</span>
          </TabsTrigger>
          <TabsTrigger value="communication" className="flex items-center space-x-2">
            <MessageSquare className="h-4 w-4" />
            <span>Communication</span>
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center space-x-2">
            <Zap className="h-4 w-4" />
            <span>Bulk Ops</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <EnhancedCustomerList
            onCreateCustomer={handleCreateCustomer}
            onEditCustomer={handleEditCustomer}
            onViewCustomer={handleViewCustomer}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <CustomerAnalytics />
        </TabsContent>

        <TabsContent value="segments" className="space-y-4">
          <CustomerSegmentation />
        </TabsContent>

        <TabsContent value="communication" className="space-y-4">
          <CustomerCommunication />
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Operations</CardTitle>
              <CardDescription>
                Perform actions on multiple customers at once
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkCustomerOperations
                selectedCustomers={[]}
                customers={[]}
                onOperationComplete={() => {}}
                onClearSelection={() => {}}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
