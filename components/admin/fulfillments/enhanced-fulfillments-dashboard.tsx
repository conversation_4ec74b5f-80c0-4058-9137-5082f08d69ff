'use client'

import { useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Truck,
  Plus,
  TrendingUp,
  TrendingDown,
  Package,
  MapPin,
  Clock,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  Download,
  Upload,
  RefreshCw,
  Search,
  Filter,
  Eye,
  Edit,
  Calendar,
  Target,
  Zap,
  Settings,
  Activity,
  Globe,
  Navigation,
  Plane,
  Ship,
  DollarSign,
  Users,
  Sparkles
} from 'lucide-react'
import { useFulfillments } from '@/lib/ecommerce/hooks/use-fulfillments'
import { EnhancedFulfillmentList } from './enhanced-fulfillment-list'
import { FulfillmentAnalytics } from './fulfillment-analytics'
import { ShippingTracker } from './shipping-tracker'
import { BulkFulfillmentOperations } from './bulk-fulfillment-operations'
import { FulfillmentAutomation } from './fulfillment-automation'
import { formatCurrency } from '@/lib/utils'
import type { Fulfillment } from '@/lib/ecommerce/types/fulfillment'

interface FulfillmentStats {
  totalFulfillments: number
  pending: number
  inTransit: number
  delivered: number
  failed: number
  averageDeliveryTime: number
  totalShippingCost: number
  onTimeDeliveryRate: number
  carrierPerformance: Record<string, number>
}

export function EnhancedFulfillmentsDashboard() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('overview')

  // Mock stats - in real app, this would come from API
  const stats: FulfillmentStats = {
    totalFulfillments: 3456,
    pending: 45,
    inTransit: 234,
    delivered: 3089,
    failed: 12,
    averageDeliveryTime: 3.2,
    totalShippingCost: 45678.90,
    onTimeDeliveryRate: 94.5,
    carrierPerformance: {
      'DHL': 96.2,
      'FedEx': 94.8,
      'UPS': 93.1,
      'PostNet': 91.5
    }
  }

  const quickActions = [
    {
      label: 'Create Fulfillment',
      href: '/admin/e-commerce/fulfillments/new',
      icon: Plus,
      description: 'Create new fulfillment',
      variant: 'default' as const
    },
    {
      label: 'Bulk Ship',
      href: '/admin/e-commerce/fulfillments/bulk-ship',
      icon: Truck,
      description: 'Process multiple shipments',
      variant: 'secondary' as const,
      badge: stats.pending > 0 ? stats.pending.toString() : undefined
    },
    {
      label: 'Track Shipments',
      href: '/admin/e-commerce/fulfillments/tracking',
      icon: Navigation,
      description: 'Track all shipments',
      variant: 'outline' as const
    },
    {
      label: 'Analytics',
      href: '/admin/e-commerce/fulfillments/analytics',
      icon: BarChart3,
      description: 'Fulfillment insights',
      variant: 'outline' as const
    }
  ]

  const statCards = [
    {
      title: 'Total Fulfillments',
      value: stats.totalFulfillments,
      change: '+8%',
      trend: 'up' as const,
      icon: Package,
      description: 'All time fulfillments'
    },
    {
      title: 'On-Time Delivery',
      value: `${stats.onTimeDeliveryRate}%`,
      change: '+2%',
      trend: 'up' as const,
      icon: CheckCircle,
      description: 'Delivered on time'
    },
    {
      title: 'Avg Delivery Time',
      value: `${stats.averageDeliveryTime} days`,
      change: '-0.3 days',
      trend: 'up' as const,
      icon: Clock,
      description: 'Average delivery time'
    },
    {
      title: 'Shipping Costs',
      value: formatCurrency(stats.totalShippingCost, 'ZAR'),
      change: '+5%',
      trend: 'down' as const,
      icon: DollarSign,
      description: 'Total shipping costs'
    }
  ]

  const statusCards = [
    {
      title: 'Pending',
      value: stats.pending,
      icon: Clock,
      variant: 'warning' as const,
      action: 'Process Now',
      href: '/admin/e-commerce/fulfillments?status=pending',
      description: 'Awaiting fulfillment'
    },
    {
      title: 'In Transit',
      value: stats.inTransit,
      icon: Truck,
      variant: 'default' as const,
      action: 'Track All',
      href: '/admin/e-commerce/fulfillments?status=in-transit',
      description: 'Currently shipping'
    },
    {
      title: 'Delivered',
      value: stats.delivered,
      icon: CheckCircle,
      variant: 'success' as const,
      action: 'View Reports',
      href: '/admin/e-commerce/fulfillments?status=delivered',
      description: 'Successfully delivered'
    },
    {
      title: 'Failed',
      value: stats.failed,
      icon: AlertTriangle,
      variant: 'destructive' as const,
      action: 'Investigate',
      href: '/admin/e-commerce/fulfillments?status=failed',
      description: 'Failed deliveries'
    }
  ]

  const handleCreateFulfillment = useCallback(() => {
    router.push('/admin/e-commerce/fulfillments/new')
  }, [router])

  const handleEditFulfillment = useCallback((fulfillment: Fulfillment) => {
    router.push(`/admin/e-commerce/fulfillments/${fulfillment.id}/edit`)
  }, [router])

  const handleViewFulfillment = useCallback((fulfillment: Fulfillment) => {
    router.push(`/admin/e-commerce/fulfillments/${fulfillment.id}`)
  }, [router])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Fulfillment Management</h1>
          <p className="text-muted-foreground">
            Manage shipping, track deliveries, and optimize logistics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <Button
                key={action.href}
                variant={action.variant}
                size="sm"
                onClick={() => router.push(action.href)}
                className="relative"
              >
                <Icon className="mr-2 h-4 w-4" />
                {action.label}
                {action.badge && (
                  <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                    {action.badge}
                  </Badge>
                )}
              </Button>
            )
          })}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => {
          const Icon = stat.icon
          const TrendIcon = stat.trend === 'up' ? TrendingUp : TrendingDown
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <TrendIcon className={`mr-1 h-3 w-3 ${
                    stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
                  }`} />
                  {stat.change} from last month
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Status Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        {statusCards.map((status) => {
          const Icon = status.icon
          return (
            <Card key={status.title} className="border-l-4 border-l-blue-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Icon className="mr-2 h-4 w-4" />
                    {status.title}
                  </CardTitle>
                  <Badge variant={status.variant}>{status.value}</Badge>
                </div>
                <CardDescription className="text-xs">
                  {status.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => router.push(status.href)}
                >
                  {status.action}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Performance Alerts */}
      {(stats.failed > 5 || stats.onTimeDeliveryRate < 90) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {stats.failed > 5 && `${stats.failed} failed deliveries need attention. `}
            {stats.onTimeDeliveryRate < 90 && `On-time delivery rate is ${stats.onTimeDeliveryRate}%.`}
            Consider reviewing carrier performance and shipping processes.
          </AlertDescription>
        </Alert>
      )}

      {/* Carrier Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Truck className="mr-2 h-5 w-5" />
            Carrier Performance
          </CardTitle>
          <CardDescription>
            Delivery success rates by shipping carrier
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Object.entries(stats.carrierPerformance).map(([carrier, rate]) => (
              <div key={carrier} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{carrier}</span>
                  <Badge variant={rate > 95 ? 'default' : rate > 90 ? 'secondary' : 'destructive'}>
                    {rate}%
                  </Badge>
                </div>
                <Progress value={rate} className="h-2" />
                <p className="text-xs text-muted-foreground">Success rate</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Package className="h-4 w-4" />
            <span>Fulfillments</span>
          </TabsTrigger>
          <TabsTrigger value="tracking" className="flex items-center space-x-2">
            <Navigation className="h-4 w-4" />
            <span>Tracking</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="automation" className="flex items-center space-x-2">
            <Sparkles className="h-4 w-4" />
            <span>Automation</span>
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center space-x-2">
            <Zap className="h-4 w-4" />
            <span>Bulk Ops</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <EnhancedFulfillmentList
            onCreateFulfillment={handleCreateFulfillment}
            onEditFulfillment={handleEditFulfillment}
            onViewFulfillment={handleViewFulfillment}
          />
        </TabsContent>

        <TabsContent value="tracking" className="space-y-4">
          <ShippingTracker />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <FulfillmentAnalytics />
        </TabsContent>

        <TabsContent value="automation" className="space-y-4">
          <FulfillmentAutomation />
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Operations</CardTitle>
              <CardDescription>
                Perform actions on multiple fulfillments at once
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkFulfillmentOperations
                selectedFulfillments={[]}
                fulfillments={[]}
                onOperationComplete={() => {}}
                onClearSelection={() => {}}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
