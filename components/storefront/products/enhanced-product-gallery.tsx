"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import type { StorefrontVariant } from "@/lib/ecommerce/utils/product-transformers"

interface EnhancedProductGalleryProps {
  images: string[]
  productName: string
  selectedVariant?: StorefrontVariant | null
}

export function EnhancedProductGallery({ 
  images, 
  productName, 
  selectedVariant 
}: EnhancedProductGalleryProps) {
  const [selectedImage, setSelectedImage] = useState(0)

  // Use Coco Milk Kids images if no images are provided
  const stockImages = [
    "/assets/images/cocomilk_kids-20221216_070828-1200554041.jpg",
    "/assets/images/cocomilk_kids-20221221_105615-2775996687.jpg",
    "/assets/images/cocomilk_kids-20230112_110126-3728995334.jpg",
    "/assets/images/cocomilk_kids-20221021_091054-2430450557.jpg",
  ]

  // Get variant-specific images
  const getDisplayImages = () => {
    let displayImages = images.length > 0 ? images : stockImages

    // If variant has a specific image, prioritize it
    if (selectedVariant?.image) {
      displayImages = [selectedVariant.image, ...displayImages.filter(img => img !== selectedVariant.image)]
    }

    return displayImages
  }

  const displayImages = getDisplayImages()

  // Reset selected image when variant changes
  useEffect(() => {
    if (selectedVariant?.image && displayImages[0] === selectedVariant.image) {
      setSelectedImage(0)
    }
  }, [selectedVariant, displayImages])

  // Ensure selected image index is valid
  useEffect(() => {
    if (selectedImage >= displayImages.length) {
      setSelectedImage(0)
    }
  }, [displayImages.length, selectedImage])

  return (
    <div className="space-y-4">
      {/* Main Image */}
      <div className="relative aspect-square overflow-hidden bg-muted image-zoom group">
        <Image
          src={displayImages[selectedImage] || "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"}
          alt={`${productName} - ${selectedVariant?.title || 'Main image'}`}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
          priority
        />
        
        {/* Variant Badge */}
        {selectedVariant && (
          <div className="absolute top-3 left-3 bg-black/80 text-white px-2 py-1 rounded text-xs">
            {selectedVariant.title}
          </div>
        )}
        
        {/* Availability Badge */}
        {selectedVariant && !selectedVariant.available && (
          <div className="absolute top-3 right-3 bg-red-600 text-white px-2 py-1 rounded text-xs">
            Out of Stock
          </div>
        )}
      </div>

      {/* Thumbnail Images */}
      {displayImages.length > 1 && (
        <div className="flex space-x-3 overflow-x-auto pb-2">
          {displayImages.map((image, index) => (
            <button
              key={`${image}-${index}`}
              className={cn(
                "relative w-20 h-20 overflow-hidden bg-muted flex-shrink-0 cursor-pointer transition-all duration-300",
                selectedImage === index 
                  ? "ring-2 ring-[#6C1411] opacity-100" 
                  : "opacity-70 hover:opacity-100",
              )}
              onClick={() => setSelectedImage(index)}
            >
              <Image
                src={image || "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"}
                alt={`${productName} thumbnail ${index + 1}`}
                fill
                className="object-cover"
              />
              
              {/* Variant indicator for first image if it's variant-specific */}
              {index === 0 && selectedVariant?.image === image && (
                <div className="absolute bottom-1 left-1 w-2 h-2 bg-[#6C1411] rounded-full"></div>
              )}
            </button>
          ))}
        </div>
      )}

      {/* Image Counter */}
      <div className="text-center text-sm text-muted-foreground">
        {selectedImage + 1} of {displayImages.length}
        {selectedVariant && (
          <span className="ml-2 text-xs">
            • {selectedVariant.title}
          </span>
        )}
      </div>
    </div>
  )
}
