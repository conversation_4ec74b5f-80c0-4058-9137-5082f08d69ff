'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback } from '../avatar'
import { Bo<PERSON> } from 'lucide-react'
import { ChatTypingProps } from './types'

export function ChatTyping({
  isVisible,
  avatar,
  className
}: ChatTypingProps) {
  if (!isVisible) return null

  return (
    <div className={cn(
      "flex gap-3 p-4 animate-in fade-in-0 slide-in-from-bottom-2",
      className
    )}>
      {/* Avatar */}
      <div className="flex-shrink-0">
        <Avatar className="h-8 w-8">
          <AvatarFallback className="bg-muted">
            <Bot className="h-4 w-4" />
          </AvatarFallback>
        </Avatar>
      </div>

      {/* Typing Indicator */}
      <div className="flex-1">
        <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
          <span className="font-medium">Assistant</span>
          <span>is typing...</span>
        </div>
        
        <div className="bg-muted rounded-lg p-3 max-w-[80px]">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-muted-foreground/60 rounded-full animate-bounce [animation-delay:-0.3s]" />
            <div className="w-2 h-2 bg-muted-foreground/60 rounded-full animate-bounce [animation-delay:-0.15s]" />
            <div className="w-2 h-2 bg-muted-foreground/60 rounded-full animate-bounce" />
          </div>
        </div>
      </div>
    </div>
  )
}