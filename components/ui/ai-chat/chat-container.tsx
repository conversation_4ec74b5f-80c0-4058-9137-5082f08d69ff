'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Card } from '../card'

interface ChatContainerProps {
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'minimal' | 'bordered' | 'floating'
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
}

export function ChatContainer({ 
  children, 
  className,
  variant = 'default',
  size = 'md'
}: ChatContainerProps) {
  const baseClasses = "flex flex-col h-full"
  
  const variantClasses = {
    default: "bg-background border rounded-lg shadow-sm",
    minimal: "bg-transparent",
    bordered: "bg-background border-2 rounded-xl shadow-md",
    floating: "bg-background border rounded-2xl shadow-lg backdrop-blur-sm"
  }
  
  const sizeClasses = {
    sm: "max-w-sm max-h-96",
    md: "max-w-md max-h-[500px]",
    lg: "max-w-2xl max-h-[600px]",
    xl: "max-w-4xl max-h-[700px]",
    full: "w-full h-full"
  }

  if (variant === 'minimal') {
    return (
      <div className={cn(
        baseClasses,
        sizeClasses[size],
        className
      )}>
        {children}
      </div>
    )
  }

  return (
    <Card className={cn(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      className
    )}>
      {children}
    </Card>
  )
}