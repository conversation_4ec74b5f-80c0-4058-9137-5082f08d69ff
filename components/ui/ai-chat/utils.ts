import { ChatMessage, ChatAttachment } from './types'
import { cn } from '@/lib/utils'

// Re-export cn for convenience
export { cn }

export function formatChatMessage(message: ChatMessage): string {
  if (typeof message.content === 'string') {
    return message.content
  }
  
  // Handle message parts for complex content
  if (message.parts) {
    return message.parts
      .filter(part => part.type === 'text')
      .map(part => part.text)
      .join('')
  }
  
  return ''
}

export function validateChatInput(input: string, maxLength?: number): boolean {
  if (!input || input.trim().length === 0) {
    return false
  }
  
  if (maxLength && input.length > maxLength) {
    return false
  }
  
  return true
}

export function sanitizeChatContent(content: string): string {
  // Basic sanitization - remove potentially harmful content
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim()
}

export function formatTimestamp(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)

  if (minutes < 1) {
    return 'Just now'
  } else if (minutes < 60) {
    return `${minutes}m ago`
  } else if (hours < 24) {
    return `${hours}h ago`
  } else if (days < 7) {
    return `${days}d ago`
  } else {
    return date.toLocaleDateString()
  }
}

export function generateMessageId(): string {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export function validateAttachment(file: File, maxSize?: number, allowedTypes?: string[]): boolean {
  if (maxSize && file.size > maxSize) {
    return false
  }
  
  if (allowedTypes && !allowedTypes.some(type => file.type.startsWith(type))) {
    return false
  }
  
  return true
}

// Re-export formatFileSize from main utils
export { formatFileSize } from '@/lib/utils'

export function createAttachmentFromFile(file: File): Promise<ChatAttachment> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      const attachment: ChatAttachment = {
        id: generateMessageId(),
        name: file.name,
        contentType: file.type,
        url: reader.result as string,
        size: file.size,
        preview: file.type.startsWith('image/') ? reader.result as string : undefined
      }
      resolve(attachment)
    }
    
    reader.onerror = () => reject(new Error('Failed to read file'))
    reader.readAsDataURL(file)
  })
}

export function extractTextFromMessage(message: ChatMessage): string {
  if (typeof message.content === 'string') {
    return message.content
  }
  
  if (message.parts) {
    return message.parts
      .filter(part => part.type === 'text')
      .map(part => part.text)
      .join(' ')
  }
  
  return ''
}

export function getMessageSources(message: ChatMessage) {
  if (!message.parts) return []
  
  return message.parts
    .filter(part => part.type === 'source')
    .map(part => part.source)
}

export function getMessageReasoning(message: ChatMessage): string {
  if (message.reasoning) return message.reasoning
  
  if (!message.parts) return ''
  
  const reasoningParts = message.parts.filter(part => part.type === 'reasoning')
  if (reasoningParts.length === 0) return ''
  
  return reasoningParts
    .map(part => 
      part.details
        ?.map(detail => detail.type === 'text' ? detail.text : '<redacted>')
        .join('')
    )
    .join('')
}

export function getMessageImages(message: ChatMessage) {
  if (!message.parts) return []
  
  return message.parts
    .filter(part => part.type === 'file' && (part as any).mimeType?.startsWith('image/'))
    .map(part => ({
      url: `data:${(part as any).mimeType};base64,${(part as any).data}`,
      alt: (part as any).name || 'Generated image'
    }))
}

// Re-export truncateText from main utils
export { truncateText } from '@/lib/utils'

export function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch (_) {
    return false
  }
}

// Re-export debounce from main utils
export { debounce } from '@/lib/utils'

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}