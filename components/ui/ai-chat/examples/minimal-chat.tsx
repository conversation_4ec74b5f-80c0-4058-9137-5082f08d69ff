'use client'

import React from 'react'
import {
  ChatContainer,
  ChatMessages,
  ChatInput,
  useAIChat
} from '../index'

interface MinimalChatProps {
  className?: string
  placeholder?: string
}

export function MinimalChat({
  className,
  placeholder = "Type a message..."
}: MinimalChatProps) {
  const {
    messages,
    input,
    isLoading,
    handleInputChange,
    sendMessage
  } = useAIChat({
    api: '/api/chat'
  })

  return (
    <ChatContainer className={className} variant="minimal" size="md">
      <ChatMessages
        messages={messages}
        isLoading={isLoading}
      />

      <ChatInput
        value={input}
        onChange={handleInputChange}
        onSubmit={(content) => sendMessage(content)}
        disabled={isLoading}
        allowAttachments={false}
        placeholder={placeholder}
      />
    </ChatContainer>
  )
}