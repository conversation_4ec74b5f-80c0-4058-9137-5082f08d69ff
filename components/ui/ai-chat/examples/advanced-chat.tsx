'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ChatHeader,
  ChatMessages,
  ChatInput,
  ChatStatus,
  ChatError,
  ChatActions,
  useAIChat,
  useChatActions
} from '../index'
import { Button } from '../../button'
import { Badge } from '../../badge'
import { Settings, Download, Share } from 'lucide-react'

interface AdvancedChatProps {
  title?: string
  subtitle?: string
  avatar?: string
  className?: string
  showActions?: boolean
  enableReasoning?: boolean
  enableSources?: boolean
}

export function AdvancedChat({
  title = "Advanced AI Assistant",
  subtitle = "With reasoning and sources",
  avatar,
  className,
  showActions = true,
  enableReasoning = true,
  enableSources = true
}: AdvancedChatProps) {
  const [showSettings, setShowSettings] = useState(false)

  const {
    messages,
    input,
    status,
    error,
    isLoading,
    handleInputChange,
    handleSubmit,
    sendMessage,
    regenerateLastMessage,
    stopGeneration,
    clearChat,
    deleteMessage,
    editMessage,
    retryMessage
  } = useAIChat({
    api: '/api/chat',
    sendUsage: true,
    sendReasoning: enableReasoning,
    sendSources: enableSources,
    experimental_throttle: 50
  })

  const {
    exportChat,
    shareChat,
    getMessageStats
  } = useChatActions({
    messages,
    onClear: clearChat
  })

  const stats = getMessageStats()

  return (
    <ChatContainer className={className} variant="bordered" size="xl">
      <ChatHeader
        title={title}
        subtitle={subtitle}
        avatar={avatar}
        status={status}
        onClear={clearChat}
        onSettings={() => setShowSettings(!showSettings)}
      />

      {/* Stats Bar */}
      {messages.length > 0 && (
        <div className="px-4 py-2 border-b bg-muted/30 flex items-center justify-between">
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <span>{stats.total} messages</span>
            <span>{stats.totalCharacters} characters</span>
            <span>Avg: {stats.averageLength} chars/msg</span>
          </div>
          
          <div className="flex items-center gap-2">
            {enableReasoning && (
              <Badge variant="outline" className="text-xs">
                Reasoning
              </Badge>
            )}
            {enableSources && (
              <Badge variant="outline" className="text-xs">
                Sources
              </Badge>
            )}
          </div>
        </div>
      )}

      <ChatMessages
        messages={messages}
        isLoading={isLoading}
        onMessageDelete={deleteMessage}
        onMessageEdit={editMessage}
        onRegenerate={regenerateLastMessage}
      />

      {error && (
        <ChatError
          error={error}
          onRetry={retryMessage}
        />
      )}

      <ChatStatus
        status={status}
        error={error}
        onRetry={retryMessage}
        onStop={stopGeneration}
      />

      {/* Actions Bar */}
      {showActions && (
        <div className="px-4 py-2 border-t bg-muted/30 flex items-center justify-between">
          <ChatActions
            onClear={clearChat}
            onExport={exportChat}
            disabled={isLoading}
          />
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={shareChat}
              disabled={messages.length === 0}
              className="h-8 px-2"
            >
              <Share className="w-4 h-4 mr-1" />
              Share
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={exportChat}
              disabled={messages.length === 0}
              className="h-8 px-2"
            >
              <Download className="w-4 h-4 mr-1" />
              Export
            </Button>
          </div>
        </div>
      )}

      <ChatInput
        value={input}
        onChange={handleInputChange}
        onSubmit={(content, attachments) => sendMessage(content, attachments)}
        disabled={isLoading}
        allowAttachments={true}
        maxLength={8000}
        placeholder="Ask me anything..."
      />
    </ChatContainer>
  )
}