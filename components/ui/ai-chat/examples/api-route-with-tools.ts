// Example API route with tool calling support
// Place this in: app/api/chat-with-tools/route.ts

import { openai } from '@ai-sdk/openai'
import { streamText, tool } from 'ai'
import { z } from 'zod'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

// Define tools that the AI can use
const tools = {
  get_weather: tool({
    description: 'Get current weather information for a location',
    parameters: z.object({
      location: z.string().describe('The city and state, e.g. San Francisco, CA'),
      unit: z.enum(['celsius', 'fahrenheit']).optional().default('celsius')
    }),
    execute: async ({ location, unit }) => {
      // In a real app, you'd call a weather API
      // For demo purposes, return mock data
      const temperature = Math.round(Math.random() * 30 + 10)
      const conditions = ['sunny', 'cloudy', 'rainy', 'partly cloudy'][Math.floor(Math.random() * 4)]
      
      return {
        location,
        temperature: unit === 'fahrenheit' ? Math.round(temperature * 9/5 + 32) : temperature,
        unit,
        conditions,
        humidity: Math.round(Math.random() * 100),
        windSpeed: Math.round(Math.random() * 20)
      }
    }
  }),

  calculate: tool({
    description: 'Perform mathematical calculations',
    parameters: z.object({
      expression: z.string().describe('Mathematical expression to evaluate, e.g., "2 + 2" or "sqrt(16)"')
    }),
    execute: async ({ expression }) => {
      try {
        // Simple math evaluation - in production, use a proper math parser
        // This is a simplified version for demo purposes
        const sanitized = expression.replace(/[^0-9+\-*/().\s]/g, '')
        const result = Function(`"use strict"; return (${sanitized})`)()
        
        if (typeof result !== 'number' || !isFinite(result)) {
          throw new Error('Invalid calculation result')
        }
        
        return {
          expression,
          result,
          formatted: `${expression} = ${result}`
        }
      } catch (error) {
        throw new Error(`Cannot calculate "${expression}": ${error.message}`)
      }
    }
  }),

  search_products: tool({
    description: 'Search for products in the Coco Milk Kids store',
    parameters: z.object({
      query: z.string().describe('Search query for products'),
      category: z.string().optional().describe('Product category to filter by'),
      maxResults: z.number().optional().default(5).describe('Maximum number of results')
    }),
    execute: async ({ query, category, maxResults }) => {
      // Mock product data - replace with actual database query
      const allProducts = [
        { id: 1, name: 'Kids Cotton T-Shirt', price: 25.99, category: 'clothing', description: 'Soft cotton t-shirt for kids' },
        { id: 2, name: 'School Uniform Set', price: 45.99, category: 'uniforms', description: 'Complete school uniform set' },
        { id: 3, name: 'Summer Dress', price: 35.99, category: 'clothing', description: 'Light summer dress for girls' },
        { id: 4, name: 'Sports Shorts', price: 18.99, category: 'activewear', description: 'Comfortable sports shorts' },
        { id: 5, name: 'Winter Jacket', price: 65.99, category: 'outerwear', description: 'Warm winter jacket for kids' }
      ]

      let filteredProducts = allProducts.filter(product =>
        product.name.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase())
      )

      if (category) {
        filteredProducts = filteredProducts.filter(product =>
          product.category.toLowerCase() === category.toLowerCase()
        )
      }

      return {
        query,
        category,
        totalFound: filteredProducts.length,
        results: filteredProducts.slice(0, maxResults).map(product => ({
          ...product,
          url: `/products/${product.id}`
        }))
      }
    }
  }),

  get_order_status: tool({
    description: 'Get the status of a customer order',
    parameters: z.object({
      orderId: z.string().describe('The order ID to check')
    }),
    execute: async ({ orderId }) => {
      // Mock order data - replace with actual database query
      const statuses = ['processing', 'shipped', 'out_for_delivery', 'delivered']
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
      
      const estimatedDelivery = new Date()
      estimatedDelivery.setDate(estimatedDelivery.getDate() + Math.floor(Math.random() * 7) + 1)

      return {
        orderId,
        status: randomStatus,
        statusMessage: getStatusMessage(randomStatus),
        estimatedDelivery: estimatedDelivery.toISOString().split('T')[0],
        trackingNumber: `TRK${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
        items: [
          { name: 'Kids T-Shirt', quantity: 2, price: 25.99 },
          { name: 'School Shorts', quantity: 1, price: 18.99 }
        ]
      }
    }
  }),

  get_store_hours: tool({
    description: 'Get store hours and location information',
    parameters: z.object({
      location: z.string().optional().describe('Store location to check')
    }),
    execute: async ({ location }) => {
      return {
        location: location || 'Main Store',
        hours: {
          monday: '9:00 AM - 8:00 PM',
          tuesday: '9:00 AM - 8:00 PM',
          wednesday: '9:00 AM - 8:00 PM',
          thursday: '9:00 AM - 8:00 PM',
          friday: '9:00 AM - 9:00 PM',
          saturday: '9:00 AM - 9:00 PM',
          sunday: '10:00 AM - 6:00 PM'
        },
        address: '123 Kids Fashion St, Shopping District, City 12345',
        phone: '+****************',
        currentlyOpen: isStoreOpen()
      }
    }
  })
}

function getStatusMessage(status: string): string {
  switch (status) {
    case 'processing': return 'Your order is being prepared'
    case 'shipped': return 'Your order has been shipped'
    case 'out_for_delivery': return 'Your order is out for delivery'
    case 'delivered': return 'Your order has been delivered'
    default: return 'Status unknown'
  }
}

function isStoreOpen(): boolean {
  const now = new Date()
  const hour = now.getHours()
  const day = now.getDay() // 0 = Sunday, 6 = Saturday
  
  if (day === 0) { // Sunday
    return hour >= 10 && hour < 18
  } else if (day >= 1 && day <= 4) { // Monday-Thursday
    return hour >= 9 && hour < 20
  } else { // Friday-Saturday
    return hour >= 9 && hour < 21
  }
}

export async function POST(req: Request) {
  try {
    const { messages } = await req.json()

    const result = streamText({
      model: openai('gpt-4-turbo'),
      system: `You are a helpful AI assistant for Coco Milk Kids, a children's clothing store.
               You can help customers with:
               - Product searches and recommendations
               - Order status inquiries
               - Store information and hours
               - General questions about kids' clothing
               - Weather-appropriate clothing suggestions
               - Basic calculations for sizing, pricing, etc.
               
               Always be friendly, helpful, and professional. When using tools, explain what you're doing.
               If you find products, provide helpful details and suggest related items when appropriate.`,
      messages,
      tools
    })

    return result.toDataStreamResponse({
      sendUsage: true,
      getErrorMessage: (error) => {
        if (error == null) return 'Unknown error occurred'
        if (typeof error === 'string') return error
        if (error instanceof Error) return error.message
        return 'An unexpected error occurred'
      }
    })
  } catch (error) {
    console.error('Chat API Error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: 'Failed to process chat request'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// Example of handling tool calls with custom validation
export async function POST_WITH_VALIDATION(req: Request) {
  try {
    const { messages, userId } = await req.json()

    // Add user context to system message
    const systemMessage = `You are a helpful AI assistant for Coco Milk Kids.
                          Customer ID: ${userId}
                          Use tools to provide accurate, helpful information.`

    const result = streamText({
      model: openai('gpt-4-turbo'),
      system: systemMessage,
      messages,
      tools
    })

    return result.toDataStreamResponse()
  } catch (error) {
    return new Response('Error', { status: 500 })
  }
}