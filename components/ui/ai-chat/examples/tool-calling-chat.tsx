'use client'

import React from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ChatMessages,
  ChatInput,
  ChatStatus,
  ChatError,
  useAIChat
} from '../index'
import { useChatTools, commonTools } from '../hooks/use-chat-tools'
import { Badge } from '../../badge'
import { Button } from '../../button'
import { Wrench as Tool, Zap } from 'lucide-react'

interface ToolCallingChatProps {
  title?: string
  subtitle?: string
  avatar?: string
  className?: string
  enabledTools?: string[]
}

export function ToolCallingChat({
  title = "AI Assistant with Tools",
  subtitle = "Can use tools to help you",
  avatar,
  className,
  enabledTools = ['get_weather', 'calculate', 'search_products', 'get_order_status']
}: ToolCallingChatProps) {
  // Filter tools based on enabled tools
  const availableTools = commonTools.filter(tool => 
    enabledTools.includes(tool.name)
  )

  const {
    executeTool,
    activeCalls,
    hasTools
  } = useChatTools({
    tools: availableTools,
    onToolCall: (toolCall) => {
      console.log('Tool called:', toolCall)
    },
    onToolResult: (toolCall, result) => {
      console.log('Tool result:', toolCall.name, result)
    },
    onToolError: (toolCall, error) => {
      console.error('Tool error:', toolCall.name, error)
    }
  })

  const {
    messages,
    input,
    status,
    error,
    isLoading,
    handleInputChange,
    sendMessage,
    regenerateLastMessage,
    stopGeneration,
    clearChat,
    deleteMessage,
    editMessage,
    retryMessage
  } = useAIChat({
    api: '/api/chat-with-tools',
    sendUsage: true,
    maxToolRoundtrips: 5,
    experimental_toolCallStreaming: true,
    onToolCall: (toolCall) => {
      // Handle tool calls from the AI
      console.log('AI requested tool call:', toolCall)
    }
  })

  const pendingCalls = activeCalls.filter(call => call.status === 'pending')

  return (
    <ChatContainer className={className} variant="bordered" size="xl">
      <ChatHeader
        title={title}
        subtitle={subtitle}
        avatar={avatar}
        status={status}
        onClear={clearChat}
      />

      {/* Tools Status Bar */}
      {hasTools && (
        <div className="px-4 py-2 border-b bg-muted/30 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Tool className="w-4 h-4" />
            <span className="text-sm font-medium">Available Tools:</span>
            <div className="flex gap-1">
              {availableTools.map((tool) => (
                <Badge key={tool.name} variant="outline" className="text-xs">
                  {tool.name.replace('_', ' ')}
                </Badge>
              ))}
            </div>
          </div>

          {pendingCalls.length > 0 && (
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 animate-pulse text-orange-500" />
              <span className="text-xs text-orange-600">
                {pendingCalls.length} tool{pendingCalls.length > 1 ? 's' : ''} running
              </span>
            </div>
          )}
        </div>
      )}

      <ChatMessages
        messages={messages}
        isLoading={isLoading}
        onMessageDelete={deleteMessage}
        onMessageEdit={editMessage}
        onRegenerate={regenerateLastMessage}
      />

      {error && (
        <ChatError
          error={error}
          onRetry={retryMessage}
        />
      )}

      <ChatStatus
        status={status}
        error={error}
        onRetry={retryMessage}
        onStop={stopGeneration}
      />

      {/* Quick Tool Actions */}
      <div className="px-4 py-2 border-t bg-muted/30">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-xs font-medium text-muted-foreground">Quick Actions:</span>
        </div>
        <div className="flex gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendMessage("What's the weather like in New York?")}
            disabled={isLoading}
            className="h-7 px-2 text-xs"
          >
            Check Weather
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendMessage("Calculate 15 * 24 + 100")}
            disabled={isLoading}
            className="h-7 px-2 text-xs"
          >
            Calculator
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendMessage("Search for kids t-shirts")}
            disabled={isLoading}
            className="h-7 px-2 text-xs"
          >
            Search Products
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sendMessage("Check order status for ORD-12345")}
            disabled={isLoading}
            className="h-7 px-2 text-xs"
          >
            Order Status
          </Button>
        </div>
      </div>

      <ChatInput
        value={input}
        onChange={handleInputChange}
        onSubmit={(content, attachments) => sendMessage(content, attachments)}
        disabled={isLoading}
        allowAttachments={true}
        placeholder="Ask me anything - I can use tools to help you!"
      />
    </ChatContainer>
  )
}