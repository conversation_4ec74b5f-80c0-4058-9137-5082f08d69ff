// Example API route for AI Chat
// Place this in: app/api/chat/route.ts

import { openai } from '@ai-sdk/openai'
import { streamText } from 'ai'

// Allow streaming responses up to 30 seconds
export const maxDuration = 30

export async function POST(req: Request) {
  try {
    const { messages } = await req.json()

    const result = streamText({
      model: openai('gpt-4-turbo'),
      system: `You are a helpful AI assistant for Coco Milk Kids store. 
               You help customers with product information, sizing, and general inquiries.
               Be friendly, helpful, and professional.`,
      messages,
    })

    return result.toDataStreamResponse({
      // Send usage information back to client
      sendUsage: true,
      
      // Enable reasoning for supported models
      sendReasoning: false,
      
      // Enable sources for supported models
      sendSources: false,
      
      // Custom error messages
      getErrorMessage: (error) => {
        if (error == null) {
          return 'Unknown error occurred'
        }
        
        if (typeof error === 'string') {
          return error
        }
        
        if (error instanceof Error) {
          return error.message
        }
        
        return 'An unexpected error occurred'
      }
    })
  } catch (error) {
    console.error('Chat API Error:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: 'Failed to process chat request'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}

// Example with custom body fields
export async function POST_WITH_CUSTOM_FIELDS(req: Request) {
  try {
    const { messages, customKey, userId, sessionId } = await req.json()

    // You can use custom fields for context, user identification, etc.
    console.log('Custom data:', { customKey, userId, sessionId })

    const result = streamText({
      model: openai('gpt-4-turbo'),
      system: `You are a helpful AI assistant. User ID: ${userId}`,
      messages,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    return new Response('Error', { status: 500 })
  }
}

// Example with file attachments handling
export async function POST_WITH_ATTACHMENTS(req: Request) {
  try {
    const { messages } = await req.json()

    // Process messages with attachments
    const processedMessages = messages.map((message: any) => {
      if (message.experimental_attachments) {
        // Handle attachments - convert to message parts
        const attachmentParts = message.experimental_attachments
          .filter((att: any) => att.contentType.startsWith('image/'))
          .map((att: any) => ({
            type: 'image',
            image: att.url
          }))

        return {
          ...message,
          content: [
            { type: 'text', text: message.content },
            ...attachmentParts
          ]
        }
      }
      return message
    })

    const result = streamText({
      model: openai('gpt-4-turbo'),
      messages: processedMessages,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    return new Response('Error', { status: 500 })
  }
}