'use client'

import React from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ChatHeader,
  ChatMessages,
  ChatInput,
  ChatStatus,
  ChatError,
  useAIChat
} from '../index'

interface BasicChatProps {
  title?: string
  subtitle?: string
  avatar?: string
  className?: string
}

export function BasicChat({
  title = "AI Assistant",
  subtitle = "Powered by AI SDK",
  avatar,
  className
}: BasicChatProps) {
  const {
    messages,
    input,
    status,
    error,
    isLoading,
    handleInputChange,
    handleSubmit,
    sendMessage,
    regenerateLastMessage,
    stopGeneration,
    clearChat,
    deleteMessage,
    editMessage,
    retryMessage
  } = useAIChat({
    api: '/api/chat',
    sendUsage: true,
    sendReasoning: false,
    sendSources: false
  })

  return (
    <ChatContainer className={className} variant="default" size="lg">
      <ChatHeader
        title={title}
        subtitle={subtitle}
        avatar={avatar}
        status={status}
        onClear={clearChat}
      />

      <ChatMessages
        messages={messages}
        isLoading={isLoading}
        onMessageDelete={deleteMessage}
        onMessageEdit={editMessage}
        onRegenerate={regenerateLastMessage}
      />

      {error && (
        <ChatError
          error={error}
          onRetry={retryMessage}
        />
      )}

      <ChatStatus
        status={status}
        error={error}
        onRetry={retryMessage}
        onStop={stopGeneration}
      />

      <ChatInput
        value={input}
        onChange={handleInputChange}
        onSubmit={(content, attachments) => sendMessage(content, attachments)}
        disabled={isLoading}
        allowAttachments={true}
        placeholder="Type your message..."
      />
    </ChatContainer>
  )
}