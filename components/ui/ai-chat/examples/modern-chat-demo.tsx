'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>r,
  ChatHeader,
  ChatMessages,
  ChatInput,
  ChatStatus,
  useAIChat
} from '../index'
import { Card } from '../../card'
import { Badge } from '../../badge'
import { <PERSON><PERSON> } from '../../button'
import { 
  Sparkles, 
  Brain, 
  Zap, 
  Code2,
  Palette,
  MessageSquare,
  Settings
} from 'lucide-react'

// Extended model providers for demo
const demoModelProviders = [
  {
    id: 'openai',
    name: 'OpenAI',
    icon: <Sparkles className="w-4 h-4" />,
    models: [
      { 
        id: 'gpt-4o', 
        name: 'GPT-4o', 
        description: 'Most capable model with vision', 
        maxTokens: 128000,
        pricing: { input: 0.005, output: 0.015 }
      },
      { 
        id: 'gpt-4o-mini', 
        name: 'GPT-4o Mini', 
        description: 'Fast and efficient', 
        maxTokens: 128000,
        pricing: { input: 0.00015, output: 0.0006 }
      },
      { 
        id: 'gpt-4-turbo', 
        name: 'GPT-4 Turbo', 
        description: 'Advanced reasoning capabilities', 
        maxTokens: 128000,
        pricing: { input: 0.01, output: 0.03 }
      },
    ]
  },
  {
    id: 'anthropic',
    name: 'Anthropic',
    icon: <Brain className="w-4 h-4" />,
    models: [
      { 
        id: 'claude-3-5-sonnet', 
        name: 'Claude 3.5 Sonnet', 
        description: 'Best overall performance', 
        maxTokens: 200000,
        pricing: { input: 0.003, output: 0.015 }
      },
      { 
        id: 'claude-3-haiku', 
        name: 'Claude 3 Haiku', 
        description: 'Fast and affordable', 
        maxTokens: 200000,
        pricing: { input: 0.00025, output: 0.00125 }
      },
    ]
  },
  {
    id: 'google',
    name: 'Google',
    icon: <Zap className="w-4 h-4" />,
    models: [
      { 
        id: 'gemini-pro', 
        name: 'Gemini Pro', 
        description: 'Multimodal capabilities', 
        maxTokens: 32000,
        pricing: { input: 0.0005, output: 0.0015 }
      },
      { 
        id: 'gemini-flash', 
        name: 'Gemini Flash', 
        description: 'Ultra-fast responses', 
        maxTokens: 32000,
        pricing: { input: 0.000075, output: 0.0003 }
      },
    ]
  },
  {
    id: 'local',
    name: 'Local Models',
    icon: <Code2 className="w-4 h-4" />,
    models: [
      { 
        id: 'llama-3-8b', 
        name: 'Llama 3 8B', 
        description: 'Open source, runs locally', 
        maxTokens: 8192
      },
      { 
        id: 'codellama-13b', 
        name: 'CodeLlama 13B', 
        description: 'Specialized for coding', 
        maxTokens: 16384
      },
    ]
  }
]

interface ModernChatDemoProps {
  title?: string
  subtitle?: string
  className?: string
}

export function ModernChatDemo({
  title = "Modern AI Chat",
  subtitle = "Experience the next generation chat interface",
  className
}: ModernChatDemoProps) {
  const [selectedModel, setSelectedModel] = useState('gpt-4o')
  
  const {
    messages,
    input,
    status,
    error,
    isLoading,
    handleInputChange,
    sendMessage,
    clearChat,
    regenerateLastMessage,
    stopGeneration,
    retryMessage
  } = useAIChat({
    api: '/api/chat',
    onResponse: () => {
      console.log('AI is responding...')
    },
    onFinish: () => {
      console.log('AI finished responding')
    },
    onError: (error) => {
      console.error('Chat error:', error)
    }
  })

  const handleSubmit = (content: string, attachments?: any[]) => {
    console.log('Sending message:', content, 'with model:', selectedModel)
    if (attachments?.length) {
      console.log('Attachments:', attachments)
    }
    sendMessage(content)
  }

  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId)
    console.log('Model changed to:', modelId)
  }

  // Get current model info for display
  const currentModel = demoModelProviders
    .flatMap(provider => provider.models)
    .find(model => model.id === selectedModel)

  return (
    <div className={className}>
      <ChatContainer className="h-[600px]" variant="bordered">
        <ChatHeader
          title={title}
          subtitle={subtitle}
          avatar={<MessageSquare className="w-5 h-5 text-blue-600" />}
          status={status}
          onClear={clearChat}
          actions={
            <Button variant="ghost" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          }
        />

        {/* Model Info Banner */}
        {currentModel && (
          <div className="px-4 py-2 bg-muted/30 border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="gap-1">
                  {demoModelProviders.find(p => p.models.some(m => m.id === selectedModel))?.icon}
                  {currentModel.name}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {currentModel.description}
                </span>
              </div>
              {currentModel.pricing && (
                <div className="text-xs text-muted-foreground">
                  ${currentModel.pricing.input}/1K input • ${currentModel.pricing.output}/1K output
                </div>
              )}
            </div>
          </div>
        )}

        {/* Welcome Message */}
        {messages.length === 0 && (
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="text-center max-w-md">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Modern AI Chat Interface</h3>
              <p className="text-muted-foreground text-sm mb-6">
                Experience our redesigned chat with model selection, file attachments, 
                voice input, and a beautiful modern interface.
              </p>
              
              <div className="grid grid-cols-2 gap-3 text-left">
                <Card className="p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Brain className="w-4 h-4 text-blue-500" />
                    <span className="text-sm font-medium">Multiple Models</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Choose from OpenAI, Anthropic, Google, and local models
                  </p>
                </Card>
                
                <Card className="p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Palette className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium">Rich Features</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    File uploads, voice input, and modern design
                  </p>
                </Card>
              </div>
            </div>
          </div>
        )}

        <ChatMessages
          messages={messages}
          isLoading={isLoading}
        />

        <ChatInput
          value={input}
          onChange={handleInputChange}
          onSubmit={handleSubmit}
          disabled={isLoading}
          placeholder="Ask me anything... Try uploading files or using voice input!"
          modelProviders={demoModelProviders}
          selectedModel={selectedModel}
          onModelChange={handleModelChange}
          showModelSelector={true}
          allowAttachments={true}
          allowVoiceInput={true}
          maxLength={8000}
        />
      </ChatContainer>
    </div>
  )
}