// AI Chat Components - Main Export File
export { ChatContainer } from './chat-container'
export { ChatMessage } from './chat-message'
export { ChatInput } from './chat-input'
export { ChatHeader } from './chat-header'
export { ChatStatus } from './chat-status'
export { ChatMessages } from './chat-messages'
export { ChatActions } from './chat-actions'
export { ChatAttachments } from './chat-attachments'
export { ChatTyping } from './chat-typing'
export { ChatError } from './chat-error'
export { ChatTools, ToolCallDisplay, ToolResultDisplay } from './chat-tools'

// Hooks
export { useAIChat } from './hooks/use-ai-chat'
export { useChatActions } from './hooks/use-chat-actions'
export { useChatStatus } from './hooks/use-chat-status'
export { useChatTools, commonTools } from './hooks/use-chat-tools'

// Examples
export { BasicChat } from './examples/basic-chat'
export { AdvancedChat } from './examples/advanced-chat'
export { MinimalChat } from './examples/minimal-chat'
export { ToolCallingChat } from './examples/tool-calling-chat'
export { ModernChatDemo } from './examples/modern-chat-demo'

// Types
export type {
  ChatMessage as ChatMessageType,
  ChatStatus as ChatStatusType,
  ChatConfig,
  ChatAttachment,
  ChatError as ChatErrorType,
  ChatMessageProps,
  ChatInputProps,
  ChatHeaderProps,
  ChatStatusProps,
  ChatMessagesProps,
  ChatActionsProps,
  ChatAttachmentsProps,
  ChatTypingProps,
  ChatErrorProps,
  ChatToolsProps,
  ToolCallDisplayProps,
  ToolResultDisplayProps,
  ToolDefinition,
  ToolCall,
  ModelProvider,
  ModelOption
} from './types'

// Utils
export { 
  formatChatMessage, 
  validateChatInput, 
  sanitizeChatContent,
  formatTimestamp,
  generateMessageId,
  validateAttachment,
  formatFileSize,
  createAttachmentFromFile,
  extractTextFromMessage,
  getMessageSources,
  getMessageReasoning,
  getMessageImages,
  cn
} from './utils'