'use client'

import { useCallback } from 'react'
import { ChatMessage } from '../types'
import { toast } from 'sonner'

interface UseChatActionsOptions {
  messages: ChatMessage[]
  onClear?: () => void
  onExport?: (messages: ChatMessage[]) => void
  onImport?: (messages: ChatMessage[]) => void
  onShare?: (messages: ChatMessage[]) => void
}

export function useChatActions({
  messages,
  onClear,
  onExport,
  onImport,
  onShare
}: UseChatActionsOptions) {
  
  const exportChat = useCallback(() => {
    if (messages.length === 0) {
      toast.error('No messages to export')
      return
    }

    try {
      const exportData = {
        messages: messages.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp,
          attachments: msg.attachments
        })),
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
      }

      if (onExport) {
        onExport(messages)
      } else {
        // Default export as JSON file
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
          type: 'application/json'
        })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `chat-export-${Date.now()}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }

      toast.success('Chat exported successfully!')
    } catch (error) {
      toast.error('Failed to export chat')
      console.error('Export error:', error)
    }
  }, [messages, onExport])

  const importChat = useCallback((file: File) => {
    if (!file) return

    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        const data = JSON.parse(content)
        
        if (!data.messages || !Array.isArray(data.messages)) {
          throw new Error('Invalid chat format')
        }

        const importedMessages: ChatMessage[] = data.messages.map((msg: any) => ({
          ...msg,
          timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date()
        }))

        if (onImport) {
          onImport(importedMessages)
        }

        toast.success(`Imported ${importedMessages.length} messages`)
      } catch (error) {
        toast.error('Failed to import chat')
        console.error('Import error:', error)
      }
    }

    reader.onerror = () => {
      toast.error('Failed to read file')
    }

    reader.readAsText(file)
  }, [onImport])

  const shareChat = useCallback(async () => {
    if (messages.length === 0) {
      toast.error('No messages to share')
      return
    }

    try {
      if (onShare) {
        onShare(messages)
      } else {
        // Default share behavior
        const shareText = messages
          .map(msg => `${msg.role === 'user' ? 'You' : 'AI'}: ${msg.content}`)
          .join('\n\n')

        if (navigator.share) {
          await navigator.share({
            title: 'AI Chat Conversation',
            text: shareText
          })
        } else {
          await navigator.clipboard.writeText(shareText)
          toast.success('Chat copied to clipboard!')
        }
      }
    } catch (error) {
      toast.error('Failed to share chat')
      console.error('Share error:', error)
    }
  }, [messages, onShare])

  const clearChat = useCallback(() => {
    if (messages.length === 0) {
      toast.info('Chat is already empty')
      return
    }

    if (onClear) {
      onClear()
    } else {
      toast.success('Chat cleared')
    }
  }, [messages.length, onClear])

  const copyMessage = useCallback((message: ChatMessage) => {
    try {
      const content = typeof message.content === 'string' 
        ? message.content 
        : (message.content as any)?.toString() || ''
      
      navigator.clipboard.writeText(content)
      toast.success('Message copied to clipboard')
    } catch (error) {
      toast.error('Failed to copy message')
      console.error('Copy error:', error)
    }
  }, [])

  const downloadAttachment = useCallback((attachment: any) => {
    try {
      const a = document.createElement('a')
      a.href = attachment.url
      a.download = attachment.name || 'attachment'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      toast.success('Download started')
    } catch (error) {
      toast.error('Failed to download attachment')
      console.error('Download error:', error)
    }
  }, [])

  const getMessageStats = useCallback(() => {
    const userMessages = messages.filter(msg => msg.role === 'user').length
    const aiMessages = messages.filter(msg => msg.role === 'assistant').length
    const totalCharacters = messages.reduce((total, msg) => {
      const content = typeof msg.content === 'string' ? msg.content : ''
      return total + content.length
    }, 0)

    return {
      total: messages.length,
      userMessages,
      aiMessages,
      totalCharacters,
      averageLength: messages.length > 0 ? Math.round(totalCharacters / messages.length) : 0
    }
  }, [messages])

  return {
    exportChat,
    importChat,
    shareChat,
    clearChat,
    copyMessage,
    downloadAttachment,
    getMessageStats
  }
}