'use client'

import { useState, useEffect, useCallback } from 'react'
import { ChatStatus, ChatError } from '../types'

interface UseChatStatusOptions {
  status: ChatStatus
  error?: Error | null
  onStatusChange?: (status: ChatStatus) => void
  onError?: (error: ChatError) => void
}

export function useChatStatus({
  status,
  error,
  onStatusChange,
  onError
}: UseChatStatusOptions) {
  const [previousStatus, setPreviousStatus] = useState<ChatStatus>(status)
  const [statusHistory, setStatusHistory] = useState<Array<{ status: ChatStatus; timestamp: Date }>>([
    { status, timestamp: new Date() }
  ])

  // Track status changes
  useEffect(() => {
    if (status !== previousStatus) {
      setPreviousStatus(status)
      setStatusHistory(prev => [
        ...prev,
        { status, timestamp: new Date() }
      ].slice(-10)) // Keep last 10 status changes
      
      onStatusChange?.(status)
    }
  }, [status, previousStatus, onStatusChange])

  // Handle errors
  useEffect(() => {
    if (error) {
      const chatError: ChatError = {
        message: error.message || 'An error occurred',
        code: 'CHAT_ERROR',
        details: error,
        timestamp: new Date()
      }
      onError?.(chatError)
    }
  }, [error, onError])

  const getStatusMessage = useCallback((currentStatus: ChatStatus): string => {
    switch (currentStatus) {
      case 'idle':
        return 'Ready to chat'
      case 'submitted':
        return 'Sending message...'
      case 'streaming':
        return 'AI is responding...'
      case 'ready':
        return 'Ready for next message'
      case 'error':
        return 'Something went wrong'
      default:
        return 'Unknown status'
    }
  }, [])

  const getStatusColor = useCallback((currentStatus: ChatStatus): string => {
    switch (currentStatus) {
      case 'idle':
        return 'text-muted-foreground'
      case 'submitted':
        return 'text-blue-500'
      case 'streaming':
        return 'text-green-500'
      case 'ready':
        return 'text-green-600'
      case 'error':
        return 'text-red-500'
      default:
        return 'text-muted-foreground'
    }
  }, [])

  const isLoading = status === 'submitted' || status === 'streaming'
  const isReady = status === 'ready' || status === 'idle'
  const hasError = status === 'error' || !!error
  const canSendMessage = isReady && !hasError
  const canStop = status === 'submitted' || status === 'streaming'

  const getLastStatusChange = useCallback(() => {
    if (statusHistory.length < 2) return null
    
    const current = statusHistory[statusHistory.length - 1]
    const previous = statusHistory[statusHistory.length - 2]
    
    return {
      from: previous.status,
      to: current.status,
      duration: current.timestamp.getTime() - previous.timestamp.getTime(),
      timestamp: current.timestamp
    }
  }, [statusHistory])

  const getAverageResponseTime = useCallback(() => {
    const responseTimes: number[] = []
    
    for (let i = 1; i < statusHistory.length; i++) {
      const current = statusHistory[i]
      const previous = statusHistory[i - 1]
      
      if (previous.status === 'submitted' && current.status === 'ready') {
        responseTimes.push(current.timestamp.getTime() - previous.timestamp.getTime())
      }
    }
    
    if (responseTimes.length === 0) return null
    
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
    return Math.round(average)
  }, [statusHistory])

  const getStatusDuration = useCallback(() => {
    const lastChange = statusHistory[statusHistory.length - 1]
    if (!lastChange) return 0
    
    return Date.now() - lastChange.timestamp.getTime()
  }, [statusHistory])

  const formatDuration = useCallback((milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    }
    return `${seconds}s`
  }, [])

  return {
    // Current status info
    status,
    statusMessage: getStatusMessage(status),
    statusColor: getStatusColor(status),
    
    // Status flags
    isLoading,
    isReady,
    hasError,
    canSendMessage,
    canStop,
    
    // Status history and analytics
    statusHistory,
    lastStatusChange: getLastStatusChange(),
    averageResponseTime: getAverageResponseTime(),
    statusDuration: getStatusDuration(),
    
    // Utilities
    getStatusMessage,
    getStatusColor,
    formatDuration
  }
}