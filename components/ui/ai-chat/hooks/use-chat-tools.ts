'use client'

import { useState, useCallback } from 'react'
import { ToolDefinition, ToolCall } from '../types'
import { toast } from 'sonner'

interface UseChatToolsOptions {
  tools?: ToolDefinition[]
  onToolCall?: (toolCall: ToolCall) => void
  onToolResult?: (toolCall: ToolCall, result: any) => void
  onToolError?: (toolCall: ToolCall, error: Error) => void
}

export function useChatTools({
  tools = [],
  onToolCall,
  onToolResult,
  onToolError
}: UseChatToolsOptions = {}) {
  const [activeCalls, setActiveCalls] = useState<Map<string, ToolCall>>(new Map())

  const executeTool = useCallback(async (
    toolName: string,
    args: Record<string, any>,
    toolCallId?: string
  ): Promise<any> => {
    const tool = tools.find(t => t.name === toolName)
    if (!tool) {
      throw new Error(`Tool "${toolName}" not found`)
    }

    const toolCall: ToolCall = {
      id: toolCallId || `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: toolName,
      args,
      status: 'pending',
      timestamp: new Date()
    }

    // Add to active calls
    setActiveCalls(prev => new Map(prev).set(toolCall.id, toolCall))
    onToolCall?.(toolCall)

    try {
      let result: any

      if (tool.execute) {
        // Custom tool execution
        result = await tool.execute(args)
      } else {
        // Default tool execution (should be handled by the API)
        throw new Error(`Tool "${toolName}" has no execution handler`)
      }

      // Update tool call with result
      const completedCall = {
        ...toolCall,
        result,
        status: 'success' as const
      }

      setActiveCalls(prev => {
        const newMap = new Map(prev)
        newMap.set(toolCall.id, completedCall)
        return newMap
      })

      onToolResult?.(completedCall, result)
      return result

    } catch (error) {
      const errorCall = {
        ...toolCall,
        result: error,
        status: 'error' as const
      }

      setActiveCalls(prev => {
        const newMap = new Map(prev)
        newMap.set(toolCall.id, errorCall)
        return newMap
      })

      onToolError?.(errorCall, error as Error)
      throw error
    }
  }, [tools, onToolCall, onToolResult, onToolError])

  const clearActiveCalls = useCallback(() => {
    setActiveCalls(new Map())
  }, [])

  const getToolCall = useCallback((id: string) => {
    return activeCalls.get(id)
  }, [activeCalls])

  const getActiveToolCalls = useCallback(() => {
    return Array.from(activeCalls.values())
  }, [activeCalls])

  const getPendingToolCalls = useCallback(() => {
    return Array.from(activeCalls.values()).filter(call => call.status === 'pending')
  }, [activeCalls])

  const getCompletedToolCalls = useCallback(() => {
    return Array.from(activeCalls.values()).filter(call => call.status === 'success')
  }, [activeCalls])

  const getFailedToolCalls = useCallback(() => {
    return Array.from(activeCalls.values()).filter(call => call.status === 'error')
  }, [activeCalls])

  return {
    // Tool execution
    executeTool,
    
    // Tool call management
    activeCalls: Array.from(activeCalls.values()),
    clearActiveCalls,
    getToolCall,
    getActiveToolCalls,
    getPendingToolCalls,
    getCompletedToolCalls,
    getFailedToolCalls,
    
    // Tool definitions
    tools,
    hasTools: tools.length > 0
  }
}

// Predefined tool definitions for common use cases
export const commonTools: ToolDefinition[] = [
  {
    name: 'get_weather',
    description: 'Get current weather information for a location',
    parameters: {
      type: 'object',
      properties: {
        location: {
          type: 'string',
          description: 'The city and state, e.g. San Francisco, CA'
        },
        unit: {
          type: 'string',
          enum: ['celsius', 'fahrenheit'],
          description: 'Temperature unit'
        }
      },
      required: ['location']
    },
    execute: async (args) => {
      // Mock weather data - replace with actual API call
      return {
        location: args.location,
        temperature: Math.round(Math.random() * 30 + 10),
        description: 'Partly cloudy',
        humidity: Math.round(Math.random() * 100),
        windSpeed: Math.round(Math.random() * 20)
      }
    }
  },
  {
    name: 'calculate',
    description: 'Perform mathematical calculations',
    parameters: {
      type: 'object',
      properties: {
        expression: {
          type: 'string',
          description: 'Mathematical expression to evaluate'
        }
      },
      required: ['expression']
    },
    execute: async (args) => {
      try {
        // Simple calculator - in production, use a proper math parser
        const result = Function(`"use strict"; return (${args.expression})`)()
        return { expression: args.expression, result }
      } catch (error) {
        throw new Error(`Invalid expression: ${args.expression}`)
      }
    }
  },
  {
    name: 'search_products',
    description: 'Search for products in the store',
    parameters: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search query for products'
        },
        category: {
          type: 'string',
          description: 'Product category to filter by'
        },
        maxResults: {
          type: 'number',
          description: 'Maximum number of results to return',
          default: 10
        }
      },
      required: ['query']
    },
    execute: async (args) => {
      // Mock product search - replace with actual API call
      const mockProducts = [
        { id: 1, name: 'Kids T-Shirt', price: 25.99, category: 'clothing' },
        { id: 2, name: 'School Uniform', price: 45.99, category: 'uniforms' },
        { id: 3, name: 'Summer Dress', price: 35.99, category: 'clothing' }
      ]
      
      return {
        query: args.query,
        results: mockProducts.slice(0, args.maxResults || 10)
      }
    }
  },
  {
    name: 'get_order_status',
    description: 'Get the status of a customer order',
    parameters: {
      type: 'object',
      properties: {
        orderId: {
          type: 'string',
          description: 'The order ID to check'
        }
      },
      required: ['orderId']
    },
    execute: async (args) => {
      // Mock order status - replace with actual API call
      const statuses = ['processing', 'shipped', 'delivered', 'cancelled']
      return {
        orderId: args.orderId,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        estimatedDelivery: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      }
    }
  }
]