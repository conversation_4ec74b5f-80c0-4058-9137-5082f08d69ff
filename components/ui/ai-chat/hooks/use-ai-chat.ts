'use client'

import { useChat } from '@ai-sdk/react'
import { useState, useCallback, useMemo } from 'react'
import { ChatConfig, ChatMessage, ChatAttachment, ChatError, ChatActions, ChatState } from '../types'
import { generateMessageId, createAttachmentFromFile } from '../utils'
import { toast } from 'sonner'

interface UseAIChatOptions extends ChatConfig {
  onError?: (error: ChatError) => void
  onFinish?: (message: ChatMessage) => void
  onResponse?: (response: Response) => void
  onToolCall?: (toolCall: any) => void
}

export function useAIChat(options: UseAIChatOptions = {}) {
  const {
    api = '/api/chat',
    headers,
    body,
    credentials,
    maxMessages,
    allowEmptySubmit = false,
    experimental_throttle,
    streamProtocol = 'data',
    sendUsage = true,
    sendReasoning = false,
    sendSources = false,
    maxToolRoundtrips = 5,
    experimental_toolCallStreaming = false,
    onError,
    onFinish,
    onResponse,
    onToolCall,
    ...restOptions
  } = options

  const [attachments, setAttachments] = useState<ChatAttachment[]>([])
  const [isTyping, setIsTyping] = useState(false)

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit: originalHandleSubmit,
    setInput,
    setMessages,
    append,
    reload,
    stop,
    status,
    error,
    ...rest
  } = useChat({
    api,
    headers,
    body,
    credentials,
    streamProtocol,
    experimental_throttle,
    maxToolRoundtrips,
    experimental_toolCallStreaming,
    onToolCall,
    onError: (err) => {
      const chatError: ChatError = {
        message: err.message || 'An error occurred',
        code: 'CHAT_ERROR',
        details: err,
        timestamp: new Date()
      }
      onError?.(chatError)
      toast.error(chatError.message)
    },
    onFinish: (message, { usage, finishReason }) => {
      const chatMessage: ChatMessage = {
        ...message,
        timestamp: new Date()
      }
      onFinish?.(chatMessage)
      setIsTyping(false)
    },
    onResponse: (response) => {
      onResponse?.(response)
      setIsTyping(true)
    },
    ...restOptions
  })

  // Enhanced messages with timestamps and attachments
  const enhancedMessages: ChatMessage[] = useMemo(() => {
    return messages.map(msg => ({
      ...msg,
      timestamp: (msg as any).timestamp || new Date(),
      attachments: msg.experimental_attachments as ChatAttachment[] || []
    }))
  }, [messages])

  const handleSubmit = useCallback(async (
    e?: React.FormEvent<HTMLFormElement>,
    customOptions?: {
      body?: Record<string, any>
      allowEmptySubmit?: boolean
      attachments?: ChatAttachment[]
    }
  ) => {
    e?.preventDefault()

    const currentInput = input.trim()
    const currentAttachments = customOptions?.attachments || attachments
    const shouldAllowEmpty = customOptions?.allowEmptySubmit ?? allowEmptySubmit

    if (!currentInput && !shouldAllowEmpty && currentAttachments.length === 0) {
      toast.error('Please enter a message or attach a file')
      return
    }

    if (maxMessages && messages.length >= maxMessages) {
      toast.error(`Maximum of ${maxMessages} messages reached`)
      return
    }

    try {
      setIsTyping(true)
      
      await originalHandleSubmit(e, {
        ...customOptions,
        experimental_attachments: currentAttachments
      })

      // Clear attachments after successful send
      setAttachments([])
    } catch (error) {
      setIsTyping(false)
      console.error('Failed to send message:', error)
    }
  }, [input, attachments, allowEmptySubmit, maxMessages, messages.length, originalHandleSubmit])

  const sendMessage = useCallback(async (content: string, messageAttachments?: ChatAttachment[]) => {
    if (!content.trim() && (!messageAttachments || messageAttachments.length === 0)) {
      toast.error('Please provide a message or attachment')
      return
    }

    try {
      setIsTyping(true)
      
      await append({
        role: 'user',
        content,
        experimental_attachments: messageAttachments
      })

      setAttachments([])
    } catch (error) {
      setIsTyping(false)
      console.error('Failed to send message:', error)
    }
  }, [append])

  const regenerateLastMessage = useCallback(() => {
    if (status !== 'ready' && status !== 'error') {
      toast.error('Cannot regenerate while AI is responding')
      return
    }
    
    setIsTyping(true)
    reload()
  }, [reload, status])

  const stopGeneration = useCallback(() => {
    stop()
    setIsTyping(false)
  }, [stop])

  const clearChat = useCallback(() => {
    setMessages([])
    setInput('')
    setAttachments([])
    setIsTyping(false)
    toast.success('Chat cleared')
  }, [setMessages, setInput])

  const deleteMessage = useCallback((messageId: string) => {
    setMessages(enhancedMessages.filter(msg => msg.id !== messageId))
    toast.success('Message deleted')
  }, [enhancedMessages, setMessages])

  const editMessage = useCallback((messageId: string, newContent: string) => {
    setMessages(enhancedMessages.map(msg => 
      msg.id === messageId 
        ? { ...msg, content: newContent }
        : msg
    ))
    toast.success('Message updated')
  }, [enhancedMessages, setMessages])

  const retryMessage = useCallback(() => {
    if (error) {
      regenerateLastMessage()
    }
  }, [error, regenerateLastMessage])

  const addAttachment = useCallback(async (file: File) => {
    try {
      const attachment = await createAttachmentFromFile(file)
      setAttachments(prev => [...prev, attachment])
      toast.success(`Added ${file.name}`)
    } catch (error) {
      toast.error('Failed to add attachment')
      console.error('Attachment error:', error)
    }
  }, [])

  const removeAttachment = useCallback((attachmentId: string) => {
    setAttachments(prev => prev.filter(att => att.id !== attachmentId))
    toast.success('Attachment removed')
  }, [])

  const addAttachments = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files)
    
    for (const file of fileArray) {
      await addAttachment(file)
    }
  }, [addAttachment])

  // Chat state
  const chatState: ChatState = {
    messages: enhancedMessages,
    input,
    status,
    error: error ? {
      message: error.message || 'An error occurred',
      code: 'CHAT_ERROR',
      details: error,
      timestamp: new Date()
    } : null,
    isLoading: status === 'submitted' || status === 'streaming',
    isTyping
  }

  // Chat actions
  const chatActions: ChatActions = {
    sendMessage,
    regenerateLastMessage,
    stopGeneration,
    clearChat,
    deleteMessage,
    editMessage,
    retryMessage
  }

  return {
    // State
    ...chatState,
    attachments,

    // Actions
    ...chatActions,
    setInput,
    setMessages,
    handleSubmit,
    handleInputChange,
    addAttachment,
    removeAttachment,
    addAttachments,

    // Original useChat returns
    ...rest
  }
}