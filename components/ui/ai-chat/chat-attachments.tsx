'use client'

import React, { useCallback, useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '../button'
import { Badge } from '../badge'
import { Progress } from '../progress'
import { 
  X, 
  Upload, 
  Image, 
  FileText, 
  File,
  AlertCircle,
  Check
} from 'lucide-react'
import { ChatAttachmentsProps, ChatAttachment } from './types'
import { formatFileSize, validateAttachment } from './utils'
import { toast } from 'sonner'

export function ChatAttachments({
  attachments,
  onRemove,
  onAdd,
  maxFiles = 5,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = ['image/', 'text/', 'application/pdf'],
  className
}: ChatAttachmentsProps) {
  const [dragOver, setDragOver] = useState(false)
  const [uploading, setUploading] = useState<string[]>([])

  const getFileIcon = (contentType: string) => {
    if (contentType.startsWith('image/')) {
      return <Image className="w-4 h-4 text-blue-500" />
    } else if (contentType.startsWith('text/')) {
      return <FileText className="w-4 h-4 text-green-500" />
    } else {
      return <File className="w-4 h-4 text-gray-500" />
    }
  }

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = e.dataTransfer.files
    if (files && onAdd) {
      onAdd(files)
    }
  }, [onAdd])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && onAdd) {
      onAdd(files)
    }
    // Reset input
    e.target.value = ''
  }, [onAdd])

  const validateFiles = useCallback((files: FileList): File[] => {
    const validFiles: File[] = []
    
    for (const file of Array.from(files)) {
      // Check file count limit
      if (attachments.length + validFiles.length >= maxFiles) {
        toast.error(`Maximum ${maxFiles} files allowed`)
        break
      }

      // Validate file
      if (!validateAttachment(file, maxFileSize, acceptedTypes)) {
        if (file.size > maxFileSize) {
          toast.error(`${file.name} is too large (max ${formatFileSize(maxFileSize)})`)
        } else {
          toast.error(`${file.name} is not a supported file type`)
        }
        continue
      }

      validFiles.push(file)
    }

    return validFiles
  }, [attachments.length, maxFiles, maxFileSize, acceptedTypes])

  const canAddMore = attachments.length < maxFiles

  return (
    <div className={cn("space-y-3", className)}>
      {/* Attachments List */}
      {attachments.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Attachments ({attachments.length})</h4>
            <Badge variant="outline" className="text-xs">
              {attachments.length}/{maxFiles}
            </Badge>
          </div>
          
          <div className="space-y-2">
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center gap-3 p-2 bg-muted rounded-lg"
              >
                {/* File Icon */}
                <div className="flex-shrink-0">
                  {getFileIcon(attachment.contentType)}
                </div>

                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium truncate">
                      {attachment.name}
                    </span>
                    {uploading.includes(attachment.id) ? (
                      <Badge variant="outline" className="text-xs">
                        Uploading...
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-xs">
                        <Check className="w-3 h-3 mr-1" />
                        Ready
                      </Badge>
                    )}
                  </div>
                  
                  {attachment.size && (
                    <div className="text-xs text-muted-foreground">
                      {formatFileSize(attachment.size)}
                    </div>
                  )}

                  {/* Upload Progress */}
                  {uploading.includes(attachment.id) && (
                    <Progress value={75} className="h-1 mt-1" />
                  )}
                </div>

                {/* Preview */}
                {attachment.preview && (
                  <div className="flex-shrink-0">
                    <img
                      src={attachment.preview}
                      alt={attachment.name}
                      className="w-10 h-10 object-cover rounded border"
                    />
                  </div>
                )}

                {/* Remove Button */}
                {onRemove && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemove(attachment.id)}
                    className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Area */}
      {canAddMore && onAdd && (
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
            dragOver 
              ? "border-primary bg-primary/5" 
              : "border-muted-foreground/25 hover:border-muted-foreground/50"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="space-y-2">
            <Upload className="w-8 h-8 mx-auto text-muted-foreground" />
            
            <div>
              <p className="text-sm font-medium">
                Drop files here or{' '}
                <label className="text-primary cursor-pointer hover:underline">
                  browse
                  <input
                    type="file"
                    multiple
                    onChange={handleFileSelect}
                    accept={acceptedTypes.join(',')}
                    className="hidden"
                  />
                </label>
              </p>
              
              <p className="text-xs text-muted-foreground mt-1">
                Max {formatFileSize(maxFileSize)} per file • {acceptedTypes.join(', ')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Limits Info */}
      {attachments.length >= maxFiles && (
        <div className="flex items-center gap-2 p-2 bg-orange-50 border border-orange-200 rounded text-orange-800">
          <AlertCircle className="w-4 h-4" />
          <span className="text-sm">
            Maximum number of files reached ({maxFiles})
          </span>
        </div>
      )}
    </div>
  )
}