'use client'

import React, { useRef } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '../button'
import { 
  Download, 
  Upload, 
  Share, 
  Trash2, 
  Settings,
  MoreHorizontal
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../dropdown-menu'
import { ChatActionsProps } from './types'
import { toast } from 'sonner'

export function ChatActions({
  onClear,
  onExport,
  onSettings,
  disabled = false,
  className
}: ChatActionsProps) {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleExport = () => {
    if (onExport) {
      onExport()
    } else {
      toast.info('Export functionality not implemented')
    }
  }

  const handleImport = () => {
    fileInputRef.current?.click()
  }

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // TODO: Implement import functionality
      toast.info('Import functionality not implemented')
    }
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: 'AI Chat Conversation',
          text: 'Check out this AI chat conversation',
          url: window.location.href
        })
      } else {
        await navigator.clipboard.writeText(window.location.href)
        toast.success('Link copied to clipboard!')
      }
    } catch (error) {
      toast.error('Failed to share')
    }
  }

  const handleClear = () => {
    if (onClear) {
      onClear()
    } else {
      toast.info('Clear functionality not implemented')
    }
  }

  const handleSettings = () => {
    if (onSettings) {
      onSettings()
    } else {
      toast.info('Settings functionality not implemented')
    }
  }

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {/* Hidden file input for import */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".json"
        onChange={handleFileImport}
        className="hidden"
      />

      {/* Quick Actions */}
      <Button
        variant="ghost"
        size="sm"
        onClick={handleExport}
        disabled={disabled}
        className="h-8 px-2"
      >
        <Download className="w-4 h-4 mr-1" />
        Export
      </Button>

      <Button
        variant="ghost"
        size="sm"
        onClick={handleShare}
        disabled={disabled}
        className="h-8 px-2"
      >
        <Share className="w-4 h-4 mr-1" />
        Share
      </Button>

      {/* More Actions Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            disabled={disabled}
            className="h-8 w-8 p-0"
          >
            <MoreHorizontal className="w-4 h-4" />
            <span className="sr-only">More actions</span>
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuItem onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export Chat
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={handleImport}>
            <Upload className="mr-2 h-4 w-4" />
            Import Chat
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={handleShare}>
            <Share className="mr-2 h-4 w-4" />
            Share Chat
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          {onSettings && (
            <>
              <DropdownMenuItem onClick={handleSettings}>
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuSeparator />
            </>
          )}
          
          <DropdownMenuItem 
            onClick={handleClear}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Clear Chat
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}