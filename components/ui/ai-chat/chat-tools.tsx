'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Badge } from '../badge'
import { Button } from '../button'
import { Card } from '../card'
import { 
  Wrench as Tool, 
  Loader2, 
  CheckCircle, 
  XCircle,
  ChevronDown,
  ChevronRight,
  Code,
  Play,
  AlertTriangle
} from 'lucide-react'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '../collapsible'
import { ChatToolsProps, ToolCallDisplayProps, ToolResultDisplayProps, ToolInvocation } from './types'

export function ChatTools({
  toolInvocations = [],
  onToolCall,
  customRenderer,
  className
}: ChatToolsProps) {
  if (!toolInvocations || toolInvocations.length === 0) {
    return null
  }

  return (
    <div className={cn("space-y-2", className)}>
      {toolInvocations.map((toolInvocation) => (
        <ToolCallDisplay
          key={toolInvocation.toolCallId}
          toolInvocation={toolInvocation}
        />
      ))}
    </div>
  )
}

export function ToolCallDisplay({
  toolInvocation,
  className
}: ToolCallDisplayProps) {
  const [isExpanded, setIsExpanded] = React.useState(false)

  const getStatusIcon = (state: string) => {
    switch (state) {
      case 'call':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
      case 'result':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'partial-call':
        return <Loader2 className="w-4 h-4 animate-spin text-orange-500" />
      default:
        return <Tool className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusLabel = (state: string) => {
    switch (state) {
      case 'call':
        return 'Calling tool...'
      case 'result':
        return 'Tool completed'
      case 'partial-call':
        return 'Preparing call...'
      default:
        return 'Unknown state'
    }
  }

  const getStatusColor = (state: string) => {
    switch (state) {
      case 'call':
        return 'bg-blue-50 border-blue-200'
      case 'result':
        return 'bg-green-50 border-green-200'
      case 'partial-call':
        return 'bg-orange-50 border-orange-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  return (
    <Card className={cn(
      "p-3 border",
      getStatusColor(toolInvocation.state),
      className
    )}>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-0 h-auto hover:bg-transparent"
          >
            <div className="flex items-center gap-2">
              {getStatusIcon(toolInvocation.state)}
              <span className="font-medium text-sm">
                {toolInvocation.toolName}
              </span>
              <Badge variant="outline" className="text-xs">
                {getStatusLabel(toolInvocation.state)}
              </Badge>
            </div>
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="mt-3 space-y-3">
          {/* Tool Arguments */}
          {toolInvocation.args && (
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Code className="w-3 h-3" />
                <span className="text-xs font-medium text-muted-foreground">
                  Arguments
                </span>
              </div>
              <pre className="text-xs bg-muted/50 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(toolInvocation.args, null, 2)}
              </pre>
            </div>
          )}

          {/* Tool Result */}
          {toolInvocation.state === 'result' && toolInvocation.result && (
            <ToolResultDisplay
              toolInvocation={toolInvocation}
              result={toolInvocation.result}
            />
          )}
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}

export function ToolResultDisplay({
  toolInvocation,
  result,
  className
}: ToolResultDisplayProps) {
  const renderResult = () => {
    if (typeof result === 'string') {
      return (
        <div className="text-sm">
          {result}
        </div>
      )
    }

    if (typeof result === 'object') {
      // Handle different result types
      if (result.error) {
        return (
          <div className="flex items-start gap-2 text-red-600">
            <AlertTriangle className="w-4 h-4 mt-0.5" />
            <div>
              <div className="font-medium text-sm">Error</div>
              <div className="text-xs">{result.error}</div>
            </div>
          </div>
        )
      }

      if (result.data) {
        return (
          <div className="space-y-2">
            <div className="text-sm font-medium">Result:</div>
            <pre className="text-xs bg-muted/50 p-2 rounded overflow-auto max-h-32">
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </div>
        )
      }

      // Generic object display
      return (
        <pre className="text-xs bg-muted/50 p-2 rounded overflow-auto max-h-32">
          {JSON.stringify(result, null, 2)}
        </pre>
      )
    }

    return (
      <div className="text-sm text-muted-foreground">
        {String(result)}
      </div>
    )
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center gap-2">
        <Play className="w-3 h-3" />
        <span className="text-xs font-medium text-muted-foreground">
          Result
        </span>
      </div>
      <div className="border rounded p-2 bg-background/50">
        {renderResult()}
      </div>
    </div>
  )
}

// Specialized tool result components for common tool types
export function WeatherToolResult({ result }: { result: any }) {
  if (!result.weather) return <ToolResultDisplay toolInvocation={{} as any} result={result} />

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <span className="text-lg">{result.weather.icon || '🌤️'}</span>
        <div>
          <div className="font-medium">{result.weather.location}</div>
          <div className="text-sm text-muted-foreground">
            {result.weather.temperature}°C, {result.weather.description}
          </div>
        </div>
      </div>
    </div>
  )
}

export function SearchToolResult({ result }: { result: any }) {
  if (!result.results) return <ToolResultDisplay toolInvocation={{} as any} result={result} />

  return (
    <div className="space-y-2">
      <div className="text-sm font-medium">
        Found {result.results.length} results
      </div>
      <div className="space-y-1">
        {result.results.slice(0, 3).map((item: any, index: number) => (
          <div key={index} className="text-xs p-2 bg-muted/30 rounded">
            <div className="font-medium">{item.title}</div>
            <div className="text-muted-foreground">{item.snippet}</div>
          </div>
        ))}
      </div>
    </div>
  )
}

export function CalculatorToolResult({ result }: { result: any }) {
  return (
    <div className="flex items-center gap-2">
      <Code className="w-4 h-4" />
      <span className="font-mono text-sm">
        = {result.result || result}
      </span>
    </div>
  )
}