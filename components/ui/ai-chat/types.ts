import { Message } from '@ai-sdk/react'

// Define ToolInvocation type since it might not be exported from @ai-sdk/react
export interface ToolInvocation {
  toolCallId: string
  toolName: string
  args: Record<string, any>
  result?: any
  state: 'partial-call' | 'call' | 'result'
}

export interface ChatMessage extends Message {
  timestamp?: Date
  isTyping?: boolean
  attachments?: ChatAttachment[]
  sources?: ChatSource[]
  reasoning?: string
  toolInvocations?: ToolInvocation[]
}

export interface ChatAttachment {
  id: string
  name: string
  contentType: string
  url: string
  size?: number
  preview?: string
}

export interface ChatSource {
  id: string
  title: string
  url: string
  snippet?: string
}

export type ChatStatus = 'idle' | 'submitted' | 'streaming' | 'ready' | 'error'

export interface ChatConfig {
  api?: string
  headers?: Record<string, string>
  body?: Record<string, any>
  credentials?: RequestCredentials
  maxMessages?: number
  allowEmptySubmit?: boolean
  experimental_throttle?: number
  streamProtocol?: 'data' | 'text'
  sendUsage?: boolean
  sendReasoning?: boolean
  sendSources?: boolean
  maxToolRoundtrips?: number
  experimental_toolCallStreaming?: boolean
}

export interface ChatError {
  message: string
  code?: string
  details?: any
  timestamp: Date
}

export interface ChatActions {
  sendMessage: (content: string, attachments?: ChatAttachment[]) => void
  regenerateLastMessage: () => void
  stopGeneration: () => void
  clearChat: () => void
  deleteMessage: (messageId: string) => void
  editMessage: (messageId: string, newContent: string) => void
  retryMessage: () => void
}

export interface ChatState {
  messages: ChatMessage[]
  input: string
  status: ChatStatus
  error: ChatError | null
  isLoading: boolean
  isTyping: boolean
}

export interface ChatContextValue extends ChatState, ChatActions {
  setInput: (input: string) => void
  setMessages: (messages: ChatMessage[]) => void
}

export interface ChatMessageProps {
  message: ChatMessage
  isLast?: boolean
  showActions?: boolean
  onDelete?: (messageId: string) => void
  onEdit?: (messageId: string, newContent: string) => void
  onRegenerate?: () => void
  customToolRenderer?: (toolCall: any, result: any) => React.ReactNode
  className?: string
}

export interface ModelProvider {
  id: string
  name: string
  description?: string
  icon?: React.ReactNode
  models: ModelOption[]
}

export interface ModelOption {
  id: string
  name: string
  description?: string
  maxTokens?: number
  pricing?: {
    input: number
    output: number
  }
}

export interface ChatInputProps {
  value: string
  onChange: (value: string) => void
  onSubmit: (value: string, attachments?: ChatAttachment[]) => void
  placeholder?: string
  disabled?: boolean
  allowAttachments?: boolean
  allowVoiceInput?: boolean
  maxLength?: number
  modelProviders?: ModelProvider[]
  selectedModel?: string
  onModelChange?: (modelId: string) => void
  showModelSelector?: boolean
  className?: string
}

export interface ChatHeaderProps {
  title?: string
  subtitle?: string
  avatar?: string
  status?: ChatStatus
  onClear?: () => void
  onSettings?: () => void
  className?: string
}

export interface ChatStatusProps {
  status: ChatStatus
  error?: ChatError | null
  onRetry?: () => void
  onStop?: () => void
  className?: string
}

export interface ChatMessagesProps {
  messages: ChatMessage[]
  isLoading?: boolean
  onMessageDelete?: (messageId: string) => void
  onMessageEdit?: (messageId: string, newContent: string) => void
  onRegenerate?: () => void
  customToolRenderer?: (toolCall: any, result: any) => React.ReactNode
  className?: string
}

export interface ChatActionsProps {
  onClear?: () => void
  onExport?: () => void
  onSettings?: () => void
  disabled?: boolean
  className?: string
}

export interface ChatAttachmentsProps {
  attachments: ChatAttachment[]
  onRemove?: (attachmentId: string) => void
  onAdd?: (files: FileList) => void
  maxFiles?: number
  maxFileSize?: number
  acceptedTypes?: string[]
  className?: string
}

export interface ChatTypingProps {
  isVisible: boolean
  avatar?: string
  className?: string
}

export interface ChatErrorProps {
  error: ChatError
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
}

// Tool-related types
export interface ToolCall {
  id: string
  name: string
  args: Record<string, any>
  result?: any
  status: 'pending' | 'success' | 'error'
  timestamp: Date
}

export interface ToolDefinition {
  name: string
  description: string
  parameters: Record<string, any>
  execute?: (args: Record<string, any>) => Promise<any> | any
}

export interface ChatToolsProps {
  toolInvocations?: ToolInvocation[]
  onToolCall?: (toolCall: ToolCall) => void
  customRenderer?: (toolCall: any, result: any) => React.ReactNode
  className?: string
}

export interface ToolCallDisplayProps {
  toolInvocation: ToolInvocation
  className?: string
}

export interface ToolResultDisplayProps {
  toolInvocation: ToolInvocation
  result: any
  className?: string
}