# AI Chat Components

A comprehensive set of modular React components for building AI-powered chatbots using the AI SDK. These components provide a complete chat interface with support for streaming responses, attachments, error handling, and more.

## Features

- 🚀 **Built on AI SDK**: Uses `@ai-sdk/react` for seamless AI integration
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 🎨 **Customizable**: Multiple variants and themes
- 📎 **File Attachments**: Support for images, documents, and more
- 🔄 **Real-time Streaming**: Live response streaming from AI
- 🛠 **Tool Calling**: AI can use tools to perform actions and get data
- 🛠 **Error Handling**: Comprehensive error states and recovery
- 🎯 **TypeScript**: Fully typed for better development experience
- ♿ **Accessible**: Built with accessibility in mind

## Installation

The components are already set up in your project. Make sure you have the required dependencies:

```bash
npm install @ai-sdk/react @ai-sdk/openai ai
```

## Quick Start

### Basic Chat

```tsx
import { BasicChat } from '@/components/ui/ai-chat/examples/basic-chat'

export function MyChat() {
  return (
    <BasicChat
      title="My AI Assistant"
      subtitle="Powered by GPT-4"
    />
  )
}
```

### Minimal Chat

```tsx
import { MinimalChat } from '@/components/ui/ai-chat/examples/minimal-chat'

export function SimpleChat() {
  return (
    <MinimalChat placeholder="Ask me anything..." />
  )
}
```

### Advanced Chat with Custom Features

```tsx
import { AdvancedChat } from '@/components/ui/ai-chat/examples/advanced-chat'

export function FullFeaturedChat() {
  return (
    <AdvancedChat
      title="Advanced AI"
      enableReasoning={true}
      enableSources={true}
      showActions={true}
    />
  )
}
```

### Tool Calling Chat

```tsx
import { ToolCallingChat } from '@/components/ui/ai-chat/examples/tool-calling-chat'

export function SmartAssistant() {
  return (
    <ToolCallingChat
      title="Smart Assistant"
      enabledTools={['get_weather', 'calculate', 'search_products']}
    />
  )
}
```

## Core Components

### ChatContainer

The main wrapper component that provides the chat layout.

```tsx
import { ChatContainer } from '@/components/ui/ai-chat'

<ChatContainer variant="default" size="lg">
  {/* Chat components */}
</ChatContainer>
```

**Props:**
- `variant`: `'default' | 'minimal' | 'bordered' | 'floating'`
- `size`: `'sm' | 'md' | 'lg' | 'xl' | 'full'`

### ChatHeader

Header component with title, status, and actions.

```tsx
import { ChatHeader } from '@/components/ui/ai-chat'

<ChatHeader
  title="AI Assistant"
  subtitle="Online"
  status={status}
  onClear={clearChat}
  onSettings={openSettings}
/>
```

### ChatMessages

Displays the conversation messages with actions.

```tsx
import { ChatMessages } from '@/components/ui/ai-chat'

<ChatMessages
  messages={messages}
  isLoading={isLoading}
  onMessageDelete={deleteMessage}
  onMessageEdit={editMessage}
  onRegenerate={regenerateLastMessage}
/>
```

### ChatInput

Input component for sending messages and attachments.

```tsx
import { ChatInput } from '@/components/ui/ai-chat'

<ChatInput
  value={input}
  onChange={handleInputChange}
  onSubmit={sendMessage}
  allowAttachments={true}
  placeholder="Type your message..."
/>
```

### ChatStatus

Shows the current chat status and loading states.

```tsx
import { ChatStatus } from '@/components/ui/ai-chat'

<ChatStatus
  status={status}
  error={error}
  onRetry={retryMessage}
  onStop={stopGeneration}
/>
```

## Hooks

### useAIChat

The main hook that provides chat functionality.

```tsx
import { useAIChat } from '@/components/ui/ai-chat'

const {
  messages,
  input,
  status,
  error,
  isLoading,
  sendMessage,
  clearChat,
  // ... more methods
} = useAIChat({
  api: '/api/chat',
  sendUsage: true,
  sendReasoning: false,
  sendSources: false
})
```

### useChatActions

Provides additional chat actions like export, import, and share.

```tsx
import { useChatActions } from '@/components/ui/ai-chat'

const {
  exportChat,
  importChat,
  shareChat,
  getMessageStats
} = useChatActions({
  messages,
  onClear: clearChat
})
```

### useChatStatus

Manages chat status and provides status-related utilities.

```tsx
import { useChatStatus } from '@/components/ui/ai-chat'

const {
  statusMessage,
  statusColor,
  isLoading,
  canSendMessage,
  averageResponseTime
} = useChatStatus({
  status,
  error
})
```

## API Endpoints

### Basic Chat Endpoint

Create an API endpoint at `/api/chat/route.ts`:

```tsx
import { openai } from '@ai-sdk/openai'
import { streamText } from 'ai'

export const maxDuration = 30

export async function POST(req: Request) {
  const { messages } = await req.json()

  const result = streamText({
    model: openai('gpt-4-turbo'),
    system: 'You are a helpful assistant.',
    messages,
  })

  return result.toDataStreamResponse()
}
```

### Tool Calling Endpoint

Create an API endpoint with tools at `/api/chat-with-tools/route.ts`:

```tsx
import { openai } from '@ai-sdk/openai'
import { streamText, tool } from 'ai'
import { z } from 'zod'

export const maxDuration = 30

const tools = {
  get_weather: tool({
    description: 'Get weather for a location',
    parameters: z.object({
      location: z.string().describe('City and state')
    }),
    execute: async ({ location }) => {
      // Call weather API
      return { location, temperature: 22, conditions: 'sunny' }
    }
  }),
  
  calculate: tool({
    description: 'Perform calculations',
    parameters: z.object({
      expression: z.string().describe('Math expression')
    }),
    execute: async ({ expression }) => {
      const result = Function(`return ${expression}`)()
      return { expression, result }
    }
  })
}

export async function POST(req: Request) {
  const { messages } = await req.json()

  const result = streamText({
    model: openai('gpt-4-turbo'),
    system: 'You are a helpful assistant with access to tools.',
    messages,
    tools,
    maxToolRoundtrips: 5
  })

  return result.toDataStreamResponse()
}
```

## Advanced Features

### Tool Calling

Enable AI to use tools for enhanced functionality:

```tsx
import { useAIChat, useChatTools, commonTools } from '@/components/ui/ai-chat'

const chat = useAIChat({
  api: '/api/chat-with-tools',
  maxToolRoundtrips: 5,
  experimental_toolCallStreaming: true
})

const tools = useChatTools({
  tools: commonTools,
  onToolCall: (toolCall) => console.log('Tool called:', toolCall),
  onToolResult: (toolCall, result) => console.log('Tool result:', result)
})
```

#### Available Built-in Tools

- `get_weather` - Get weather information for any location
- `calculate` - Perform mathematical calculations
- `search_products` - Search store products
- `get_order_status` - Check order status
- `get_store_hours` - Get store hours and location info

#### Creating Custom Tools

```tsx
import { ToolDefinition } from '@/components/ui/ai-chat'

const customTool: ToolDefinition = {
  name: 'custom_action',
  description: 'Performs a custom action',
  parameters: {
    type: 'object',
    properties: {
      param1: { type: 'string', description: 'First parameter' }
    },
    required: ['param1']
  },
  execute: async (args) => {
    // Your custom logic here
    return { result: 'Custom action completed' }
  }
}
```

### File Attachments

Enable file attachments in your chat:

```tsx
<ChatInput
  allowAttachments={true}
  // ... other props
/>
```

### Reasoning Support

Enable AI reasoning display:

```tsx
const chat = useAIChat({
  sendReasoning: true,
  // ... other options
})
```

### Sources Support

Enable source citations:

```tsx
const chat = useAIChat({
  sendSources: true,
  // ... other options
})
```

### Custom Error Handling

```tsx
const chat = useAIChat({
  onError: (error) => {
    console.error('Chat error:', error)
    // Custom error handling
  },
  onFinish: (message) => {
    console.log('Message finished:', message)
    // Custom completion handling
  }
})
```

## Styling

The components use Tailwind CSS and are compatible with your existing design system. You can customize the appearance by:

1. **CSS Classes**: Pass custom `className` props
2. **Variants**: Use built-in variants for different styles
3. **Theme**: Components respect your theme configuration

## Examples

Check the `/examples` folder for complete implementations:

- `basic-chat.tsx` - Simple chat interface
- `advanced-chat.tsx` - Full-featured chat with all options
- `minimal-chat.tsx` - Minimal chat for simple use cases
- `tool-calling-chat.tsx` - Chat with AI tool calling capabilities
- `api-route-example.ts` - Basic API route implementation
- `api-route-with-tools.ts` - API route with tool calling support

## TypeScript Support

All components are fully typed. Import types as needed:

```tsx
import type {
  ChatMessage,
  ChatStatus,
  ChatConfig,
  ChatAttachment
} from '@/components/ui/ai-chat'
```

## Contributing

When adding new features:

1. Update the relevant component
2. Add TypeScript types
3. Update this README
4. Add examples if needed

## Troubleshooting

### Common Issues

1. **API Endpoint Not Found**: Ensure you have created `/api/chat/route.ts`
2. **Streaming Not Working**: Check your API endpoint returns `result.toDataStreamResponse()`
3. **Attachments Not Uploading**: Verify file size and type restrictions
4. **Styling Issues**: Ensure Tailwind CSS is properly configured
5. **Tool Calling Not Working**: 
   - Ensure you're using the correct API endpoint with tools
   - Check that `zod` is installed for parameter validation
   - Verify tool definitions match the expected schema
6. **Tool Execution Errors**: Check tool execute functions for proper error handling

### Debug Mode

Enable debug mode in development:

```tsx
const chat = useAIChat({
  // ... other options
  onError: (error) => {
    if (process.env.NODE_ENV === 'development') {
      console.error('Chat Debug:', error)
    }
  }
})
```

## License

These components are part of your project and follow your project's license.