'use client'

import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '../button'
import { Badge } from '../badge'
import { Avatar, AvatarFallback, AvatarImage } from '../avatar'
import { 
  Copy, 
  Edit, 
  Trash2, 
  MoreHorizontal,
  User,
  Bot,
  RefreshCw,
  ExternalLink,
  Download,
  Eye,
  EyeOff
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../dropdown-menu'
import { Textarea } from '../textarea'
import { ChatMessageProps } from './types'
import { formatTimestamp, extractTextFromMessage, getMessageSources, getMessageReasoning, getMessageImages } from './utils'
import { ChatTools } from './chat-tools'
import { toast } from 'sonner'

export function ChatMessage({
  message,
  isLast = false,
  showActions = true,
  onDelete,
  onEdit,
  onRegenerate,
  customToolRenderer,
  className
}: ChatMessageProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(extractTextFromMessage(message))
  const [showReasoning, setShowReasoning] = useState(false)

  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'
  const messageText = extractTextFromMessage(message)
  const sources = getMessageSources(message)
  const reasoning = getMessageReasoning(message)
  const images = getMessageImages(message)
  const toolInvocations = message.toolInvocations || []

  const handleCopy = () => {
    navigator.clipboard.writeText(messageText)
    toast.success('Message copied to clipboard')
  }

  const handleEdit = () => {
    if (isEditing) {
      onEdit?.(message.id, editContent)
      setIsEditing(false)
    } else {
      setIsEditing(true)
    }
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditContent(messageText)
  }

  const handleDelete = () => {
    onDelete?.(message.id)
  }

  const handleRegenerate = () => {
    onRegenerate?.()
  }

  return (
    <div className={cn(
      "group flex gap-3 p-4 hover:bg-muted/30 transition-colors",
      isUser && "flex-row-reverse",
      className
    )}>
      {/* Avatar */}
      <div className="flex-shrink-0">
        <Avatar className="h-8 w-8">
          <AvatarFallback className={cn(
            isUser ? "bg-primary text-primary-foreground" : "bg-muted"
          )}>
            {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
          </AvatarFallback>
        </Avatar>
      </div>

      {/* Message Content */}
      <div className={cn(
        "flex-1 space-y-2 min-w-0",
        isUser && "text-right"
      )}>
        {/* Message Header */}
        <div className={cn(
          "flex items-center gap-2 text-xs text-muted-foreground",
          isUser && "justify-end"
        )}>
          <span className="font-medium">
            {isUser ? 'You' : 'Assistant'}
          </span>
          {message.timestamp && (
            <span>{formatTimestamp(message.timestamp)}</span>
          )}
        </div>

        {/* Message Body */}
        <div className={cn(
          "rounded-lg p-3 max-w-[80%]",
          isUser 
            ? "bg-primary text-primary-foreground ml-auto" 
            : "bg-muted",
          isUser && "text-right"
        )}>
          {isEditing ? (
            <div className="space-y-2">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="min-h-[60px] resize-none"
                autoFocus
              />
              <div className="flex gap-2 justify-end">
                <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                  Cancel
                </Button>
                <Button size="sm" onClick={handleEdit}>
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {/* Main message content */}
              <div className="whitespace-pre-wrap break-words">
                {messageText}
              </div>

              {/* Reasoning (if available) */}
              {reasoning && (
                <div className="border-t pt-2 mt-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowReasoning(!showReasoning)}
                    className="text-xs p-1 h-auto"
                  >
                    {showReasoning ? <EyeOff className="w-3 h-3 mr-1" /> : <Eye className="w-3 h-3 mr-1" />}
                    {showReasoning ? 'Hide' : 'Show'} Reasoning
                  </Button>
                  
                  {showReasoning && (
                    <pre className="text-xs bg-muted/50 p-2 rounded mt-2 whitespace-pre-wrap">
                      {reasoning}
                    </pre>
                  )}
                </div>
              )}

              {/* Tool Invocations */}
              {toolInvocations.length > 0 && (
                <div className="mt-3">
                  <ChatTools 
                    toolInvocations={toolInvocations} 
                    customRenderer={customToolRenderer}
                  />
                </div>
              )}

              {/* Generated Images */}
              {images.length > 0 && (
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {images.map((image, index) => (
                    <img
                      key={index}
                      src={image.url}
                      alt={image.alt}
                      className="rounded border max-w-full h-auto"
                    />
                  ))}
                </div>
              )}

              {/* Attachments */}
              {message.attachments && message.attachments.length > 0 && (
                <div className="space-y-1 mt-2">
                  {message.attachments.map((attachment) => (
                    <div key={attachment.id} className="flex items-center gap-2 text-xs">
                      <Badge variant="outline" className="text-xs">
                        {attachment.name}
                      </Badge>
                      {attachment.url && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-auto p-1"
                          onClick={() => window.open(attachment.url, '_blank')}
                        >
                          <Download className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Sources */}
              {sources.length > 0 && (
                <div className="border-t pt-2 mt-2">
                  <div className="text-xs text-muted-foreground mb-1">Sources:</div>
                  <div className="space-y-1">
                    {sources.map((source) => (
                      <a
                        key={source.id}
                        href={source.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        <ExternalLink className="w-3 h-3" />
                        {source.title || new URL(source.url).hostname}
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Message Actions */}
        {showActions && !isEditing && (
          <div className={cn(
            "flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",
            isUser && "justify-end"
          )}>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}
              className="h-6 px-2 text-xs"
            >
              <Copy className="w-3 h-3" />
            </Button>

            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleEdit}
                className="h-6 px-2 text-xs"
              >
                <Edit className="w-3 h-3" />
              </Button>
            )}

            {isAssistant && isLast && onRegenerate && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRegenerate}
                className="h-6 px-2 text-xs"
              >
                <RefreshCw className="w-3 h-3" />
              </Button>
            )}

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                >
                  <MoreHorizontal className="w-3 h-3" />
                </Button>
              </DropdownMenuTrigger>
              
              <DropdownMenuContent align={isUser ? "end" : "start"} className="w-40">
                <DropdownMenuItem onClick={handleCopy}>
                  <Copy className="mr-2 h-3 w-3" />
                  Copy
                </DropdownMenuItem>
                
                {onEdit && (
                  <DropdownMenuItem onClick={handleEdit}>
                    <Edit className="mr-2 h-3 w-3" />
                    Edit
                  </DropdownMenuItem>
                )}
                
                {isAssistant && isLast && onRegenerate && (
                  <DropdownMenuItem onClick={handleRegenerate}>
                    <RefreshCw className="mr-2 h-3 w-3" />
                    Regenerate
                  </DropdownMenuItem>
                )}
                
                <DropdownMenuSeparator />
                
                {onDelete && (
                  <DropdownMenuItem 
                    onClick={handleDelete}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="mr-2 h-3 w-3" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>
    </div>
  )
}