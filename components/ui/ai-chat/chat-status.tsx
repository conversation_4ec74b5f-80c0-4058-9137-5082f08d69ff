'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '../button'
import { Badge } from '../badge'
import { 
  Loader2, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  StopCircle,
  RefreshCw
} from 'lucide-react'
import { ChatStatusProps } from './types'

export function ChatStatus({
  status,
  error,
  onRetry,
  onStop,
  className
}: ChatStatusProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'idle':
        return {
          icon: Clock,
          label: 'Ready',
          color: 'text-muted-foreground',
          bgColor: 'bg-muted/50',
          showActions: false
        }
      case 'submitted':
        return {
          icon: Loader2,
          label: 'Sending...',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          showActions: true,
          spinning: true
        }
      case 'streaming':
        return {
          icon: Loader2,
          label: 'AI is responding...',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          showActions: true,
          spinning: true
        }
      case 'ready':
        return {
          icon: CheckCircle,
          label: 'Ready for next message',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          showActions: false
        }
      case 'error':
        return {
          icon: AlertCircle,
          label: error?.message || 'Something went wrong',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          showActions: true
        }
      default:
        return {
          icon: Clock,
          label: 'Unknown status',
          color: 'text-muted-foreground',
          bgColor: 'bg-muted/50',
          showActions: false
        }
    }
  }

  const config = getStatusConfig()
  const Icon = config.icon
  const canStop = status === 'submitted' || status === 'streaming'
  const canRetry = status === 'error'

  // Don't show status for ready state unless there's an error
  if (status === 'ready' && !error) {
    return null
  }

  return (
    <div className={cn(
      "flex items-center justify-between p-3 border-t",
      config.bgColor,
      className
    )}>
      <div className="flex items-center gap-2">
        <Icon className={cn(
          "w-4 h-4",
          config.color,
          config.spinning && "animate-spin"
        )} />
        
        <span className={cn("text-sm font-medium", config.color)}>
          {config.label}
        </span>

        {status === 'streaming' && (
          <Badge variant="outline" className="text-xs animate-pulse">
            Typing...
          </Badge>
        )}
      </div>

      {config.showActions && (
        <div className="flex items-center gap-2">
          {canStop && onStop && (
            <Button
              variant="outline"
              size="sm"
              onClick={onStop}
              className="h-7 px-2 text-xs"
            >
              <StopCircle className="w-3 h-3 mr-1" />
              Stop
            </Button>
          )}

          {canRetry && onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="h-7 px-2 text-xs"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Retry
            </Button>
          )}
        </div>
      )}
    </div>
  )
}