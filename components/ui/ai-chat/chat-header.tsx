'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '../button'
import { Badge } from '../badge'
import { Avatar, AvatarFallback, AvatarImage } from '../avatar'
import { 
  Settings, 
  Trash2, 
  MoreHorizontal,
  Bot,
  Wifi,
  WifiOff
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../dropdown-menu'
import { ChatHeaderProps, ChatStatus } from './types'

export function ChatHeader({
  title = "AI Assistant",
  subtitle,
  avatar,
  status = 'ready',
  onClear,
  onSettings,
  className
}: ChatHeaderProps) {
  const getStatusInfo = (currentStatus: ChatStatus) => {
    switch (currentStatus) {
      case 'idle':
        return { label: 'Ready', color: 'bg-green-500', icon: Wifi }
      case 'submitted':
        return { label: 'Sending', color: 'bg-blue-500', icon: Wifi }
      case 'streaming':
        return { label: 'Responding', color: 'bg-blue-500 animate-pulse', icon: Wifi }
      case 'ready':
        return { label: 'Online', color: 'bg-green-500', icon: Wifi }
      case 'error':
        return { label: 'Error', color: 'bg-red-500', icon: WifiOff }
      default:
        return { label: 'Unknown', color: 'bg-gray-500', icon: WifiOff }
    }
  }

  const statusInfo = getStatusInfo(status)
  const StatusIcon = statusInfo.icon

  return (
    <div className={cn(
      "flex items-center justify-between p-4 border-b bg-muted/30",
      className
    )}>
      <div className="flex items-center space-x-3">
        <div className="relative">
          <Avatar className="h-10 w-10">
            <AvatarImage src={avatar} alt={title} />
            <AvatarFallback>
              <Bot className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>
          
          {/* Status indicator */}
          <div className={cn(
            "absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-background",
            statusInfo.color
          )} />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-sm truncate">
              {title}
            </h3>
            <Badge variant="outline" className="text-xs">
              <StatusIcon className="w-3 h-3 mr-1" />
              {statusInfo.label}
            </Badge>
          </div>
          
          {subtitle && (
            <p className="text-xs text-muted-foreground truncate">
              {subtitle}
            </p>
          )}
        </div>
      </div>

      <div className="flex items-center space-x-1">
        {onSettings && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onSettings}
            className="h-8 w-8 p-0"
          >
            <Settings className="h-4 w-4" />
            <span className="sr-only">Settings</span>
          </Button>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">More options</span>
            </Button>
          </DropdownMenuTrigger>
          
          <DropdownMenuContent align="end" className="w-48">
            {onSettings && (
              <>
                <DropdownMenuItem onClick={onSettings}>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
              </>
            )}
            
            {onClear && (
              <DropdownMenuItem 
                onClick={onClear}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Clear Chat
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
}