'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { Button } from '../button'
import { Alert, AlertDescription } from '../alert'
import { 
  AlertTriangle, 
  RefreshCw, 
  X,
  Wifi,
  WifiOff,
  Server,
  Clock
} from 'lucide-react'
import { ChatErrorProps } from './types'

export function ChatError({
  error,
  onRetry,
  onDismiss,
  className
}: ChatErrorProps) {
  const getErrorConfig = (errorMessage: string, errorCode?: string) => {
    // Network errors
    if (errorMessage.toLowerCase().includes('network') || errorMessage.toLowerCase().includes('fetch')) {
      return {
        icon: WifiOff,
        title: 'Connection Error',
        description: 'Unable to connect to the server. Please check your internet connection.',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50 border-orange-200'
      }
    }

    // Rate limit errors
    if (errorMessage.toLowerCase().includes('rate limit') || errorMessage.toLowerCase().includes('too many')) {
      return {
        icon: Clock,
        title: 'Rate Limit Exceeded',
        description: 'Too many requests. Please wait a moment before trying again.',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50 border-yellow-200'
      }
    }

    // Server errors
    if (errorMessage.toLowerCase().includes('server') || errorMessage.toLowerCase().includes('500')) {
      return {
        icon: Server,
        title: 'Server Error',
        description: 'The server encountered an error. Please try again later.',
        color: 'text-red-600',
        bgColor: 'bg-red-50 border-red-200'
      }
    }

    // Authentication errors
    if (errorMessage.toLowerCase().includes('auth') || errorMessage.toLowerCase().includes('unauthorized')) {
      return {
        icon: AlertTriangle,
        title: 'Authentication Error',
        description: 'Please check your credentials and try again.',
        color: 'text-red-600',
        bgColor: 'bg-red-50 border-red-200'
      }
    }

    // Default error
    return {
      icon: AlertTriangle,
      title: 'Something went wrong',
      description: errorMessage || 'An unexpected error occurred. Please try again.',
      color: 'text-red-600',
      bgColor: 'bg-red-50 border-red-200'
    }
  }

  const config = getErrorConfig(error.message, error.code)
  const Icon = config.icon

  return (
    <Alert className={cn(
      "m-4",
      config.bgColor,
      className
    )}>
      <div className="flex items-start gap-3">
        <Icon className={cn("w-5 h-5 mt-0.5", config.color)} />
        
        <div className="flex-1 min-w-0">
          <div className={cn("font-medium text-sm", config.color)}>
            {config.title}
          </div>
          
          <AlertDescription className="mt-1 text-sm">
            {config.description}
          </AlertDescription>

          {/* Error details (for development) */}
          {process.env.NODE_ENV === 'development' && error.details && (
            <details className="mt-2">
              <summary className="text-xs text-muted-foreground cursor-pointer hover:text-foreground">
                Technical Details
              </summary>
              <pre className="mt-1 text-xs bg-muted/50 p-2 rounded overflow-auto max-h-32">
                {JSON.stringify(error.details, null, 2)}
              </pre>
            </details>
          )}

          {/* Error timestamp */}
          <div className="mt-2 text-xs text-muted-foreground">
            {error.timestamp.toLocaleTimeString()}
          </div>
        </div>

        <div className="flex items-center gap-1">
          {onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRetry}
              className="h-8 px-2 text-xs"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Retry
            </Button>
          )}

          {onDismiss && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDismiss}
              className="h-8 w-8 p-0"
            >
              <X className="w-3 h-3" />
            </Button>
          )}
        </div>
      </div>
    </Alert>
  )
}