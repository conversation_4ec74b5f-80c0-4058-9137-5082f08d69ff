'use client'

import React, { useEffect, useRef } from 'react'
import { cn } from '@/lib/utils'
import { ScrollArea } from '../scroll-area'
import { ChatMessage } from './chat-message'
import { ChatTyping } from './chat-typing'
import { ChatMessagesProps } from './types'

export function ChatMessages({
  messages,
  isLoading = false,
  onMessageDelete,
  onMessageEdit,
  onRegenerate,
  customToolRenderer,
  className
}: ChatMessagesProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const bottomRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [messages.length, isLoading])

  if (messages.length === 0 && !isLoading) {
    return (
      <div className={cn(
        "flex-1 flex items-center justify-center p-8",
        className
      )}>
        <div className="text-center space-y-2">
          <div className="text-4xl">💬</div>
          <h3 className="text-lg font-medium">Start a conversation</h3>
          <p className="text-sm text-muted-foreground max-w-sm">
            Send a message to begin chatting with the AI assistant.
          </p>
        </div>
      </div>
    )
  }

  return (
    <ScrollArea 
      ref={scrollAreaRef}
      className={cn("flex-1", className)}
    >
      <div className="space-y-0">
        {messages.map((message, index) => (
          <ChatMessage
            key={message.id}
            message={message}
            isLast={index === messages.length - 1}
            onDelete={onMessageDelete}
            onEdit={onMessageEdit}
            onRegenerate={index === messages.length - 1 ? onRegenerate : undefined}
            customToolRenderer={customToolRenderer}
          />
        ))}
        
        {isLoading && (
          <ChatTyping isVisible={true} />
        )}
        
        {/* Scroll anchor */}
        <div ref={bottomRef} className="h-1" />
      </div>
    </ScrollArea>
  )
}