'use client'

import React, { useState, useRef, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '../button'
import { Textarea } from '../textarea'
import { Badge } from '../badge'
import { 
  Send, 
  Paperclip, 
  X,
  Image,
  FileText,
  Mic,
  MicOff,
  Sparkles,
  ChevronDown,
  Zap,
  Brain,
  Code,
  Palette,
  Plus
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../popover'
import { ChatInputProps, ChatAttachment, ModelProvider } from './types'
import { createAttachmentFromFile, validateAttachment, formatFileSize } from './utils'
import { toast } from 'sonner'

// Default model providers
const defaultModelProviders: ModelProvider[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    icon: <Sparkles className="w-4 h-4" />,
    models: [
      { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable model', maxTokens: 128000 },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Fast and efficient', maxTokens: 128000 },
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Advanced reasoning', maxTokens: 128000 },
    ]
  },
  {
    id: 'anthropic',
    name: 'Anthropic',
    icon: <Brain className="w-4 h-4" />,
    models: [
      { id: 'claude-3-5-sonnet', name: 'Claude 3.5 Sonnet', description: 'Best overall performance', maxTokens: 200000 },
      { id: 'claude-3-haiku', name: 'Claude 3 Haiku', description: 'Fast and affordable', maxTokens: 200000 },
    ]
  },
  {
    id: 'google',
    name: 'Google',
    icon: <Zap className="w-4 h-4" />,
    models: [
      { id: 'gemini-pro', name: 'Gemini Pro', description: 'Multimodal capabilities', maxTokens: 32000 },
      { id: 'gemini-flash', name: 'Gemini Flash', description: 'Ultra-fast responses', maxTokens: 32000 },
    ]
  }
]

export function ChatInput({
  value,
  onChange,
  onSubmit,
  placeholder = "Type your message...",
  disabled = false,
  allowAttachments = true,
  allowVoiceInput = true,
  maxLength = 4000,
  modelProviders = defaultModelProviders,
  selectedModel = 'gpt-4o',
  onModelChange,
  showModelSelector = true,
  className
}: ChatInputProps) {
  const [attachments, setAttachments] = useState<ChatAttachment[]>([])
  const [isRecording, setIsRecording] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Get current model info
  const currentModel = modelProviders
    .flatMap(provider => provider.models)
    .find(model => model.id === selectedModel)
  
  const currentProvider = modelProviders
    .find(provider => provider.models.some(model => model.id === selectedModel))

  const handleSubmit = useCallback((e?: React.FormEvent) => {
    e?.preventDefault()
    
    if ((!value.trim() && attachments.length === 0) || disabled) {
      return
    }

    onSubmit(value, attachments)
    setAttachments([])
  }, [value, attachments, disabled, onSubmit])

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }, [handleSubmit])

  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || !allowAttachments) return

    const maxFileSize = 10 * 1024 * 1024 // 10MB
    const allowedTypes = ['image/', 'text/', 'application/pdf']

    for (const file of Array.from(files)) {
      if (!validateAttachment(file, maxFileSize, allowedTypes)) {
        toast.error(`File ${file.name} is too large or not supported`)
        continue
      }

      try {
        const attachment = await createAttachmentFromFile(file)
        setAttachments(prev => [...prev, attachment])
        toast.success(`Added ${file.name}`)
      } catch (error) {
        toast.error(`Failed to add ${file.name}`)
      }
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [allowAttachments])

  const removeAttachment = useCallback((attachmentId: string) => {
    setAttachments(prev => prev.filter(att => att.id !== attachmentId))
  }, [])

  const toggleRecording = useCallback(() => {
    if (!isRecording) {
      // Start recording
      setIsRecording(true)
      toast.info('Voice recording started')
      // TODO: Implement actual voice recording
    } else {
      // Stop recording
      setIsRecording(false)
      toast.info('Voice recording stopped')
      // TODO: Process recorded audio
    }
  }, [isRecording])

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = 'auto'
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`
    }
  }, [])

  React.useEffect(() => {
    adjustTextareaHeight()
  }, [value, adjustTextareaHeight])

  const canSubmit = (value.trim().length > 0 || attachments.length > 0) && !disabled
  const remainingChars = maxLength - value.length

  return (
    <div className={cn("bg-background", className)}>
      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <div className="px-4 py-3 border-t bg-muted/30">
          <div className="flex flex-wrap gap-2">
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center gap-2 bg-background rounded-lg p-2 text-sm border shadow-sm"
              >
                {attachment.contentType.startsWith('image/') ? (
                  <Image className="w-4 h-4 text-blue-500" />
                ) : (
                  <FileText className="w-4 h-4 text-gray-500" />
                )}
                
                <div className="flex-1 min-w-0">
                  <div className="truncate font-medium">{attachment.name}</div>
                  {attachment.size && (
                    <div className="text-xs text-muted-foreground">
                      {formatFileSize(attachment.size)}
                    </div>
                  )}
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAttachment(attachment.id)}
                  className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Main Input Container */}
      <div className="p-4 border-t">
        <form onSubmit={handleSubmit} className="space-y-3">
          {/* Model Selector */}
          {showModelSelector && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-muted-foreground">Model:</span>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 gap-2 text-sm"
                      disabled={disabled}
                    >
                      {currentProvider?.icon}
                      <span className="font-medium">{currentModel?.name || 'Select Model'}</span>
                      <ChevronDown className="w-3 h-3 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80 p-0" align="start">
                    <div className="p-2">
                      <div className="text-sm font-medium mb-2 px-2">Choose Model</div>
                      {modelProviders.map((provider) => (
                        <div key={provider.id} className="mb-3 last:mb-0">
                          <div className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-muted-foreground">
                            {provider.icon}
                            {provider.name}
                          </div>
                          <div className="space-y-1">
                            {provider.models.map((model) => (
                              <Button
                                key={model.id}
                                variant={selectedModel === model.id ? "secondary" : "ghost"}
                                size="sm"
                                className="w-full justify-start h-auto p-2"
                                onClick={() => {
                                  onModelChange?.(model.id)
                                }}
                              >
                                <div className="text-left">
                                  <div className="font-medium">{model.name}</div>
                                  {model.description && (
                                    <div className="text-xs text-muted-foreground">
                                      {model.description}
                                    </div>
                                  )}
                                </div>
                              </Button>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              
              {currentModel?.maxTokens && (
                <Badge variant="outline" className="text-xs">
                  {currentModel.maxTokens.toLocaleString()} tokens
                </Badge>
              )}
            </div>
          )}

          {/* Input Area */}
          <div className={cn(
            "relative rounded-lg border transition-all duration-200",
            isFocused ? "ring-2 ring-ring ring-offset-2 border-ring" : "border-input",
            disabled && "opacity-50"
          )}>
            <Textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder={placeholder}
              disabled={disabled}
              maxLength={maxLength}
              className="min-h-[60px] max-h-[200px] resize-none border-0 bg-transparent px-4 py-3 pr-20 focus-visible:ring-0 focus-visible:ring-offset-0"
              rows={1}
            />
            
            {/* Inline Action Buttons */}
            <div className="absolute bottom-2 right-2 flex items-center gap-1">
              {/* Voice Recording Button */}
              {allowVoiceInput && (
                <Button
                  type="button"
                  variant={isRecording ? "destructive" : "ghost"}
                  size="sm"
                  onClick={toggleRecording}
                  className="h-8 w-8 p-0"
                  disabled={disabled}
                >
                  {isRecording ? (
                    <MicOff className="w-4 h-4" />
                  ) : (
                    <Mic className="w-4 h-4" />
                  )}
                </Button>
              )}

              {/* File Attachment Button */}
              {allowAttachments && (
                <>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    onChange={(e) => handleFileSelect(e.target.files)}
                    className="hidden"
                    accept="image/*,text/*,.pdf"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={disabled}
                    className="h-8 w-8 p-0"
                  >
                    <Paperclip className="w-4 h-4" />
                  </Button>
                </>
              )}

              {/* Send Button */}
              <Button
                type="submit"
                disabled={!canSubmit}
                size="sm"
                className={cn(
                  "h-8 w-8 p-0 transition-all duration-200",
                  canSubmit 
                    ? "bg-primary hover:bg-primary/90 text-primary-foreground" 
                    : "bg-muted text-muted-foreground"
                )}
              >
                <Send className="w-4 h-4" />
              </Button>
            </div>

            {/* Character count */}
            {maxLength && (
              <div className={cn(
                "absolute top-2 right-2 text-xs transition-colors",
                remainingChars < 100 ? "text-orange-500" : "text-muted-foreground",
                remainingChars < 0 && "text-destructive"
              )}>
                {remainingChars}
              </div>
            )}
          </div>

          {/* Helper Text */}
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div>
              Press <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Enter</kbd> to send, 
              <kbd className="px-1 py-0.5 bg-muted rounded text-xs ml-1">Shift+Enter</kbd> for new line
            </div>
            {allowAttachments && (
              <div>Drag & drop files to attach</div>
            )}
          </div>
        </form>
      </div>
    </div>
  )
}